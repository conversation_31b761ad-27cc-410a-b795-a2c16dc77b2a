<?php

namespace App\Events;

use App\Events\Event;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

/**
 * Class ImportRateHotel
 * @package App\Events
 */
class ImportRateHotel extends Event
{
    use SerializesModels;

    /**
     * ImportRateHotel constructor.
     * @param $Data
     */
    public function __construct($Data)
    {
        $this->Data = $Data;
    }

    /**
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
