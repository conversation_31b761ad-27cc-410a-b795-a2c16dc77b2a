<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Class Notice
 * @package App\Events
 */
class Notice implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    var $data;

    /**
     * Create a new event instance.
     *
     * @param $data
     * @return void
     */
    public function __construct($data = [])
    {
        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('notice-channel');
    }

    /**
     * @return string
     */
    public function broadcastAs()
    {
        return 'notice-received';
    }
}
