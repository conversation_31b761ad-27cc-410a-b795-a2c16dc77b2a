<?php

namespace App\Events;

use App\Events\Event;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

/**
 * Class PrintFile
 * @package App\Events
 */
class PrintFile extends Event
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param $Data
     */
    public function __construct($Data)
    {
        $this->Data = $Data;
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [];
    }
}
