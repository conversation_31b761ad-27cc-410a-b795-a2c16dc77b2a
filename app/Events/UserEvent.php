<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

/**
 * Class UserEvent
 * @package App\Events
 */
class UserEvent implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    protected $data;
    protected $UserID;

    /**
     * Create a new event instance.
     *
     * @param $UserID
     * @param $data
     */
    public function __construct($UserID,$data)
    {
        $this->UserID = $UserID;
        $this->data = $data;
    }

    /**
     * @return array
     */
    public function broadcastWith()
    {
        return ['data' => $this->data];
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('user-channel-'.$this->UserID);
    }

    /**
     * @return string
     */
    public function broadcastAs()
    {
        return 'user-event';
    }
}
