<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Class TestEvent
 * @package App\Events
 */
class TestEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $message;

    /**
     * TestEvent constructor.
     * @param $message
     */
    public function __construct($message)
    {
        $this->message = $message;
    }


    /**
     * @return array
     */
    public function broadcastWith()
    {
        return ['msg' => $this->message];
    }


    /**
     * @return Channel|Channel[]
     */
    public function broadcastOn()
    {
        return new Channel('ChatPublic');
    }

    /**
     * @return string
     */
    public function broadcastAs()
    {
        return 'test.msg';
    }
}



