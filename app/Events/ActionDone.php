<?php

namespace App\Events;

use App\Events\Event;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

/**
 * Class ActionDone
 * @package App\Events
 */
class ActionDone extends Event implements ShouldBroadcast
{
    use SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the channels the event should be broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return ['action-did-occur'];
    }

    /**
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'user' => [
                'name' => 'Klark Cent',
                'age' => 30,
                'planet' => 'Crypton',
                'abilities' => 'Bashing'
            ]
        ];
    }
}
