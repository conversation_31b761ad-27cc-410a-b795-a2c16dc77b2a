<?php

namespace App\Events;

use App\Events\Event;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

/**
 * Class QuotationEmail
 * @package App\Events
 */
class QuotationEmail extends Event
{
    use SerializesModels;


    /**
     * QuotationEmail constructor.
     * @param $Data
     */
    public function __construct($Data)
    {
        $this->Data = $Data;
    }



}
