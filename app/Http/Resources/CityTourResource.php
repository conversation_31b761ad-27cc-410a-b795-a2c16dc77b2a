<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CityTourResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
          'ID' => $this->ID,
          'place' => $this->place,
          'name' => $this->name,
          'description' => $this->description,
          'duration' => $this->duration,
          'start_time' => $this->start_time,
          'end_time' => $this->end_time,
          'distance' => $this->distance,
          'type' => $this->type,
        ];
    }
}
