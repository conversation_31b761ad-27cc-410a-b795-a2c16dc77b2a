<?php

namespace App\Http\Resources;

use App\Model\Place\Place;
use Illuminate\Http\Resources\Json\JsonResource;

class ShortestPathResource2 extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $data = [
            'distance' => $this['distance'],
            'distance_formatted' => $this['distance_formatted'],
//           'same_paths' => [],
            'each_details' => [],
        ];


        /*if (is_array($this['path'])){

            foreach ($this['path'] as $key => $place){
                $data['path'][$key]['place'] = $place;
            }

        } else {
            $data['path']['place'] = [];
        }*/

        if (is_array($this['index'])){

            foreach ($this['index'] as $key => $index){
//                $data['index'][$key]['index'] = $index;

                foreach ($this['original_places_list'] as $key => $oriPlace){
                    if ($index == $key){

                        $coordinates = Place::find($oriPlace['cityId']);

//                        $oriPlace['longitude'] = $coordinates->longitude;
//                        $oriPlace['latitude'] = $coordinates->latitude;

                        $data['path'][$key] = $oriPlace;
                    }
                }
            }

        } else {
            $data['index']['index'] = [];
        }

        /*if (is_array($this['same_paths'])){

            foreach ($this['same_paths'] as $key => $samePath){
                $data['same_paths'][$key]['same_paths'] = $samePath;
            }

        } else {
            $data['same_paths']['same_paths'] = [];
        }*/

        if (is_array($this['each_details'])){

            foreach ($this['each_details'] as $key => $eachDetail){
//                $data['each_details'][$key] = current($eachDetail);

                $eachDetail = current($eachDetail);

                $data['each_details'][$key] = [
                    'ID' => $eachDetail['ID'],
                    'from' => $eachDetail['from'],
                    'to' => $eachDetail['to'],
                    'distance' => $eachDetail['distance'],
                    'time' => $eachDetail['time'],
//                    'created_at' => $eachDetail['created_at'] === null ? '' : $eachDetail['created_at'],
                ];


            }

        } else {
            $data['each_details']['each_details'] = [];
        }

        return $data;
    }
}
