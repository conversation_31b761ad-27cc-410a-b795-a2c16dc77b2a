<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AttractionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'ID' => $this->ID,
            'place' => $this->place,
            'duration' => $this->duration,
            'address' => $this->address,
            'description' => $this->description,
            'point' => $this->point,
            'name' => $this->name,
            'opening' => $this->operning,
            'closing' => $this->closing,
            'distance' => $this->distance,
            'time' => $this->time,
            'longitude' => $this->longitude,
            'latitude' => $this->latitude,
            'type' => $this->type,
            'en_route' => $this->en_route,
        ];
    }
}
