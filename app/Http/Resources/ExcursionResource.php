<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ExcursionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
          'ID' => $this->ID,
          'from' => $this->from,
          'to' => $this->to,
          'name' => $this->name,
          'description' => $this->description,
          'duration' => $this->duration,
          'opening' => $this->operning,
          'closing' => $this->closing,
          'distance' => $this->distance,
          'type' => $this->type
        ];
    }
}
