<?php

namespace App\Http\Resources;

use App\Model\Place\Place;
use App\Model\Place\Stop;
use Illuminate\Http\Resources\Json\JsonResource;

class ShortestPathResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {

        $data = [
            'distance' => $this['distance'],
            'distance_formatted' => $this['distance_formatted'],
            'same_paths' => [],
            'each_details' => [],
        ];


        if (is_array($this['path'])) {
            foreach ($this['path'] as $key => $place) {
                $original = $this['original_places_list'];
//                $data['path'][$key]['place'] = $place;
                array_splice($original, 0, 0);
                foreach ($original as $keyP => $ori) {
                    if ($ori == $place) {
                        $data['path'][$key]['type'] = $keyP;
                        break;
                    } else {
                        $data['path'][$key]['type'] = "city";
                    }
                }
                if (Count($this['path']) - 1 == $key)
                    $data['path'][$key]['type'] = "dropoff";

                $coordinates = Place::find($place);
                $data['path'][$key]['cityId'] = $place;
                $data['path'][$key]['city_name'] = $coordinates->name;
                $data['path'][$key]['city_description'] = $coordinates->description;
                $data['path'][$key]['longitude'] = $coordinates->longitude;
                $data['path'][$key]['latitude'] = $coordinates->latitude;
            }

        } else {
            $data['path']['place'] = [];
        }

        if (is_array($this['index'])) {

            foreach ($this['index'] as $key => $index) {
                $data['index'][$key]['index'] = $index;
            }

        } else {
            $data['index']['index'] = [];
        }

        if (is_array($this['same_paths'])) {

            foreach ($this['same_paths'] as $key => $samePath) {
                $data['same_paths'][$key]['same_paths'] = $samePath;
            }

        } else {
            $data['same_paths']['same_paths'] = [];
        }

        if (is_array($this['each_details'])) {

            foreach ($this['each_details'] as $key => $eachDetail) {
//                $data['each_details'][$key] = current($eachDetail);

                $eachDetail = current($eachDetail);

                $data['each_details'][$key] = [
                    'ID' => $eachDetail['ID'],
                    'from' => $eachDetail['from'],
                    'from_city' => Place::find($eachDetail['from'])->name,
                    'to' => $eachDetail['to'],
                    'to_city' => Place::find($eachDetail['to'])->name,
                    'distance' => $eachDetail['distance'],
                    'time' => $eachDetail['time'],
//                    'created_at' => $eachDetail['created_at'] === null ? '' : $eachDetail['created_at'],
                ];


            }

        } else {
            $data['each_details']['each_details'] = [];
        }

        return $data;
    }
}
