<?php

namespace App\Http\Controllers\Profile;

use App\Http\Controllers\Controller;
use App\Model\Image\Image;
use App\Model\Profile\Profile as UserProfile;
use App\User;
use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;

/**
 * Class Profile
 * @package App\Http\Controllers\Profile
 */
class Profile extends Controller
{

    /**
     * @param bool $ID
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getProfileView($ID = false)
    {
        return view("user.main");
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getProfileEdit()
    {
        return view("user.profile-edit");
    }

    /**
     * @param Request $request
     * @return mixed
     */
    function getProfileSave(Request $request)
    {
        $rules = [
            "first_name" => "required",
            "last_name" => "required",
            "email" => "required|email|unique:users,email," . Auth::user()->id,
            "tel" => "required|numeric",
            "dob" => "required|date",

        ];
        $rules_pw = [
            "password" => "required|min:3'",
            "password_confirmation" => "required|same:password",

        ];

        $Data = $request->all();
        $Validator = \Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'][] = $messages;
            $Status['status'] = 'invalid';
        }


        #if password set
        if ($request->has('password') && !empty(request('s'))) {

            $ValidatorPW = \Validator::make($Data, $rules_pw);


            if ($ValidatorPW->fails()) {
                $messages = $ValidatorPW->messages();
                $Status['error'][] = $messages;
                $Status['status'] = 'invalid';
            }
        }

        if (empty($Status)) {

            try {
                DB::transaction(function () use ($Data, $request) {

                    $UserUpdate = User::find(\Auth::user()->id);
                    $UserUpdate->name = trim($Data['first_name'] . ' ' . $Data['last_name']);
                    $UserUpdate->email = $Data['email'];

                    if ($request->input('password')) {
                        $UserUpdate->password = \Hash::make($Data['password']);
                    }


                    $UserUpdate->save();


                    #PROfile
                    UserProfile::where('user', Auth::user()->id)
                        ->update([
                            'first_name' => $Data['first_name'],
                            'tel' => $Data['tel'],
                            'last_name' => $Data['last_name'],
                            'dob' => $Data['dob']
                        ]);
                });

                if ($request->file('pro_pic')) {
                    //image upload
                    \File::deleteDirectory(public_path('assets/image/user/' . Auth::user()->id));
                    \Storage::disk('image')->put('/user/' . Auth::user()->id . '/0.jpg', file_get_contents($request->file('pro_pic')));

                    #profile
                    User::where('id', Auth::user()->id)->update(['updated_at' => Carbon::now()]);
                }
                $img['1x'] = Image::getImageDirect(Auth::user()->id, '1x', 'user', 1, Auth::user()->name)[0] . '?' . time();
                $img['2x'] = Image::getImageDirect(Auth::user()->id, '2x', 'user', 1, Auth::user()->name)[0] . '?' . time();
                $img['3x'] = Image::getImageDirect(Auth::user()->id, '3x', 'user', 1, Auth::user()->name)[0] . '?' . time();

                $Status['html'] = \View::make("system.success.success-bootstrap", ['Message' => "Setting is Updated!"])->render();
                $Status['data']['img'] = $img;
                $Status['status'] = 'OK';
                return $Status;

            } catch (\Exception $e) {

                $Status['html'] = \View::make("system.error.error-bootstrap", ['Message' => $e->getMessage().$e->getLine()])->render();
                $Status['status'] = 'OK';
                return $Status;
            } catch (\Throwable $e) {
            }
        } else
            return $Status;

    }

    /**
     * @return \Illuminate\Support\Collection
     */
    function getUsers(){


        $Data = request()->all();

        $List = new User();

        if (!empty($Data['q'])) {//name
            $List = $List->where('name', 'like', '%' . $Data['q'] . '%');
        }
        return $List->limit(10)->get();

    }
}
