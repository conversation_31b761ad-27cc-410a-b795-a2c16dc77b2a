<?php


namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use App\Model\Hotel\Hotel;
use App\Model\Image\Image;
use App\Model\Place\Attraction;
use App\Model\Place\Place;
use App\Model\Place\Stop;
use App\Model\Weather\Weather;
use DB;
use Auth;

/**
 * Class Map
 * @package App\Http\Controllers\Home
 */
class Map extends Controller
{


    /**
     * @param bool $type
     * @return array
     */
    function search($type = false)
    {

        $q = request('q') ?? "";
        $q = urldecode($q);


        $Image = new Image();
        $Place = new Place();
        $Result = array();


        if (!$type) {

            if ($Cordinate = $Place->isCorditante($q)) {

                $Result[] = array(
                    'id' => $Cordinate['lat'],
                    'value' => $Cordinate['lat'] . ', ' . $Cordinate['lng'],
                    'lat' => $Cordinate['lat'],
                    'lng' => $Cordinate['lng'],
                    'name' => $Cordinate['lat'] . ', ' . $Cordinate['lng'],
                    'description' => "Cordinates",
                    'type' => "Cordinate",
                    'img' => ''

                );


            }

            //city list
            foreach (Place::where('name', 'LIKE', "%$q%")->get() as $PlaceItem) {

                $StopAv = Stop::where('place', $PlaceItem->ID)->first();
                $Result[] = array(
                    'id' => $PlaceItem->ID,
                    'value' => $PlaceItem->value,
                    'lat' => $PlaceItem->latitude,
                    'lng' => $PlaceItem->longitude,
                    'name' => $PlaceItem->name,
                    'description' => $PlaceItem->description,
                    'img' => $Image->getImage($PlaceItem->ID, '1x', 'place', 1),
                    'type' => "Place",
                    'is_stop' => $StopAv ? true : false,
                    'stop_type' => $StopAv ? $StopAv->type : true

                );
            }


            //attraction list
            foreach (Attraction::where('name', 'LIKE', "%$q%")->select(DB::raw("name as value,latitude as lat, longitude as lon, name, description, id"))->get() as $ItemAttraction) {
                $Result[] = array(
                    'id' => $ItemAttraction->id,
                    'value' => $ItemAttraction->value,
                    'lat' => $ItemAttraction->lat,
                    'lng' => $ItemAttraction->lon,
                    'name' => $ItemAttraction->name,
                    'description' => $ItemAttraction->description,
                    'img' => $Image->getImage($ItemAttraction->id, '1x', 'attraction', 1),
                    'type' => "Attraction"

                );
            }

            //hotel list
            foreach (Hotel::where('name', 'LIKE', "%$q%")->select(DB::raw("name as value,latitude as lat, longitude as lon, name, SUBSTRING(description,1,150) as description, id,provider"))->limit(20)->get() as $ItemHotel) {
                $Result[] = array(
                    'id' => $ItemHotel->id,
                    'value' => $ItemHotel->value,
                    'lat' => $ItemHotel->lat,
                    'lng' => $ItemHotel->lon,
                    'name' => $ItemHotel->name,
                    'description' => $ItemHotel->description,
                    'type' => "Hotel " . ($ItemHotel->provider == 1 ? '(HB)' : '(AP)'),
                    'img' => $Image->getImage($ItemHotel->id, '1x', 'hotel', 1)

                );
            }

        } elseif ($type == 'place') {

            $country = request('country');
            $stops = request('stops');


            $PlaceList = Place::where('name', 'LIKE', "%$q%")
                ->where('availability', '=', 0)
                ->where('type', '=', 1);

            if ($country) {
                $PlaceList->where('country', "$country");
            }
            if ($stops) {
                $PlaceList->has("stop");
            }

            $PlaceList->orderBy(DB::raw("LOCATE('$q', name)"));
            $PlaceList->orderBy('popularity', "DESC");
            $PlaceList->limit(50);


            //city list
            foreach ($PlaceList->get() as $PlaceItem) {

                $StopAv = Stop::where('place', $PlaceItem->ID)->first();



                $Result[] = [
                    'id' => $PlaceItem->ID,
                    'popularity' => $PlaceItem->popularity,
                    'lat' => doubleval($PlaceItem->latitude),
                    'lng' => doubleval($PlaceItem->longitude),
                    'name' => $PlaceItem->name,
                    'description' => $PlaceItem->description ? implode(' ', array_slice(explode(' ', $PlaceItem->description), 0, 6)): $PlaceItem->latitude . ", " . $PlaceItem->longitude,
                    'img' => $Image->getImage($PlaceItem->ID, '1x', 'place', 1),
                    'type' => "Place",
                    'country' => intval($PlaceItem->country),
                    'is_stop' => $StopAv ? true : false,
                    'stop_type' => $StopAv ? intval($StopAv->type) : true


                ];
            }

        } elseif ($type == 'country') {
            $userCompany = Auth::user()->Profile()->first()->company;
            $PlaceList = Place::where('name', 'LIKE', "%$q%")
                ->where('status', '=', 1)
                ->where('type', '=', 2);

            if($userCompany != 1) {
                $PlaceList->whereNotIn('ID',[256]);
            }


            $PlaceList->orderBy(DB::raw("LOCATE('$q', name)"));
            $PlaceList->orderBy('popularity', "DESC");
            $PlaceList->limit(50);


            //city list
            foreach ($PlaceList->get() as $PlaceItem) {

                $StopAv = Stop::where('place', $PlaceItem->ID)->first();

                $Result[] = array(
                    'id' => $PlaceItem->ID,
                    'popularity' => $PlaceItem->popularity,
                    'lat' => $PlaceItem->latitude,
                    'lng' => $PlaceItem->longitude,
                    'name' => $PlaceItem->name,
                    'description' => $PlaceItem->description ?? $PlaceItem->latitude . ", " . $PlaceItem->longitude,
                    'img' => $Image->getImage($PlaceItem->ID, '1x', 'place', 1),
                    'type' => "Place",
                    'country' => $PlaceItem->country,
                    'is_stop' => $StopAv ? true : false,
                    'stop_type' => $StopAv ? $StopAv->type : true


                );
            }

        } elseif ($type == 'hotel') {
            //hotel list
            foreach (Hotel::where('name', 'LIKE', "%$q%")->select(DB::raw("name as value,latitude as lat, longitude as lon, name, SUBSTRING(description,1,150) as description, id,provider"))->limit(20)->get() as $ItemHotel) {
                $Result[] = array(
                    'id' => $ItemHotel->id,
                    'value' => $ItemHotel->value,
                    'lat' => $ItemHotel->lat,
                    'lng' => $ItemHotel->lon,
                    'name' => $ItemHotel->name,
                    'description' => $ItemHotel->description,
                    'type' => "Hotel " . ($ItemHotel->provider == 1 ? '(HB)' : '(AP)'),
                    'img' => $Image->getImage($ItemHotel->id, '1x', 'hotel', 1)

                );
            }

        }


        return $Result;


    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getPlaceMarker()
    {

        return view("element.map.place-marker");

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getMarker()
    {

        return view("element.map.marker");

    }

    /**
     * @param $PlaceID
     * @return array
     */
    function getPlace($PlaceID)
    {

        $Weather = new Weather();
        $Place = Place::find($PlaceID)->toArray();
        $StopAv = Stop::where('place', $Place['ID'])->first();

        $Place['is_stop'] = $StopAv ? true : false;
        $Place['stop_type'] = $StopAv ? $StopAv->type : false;
        $Place['stop_icon'] = Place::getStopType(Stop::where("place",$Place['ID'])->first()->type??false);

        $Place['weather'] = $Weather->getPlaceWeather($PlaceID);


        return $Place;

    }

}