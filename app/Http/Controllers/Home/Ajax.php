<?php

namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use App\Model\Home\Map;
use App\Model\Image\Image;
use App\Model\Place\Place;
use Illuminate\Http\Request;

/**
 * Class Ajax
 * @package App\Http\Controllers\Home
 */
class Ajax extends Controller
{


    public function index()
    {

    }


    /**
     * @param $markerID
     * @param $PlaceID
     * @return \Illuminate\Http\JsonResponse
     */
    function getPlaceName($markerID, $PlaceID)
    {

        $Place = new Place();
        $Place = $Place->getPlace($PlaceID);

        return response()->json(["ID" => $Place->ID, "markerID" => $markerID, "name" => $Place->name]);

    }

    /**
     * @param $PlaceID
     * @return \Illuminate\Http\JsonResponse
     */
    function getPlace($PlaceID)
    {

        $Place = new Place();
        $Place = $Place->getPlace($PlaceID);


        return response()->json($Place);


    }

    /**
     * @param $PlaceID
     * @return \Illuminate\Http\JsonResponse
     */
    function getPlaceInfo($PlaceID)
    {

        $Place = new Place();
        $Image = new Image();

        $Place = $Place->getPlace($PlaceID);
        $PlaceImages = $Image->getImage($PlaceID, '3x', "place", 10);

        return response()->json(['place_details' => $Place, 'place_images' => $PlaceImages]);


    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    function getAirportPlaces()
    {
        $Place = new Place();
        $Places = $Place->getAirportPlaces();
        return response()->json($Places);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    function getDefaultPlacesJson()
    {
        $Place = new Place();
        $Places = $Place->getDefaultPlacesJson();
        return response()->json($Places);
    }

    /**
     * @param int $Limit
     * @return \Illuminate\Http\JsonResponse
     */
    function getPlacesJson($Limit = 5)
    {


        $Place = new Place();
        $Places = $Place->getPlaceList($Limit);

        return response()->json($Places);


    }


    /**
     * @param Request $request
     * @param $lan
     * @param $lat
     * @return array
     */
    function getNearestPlace(Request $request , $lan, $lat)
    {

        $Place = new Place();
        $Place = $Place->getNearestPlace($lan, $lat, $request->session()->get('quotation.country'));

        if ($Place)
            return ["status"=>true,'data'=>$Place];
        else
            return ["status"=>false,'data'=>['longitude' => $lat, 'latitude' => $lan]];

    }


    /**
     * @param $ID
     * @param $marker
     * @param $type
     * @param $Selected
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getMarkerMenu($ID, $marker, $type, $Selected)
    {

        switch ($type) {
            case 'place':
                return view('home.markermenu', ['ID' => $ID, 'marker' => $marker, 'type' => $type, 'Selected' => $Selected]);
                break;
            case 'hotel':
                return view('element.hotel.hotel-menu', ['ID' => $ID, 'marker' => $marker, 'type' => $type, 'Selected' => $Selected]);
                break;
            case 'attraction':
                return view('element.attraction.attraction-menu', ['ID' => $ID, 'marker' => $marker, 'type' => $type, 'Selected' => $Selected]);
                break;

        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function searchPlace(Request $request)
    {

        $name = $request->input('q');

        $Place = new Place();
        $Place = $Place->searchPlace($name);
        return response()->json($Place);
    }

    /**
     * @param $Type
     * @param $Waypoints
     */
    function getRouterJson($Type, $Waypoints)
    {


        $Map = new Map();
        $Direction = $Map->getORSMDirection($Type, $Waypoints);

        header('Content-Type: application/json');

        //router.project-osrm.org/route/v1

        //print_r($_REQUEST);

        echo $Direction;

    }

    /**
     * @param $lng
     * @param $lat
     * @return \Illuminate\Http\JsonResponse
     */
    function getMapAreaPlaces($lng, $lat)
    {


        $Place = new Place();
        $Places = $Place->getAreaPlaces($lng, $lat);


        if ($Place)
            return response()->json($Places);
        else
            return response()->json(array('longitude' => $lat, 'latitude' => $lan));

    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function getShortestPath(Request $request)
    {

        $Place = new Place();
        $Places = $request->input('place');
        $Markers = array_keys($Places);




        $Airport = $Place->getDefaultAirport();


        if ($request->input('airport_pick_up')) {
            array_unshift($Markers, -1);
            array_unshift($Places, $Airport->ID);
        }
        if ($request->input('airport_drop_off')) {
            array_push($Markers, -2);
            array_push($Places, $Airport->ID);
        }

        $ShortestPath = $Place->getShortestPath($Places, $Markers);

        return response()->json($ShortestPath);
    }

    /**
     * @param Request $request
     */
    function getBestPath(Request $request)
    {

        $Place = new Place();
        $Places = $request->input('place');
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function getDirection(Request $request)
    {

        $Place = new Place();
        $Places = $request->input('place');


        return response()->json($Place->getDirection($Places));

    }


    /**
     * @param Request $request
     */
    function getDirectionFlight(Request $request)
    {
        $Place = new Place();
        $Places = $request->input('place');
        $TravelBy = $request->input('travel_by');
        $Airport = $Place->getDefaultAirport();

        $Direction = $Place->getDirection($Places, $TravelBy);


    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function getDirectionPolyline(Request $request)
    {

        $Place = new Place();
        $Places = $request->input('place');
        $TravelType = $request->input('type');

        return jsona($Place->getDirectionPolyline($Places, $TravelType));
    }


}