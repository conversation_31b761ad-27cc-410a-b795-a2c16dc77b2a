<?php


namespace App\Http\Controllers\Tool;

use App\Events\TestEvent;
use App\Http\Controllers\Controller;
use App\Model\Hotel\Allotment;
use App\Model\Hotel\Contact;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\HotelTBO;
use App\Model\Hotel\Rates;
use App\Model\Hotel\RatesChild;
use App\Model\Hotel\StopSale;
use App\Model\Hotel\Supplement;
use App\Model\Place\Attraction;
use App\Model\Place\AttractionType;
use App\Model\Place\CityTour;
use App\Model\Place\Distance;
use App\Model\Place\Excursion;
use App\Model\Place\Place;
use App\Model\Place\PlaceHotelbed;
use App\Model\Place\Stop;
use App\Model\Restaurant\Restaurant;
use App\Model\Restaurant\RestaurantMealRate;
use App\Model\Tool\Tool as ToolModel;
use App\Model\Vehicle\TransportCost;
use App\Notifications\EmailSent;
use Carbon\Carbon;
use Colors\RandomColor;
use DB;
use File;
use Goodby\CSV\Import\Standard\Interpreter;
use Goodby\CSV\Import\Standard\Lexer;
use Goodby\CSV\Import\Standard\LexerConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use ImageGenerator;
use Intervention\Image\ImageManager;
use Lifeeka\JSQL\Client;
use Mail;
use Session;
use SKAgarwal\GoogleApi\PlacesApi;

/**
 * Class Tool
 * @package App\Http\Controllers\Tool
 */
class Tool extends Controller
{


    function Test()
    {
        \Log::error('My Error', ['custom_message' => 'new error that has a different custom message will group...'],'ds');

    }

    function setCoordinates()
    {
        ini_set('max_execution_time', 0);
        foreach (Place::whereNull("longitude")->get() as $PlaceItem) {

            $Country = urlencode(Place::find($PlaceItem->country)->name);


            $Json = json_decode(@file_get_contents("https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($PlaceItem->name) . "+$Country+&key=AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc"), true);


            if (!isset($Json['results'][0]['geometry']['location']))
                continue;


            $lat = $Json['results'][0]['geometry']['location']['lat'];
            $lng = $Json['results'][0]['geometry']['location']['lng'];

            Place::where('ID', $PlaceItem->ID)->update(['longitude' => $lng, 'latitude' => $lat]);

        }

    }

    function setCountryPlace()
    {
        ini_set('max_execution_time', 0);
        foreach (Place::where('type', '=', 2)->get() as $PlaceItem) {

            /*//$Country = urlencode(Place::find($PlaceItem->country)->name);

            $googlePlaces = new PlacesApi(env('GOOGLE_API_KEY'));
            $response = $googlePlaces->textSearch(urlencode($PlaceItem->name));
            sd($response);*/

            //set country
            Place::where('ID', $PlaceItem->ID)->update(['country' => $PlaceItem->ID]);


        }

    }

    function setDescriptions()
    {


        $Tool = new \App\Model\Tool\Tool();

        $Tool->getWikiContent('Mirissa');

    }


    function setHotelCordinates()
    {
        ini_set('max_execution_time', 0);
        foreach (Hotel::whereNull('longitude')->get() as $HotelItem) {

            $Place = urlencode(Place::find($HotelItem->city)->name);
            $Country = urlencode(Place::find(Place::find($HotelItem->city)->country)->name);


            $Json = json_decode(@file_get_contents("https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($HotelItem->name) . "+$Place+$Country+&key=AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc"), true);


            if (!isset($Json['results'][0]['geometry']['location'])) {
                s($HotelItem->toArray());
                continue;
            }


            $lat = $Json['results'][0]['geometry']['location']['lat'];
            $lng = $Json['results'][0]['geometry']['location']['lng'];
            $address = $Json['results'][0]['formatted_address'];

            Hotel::where('ID', $HotelItem->ID)->update(['longitude' => $lng, 'latitude' => $lat, 'address' => $address]);


        }


    }

    function setCordinatesHotelBeds()
    {
        ini_set('max_execution_time', 0);
        foreach (PlaceHotelbed::all() as $PlaceItem) {

            $Country = urlencode(Place::find($PlaceItem->country)->name);

            $Json = json_decode(@file_get_contents("https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($PlaceItem->name) . "+$Country+&key=AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc"), true);


            if (!isset($Json['results'][0]['geometry']['location']))
                continue;


            $lat = $Json['results'][0]['geometry']['location']['lat'];
            $lng = $Json['results'][0]['geometry']['location']['lng'];

            PlaceHotelbed::where('ID', $PlaceItem->ID)->update(['longitude' => $lng, 'latitude' => $lat]);


        }


    }

    function csvReader()
    {
        ini_set('max_execution_time', 0);

        $lexer = new Lexer(new LexerConfig());
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {

            $row = arrayMapMulti('getActualDataType', $row);


            if ($Line) {

                $Distance = Distance::where("from", $row[0])->where('to', $row[1])->first();


                if ($Distance) {
                    $Transport = new TransportCost();
                    $Transport->distance_id = $Distance->ID;
                    $Transport->vehicle_type = $row[2];
                    $Transport->rate = floatval(str_replace(',', '', $row[3]));
                    $Transport->save();


                } else {
                    sd($Distance, $row);
                }

            }

            $Line++;


        });

        $lexer->parse(base_path('data_file/dds.csv'), $interpreter);


    }


    function setDistance()
    {

        ini_set('max_execution_time', 0);
        $ToolModel = new ToolModel();


        $Permutations = $ToolModel->getPermutaion(Place::where('country', 256)->where('type', 1)->pluck('ID')->toArray(), 2);
        //DB::table('apple_place_distances')->truncate();


        foreach ($Permutations as $PermutationsCouple) {

            $PlaceItem1 = Place::find($PermutationsCouple[0]);
            $PlaceItem2 = Place::find($PermutationsCouple[1]);


            $DistanceAvailable = DB::table('apple_place_distances')->where('from', '=', $PermutationsCouple[0])->where('to', '=', $PermutationsCouple[1])->get();

            if ($DistanceAvailable->isNotEmpty()) {
                continue;
            }

            $Json = json_decode(file_get_contents("https://maps.googleapis.com/maps/api/distancematrix/json?origins=$PlaceItem1->longitude+$PlaceItem1->latitude&destinations=$PlaceItem2->longitude+$PlaceItem2->latitude&key=" . env('GOOGLE_API_KEY')), true);


            if (isset($Json['rows'][0]['elements'][0]['distance'])) {

                $DistanceData = $Json['rows'][0]['elements'][0];

                $Distance = new Distance();

                $Distance->from = $PermutationsCouple[0];
                $Distance->to = $PermutationsCouple[1];
                $Distance->distance = $DistanceData['distance']['value'];
                $Distance->time = $DistanceData['duration']['value'];
                $Distance->save();


            } else {
                $Distance = new Distance();

                $Distance->from = $PermutationsCouple[0];
                $Distance->to = $PermutationsCouple[1];
                $Distance->distance = 0;
                $Distance->time = 0;
                $Distance->save();

            }
        }


    }

    function setDirection()
    {

        ini_set('max_execution_time', 0);

        $DistanceTable = DB::table('apple_place_distances')->get();

        foreach ($DistanceTable as $PlacePair) {


            $DistanceAvailable = DB::table('apple_place_direction')->where('distance_id', '=', $PlacePair->ID)->get();


            if ($DistanceAvailable) {
                continue;
            }


            $Origin = Place::find($PlacePair->from);
            $Destination = Place::find($PlacePair->to);

            $Json = json_decode(file_get_contents("https://maps.googleapis.com/maps/api/directions/json?origin=$Origin->latitude+$Origin->longitude&destination=$Destination->latitude+$Destination->longitude&key=AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc"));


            foreach ($Json->routes as $route) {
                foreach ($route->legs as $leg) {
                    foreach ($leg->steps as $StepID => $step) {

                        sp($PlacePair->ID);
                        sp($StepID);
                        sp($step->distance->value);
                        sp($step->duration->value);
                        sp($step->polyline->points);
                        sp($step->travel_mode);
                        sp($step->html_instructions);
                        sp("---------------------------------------");


                        sd();

                        DB::table('apple_place_direction')->insert(
                            [
                                'distance_id' => $PlacePair->ID,
                                'step' => $StepID,
                                'distance' => $step->distance->value,
                                'duration' => $step->duration->value,
                                'polyline' => $step->polyline->points,
                                'travel_mode' => $step->travel_mode,
                                'instructions' => $step->html_instructions

                            ]
                        );


                    }
                }

            }

        }


    }

    /**
     * @param Request $request
     */
    function showSession(Request $request)
    {
        sd($request->session()->get('quotation'));
    }

    /**
     * @param Request $request
     */
    function showSessionAll(Request $request)
    {
        sd($request->session()->all());
    }

    /**
     * @param Request $request
     */
    function showSessionDD(Request $request)
    {
        dd($request->session()->get('quotation'));


    }
    function  showSessionjson(Request $request){
        return $request->session()->get('quotation');
    }

    /**
     * @param Request $request
     */
    function sessionTest(Request $request)
    {
        Session::put("quotation.hotel", file_get_contents(storage_path("test/sample.txt")));


    }

    /**
     * @return mixed
     */
    function createImage()
    {


        // create a new image resource
        $img = ImageGenerator::canvas(50, 50, RandomColor::one(array('hue' => 'red')));
        $img->text('S', 20, 25, function ($font) {
            $font->file(public_path("/build/assets/fonts/OpenSans-Regular.ttf"));
            $font->size(30);
            $font->color('#fff');
            $font->align('center');
            $font->valign('middle');
        });

        // send HTTP header and output image data
        return $img->response();

    }

    /**
     * @return bool|null
     */
    function removeHotelbedsRates()
    {
        return Rates::whereNotNull('rate_key')->forceDelete();
    }


    function importStopSells()
    {
        ini_set('max_execution_time', 0);

        $lexer = new Lexer(new LexerConfig());
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {

            if ($Line != 0) {

                if (Hotel::where("ID", $row[3])->first()) {


                    $StartDate = Carbon::createFromFormat('d/m/Y', date('d/m/Y', $row[1]));
                    $EndDate = Carbon::createFromFormat('d/m/Y', date('d/m/Y', $row[2]));


                    $AllotmentUsed = new StopSale();

                    $AllotmentUsed->hotel = $row[3];
                    $AllotmentUsed->start_year = $StartDate->year;
                    $AllotmentUsed->start_month = $StartDate->month;
                    $AllotmentUsed->start_day = $StartDate->day;
                    $AllotmentUsed->end_year = $EndDate->year;
                    $AllotmentUsed->end_month = $EndDate->month;
                    $AllotmentUsed->end_day = $EndDate->day;


                    $AllotmentUsed->save();
                } else {
                    sd($row);
                }


            };

            $Line++;
        });

        $lexer->parse(base_path('data_file/stopsales.csv'), $interpreter);


    }


    function importSupplements()
    {
        ini_set('max_execution_time', 0);

        $lexer = new Lexer(new LexerConfig());
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {

            if ($Line != 0) {

                if (Hotel::where("ID", $row[5])->first()) {


                    $StartDate = Carbon::createFromFormat('d/m/Y', date('d/m/Y', $row[2]));
                    $EndDate = Carbon::createFromFormat('d/m/Y', date('d/m/Y', $row[3]));


                    $Supplement = new Supplement();

                    $Supplement->hotel = $row[5];
                    $Supplement->name = $row[1];
                    $Supplement->type = $row[4];
                    $Supplement->meal_type = $row[6];
                    $Supplement->adult_rate = $row[7];
                    $Supplement->child_rate = $row[8];
                    $Supplement->additional = $row[9];

                    $Supplement->start_year = $StartDate->year;
                    $Supplement->start_month = $StartDate->month;
                    $Supplement->start_day = $StartDate->day;
                    $Supplement->end_year = $EndDate->year;
                    $Supplement->end_month = $EndDate->month;
                    $Supplement->end_day = $EndDate->day;

                    $Supplement->save();
                } else {
                    s($row[5]);
                }


            };

            $Line++;
        });

        $lexer->parse(base_path('data_file/suppliments.csv'), $interpreter);


    }


    function importAllotments()
    {
        ini_set('max_execution_time', 0);

        $lexer = new Lexer(new LexerConfig());
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {

            if ($Line != 0) {

                if (Hotel::where("ID", $row[5])->first()) {


                    $StartDate = Carbon::createFromFormat('d/m/Y', date('d/m/Y', $row[1]));
                    $EndDate = Carbon::createFromFormat('d/m/Y', date('d/m/Y', $row[2]));


                    $AllotmentUsed = new Allotment();

                    $AllotmentUsed->hotel = $row[5];
                    $AllotmentUsed->room = $row[3];
                    $AllotmentUsed->start_year = $StartDate->year;
                    $AllotmentUsed->start_month = $StartDate->month;
                    $AllotmentUsed->start_day = $StartDate->day;
                    $AllotmentUsed->end_year = $EndDate->year;
                    $AllotmentUsed->end_month = $EndDate->month;
                    $AllotmentUsed->end_day = $EndDate->day;
                    $AllotmentUsed->release_period = $row[4];


                    $AllotmentUsed->save();
                } else {
                    sd($row[5]);
                }


            };

            $Line++;
        });

        $lexer->parse(base_path('data_file/allotment.csv'), $interpreter);


    }

    function importHotelRate()
    {
        ini_set('max_execution_time', 0);

        $config = new LexerConfig();
        $config
            ->setToCharset('UTF-8')// Customize target encoding. Default value is null, no converting.
            ->setFromCharset('Windows-1252') // Customize CSV file encoding. Default value is null.
        ;


        $lexer = new Lexer($config);
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {


            if ($Line != 0) {//ignore first line

                $StartDate = Carbon::parse($row[1]);
                $EndDate = Carbon::parse($row[2]);

                $HotelID = $row[4];
                /*
                                $StartDate = Carbon::parse($row[1]);
                                $EndDate = Carbon::parse($row[2]);
                */


                $Rate = new Rates();

                $Rate->start_year = $StartDate->year;
                $Rate->start_month = $StartDate->month;
                $Rate->start_day = $StartDate->day;
                $Rate->end_year = $EndDate->year;
                $Rate->end_month = $EndDate->month;
                $Rate->end_day = $EndDate->day;


                $Rate->meal = $row[3];
                $Rate->hotel = $HotelID;
                $Rate->rate = $row[5];
                $Rate->room_type = $row[8];
                $Rate->room_category = $row[9];
                $Rate->market = $row[10];

                $Rate->save();


                $RateChild = new RatesChild();

                $RateChild->start_year = $StartDate->year;
                $RateChild->start_month = $StartDate->month;
                $RateChild->start_day = $StartDate->day;
                $RateChild->end_year = $EndDate->year;
                $RateChild->end_month = $EndDate->month;
                $RateChild->end_day = $EndDate->day;

                $RateChild->age_from = 2;
                $RateChild->age_to = 12;

                $RateChild->meal = $row[3];
                $RateChild->hotel = $HotelID;
                $RateChild->rate = $row[6];
                $RateChild->room_type = $row[8];
                $RateChild->room_category = $row[9];
                $RateChild->market = $row[10];

                $RateChild->save();


                $RateChild = new RatesChild();

                $RateChild->start_year = $StartDate->year;
                $RateChild->start_month = $StartDate->month;
                $RateChild->start_day = $StartDate->day;
                $RateChild->end_year = $EndDate->year;
                $RateChild->end_month = $EndDate->month;
                $RateChild->end_day = $EndDate->day;

                $RateChild->age_from = 0;
                $RateChild->age_to = 2;

                $RateChild->meal = $row[3];
                $RateChild->hotel = $HotelID;
                $RateChild->rate = $row[7];
                $RateChild->room_type = $row[8];
                $RateChild->room_category = $row[9];
                $RateChild->market = $row[10];

                $RateChild->save();


            }


            $Line++;
        });

        $lexer->parse(base_path('data_file/2017_6_1 rates.csv'), $interpreter);


    }


    function importHotelRateChild()
    {
        ini_set('max_execution_time', 0);

        $config = new LexerConfig();
        $config
            ->setToCharset('UTF-8')// Customize target encoding. Default value is null, no converting.
            ->setFromCharset('Windows-1252') // Customize CSV file encoding. Default value is null.
        ;


        $lexer = new Lexer($config);
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {


            if ($Line == 0) {//ignore first line
                sp($row);
            }

            if ($Line != 0) {//ignore first line


                $StartDate = Carbon::createFromTimestamp($row[1]);
                $EndDate = Carbon::createFromTimestamp($row[2]);

//                DB::beginTransaction();
                try {


                    $RateChild = new RatesChild();

                    $RateChild->start_year = $StartDate->year;
                    $RateChild->start_month = $StartDate->month;
                    $RateChild->start_day = $StartDate->day;
                    $RateChild->end_year = $EndDate->year;
                    $RateChild->end_month = $EndDate->month;
                    $RateChild->end_day = $EndDate->day;

                    $RateChild->age_from = 0;
                    $RateChild->age_to = 2;

                    $RateChild->meal = $row[3];
                    $RateChild->hotel = $row[4];
                    $RateChild->rate = $row[7];
                    $RateChild->room_type = $row[8];
                    $RateChild->room_category = $row[9];
                    $RateChild->market = $row[10];

                    $RateChild->save();
                    DB::commit();


                    $RateChild = new RatesChild();

                    $RateChild->start_year = $StartDate->year;
                    $RateChild->start_month = $StartDate->month;
                    $RateChild->start_day = $StartDate->day;
                    $RateChild->end_year = $EndDate->year;
                    $RateChild->end_month = $EndDate->month;
                    $RateChild->end_day = $EndDate->day;

                    $RateChild->age_from = 2;
                    $RateChild->age_to = 12;

                    $RateChild->meal = $row[3];
                    $RateChild->hotel = $row[4];
                    $RateChild->rate = $row[6];
                    $RateChild->room_type = $row[8];
                    $RateChild->room_category = $row[9];
                    $RateChild->market = $row[10];

                    $RateChild->save();


//                    DB::commit();


                } catch (\Exception $e) {
//                    DB::rollback();
                    sp($e->getMessage());
                    sp($Line);
                    spd($row);
                }

            }


            $Line++;
        });

        $lexer->parse(base_path('data_file/child rate missing.csv'), $interpreter);


    }


    function importHotelRateHotelMatch()
    {
        ini_set('max_execution_time', 0);

        $config = new LexerConfig();
        $config
            ->setToCharset('UTF-8')// Customize target encoding. Default value is null, no converting.
            ->setFromCharset('Windows-1252') // Customize CSV file encoding. Default value is null.
        ;


        $lexer = new Lexer($config);
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {


            /*if (Hotel::where('name', trim(iconv("Windows-1252","UTF-8", $row[0])))->get()->isEmpty()) {
                sd($row[0]);
            }*/


            if ($Line != 0) {//ignore first line


                if ($HotelID = Hotel::where('name', trim($row[0]))->first()) {
                    $HotelID = $HotelID->ID;
                } else {
                    s($Line);
                    $HotelID = Hotel::where('name', 'like', '"' . trim($row[0] . '"'))->first();
                    s(trim($row[0]));
                    s($HotelID);
                    sd($row);
                }

                /*
                                $StartDate = Carbon::createFromFormat('d/m/Y', date('d/m/Y', $row[1]));
                                $EndDate = Carbon::createFromFormat('d/m/Y', date('d/m/Y', $row[2]));

                              $HotelID = $row[4];
               */

                $StartDate = Carbon::parse($row[1]);
                $EndDate = Carbon::parse($row[2]);


                $Rate = new Rates();

                $Rate->start_year = $StartDate->year;
                $Rate->start_month = $StartDate->month;
                $Rate->start_day = $StartDate->day;
                $Rate->end_year = $EndDate->year;
                $Rate->end_month = $EndDate->month;
                $Rate->end_day = $EndDate->day;


                $Rate->meal = $row[4];
                $Rate->hotel = $HotelID;
                $Rate->rate = $row[3];
                $Rate->room_type = $row[7];
                $Rate->room_category = $row[8];
                $Rate->market = $row[9];

                $Rate->save();


                $RateChild = new RatesChild();

                $RateChild->start_year = $StartDate->year;
                $RateChild->start_month = $StartDate->month;
                $RateChild->start_day = $StartDate->day;
                $RateChild->end_year = $EndDate->year;
                $RateChild->end_month = $EndDate->month;
                $RateChild->end_day = $EndDate->day;

                $RateChild->age_from = 0;
                $RateChild->age_to = 2;

                $RateChild->meal = $row[4];
                $RateChild->hotel = $HotelID;
                $RateChild->rate = $row[5];
                $RateChild->room_type = $row[7];
                $RateChild->room_category = $row[8];
                $RateChild->market = $row[9];

                $RateChild->save();


                $RateChild = new RatesChild();

                $RateChild->start_year = $StartDate->year;
                $RateChild->start_month = $StartDate->month;
                $RateChild->start_day = $StartDate->day;
                $RateChild->end_year = $EndDate->year;
                $RateChild->end_month = $EndDate->month;
                $RateChild->end_day = $EndDate->day;

                $RateChild->age_from = 2;
                $RateChild->age_to = 12;

                $RateChild->meal = $row[4];
                $RateChild->hotel = $HotelID;
                $RateChild->rate = $row[6];
                $RateChild->room_type = $row[7];
                $RateChild->room_category = $row[8];
                $RateChild->market = $row[9];

                $RateChild->save();


            }


            $Line++;
        });

        $lexer->parse(base_path('data_file/wasundara rates.csv'), $interpreter);


    }


    function searchTBOhotel()
    {

        $HotelTBO = new HotelTBO();
        $HotelResult = $HotelTBO->getLowestHotel();

        spd($HotelResult);


        foreach ($HotelResult['HotelResultList']['HotelResult'] as $HotelIndex => $HotelSettings) {


        }

    }

    function sendCustomSoap()
    {

        $HotelTBO = new HotelTBO();
        $HotelTBO->sendCustomSoap();

    }


    function hotelBedPlaces()
    {
        ini_set('max_execution_time', 0);

//        $GoogleCLient = new \Google_Client();
//        $GoogleCLient->setApplicationName("geocode");
//        $GoogleCLient->setDeveloperKey("AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc");
//
//
//        sd();


        $HotelBed = new HotelBed();
        $List = $HotelBed->getCountryList(1, 1000, 'LK');

        foreach ($List as $Countryitem) {


//            if(Place::where('A2',$Countryitem['code'])->count())
//                continue;
//
//            $Place = new Place();
//
//            $Place->name = $Countryitem['description']['content'];
//            $Place->A2  = $Countryitem['code'];
//            $Place->status  =  1;
//            $Place->type  = 2;
//
//            $Place->save();


            $HotelBed = new HotelBed();
            $List2 = $HotelBed->getDestinationList(1, 1000, $Countryitem['code']);


            foreach ($List2 as $Countryitem2) {

                if (!isset($Countryitem2['name'])) {
                    sp($Countryitem2);
                    continue;
                }

                $Cn = Place::where("A2", $Countryitem2['countryCode'])->first();
                $Json = json_decode(@file_get_contents("https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($Countryitem2['name']['content']) . "+$Cn->name+&key=AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc"), true);

                $Location = $Json['results'][0]['geometry']['location'];

                DB::table('apple_places_hotelbed')->insert(['name' => $Countryitem2['name']['content'], 'country' => $Cn->ID, 'code' => $Countryitem2['code'], 'longitude' => $Location['lng'], 'latitude' => $Location['lat']]);


            }

        }


    }

    function fixChildRate()
    {


        ini_set('max_execution_time', 0);

        $RateArray = [];
        $DataItemPrev = [];


        foreach (DB::table('apple_hotel_room_rates_child')->get() as $DataItem) {
//            ->where('ID','>',10000)

            /*
                        if($DataItem->age_from==0) {
                            $RateArray['cnb']['rate'] = $DataItem->rate;
                            $RateArray['cnb']['ID'] = $DataItem->ID;
                        }

                        if($DataItem->age_from==2) {
                            $RateArray['cwb']['rate'] = $DataItem->rate;
                            $RateArray['cwb']['ID'] = $DataItem->ID;
                        }

                        if(count($RateArray)==2){

                            if(
                                $RateArray['cwb']['rate']>0 &&

                                $DataItem->start_year == $DataItemPrev->start_year  &&
                                $DataItem->start_month  == $DataItemPrev->start_month &&
                                $DataItem->start_day  == $DataItemPrev->start_day &&
                                $DataItem->end_year  == $DataItemPrev->end_year &&
                                $DataItem->end_day   == $DataItemPrev->end_day &&
                                $DataItem->hotel   == $DataItemPrev->hotel &&
                                $DataItem->room_type  == $DataItemPrev->room_type &&
                                $DataItem->room_category  == $DataItemPrev->room_category &&
                                $DataItem->market   == $DataItemPrev->market &&

                                ($DataItem->room_type == 1 || $DataItem->room_type == 3)
                            ){


                                //DB::table('apple_hotel_room_rates_child')->where("ID",$RateArray['cwb']['ID'])->update(['rate'=>$RateArray['cnb']['rate']]);
                                //DB::table('apple_hotel_room_rates_child')->where("ID",$RateArray['cnb']['ID'])->update(['rate'=>$RateArray['cwb']['rate']]);


                                sd($DataItem);
                                $RateArray = [];

                            }

                        }


                        $DataItemPrev = $DataItem;*/

            if (($DataItem->room_type == 1 || $DataItem->room_type == 3) && $DataItem->age_from == 2) {
                DB::table('apple_hotel_room_rates_child')->where("ID", $DataItem->ID)->update(['rate' => 0]);
            }


        }

    }

    function pdfToXML()
    {
        $c = curl_init();
        $cfile = curl_file_create(base_path('data_file/test.pdf'), 'application/pdf');
        curl_setopt($c, CURLOPT_URL, 'https://pdftables.com/api?key=enyynyxjevey&format=csv');
        curl_setopt($c, CURLOPT_POSTFIELDS, array('file' => $cfile));
        curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($c, CURLOPT_ENCODING, "gzip,deflate");
        $result = curl_exec($c);
        if (curl_errno($c)) {
            print('Error calling PDFTables: ' . curl_error($c));
        }

//        sd($result);

// save the XML we got from PDFTables to a file
        file_put_contents("test.csv", $result);
        curl_close($c);

    }


    function importRestaurantsList()
    {
        ini_set('max_execution_time', 0);

        $config = new LexerConfig();
        $config
            ->setToCharset('UTF-8')// Customize target encoding. Default value is null, no converting.
            ->setFromCharset('Windows-1252') // Customize CSV file encoding. Default value is null.
        ;


        $lexer = new Lexer($config);
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {

            if ($Line == 0) {//ignore first line
                sp($row);
            }
            if ($Line != 0) {//ignore first line
                /*  [0] => City
                    [1] => Restaurant
                    [2] => Cusine
                    [3] => Rate
                    [4] => Breakfast
                    [5] => Lunch
                    [6] => Dinner
                    [7] => Breakfast
                    [8] => Lunch
                    [9] => Dinner
                    [10] => Remarks
                    [11] => Sales Person
                    [12] => Contact No.
                    [13] => E.mail
                    [14] => Restaurant Contact
                    [15] => Contact No.
                    [16] => E.mail
                    [17] => Restaurant Address
                    [18] => Website*/

                try {

                    $p2 = null;
                    if ($es = Place::where("name", $row[1])->first()) {
                        $p2 = $es->ID;
                    }


                    $Restaurant = new Restaurant();
                    $Restaurant->from = Place::where("name", $row[0])->first()->ID;
                    $Restaurant->to = $p2;
                    $Restaurant->name = $row[2];
                    $Restaurant->expensive = strlen($row[4]);
                    $Restaurant->description = $row[2];
                    $Restaurant->remarks = $row[11];
                    $Restaurant->sales_person = $row[12];
                    $Restaurant->tel = $row[13];
                    $Restaurant->email = $row[14];
                    $Restaurant->restaurant_contact = $row[15];
                    $Restaurant->restaurant_tel = $row[16];
                    $Restaurant->restaurant_email = $row[17];
                    $Restaurant->address = $row[18];
                    $Restaurant->web = $row[19];
                    $Restaurant->save();


                    if (!empty($row[8])) {
                        $RestaurantMealRate = new RestaurantMealRate();
                        $RestaurantMealRate->restaurant = $Restaurant->id;
                        $RestaurantMealRate->meal_time = 1;
                        $RestaurantMealRate->rate = $row[8];
                        $RestaurantMealRate->save();
                    }
                    if (!empty($row[9])) {

                        $RestaurantMealRate = new RestaurantMealRate();
                        $RestaurantMealRate->restaurant = $Restaurant->id;
                        $RestaurantMealRate->meal_time = 2;
                        $RestaurantMealRate->rate = $row[9];
                        $RestaurantMealRate->save();
                    }
                    if (!empty($row[10])) {

                        $RestaurantMealRate = new RestaurantMealRate();
                        $RestaurantMealRate->restaurant = $Restaurant->id;
                        $RestaurantMealRate->meal_time = 3;
                        $RestaurantMealRate->rate = $row[10];
                        $RestaurantMealRate->save();
                    }
                } catch (\Exception $e) {
                    s($row);
                    sd($e->getMessage());
                }


            }


            $Line++;
        });

        $lexer->parse(base_path('data_file/RestaurantList.csv'), $interpreter);


    }


    function importAttraction()
    {
        ini_set('max_execution_time', 0);

        $config = new LexerConfig();
        $config
            ->setToCharset('UTF-8')// Customize target encoding. Default value is null, no converting.
            ->setFromCharset('Windows-1252') // Customize CSV file encoding. Default value is null.
        ;


        $lexer = new Lexer($config);
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {


            sp($row);


            if ($Line != 0) {//ignore first line

                if ($HotelID = AttractionType::where('type', trim($row[13]))->first()) {
                    $TypeID = $HotelID->ID;
                } else {
                    s($Line);
                    sd($row);
                }

                $Attraction = new Attraction();

                $Attraction->ID = $row[0];
                $Attraction->place = $row[1];
                $Attraction->duration = $row[2];
                $Attraction->address = $row[3];
                $Attraction->description = $row[4];
                $Attraction->point = $row[5];
                $Attraction->name = $row[6];
                $Attraction->operning = $row[7];
                $Attraction->closing = $row[8];
                $Attraction->distance = $row[9];
                $Attraction->time = $row[10];
                $Attraction->longitude = $row[11];
                $Attraction->latitude = $row[12];
                $Attraction->type = $TypeID;
                $Attraction->save();


            }


            $Line++;
        });

        $lexer->parse(base_path('data_file/sd/at_info.csv'), $interpreter);


    }

    function importExcursion()
    {

        ini_set('max_execution_time', 0);

        $config = new LexerConfig();
        $config
            ->setToCharset('UTF-8')// Customize target encoding. Default value is null, no converting.
            ->setFromCharset('Windows-1252') // Customize CSV file encoding. Default value is null.
        ;


        $lexer = new Lexer($config);
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {


            sp($row);


            if ($Line != 0) {//ignore first line

                if ($HotelID = AttractionType::where('type', trim($row[9]))->first()) {
                    $TypeID = $HotelID->ID;
                } else {
                    s($Line);
                    sd($row);
                }

                $Attraction = new Excursion();

                $Attraction->ID = $row[0];
                $Attraction->from = $row[1];
                $Attraction->to = $row[2];
                $Attraction->name = $row[3];
                $Attraction->description = $row[4];
                $Attraction->duration = $row[5];
                $Attraction->operning = $row[5];
                $Attraction->closing = $row[7];
                $Attraction->distance = $row[8];
                $Attraction->type = $TypeID;
                $Attraction->save();


            }


            $Line++;
        });

        $lexer->parse(base_path('data_file/sd/ex_info.csv'), $interpreter);

    }

    function importCityTour()
    {
        ini_set('max_execution_time', 0);

        $config = new LexerConfig();
        $config
            ->setToCharset('UTF-8')// Customize target encoding. Default value is null, no converting.
            ->setFromCharset('Windows-1252') // Customize CSV file encoding. Default value is null.
        ;


        $lexer = new Lexer($config);
        $interpreter = new Interpreter();

        $Line = 0;
        $interpreter->addObserver(function (array $row) use (&$Line) {


            sp($row);


            if ($Line != 0) {//ignore first line

                if ($HotelID = AttractionType::where('type', trim($row[8]))->first()) {
                    $TypeID = $HotelID->ID;
                } else {
                    s($Line);
                    sd($row[8]);
                }


                $Attraction = new CityTour();

                $Attraction->ID = $row[0];
                $Attraction->place = $row[1];
                $Attraction->name = $row[2];
                $Attraction->description = $row[3];
                $Attraction->duration = $row[4];
                $Attraction->start_time = $row[5];
                $Attraction->end_time = $row[6];
                $Attraction->distance = $row[7];
                $Attraction->type = $TypeID;
                $Attraction->save();


            }


            $Line++;
        });

        $lexer->parse(base_path('data_file/sd/ct_info.csv'), $interpreter);


    }


    /**
     * @param int $from
     * @param int $To
     * @return array
     */
    function getHotelbedsHotel($from = 1, $To = 500)
    {

        $HotelBeds = new HotelBed();

        $HotelList = $HotelBeds->getHotelLists(["name", "address", "coordinates", "description", "images", 'destinationCode', 'countryCode', 'categoryCode'], false, false, 'LK', false, false, $from, $To);

        $Data = [];


        foreach ($HotelList->body['hotels'] as $k => $hotelItem) {
            $hotelItem['id'] = $hotelItem['code'];
            unset($hotelItem['code']);
            $Data[$k] = objectFlatten(toObject($hotelItem));
        }


        $config['host'] = env('DB_HOST');
        $config['db'] = 'test';
        $config['username'] = 'test';
        $config['password'] = 'test';
        $config['main_table'] = 'apple_hotel_hotelbeds';


        $JSQL = new Client($config);

        $JSQL->loadText(json_encode($Data));
        $JSQL->migrate();

        return [];

    }

    function importHotelContacts()
    {

        ini_set('max_execution_time', 0);


        $Excel = \App::make('excel');


        $Data = $Excel->load(base_path('data_file/hc/he.xlsx'), function ($reader) {
        });
        $DataArray = $Data->get()->toArray();

        foreach ($DataArray as $DataItem) {

            if (Hotel::find($DataItem['id'])->first()) {


                $emails = explode(",", $DataItem['email']);
                foreach ($emails as $c => $EmailItem) {

                    preg_match_all('/[\._a-zA-Z0-9-]+@[\._a-zA-Z0-9-]+/i', $EmailItem, $ext);

                    if (empty($ext[0][0]))
                        continue;

                    $Contact = new Contact();
                    $Contact->hotel = $DataItem['id'];
                    $Contact->contact_id = $ext[0][0];
                    $Contact->type = 2;
                    $Contact->note = "";
                    $Contact->save();


                }


                $Contact = new Contact();
                $Contact->hotel = $DataItem['id'];
                $Contact->contact_id = getActualDataType($DataItem['telephone_number']);
                $Contact->type = 3;
                $Contact->note = "";
                $Contact->save();


            } else {
                s($DataItem);
            }

        }

    }

    function TestMail()
    {


        s(env('MAIL_HOST'));
        s(env('MAIL_DRIVER'));
        s(env('MAIL_ENCRYPTION'));
        s(env('MAIL_PORT'));


        Mail::queue('tool.email', [], function ($m) {

            $m->to('<EMAIL>');
            $m->subject('Test Email Appleholidays!');
            $m->from('<EMAIL>');
        });

        sd(Mail::failures());


    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function sendReportForm()
    {

        $Data['screen'] = request("image");

        return view("admin.error.send-error-report", $Data);

    }

    function sendReport()
    {

        $Data['des'] = request("des");
        $Data['image'] = request("image");
        $Data['quotation'] = json_encode(Session::get('quotation'));

        $Data['d_image'] = preg_replace('#data:image/[^;]+;base64,#', '', $Data['image']);

        Mail::send('tool.email-report', $Data, function ($m) use ($Data) {

            $name = \Auth::user()->name;

//            $m->to('<EMAIL>');
            $m->to('<EMAIL>');
            $m->subject("Error Report: $name");
            $m->from(\Auth::user()->email);
            $m->attachData(base64_decode($Data['d_image']), "file.png");


        });

        if (empty(Mail::failures()))
            redirect("/");
        else
            redirect("/");


    }

    /**
     * @return array|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * @throws \Exception
     */
    function createFromCode()
    {

        try {
            $QuotationArray = json_decode(request('code'), true);

            if ($QuotationArray) {
                Session::put('quotation', $QuotationArray);
                return redirect("/");
            } else {
                return ['error' => [
                    'error' => "Invalid Json!"
                ]];
            }
        } catch (\Exception $e) {
            throw new \Exception($e);
        }


    }


    function saveHotelImages()
    {

        ini_set('max_execution_time', 0);

        $Hotel = new Hotel();

        foreach ($Hotel->orderBy('ID', 'DESC')->get() as $HotelItem) {


            if (!File::exists(public_path("assets/image/hotel/$HotelItem->ID")) || empty(\File::allFiles(public_path("assets/image/hotel/$HotelItem->ID")))) {

                echo $HotelItem->ID . '<br>';


                if (!File::exists(public_path("assets/image/hotel/$HotelItem->ID")))
                    File::makeDirectory(public_path("assets/image/hotel/$HotelItem->ID"));

                try {

                    $googlePlaces = new PlacesApi(env('GOOGLE_API_KEY'));
                    $response = $googlePlaces->nearbySearch("$HotelItem->latitude,$HotelItem->longitude", 500, [
                        'name' => $HotelItem->name
                    ]);


                    $place_id = $response->get('results')->first()['place_id'];
                    $PlaceDetails = $googlePlaces->placeDetails($place_id);
                    $manager = new ImageManager();


                    foreach ($PlaceDetails->get('result')['photos'] as $K => $PhotoItem) {

                        $photo_reference = $PhotoItem['photo_reference'];
                        $image_url = 'https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photoreference=' . $photo_reference . '&key=' . env('GOOGLE_API_KEY');

                        $manager->make($image_url)->save(public_path("assets/image/hotel/$HotelItem->ID/google_image_$K.jpg"));


                        //echo '<img src="https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photoreference=' . $photo_reference . '&key=' . env('GOOGLE_API_KEY') . '" >';

                        if ($K > 4)
                            break;

                    }

                } catch (\Exception $e) {

                    echo $e->getMessage();
                    echo "<br>";


                }

            }

        }

    }


    function saveAttractionImages()
    {

        ini_set('max_execution_time', 0);

        $Hotel = new Attraction();

        foreach ($Hotel->orderBy('ID', 'DESC')->get() as $HotelItem) {


            if (!File::exists(public_path("assets/image/attraction/$HotelItem->ID")) || empty(\File::allFiles(public_path("assets/image/attraction/$HotelItem->ID")))) {

                echo $HotelItem->ID . '<br>';


                if (!File::exists(public_path("assets/image/attraction/$HotelItem->ID")))
                    File::makeDirectory(public_path("assets/image/attraction/$HotelItem->ID"));

                try {

                    $googlePlaces = new PlacesApi(env('GOOGLE_API_KEY'));
                    $response = $googlePlaces->nearbySearch("$HotelItem->latitude,$HotelItem->longitude", 500, [

                    ]);


                    $place_id = $response->get('results')->first()['place_id'];
                    $PlaceDetails = $googlePlaces->placeDetails($place_id);
                    $manager = new ImageManager();


                    foreach ($PlaceDetails->get('result')['photos'] as $K => $PhotoItem) {

                        $photo_reference = $PhotoItem['photo_reference'];
                        $image_url = 'https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photoreference=' . $photo_reference . '&key=' . env('GOOGLE_API_KEY');
                        $manager->make($image_url)->save(public_path("assets/image/attraction/$HotelItem->ID/google_image_$K.jpg"));

                        //echo '<img src="https://maps.googleapis.com/maps/api/place/photo?maxwidth=400&photoreference=' . $photo_reference . '&key=' . env('GOOGLE_API_KEY') . '" >';

                        if ($K > 4)
                            break;

                    }

                } catch (\Exception $e) {

                    echo $e->getMessage();
                    echo "<br>";


                }

            }


        }
    }

    /**
     * @return array|null
     */
    function eventTest()
    {

        return event(new TestEvent("Test sdf"));
    }

    function NotificationTest()
    {
        $user = \Auth::user();

        //broadcast(new TestEvent("test fiction {time()}"))->toOthers();

        $user->notify(new EmailSent([
            'message' => "Email has been sent!"
        ]));


    }

    /**
     * @return string
     */
    function redis()
    {

        $redis = Redis::connection();

        $redis->publish("public", "{s:45}");

        return 'done';


    }

    function setAirports()
    {
        set_time_limit(0);

        $list = DB::table('apple_airports')->get();

        $UpdateID = substr(time() . \Auth::user()->id, -9);

        foreach ($list as $TableItem) {

            $AirportPlace = Place::where("A2", $TableItem->iata_code)
                ->first();

            if ($AirportPlace) {


                /* $Place = new Place();

                 $Place->name = $TableItem->name;
                 $Place->description = $TableItem->keywords;
                 $Place->status = 1;
                 $Place->longitude = $TableItem->longitude_deg;
                 $Place->latitude = $TableItem->latitude_deg;
                 $Place->country = $AirportPlace->country;
                 $Place->A2 = $TableItem->iata_code;
                 $Place->type = 1;
                 $Place->upload_id = $UpdateID;

                 $Place->save();

                 $Stop = new Stop();
                 $Stop->place = $Place->id;
                 $Stop->type = 1;//airport
                 $Stop->country = $AirportPlace->country;
                 $Stop->upload_id = $UpdateID;
                 $Stop->save();*/

            } else {
                $CodeE = Place::where("A2", $TableItem->iso_country)->first();

                if ($CodeE) {
                    $Place = new Place();

                    $Place->name = $TableItem->name;
                    $Place->description = $TableItem->keywords;
                    $Place->status = 1;
                    $Place->longitude = $TableItem->longitude_deg;
                    $Place->latitude = $TableItem->latitude_deg;
                    $Place->country = $CodeE->country;
                    $Place->A2 = $TableItem->iata_code;
                    $Place->type = 1;
                    $Place->upload_id = $UpdateID;

                    $Place->save();

                    $Stop = new Stop();
                    $Stop->place = $Place->id;
                    $Stop->type = 1;//airport
                    $Stop->country = $CodeE->country;
                    $Stop->upload_id = $UpdateID;
                    $Stop->save();

                } else {
                    //s("No Country",$TableItem);
                }

            }
        }


    }


}
