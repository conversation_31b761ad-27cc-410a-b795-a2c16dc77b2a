<?php


namespace App\Http\Controllers\PNL;

use App\Http\Controllers\Controller;
use App\Model\FollowUP\ClientFollowUP;
use App\Model\QuotationManage\Quotation as QuotationManage;
use App\User;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Model\FollowUP\FollowUP;
use Illuminate\Support\Facades\DB;


use App\Model\Quotation\Quotation;
use App\Model\Quotation\QuotationHotel;
use App\Model\Meal\Meal;
use App\Model\Hotel\Hotel;
use App\Model\Quotation\QuotationTransport;
use App\Model\Place\Place;
use App\Model\Meal\MealTime;



use Maatwebsite\Excel\Facades\Excel;

/**
 * Class PNLController
 * @package App\Http\Controllers\Flight
 */
class PNLController extends Controller {
    public $i = 0;
    function filters(Request $request) {
        return view("quotation.pnl.filters");
    }

    function filtersSearch(Request $request) {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '-1');

        $data = $request->all();
        $html = "";
        if (true) {
            $Quotes = QuotationManage::whereHas('Main', function ($Main) use ($data) {
                
                if (isset($data['start_date'])) {
                    $Main->where(DB::raw("STR_TO_DATE( CONCAT(apple_quotation_main.`arrival_year`, '-', apple_quotation_main.`arrival_month`, '-', apple_quotation_main.`arrival_day`), '%Y-%m-%d')"), '>=', $data['start_date']);
                }
                if (isset($data['end_date'])) {
                    $Main->where(DB::raw("STR_TO_DATE( CONCAT(apple_quotation_main.`arrival_year`, '-', apple_quotation_main.`arrival_month`, '-', apple_quotation_main.`arrival_day`), '%Y-%m-%d')"), '<=', $data['end_date']);
                }
                $Main->where('status', 2);
            });

            if(!empty($request->tour_no_start) && !empty($request->tour_no_end)) {
                $tour_no_start = explode(' ', $request->tour_no_start);
                $last_tour_no_start = end($tour_no_start);
                $tour_no_end = explode(' ', $request->tour_no_end);
                $last_tour_no_end = end($tour_no_end);

                $Quotes = $Quotes->whereHas('IsNumber', function ($Main) use ($last_tour_no_start, $last_tour_no_end) {
                    $Main->whereBetween(DB::raw("CAST(SUBSTRING_INDEX(is_number, ' ', -1) AS UNSIGNED)"), [$last_tour_no_start, $last_tour_no_end]);
                });
            }

            if(!empty($request->cntl_no_start) && !empty($request->cntl_no_end)) {
                $Quotes = $Quotes->whereBetween(DB::raw("CAST(SUBSTRING_INDEX(quotation_no, ' ', -1) AS UNSIGNED)"), [$request->cntl_no_start, $request->cntl_no_end]);
            }

            if(!empty($request->agent) && $request->agent != 'all') {
                $Quotes = $Quotes->whereHas('Status', function ($Main) use ($request) {
                    $Main->where("agent", $request->agent);
                });
            }

            $Quotes = $Quotes->paginate(10);
            $html .= '<table width="100%" class="table table-bordered table-condensed small">
                        <thead class="table-dark">
                        <tr>
                            <th>CNTL</th>
                            <th>Tour ID</th>
                            <th>Type</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Check In date</th>
                            <th>Check out date</th>
                            <th>Agent</th>
                            <th>Client</th>
                            <th>Product name</th>
                            <th>Value</th>
                            <th>Base C USD</th>
                            <th>Exchange Rate</th>
                            <th>Converted</th>
                            <th class="text-right">Currency</th>
                        </tr>
                        </thead>
                        <tbody>';
            foreach($Quotes as $key => $Quote) {
                //dump($Quote);
                if (isset($Quote->ID) && isset($Quote->quotation_no)) {
                    $QuotationArray = QuotationManage::getQuotation($Quote->ID, $Quote->quotation_no);
                    $html .= view("quotation.lost-profit-summery", ['QuotationArray' => $QuotationArray, 'requested_currency' => "USD"]);
                    //dump($QuotationArray);
                }
            }
            
            $html .= '</tbody> </table>';
            $html .= $Quotes->links();
            return $html;
        }
    }

    public function downloadPNLToExcel(Request $request) {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '-1');

        $data = $request->all();
        $html = [];
        if (!empty($request->start_date) && !empty($request->end_date)) {
            $Quotes = QuotationManage::whereHas('Main', function ($Main) use ($data) {
                if (isset($data['start_date'])) {
                    $Main->where(DB::raw("STR_TO_DATE( CONCAT(apple_quotation_main.`arrival_year`, '-', apple_quotation_main.`arrival_month`, '-', apple_quotation_main.`arrival_day`), '%Y-%m-%d')"), '>=', $data['start_date']);
                }
                if (isset($data['end_date'])) {
                    $Main->where(DB::raw("STR_TO_DATE( CONCAT(apple_quotation_main.`arrival_year`, '-', apple_quotation_main.`arrival_month`, '-', apple_quotation_main.`arrival_day`), '%Y-%m-%d')"), '<=', $data['end_date']);
                }
                $Main->where('status', 2);
            });

            $Quotes = $Quotes->paginate(10);

            foreach($Quotes as $key => $Quote) {
                if (isset($Quote->ID) && isset($Quote->quotation_no)) {
                    $QuotationArray = QuotationManage::getQuotation($Quote->ID, $Quote->quotation_no);
                    
                    $html[] = self::logicPNL($QuotationArray, "USD");
                }
            }
        }
        $i = 0;
        $data=[];
        foreach ($html as $key => $follow){
            foreach ($follow as $f){
                $data[$i]["CNTL"] = $f[0];
                $data[$i]["Tour_ID"] = $f[1];
                $data[$i]["Type"] = $f[2];
                $data[$i]["Start_Date"] = $f[3];
                $data[$i]["End_Date"] = $f[4];
                $data[$i]["Check_In_date"] = $f[5];
                $data[$i]["Check_out_date"] = $f[6];
                $data[$i]["Agent"] = $f[7];
                $data[$i]["Client"] = $f[8];
                $data[$i]["Product_name"] = $f[9];
                $data[$i]["Value"] = $f[10];
                $data[$i]["Base_C_USD"] = $f[11];
                $data[$i]["Exchange_Rate"] = $f[12];
                $data[$i]["Converted"] = $f[13];
                $data[$i]["Currency"] = $f[14];
                $i++;
            }

        }

        Excel::create('PNL Data', function($excel) use ($data) {

            $excel->sheet('Sheet', function($sheet) use ($data) {

                $sheet->fromArray($data);
            });
        })->download('xls');

        return redirect('#pnl_report_container');
        
    }


    static function logicPNL($QuotationArray, $requested_currency) {
        $Quotation = new Quotation();
        $QuotationHotel = new QuotationHotel();
        $Costcut = new \App\Model\Costcut\Costcut();
        $QuotationTransport = new QuotationTransport();

        $Budget = Quotation::getBudget($QuotationArray, $requested_currency);
        $AttractionBreakDown = Quotation::getAttractionBreakDown($QuotationArray);
        $dayCity = Quotation::getDayCity($QuotationArray);

        $OBVehicleRate = Quotation::getOBVehicleRate($QuotationArray);

        $Cost['cost_cut'] = $Costcut->getCostCutRate($QuotationArray);
        $Cost['cost_cut_pkg'] = $Costcut->getCostCutPkgRate($QuotationArray);

        $Cost = $Quotation->getCost($QuotationArray, false, true);

        //////////////////////////////////////////////

        $increment = -1;
        $AccomadationTotal = 0;
        $AccomadationDays = 0;
        $isLocal = true;
        if($QuotationArray['country'] != 62) {
            $isLocal = false;
        }

        if ($QuotationArray['tour_type'] == 6)
        {
            $counts = array_count_values($QuotationArray['accommodation']);

            if (isset($counts[2]))
            {
                /*$ownValue = $TransportCost['vehicle']['driver_accommodation'] * $counts[2];
                $Total += $ownValue * ($Days - 1); // get night*/

                foreach ($QuotationArray['accommodation'] as $key => $accommodation) {
                    if($accommodation == 2) {
                        $ownData = $QuotationArray['hotel'][$key];
                        if(isset($ownData['driver_accommodation']) && $ownData['driver_accommodation'] == 1) {
                            $count = $QuotationHotel->getNightHotelBook($ownData['check_in'], $ownData['check_out']);
                            $AccomadationTotal += $count;
                            $AccomadationDays += $count;
                        }
                    }
                }

            } /*else {
                $Total += $TransportCost['vehicle']['driver_accommodation'] * ($Days - 1); // get night
            }*/

        }

        if ($QuotationArray['tour_type'] == 3) {
            foreach ($QuotationArray['accommodation'] as $key => $accommodation) {
                $ownData = $QuotationArray['hotel'][$key];
                $rates = $QuotationArray['rate']['hotel'][$key] ?? null;
                
                if(isset($rates) && isset($rates['driver_accommodation'])) {
                    // dd($rates['driver_accommodation']);
                    foreach($rates['driver_accommodation'] as $day => $details) {
                        $AccomadationTotal += $details['modified_rate'];
                        $AccomadationDays = $AccomadationDays+1;
                    }
                } else if(isset($ownData)) {
                    if(isset($ownData['driver_accommodation']) && $ownData['driver_accommodation'] == 1) {
                        $count = $QuotationHotel->getNightHotelBook($ownData['check_in'], $ownData['check_out']);
                        $AccomadationTotal += 8 * $count; // 8 usd driver acc.
                        $AccomadationDays += $count;
                    }
                }
            }
        }
        ///////////////////////////////////////////////////////////////////////////////


        $TotalHotelCost = 0;
        $TotalTransportCostCost = 0;

        $HotelRate = $Cost['hotel'];
        $CruiseRate = $Cost['cruise'];
        $Supplement = $Cost['supplement'];
        $AttractionRate = $Cost['attraction'];
        $TransportRate = $Cost['transport'];
        $MealRate = $Cost['meal'];
        $OtherRate = $Cost['other'];
        $PP = $Cost['pp'];

        $QuotationArrayWM = $QuotationArray;
        $QuotationArrayWM['markup_amount'] = 0;
        $QuotationArrayWM['markup_amount_child'] = 0;
        $CostWM = $Quotation->getCost($QuotationArrayWM);
        $MealRates = Meal::getMeal($QuotationArray);


        if(isset($QuotationArray['other_rate'])) {
            $dayUse = $QuotationArray['other_rate'];
        }

        $beds = [];
        $totalRoomsValue = 0;
        if(!empty($dayUse)) {
            foreach ($dayUse as $key => $room) {
                if(isset($room['details']['hotel_id']) && $room['details']['hotel_id'] != 0) {
                    $beds[$key]['hotel_id'] = $room['details']['hotel_id'];
                    $hotel = Hotel::find($room['details']['hotel_id']);

                    $beds[$key]['hotel_name'] = $hotel->name;

                    $beds[$key]['singleBed'] = $room['details']['single_bed_room_count'] ?? 0;
                    $beds[$key]['doubleBed'] = $room['details']['double_bed_room_count'] ?? 0;
                    $beds[$key]['tripleBed'] = $room['details']['triple_bed_room_count'] ?? 0;

                    $singleBedRate = ($room['details']['single_bed_rate'] ?? 0) * $room['details']['single_bed_room_count'] ?? 0;
                    $doubleBedRate = ($room['details']['double_bed_rate'] ?? 0) * $room['details']['double_bed_room_count'] ?? 0;
                    $tripleBedRate = ($room['details']['triple_bed_rate'] ?? 0) * $room['details']['triple_bed_room_count'] ?? 0;

                    $totalRoomsValue += ($singleBedRate + $doubleBedRate + $tripleBedRate);

                    $beds[$key]['singleBedRate'] = $singleBedRate;
                    $beds[$key]['doubleBedRate'] = $doubleBedRate;
                    $beds[$key]['tripleBedRate'] = $tripleBedRate;

                    $beds[$key]['singleBedRatePP'] = $room['details']['single_bed_rate']/1;
                    $beds[$key]['doubleBedRatePP'] = $room['details']['double_bed_rate']/2;
                    $beds[$key]['tripleBedRatePP'] = $room['details']['triple_bed_rate']/3;
                }
            }
        }

        $Cost['total'] += $totalRoomsValue;
        $CostWM['total'] += $totalRoomsValue;

        $totalPax = $QuotationArray['pax']['adult'];

        $perPerson = $totalRoomsValue / $totalPax;

        $Nights = Quotation::getNights($QuotationArray);
        $Days = Quotation::getDays($QuotationArray);

        $Data = [];
        if (isset($QuotationArray['guide'])) {
            $Data['guide'] = QuotationTransport::getGuideCostList($QuotationArray['pax'], @$QuotationArray['guide'], $Nights, $Days);
        } else {
            $Data['guide']['total'] = 0;
        }

        $Dates = $QuotationHotel->getHotelBookDates(["year" => $QuotationArray['arrival_date']["year"], "month" => $QuotationArray['arrival_date']["month"], "day" => $QuotationArray['arrival_date']["day"]], $QuotationArray['place']);

        $arrival = (reset($Dates)['check_in']);
        $arrival_date = Carbon::create($arrival["year"], $arrival["month"], $arrival["day"])->format('d-M-Y');
        $departure = (end($Dates)['check_out']);
        $departure_date = Carbon::create($departure["year"], $departure["month"], $departure["day"])->format('d-M-Y');
        // dd($arrival, $departure);
        //dd($QuotationArray);

        $DaysDetail = Quotation::getTourDaysDetail($QuotationArray, true);
        $i = 0;
        $dataItem = [];
        if($isLocal) {
            $dataItem[$i][]  = $QuotationArray['id_list'][2];
            $dataItem[$i][]  = $QuotationArray['is_number'] ?? "";
            $dataItem[$i][]  = "Invoice";
            $dataItem[$i][]  = $arrival_date;
            $dataItem[$i][]  = $departure_date;
            $dataItem[$i][]  = "";
            $dataItem[$i][]  = "";
            $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
            $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
            $dataItem[$i][]  = "Invoice / Total cost";
            $dataItem[$i][]  = currency($Cost['total'],'USD', $requested_currency);
            $dataItem[$i][]  = currency($Cost['total'],'USD', $requested_currency);
            $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
            $dataItem[$i][]  = currency($Cost['total'],'USD', "LKR");
            $dataItem[$i][]  = "LKR";

            foreach($Budget['hotel']??[] as $key => $HotelItem) {
                    $i++;
                    $dataItem[$i][]  = $QuotationArray['id_list'][2];
                    $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                    $dataItem[$i][]  = "Hotel";
                    $dataItem[$i][]  = $arrival_date;
                    $dataItem[$i][]  = $departure_date;
                    $dataItem[$i][]  = Carbon::create($QuotationArray['hotel'][$key]['check_in']['year'], $QuotationArray['hotel'][$key]['check_in']['month'], $QuotationArray['hotel'][$key]['check_in']['day'])->format('d-M-Y');
                    $dataItem[$i][]  = Carbon::create($QuotationArray['hotel'][$key]['check_out']['year'], $QuotationArray['hotel'][$key]['check_out']['month'], $QuotationArray['hotel'][$key]['check_out']['day'])->format('d-M-Y');
                    $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                    $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                    $dataItem[$i][]  = \App\Model\Hotel\Hotel::getHotelFromAll($HotelItem['hotel_settings']['hotel'])->name;
                    $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', $requested_currency);
                    $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', $requested_currency);
                    $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                    $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', "LKR");
                    $dataItem[$i][]  = "LKR";
            }

            foreach($Budget['cruise']??[] as $key => $HotelItem) {
                    $i++;
                    $dataItem[$i][]  = $QuotationArray['id_list'][2];
                    $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                    $dataItem[$i][]  = "Cruise";
                    $dataItem[$i][]  = $arrival_date;
                    $dataItem[$i][]  = $departure_date;
                    $dataItem[$i][]  = Carbon::create($QuotationArray['cruise'][$key]['check_in']['year'], $QuotationArray['cruise'][$key]['check_in']['month'], $QuotationArray['cruise'][$key]['check_in']['day'])->format('d-M-Y');
                    $dataItem[$i][]  = Carbon::create($QuotationArray['cruise'][$key]['check_out']['year'], $QuotationArray['cruise'][$key]['check_out']['month'], $QuotationArray['cruise'][$key]['check_out']['day'])->format('d-M-Y');
                    $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                    $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                    $dataItem[$i][]  = \App\Model\Cruise\Cruise::find($HotelItem['hotel_settings']['hotel'])->name;
                    $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', $requested_currency);
                    $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', $requested_currency);
                    $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                    $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', "LKR");
                    $dataItem[$i][]  = "LKR";
            }
            
            if(!empty($Budget['attraction']['ob_items'])) {
                foreach($Budget['attraction']['ob_items'] as $dayKey => $day) {
                    if(isset($day) && !empty($day)) {
                        foreach($day as $attrType => $item) {
                            foreach($item as $subitem) {
                                $i++;
                                    $dataItem[$i][]  = $QuotationArray['id_list'][2];
                                    $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                                    $dataItem[$i][]  = $attrType;
                                    $dataItem[$i][]  = $arrival_date;
                                    $dataItem[$i][]  = $departure_date;
                                    $dataItem[$i][]  = Carbon::create($DaysDetail[$dayKey]['date']['year'], $DaysDetail[$dayKey]['date']['month'], $DaysDetail[$dayKey]['date']['day'])->format('d-M-Y');
                                    $dataItem[$i][]  = Carbon::create($DaysDetail[$dayKey]['date']['year'], $DaysDetail[$dayKey]['date']['month'], $DaysDetail[$dayKey]['date']['day'])->format('d-M-Y');
                                    $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                                    $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                                    $dataItem[$i][]  = $dayCity[$dayKey]['name'];
                                    
                                    $tot = (
                                        ($Budget['attraction']['rates'][$attrType][$subitem['ID']]['adult']??0 * intval($QuotationArray['pax']['adult'])) + 
                                        ($Budget['attraction']['rates'][$attrType][$subitem['ID']]['child']??0 * intval($QuotationArray['pax']['cwb'])) + 
                                        ($Budget['attraction']['rates'][$attrType][$subitem['ID']]['child']??0 * intval($QuotationArray['pax']['cnb'])));

                                    $dataItem[$i][]  = currency($tot,'USD', $requested_currency);
                                    $dataItem[$i][]  = currency($tot,'USD', $requested_currency);
                                    $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                                    $dataItem[$i][]  = currency($tot,'USD', "LKR");
                                    $dataItem[$i][]  = "LKR";
                            }
                        }
                    }
                }
            }

            $i++;
            $dataItem[$i][]  = $QuotationArray['id_list'][2];
            $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
            $dataItem[$i][]  = "Transport";
            $dataItem[$i][]  = $arrival_date;
            $dataItem[$i][]  = $departure_date;
            $dataItem[$i][]  = "";
            $dataItem[$i][]  = "";
            $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
            $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
            $dataItem[$i][]  = "Transport";
            $dataItem[$i][]  = currency($Budget['transport']["rates"]['total']??0,'USD', $requested_currency);
            $dataItem[$i][]  = currency($Budget['transport']["rates"]['total']??0,'USD', $requested_currency);
            $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
            $dataItem[$i][]  = currency($Budget['transport']["rates"]['total']??0,'USD', "LKR");
            $dataItem[$i][]  = "LKR";

            if($Budget['other']) {
                $i++;
                $dataItem[$i][]  = $QuotationArray['id_list'][2];
                $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                $dataItem[$i][]  = "Other";
                $dataItem[$i][]  = $arrival_date;
                $dataItem[$i][]  = $departure_date;
                $dataItem[$i][]  = "";
                $dataItem[$i][]  = "";
                $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                $dataItem[$i][]  = "Other";
                $dataItem[$i][]  = currency($Budget['other']['cost']??0,'USD', $requested_currency);
                $dataItem[$i][]  = currency($Budget['other']['cost']??0,'USD', $requested_currency);
                $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                $dataItem[$i][]  = currency($Budget['other']['cost']??0,'USD', "LKR");
                $dataItem[$i][]  = "LKR";
            }
            if(!empty($MealRates["adult"])) {
                $i++;
                $dataItem[$i][]  = $QuotationArray['id_list'][2];
                $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                $dataItem[$i][]  = "Meals";
                $dataItem[$i][]  = $arrival_date;
                $dataItem[$i][]  = $departure_date;
                $dataItem[$i][]  = "";
                $dataItem[$i][]  = "";
                $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                $dataItem[$i][]  = "Meals";
                $dataItem[$i][]  = currency($MealRate["cost"]["total"]??0,'USD', $requested_currency);
                $dataItem[$i][]  = currency($MealRate["cost"]["total"]??0,'USD', $requested_currency);
                $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                $dataItem[$i][]  = currency($MealRate["cost"]["total"]??0,'USD', "LKR");
                $dataItem[$i][]  = "LKR";
            }
        } else {
            {
                $dataItem[$i][]  = $QuotationArray['id_list'][2];
                $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                $dataItem[$i][]  = "Invoice";
                $dataItem[$i][]  = $arrival_date;
                $dataItem[$i][]  = $departure_date;
                $dataItem[$i][]  = "";
                $dataItem[$i][]  = "";
                $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                $dataItem[$i][]  = "Invoice / Total cost";
                $dataItem[$i][]  = currency($Cost['total'],'USD', $requested_currency);
                $dataItem[$i][]  = currency($Cost['total'],'USD', $requested_currency);
                $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                $dataItem[$i][]  = currency($Cost['total'],'USD', "LKR");
                $dataItem[$i][]  = "LKR";
    
                foreach($Budget['hotel']??[] as $key => $HotelItem) {
                        $i++;
                        $dataItem[$i][]  = $QuotationArray['id_list'][2];
                        $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                        $dataItem[$i][]  = "Hotel";
                        $dataItem[$i][]  = $arrival_date;
                        $dataItem[$i][]  = $departure_date;
                        $dataItem[$i][]  = Carbon::create($QuotationArray['hotel'][$key]['check_in']['year'], $QuotationArray['hotel'][$key]['check_in']['month'], $QuotationArray['hotel'][$key]['check_in']['day'])->format('d-M-Y');
                        $dataItem[$i][]  = Carbon::create($QuotationArray['hotel'][$key]['check_out']['year'], $QuotationArray['hotel'][$key]['check_out']['month'], $QuotationArray['hotel'][$key]['check_out']['day'])->format('d-M-Y');
                        $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                        $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                        $dataItem[$i][]  = \App\Model\Hotel\Hotel::getHotelFromAll($HotelItem['hotel_settings']['hotel'])->name;
                        $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', $requested_currency);
                        $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', $requested_currency);
                        $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                        $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', "LKR");
                        $dataItem[$i][]  = "LKR";
                }
    
                foreach($Budget['cruise']??[] as $key => $HotelItem) {
                        $i++;
                        $dataItem[$i][]  = $QuotationArray['id_list'][2];
                        $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                        $dataItem[$i][]  = "Cruise";
                        $dataItem[$i][]  = $arrival_date;
                        $dataItem[$i][]  = $departure_date;
                        $dataItem[$i][]  = Carbon::create($QuotationArray['cruise'][$key]['check_in']['year'], $QuotationArray['cruise'][$key]['check_in']['month'], $QuotationArray['cruise'][$key]['check_in']['day'])->format('d-M-Y');
                        $dataItem[$i][]  = Carbon::create($QuotationArray['cruise'][$key]['check_out']['year'], $QuotationArray['cruise'][$key]['check_out']['month'], $QuotationArray['cruise'][$key]['check_out']['day'])->format('d-M-Y');
                        $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                        $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                        $dataItem[$i][]  = \App\Model\Cruise\Cruise::find($HotelItem['hotel_settings']['hotel'])->name;
                        $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', $requested_currency);
                        $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', $requested_currency);
                        $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                        $dataItem[$i][]  = currency($HotelItem['rate']['total'],'USD', "LKR");
                        $dataItem[$i][]  = "LKR";
                }
                
                if(!empty($Budget['attraction']['ob_items'])) {
                    foreach($Budget['attraction']['ob_items'] as $dayKey => $day) {
                        if(isset($day) && !empty($day)) {
                            foreach($day as $attrType => $item) {
                                foreach($item as $subitem) {
                                    $i++;
                                        $dataItem[$i][]  = $QuotationArray['id_list'][2];
                                        $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                                        $dataItem[$i][]  = $attrType;
                                        $dataItem[$i][]  = $arrival_date;
                                        $dataItem[$i][]  = $departure_date;
                                        $dataItem[$i][]  = Carbon::create($DaysDetail[$dayKey]['date']['year'], $DaysDetail[$dayKey]['date']['month'], $DaysDetail[$dayKey]['date']['day'])->format('d-M-Y');
                                        $dataItem[$i][]  = Carbon::create($DaysDetail[$dayKey]['date']['year'], $DaysDetail[$dayKey]['date']['month'], $DaysDetail[$dayKey]['date']['day'])->format('d-M-Y');
                                        $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                                        $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                                        $dataItem[$i][]  = $dayCity[$dayKey]['name'];
                                        
                                        $tot = (
                                            ($Budget['attraction']['rates'][$attrType][$subitem['ID']]['adult']??0 * intval($QuotationArray['pax']['adult'])) + 
                                            ($Budget['attraction']['rates'][$attrType][$subitem['ID']]['child']??0 * intval($QuotationArray['pax']['cwb'])) + 
                                            ($Budget['attraction']['rates'][$attrType][$subitem['ID']]['child']??0 * intval($QuotationArray['pax']['cnb'])));
    
                                        $dataItem[$i][]  = currency($tot,'USD', $requested_currency);
                                        $dataItem[$i][]  = currency($tot,'USD', $requested_currency);
                                        $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                                        $dataItem[$i][]  = currency($tot,'USD', "LKR");
                                        $dataItem[$i][]  = "LKR";
                                }
                            }
                        }
                    }
                }
    
                $i++;
                $dataItem[$i][]  = $QuotationArray['id_list'][2];
                $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                $dataItem[$i][]  = "Transport";
                $dataItem[$i][]  = $arrival_date;
                $dataItem[$i][]  = $departure_date;
                $dataItem[$i][]  = "";
                $dataItem[$i][]  = "";
                $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                $dataItem[$i][]  = "Transport";
                $dataItem[$i][]  = currency($Budget['transport']["rates"]['total']??0,'USD', $requested_currency);
                $dataItem[$i][]  = currency($Budget['transport']["rates"]['total']??0,'USD', $requested_currency);
                $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                $dataItem[$i][]  = currency($Budget['transport']["rates"]['total']??0,'USD', "LKR");
                $dataItem[$i][]  = "LKR";
    
                if($Budget['other']) {
                    $i++;
                    $dataItem[$i][]  = $QuotationArray['id_list'][2];
                    $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                    $dataItem[$i][]  = "Other";
                    $dataItem[$i][]  = $arrival_date;
                    $dataItem[$i][]  = $departure_date;
                    $dataItem[$i][]  = "";
                    $dataItem[$i][]  = "";
                    $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                    $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                    $dataItem[$i][]  = "Other";
                    $dataItem[$i][]  = currency($Budget['other']['cost']??0,'USD', $requested_currency);
                    $dataItem[$i][]  = currency($Budget['other']['cost']??0,'USD', $requested_currency);
                    $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                    $dataItem[$i][]  = currency($Budget['other']['cost']??0,'USD', "LKR");
                    $dataItem[$i][]  = "LKR";
                }
                if(!empty($MealRates["adult"])) {
                    $i++;
                    $dataItem[$i][]  = $QuotationArray['id_list'][2];
                    $dataItem[$i][]  = $QuotationArray['is_number'] ?? "" ;
                    $dataItem[$i][]  = "Meals";
                    $dataItem[$i][]  = $arrival_date;
                    $dataItem[$i][]  = $departure_date;
                    $dataItem[$i][]  = "";
                    $dataItem[$i][]  = "";
                    $dataItem[$i][]  = \App\Model\Agent\Agent::find($QuotationArray['agent'])->name;
                    $dataItem[$i][]  = $QuotationArray['confirm']['client_name'];
                    $dataItem[$i][]  = "Meals";
                    $dataItem[$i][]  = currency($MealRate["cost"]["total"]??0,'USD', $requested_currency);
                    $dataItem[$i][]  = currency($MealRate["cost"]["total"]??0,'USD', $requested_currency);
                    $dataItem[$i][]  = \App\Currency::find(80)->exchange_rate;
                    $dataItem[$i][]  = currency($MealRate["cost"]["total"]??0,'USD', "LKR");
                    $dataItem[$i][]  = "LKR";
                }
            }
        }

        return $dataItem;
    }


    public function downloadCostingToExcel(Request $request) {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '-1');

        $data = $request->all();
        $QuotationArray = QuotationManage::getQuotation($data['reference_id'], $data['quotation_no']);

        $data = self::logicCostSheet($QuotationArray, "USD");

        Excel::create('Costing Data', function($excel) use ($data) {

            $excel->sheet('Sheet', function($sheet) use ($data) {
                $sheet->fromArray($data[0], null, 'A1', false, false);
                foreach($data[1] as $styleType => $style) {
                    if($styleType == "light") {
                        foreach($style as $sl) {
                            $sheet->row($sl, function($row) use ($data) {
                                $row->setBackground('#dddddd');
                            });
                        }
                    }
                    if($styleType == "dark") {
                        foreach($style as $sl) {
                            $sheet->row($sl, function($row) use ($data) {
                                $row->setBackground('#333333');
                                $row->setFontColor('#ffffff');
                            });
                        }
                    }
                }
            });
        })->download('xls');

        return redirect('#pnl_report_container');
        
    }

    static function logicCostSheet($QuotationArray, $requested_currency) {
        $Quotation = new Quotation();
        $QuotationHotel = new QuotationHotel();
        $Costcut = new \App\Model\Costcut\Costcut();
        $QuotationTransport = new QuotationTransport();

        $Budget = Quotation::getBudget($QuotationArray, $requested_currency);
        $AttractionBreakDown = Quotation::getAttractionBreakDown($QuotationArray);
        $dayCity = Quotation::getDayCity($QuotationArray);

        $OBVehicleRate = Quotation::getOBVehicleRate($QuotationArray);

        $Cost['cost_cut'] = $Costcut->getCostCutRate($QuotationArray);
        $Cost['cost_cut_pkg'] = $Costcut->getCostCutPkgRate($QuotationArray);

        $Cost = $Quotation->getCost($QuotationArray, false, true);
        $requested_currency = $Cost['currency']['code'];
        //////////////////////////////////////////////

        $increment = -1;
        $AccomadationTotal = 0;
        $AccomadationDays = 0;
        $isLocal = true;
        if($QuotationArray['country'] != 62) {
            $isLocal = false;
        }

        if ($QuotationArray['tour_type'] == 6)
        {
            $counts = array_count_values($QuotationArray['accommodation']);

            if (isset($counts[2]))
            {
                /*$ownValue = $TransportCost['vehicle']['driver_accommodation'] * $counts[2];
                $Total += $ownValue * ($Days - 1); // get night*/

                foreach ($QuotationArray['accommodation'] as $key => $accommodation) {
                    if($accommodation == 2) {
                        $ownData = $QuotationArray['hotel'][$key];
                        if(isset($ownData['driver_accommodation']) && $ownData['driver_accommodation'] == 1) {
                            $count = $QuotationHotel->getNightHotelBook($ownData['check_in'], $ownData['check_out']);
                            $AccomadationTotal += $count;
                            $AccomadationDays += $count;
                        }
                    }
                }

            } /*else {
                $Total += $TransportCost['vehicle']['driver_accommodation'] * ($Days - 1); // get night
            }*/

        }

        if ($QuotationArray['tour_type'] == 3) {
            foreach ($QuotationArray['accommodation'] as $key => $accommodation) {
                $ownData = $QuotationArray['hotel'][$key];
                $rates = $QuotationArray['rate']['hotel'][$key] ?? null;
                
                if(isset($rates) && isset($rates['driver_accommodation'])) {
                    // dd($rates['driver_accommodation']);
                    foreach($rates['driver_accommodation'] as $day => $details) {
                        $AccomadationTotal += $details['modified_rate'];
                        $AccomadationDays = $AccomadationDays+1;
                    }
                } else if(isset($ownData)) {
                    if(isset($ownData['driver_accommodation']) && $ownData['driver_accommodation'] == 1) {
                        $count = $QuotationHotel->getNightHotelBook($ownData['check_in'], $ownData['check_out']);
                        $AccomadationTotal += 8 * $count; // 8 usd driver acc.
                        $AccomadationDays += $count;
                    }
                }
            }
        }
        ///////////////////////////////////////////////////////////////////////////////


        $TotalHotelCost = 0;
        $TotalTransportCostCost = 0;

        $HotelRate = $Cost['hotel'];
        $CruiseRate = $Cost['cruise'];
        $Supplement = $Cost['supplement'];
        $AttractionRate = $Cost['attraction'];
        $TransportRate = $Cost['transport'];
        $MealRate = $Cost['meal'];
        $OtherRate = $Cost['other'];
        $PP = $Cost['pp'];

        $QuotationArrayWM = $QuotationArray;
        $QuotationArrayWM['markup_amount'] = 0;
        $QuotationArrayWM['markup_amount_child'] = 0;
        $CostWM = $Quotation->getCost($QuotationArrayWM, false, true);
        $MealRates = Meal::getMeal($QuotationArray);


        if(isset($QuotationArray['other_rate'])) {
            $dayUse = $QuotationArray['other_rate'];
        }

        $beds = [];
        $totalRoomsValue = 0;
        if(!empty($dayUse)) {
            foreach ($dayUse as $key => $room) {
                if(isset($room['details']['hotel_id']) && $room['details']['hotel_id'] != 0) {
                    $beds[$key]['hotel_id'] = $room['details']['hotel_id'];
                    $hotel = Hotel::find($room['details']['hotel_id']);

                    $beds[$key]['hotel_name'] = $hotel->name;

                    $beds[$key]['singleBed'] = $room['details']['single_bed_room_count'] ?? 0;
                    $beds[$key]['doubleBed'] = $room['details']['double_bed_room_count'] ?? 0;
                    $beds[$key]['tripleBed'] = $room['details']['triple_bed_room_count'] ?? 0;

                    $singleBedRate = ($room['details']['single_bed_rate'] ?? 0) * $room['details']['single_bed_room_count'] ?? 0;
                    $doubleBedRate = ($room['details']['double_bed_rate'] ?? 0) * $room['details']['double_bed_room_count'] ?? 0;
                    $tripleBedRate = ($room['details']['triple_bed_rate'] ?? 0) * $room['details']['triple_bed_room_count'] ?? 0;

                    $totalRoomsValue += ($singleBedRate + $doubleBedRate + $tripleBedRate);

                    $beds[$key]['singleBedRate'] = $singleBedRate;
                    $beds[$key]['doubleBedRate'] = $doubleBedRate;
                    $beds[$key]['tripleBedRate'] = $tripleBedRate;

                    $beds[$key]['singleBedRatePP'] = $room['details']['single_bed_rate']/1;
                    $beds[$key]['doubleBedRatePP'] = $room['details']['double_bed_rate']/2;
                    $beds[$key]['tripleBedRatePP'] = $room['details']['triple_bed_rate']/3;
                }
            }
        }

        $Cost['total'] += $totalRoomsValue;
        $CostWM['total'] += $totalRoomsValue;

        $totalPax = $QuotationArray['pax']['adult'];

        $perPerson = $totalRoomsValue / $totalPax;

        $Nights = Quotation::getNights($QuotationArray);
        $Days = Quotation::getDays($QuotationArray);

        $Data = [];
        if (isset($QuotationArray['guide'])) {
            $Data['guide'] = QuotationTransport::getGuideCostList($QuotationArray['pax'], @$QuotationArray['guide'], $Nights, $Days);
        } else {
            $Data['guide']['total'] = 0;
        }

        $Dates = $QuotationHotel->getHotelBookDates(["year" => $QuotationArray['arrival_date']["year"], "month" => $QuotationArray['arrival_date']["month"], "day" => $QuotationArray['arrival_date']["day"]], $QuotationArray['place']);

        $arrival = (reset($Dates)['check_in']);
        $arrival_date = Carbon::create($arrival["year"], $arrival["month"], $arrival["day"])->format('d-M-Y');
        $departure = (end($Dates)['check_out']);
        $departure_date = Carbon::create($departure["year"], $departure["month"], $departure["day"])->format('d-M-Y');

        $mealTimes = [1=>"Breakfast", 2=>"Lunch", 3=>"Dinner"];

        $TimeDetail = $Quotation->getTimeDetails($QuotationArray);
        $HotelsNames = "";
        foreach($Budget['hotel'] as $HotelItem) { 
            $HotelsNames .= \App\Model\Hotel\Hotel::getHotelFromAll($HotelItem['hotel_settings']['hotel'])->name . " | ";
        }

        $dataStyles['light'] = [];
        $dataStyles['dark'] = [];
        $dataBase[] = ["Name", ""];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["Travel Date", $arrival_date . "-" . $departure_date];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["Agent", \App\Model\Agent\Agent::find($QuotationArray['confirm']['agent']??$QuotationArray['agent'])->name];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["Hotel", $HotelsNames];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["No of Night", $Nights];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["Pax", $QuotationArray['pax']['adult'] . " Adult(s) / " . ($QuotationArray['pax']['cwb'] + $QuotationArray['pax']['cnb'] + $QuotationArray['pax']['infant']) . " Child"];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["Tour Number", $QuotationArray['quotation_no'] ?? "NA"];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["File No", $QuotationArray['is_number'] ?? "NA"];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["Currency", $Cost['currency']['code']];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["Exchange Rate", $Cost['currency']['exchange_rate']];
        $dataStyles['light'][] = count($dataBase);
        $dataBase[] = ["", ""];
        
        $dataBase[] = ["Accommodation  - ", "SGL", "DBL", "TPL", "CWB","CNB", "NO.of Nights", "Room Night", "Total"];
        $dataStyles['dark'][] = count($dataBase);
        foreach($Budget['hotel'] as $HotelItem) {
            $room1 = "";
            if(isset($HotelItem['rate']['adult'][0][1]['rate']) && !empty($HotelItem['rate']['adult'][0][1]['rate'])) {
                foreach($HotelItem['rate']['adult'] as $HotelDayItem) {
                    $room1 .= $HotelDayItem[1]['rate'] . " / ";
                }
            } else {
                $room1 .= 0;
            }

            $room2 = "";
            if(isset($HotelItem['rate']['adult'][0][2]['rate']) && !empty($HotelItem['rate']['adult'][0][2]['rate'])) {
                foreach($HotelItem['rate']['adult'] as $HotelDayItem) {
                    $room2 .= $HotelDayItem[2]['rate'] . " / ";
                }
            } else {
                $room2 .= 0;
            }

            $room3 = "";
            if(isset($HotelItem['rate']['adult'][0][3]['rate']) && !empty($HotelItem['rate']['adult'][0][3]['rate'])) {
                foreach($HotelItem['rate']['adult'] as $HotelDayItem) {
                    $room3 .= $HotelDayItem[3]['rate'] . " / ";
                }
            } else {
                $room3 .= 0;
            }
            $dataBase[] = [\App\Model\Hotel\Hotel::getHotelFromAll($HotelItem['hotel_settings']['hotel'])->name,
                            $room1,
                            $room2,
                            $room3,
                            $HotelItem['rate']['cwb'][0]['rate'] ?? 0,
                            $HotelItem['rate']['cnb'][0]['rate'] ?? 0,
                            $QuotationHotel->getNightHotelBook($HotelItem['hotel_settings']['check_in'],$HotelItem['hotel_settings']['check_out']),
                            currency_format($HotelItem['rate']['total'] / $QuotationHotel->getNightHotelBook($HotelItem['hotel_settings']['check_in'],$HotelItem['hotel_settings']['check_out'])),
                            currency_format($HotelItem['rate']['total'])];
            $TotalHotelCost += $HotelItem['rate']['total'];
        }
        $dataBase[] = ["Total", "", "", "", "", "", "", "", currency_format($TotalHotelCost) ?? 0 . " " . $requested_currency];
        $dataBase[] = ["", ""];

        $dataBase[] = ["Meal  - ", "Price", "Adults", "Price", "Child", "Total"];
        $dataStyles['dark'][] = count($dataBase);
        $total=0;
        if(!empty($MealRates["adult"])) {
            foreach($MealRates["adult"] as $dayKey => $day) {
                for($i=1; $i<=3;$i++) {
                    $childRate=0;
                    $childRate = (isset($MealRates["child"][$dayKey][$i]) && $MealRates["child"][$dayKey][$i]> 0) ? $MealRates["child"][$dayKey][$i] : $day[$i]/2;
                    if(isset($day[$i])) {
                        $total = ($day[$i] * $QuotationArray['pax']['adult']) + (($childRate) * ($QuotationArray['pax']['cwb'] + $QuotationArray['pax']['cnb']));
                        $dataBase[] = [$mealTimes[$i] . " - Day " . $dayKey, 
                                        isset($day[$i]) ? $day[$i] : 0,
                                        $QuotationArray['pax']['adult'],
                                        $childRate,
                                        ($QuotationArray['pax']['cwb'] + $QuotationArray['pax']['cnb']),
                                        currency_format($total) . " " . $requested_currency];
                    }
                }
            }
        }
        $dataBase[] = ["Total", "", "", "", "", currency_format($MealRate["cost"]["total"]) . " " . $requested_currency];
        $dataBase[] = ["", ""];

        $dataBase[] = ["Tickets  - ", "Price", "Adults", "Price", "Child", "Total"];
        $dataStyles['dark'][] = count($dataBase);
        if(!empty($Budget['attraction']['items']['attraction'])) {
            foreach($Budget['attraction']['items']['attraction'] as $item) {
                if($item['pnl_type'] == "") {
                    $dataBase[] = [$item['name'],
                            $Budget['attraction']['rates']['attraction'][$item['ID']]['adult'],
                            $QuotationArray['pax']['adult'],
                            isset($Budget['attraction']['rates']['attraction'][$item['ID']]['child'])?$Budget['attraction']['rates']['attraction'][$item['ID']]['child']:0,
                            $QuotationArray['pax']['cwb'] + $QuotationArray['pax']['cnb'],
                            currency_format($Budget['attraction']['attraction_individual']['attraction'][$item['ID']]??0)];
                }
            }
        }
        $dataBase[] = ["Total", "", "", "", "", currency_format($Budget['attraction']['total_attraction']??0) . " " . $requested_currency];
        $dataBase[] = ["", ""];

        

        if($isLocal) {
            $dataBase[] = ["Transfers  - ", "SIC - PRICE", "SIC - PAX", "Private", "Guide", "Total"];
            $dataStyles['dark'][] = count($dataBase);
            foreach ($Budget['transport']['mileage']['each_details']??[] as $to => $from) {
                $dataBase[] = [Place::find(key($from))->name . " to " . Place::find($to)->name,
                            currency_format($Budget['transport']['vehicle']['rate']),
                            $QuotationArray['pax']['adult'],
                            currency_format((array_values($from)[0]->distance/1000) * $Budget['transport']['vehicle']['rate']),
                            "",
                            currency_format((array_values($from)[0]->distance/1000) * $Budget['transport']['vehicle']['rate'])];
            }
            $dataBase[] = ["Bata", currency_format($Budget['transport']['vehicle']['bata']??0), $QuotationArray['pax']['adult'], "", "", currency_format($Budget['transport']['vehicle']['bata']??0*($Quotation->getNights($QuotationArray) + 1))];
            $dataBase[] = ["Paging", currency_format($Budget['transport']['vehicle']['Paging']??0), $QuotationArray['pax']['adult'], "", "", currency_format($Budget['transport']['vehicle']['paging']??0)];
            $dataBase[] = ["Highway Charges", currency_format($Budget['transport']['vehicle']['highway_charges']??0), $QuotationArray['pax']['adult'], "", "", currency_format($Budget['transport']['vehicle']['highway_charges']??0)];
            $dataBase[] = ["Driver Accomodation", currency_format($AccomadationTotal), $QuotationArray['pax']['adult'], "", "", currency_format($AccomadationTotal)];
            $dataBase[] = ["Guide Fee", currency_format($Data['guide']['total']), $QuotationArray['pax']['adult'], "", "", currency_format($Data['guide']['total'])];
            $dataBase[] = ["Water Bottles", "Adt - " . $Cost['water_bottle']['PP']['adult']['rate'] ?? 0 . ", cwb - " . $Cost['water_bottle']['PP']['cwb']['rate'] ?? 0 . ", cnb - " . $Cost['water_bottle']['PP']['cnb']['rate'] ?? 0, $QuotationArray['pax']['adult'], "", "", currency_format($Cost['water_bottle']['cost'])];
            $dataBase[] = ["Total", "", "", "", "", $Budget['transport']['rates'] ? currency_format($Budget['transport']['rates']['total'] + ($Budget['transport']["meal_transfer"]['cost']??0 + $Cost['water_bottle']['cost']??0)) : currency_format($Cost["transport"]["cost"]["total"] + $Cost['water_bottle']['cost']??0) . " " . $requested_currency];
        } else {
            $dataBase[] = ["Transfers - (Singapore/Malaysia/Vietnam)  - ", "SIC - PRICE (Adult)", "SIC - PAX (Adult)", "SIC - PRICE-(Child)", "SIC - PAX -(Child)", "Private", "Guide", "Total"];
            $dataStyles['dark'][] = count($dataBase);
            
            if(!empty($Budget['attraction']['items']['attraction'])) {
                foreach($Budget['attraction']['items']['attraction'] as $item) {
                    if($item['pnl_type'] != "") {
                        $dataBase[] = [$item['name'],
                                "",
                                "",
                                "",
                                "",
                                currency_format($Budget['attraction']['attraction_individual']['attraction'][$item['ID']]??0),
                                "",
                                currency_format($Budget['attraction']['attraction_individual']['attraction'][$item['ID']]??0)];
                    }
                }
            }
            if(!empty($Budget['attraction']['items']['city_tour'])) {
                foreach($Budget['attraction']['items']['city_tour'] as $item) {
                    $dataBase[] = [$item['name'],
                                $QuotationArray['pax']['adult'],
                                $Budget['attraction']['rates']['city_tour'][$item['ID']]['adult']??0,
                                $QuotationArray['pax']['cwb'] + $QuotationArray['pax']['cnb'],
                                isset($Budget['attraction']['rates']['city_tour'][$item['ID']]['child'])?$Budget['attraction']['rates']['city_tour'][$item['ID']]['child']:0,
                                "",
                                "",
                                currency_format($Budget['attraction']['attraction_individual']['city_tour'][$item['ID']]??0)];
                }
            }
    
            if(!empty($Budget['attraction']['items']['excursion'])) {
                foreach($Budget['attraction']['items']['excursion'] as $item) {
                    $dataBase[] = [$item['name'],
                                $QuotationArray['pax']['adult'],
                                isset($Budget['attraction']['rates']['excursion'][$item['ID']]['adult'])?$Budget['attraction']['rates']['excursion'][$item['ID']]['adult']:0,
                                $QuotationArray['pax']['cwb'] + $QuotationArray['pax']['cnb'],
                                isset($Budget['attraction']['rates']['excursion'][$item['ID']]['child'])?$Budget['attraction']['rates']['excursion'][$item['ID']]['child']:0,
                                "",
                                "",
                                currency_format(isset($Budget['attraction']['attraction_individual']['excursion'][$item['ID']])?$Budget['attraction']['attraction_individual']['excursion'][$item['ID']]:0)];
                }
            }
            foreach ($Budget['transport']['mileage']['each_details']??[] as $key => $place) {
                $to = $key;
                $from = key($place);
                $from = Place::find($from)->name ?? "";
                $to = Place::find($to)->name ?? "";
                $increment++;
                $dataBase[] = ["Transport " . $from . " - " . $to,
                            "",
                            $QuotationArray['pax']['adult'],
                            "",
                            "",
                            "",
                            "",
                            currency_format($Budget['transport']['rates']['rate_array'][$increment] ??0)];
            }
            if (isset($Budget['transport']['meal_transfer']['break_down'])) {
                foreach ($Budget['transport']['meal_transfer']['break_down'] as $day => $mealData) {
                    foreach ($mealData as $meal => $rate) {
                        $dataBase[] = ["Day " . $day . ", " . MealTime::find($meal)->type ?? "",
                            "",
                            currency_format($rate / $QuotationArray['pax']['adult'] )??0,
                            "",
                            "",
                            "",
                            "",
                            currency_format($rate)??0];
                    }
                }
            }
            $dataBase[] = ["Total", "", "", "", "","", "", currency_format($Budget['attraction']['total_none_attraction'] + $Budget['transport']["rates"]['total'] + $Budget['transport']['meal_transfer']["cost"])];
        }
        $dataBase[] = ["", ""];

        $dataBase[] = ["Others  - ", "Price", "Adults", "Price", "Child", "Total"];
        $dataStyles['dark'][] = count($dataBase);
        if(!empty($Budget['other'])) {
            if(isset($Budget['other']['PP'])) {
                foreach($Budget['other']['PP']['adult'] as $key => $RateItem) {
                    if($item['pnl_type'] == "") {
                        $dataBase[] = [$RateItem['text'],
                                $RateItem['rate'],
                                $QuotationArray['pax']['adult'],
                                "CWB : " . $Budget['other']['PP']['cwb'][$key]['rate'] . " CNB : " . $Budget['other']['PP']['cnb'][$key]['rate'],
                                "CWB : " . $QuotationArray['pax']['cwb']  . " CNB : " . $QuotationArray['pax']['cnb'],
                                currency_format($QuotationArray['pax']['adult'] * $RateItem['rate'] + 
                                        $QuotationArray['pax']['cwb'] * $Budget['other']['PP']['cwb'][$key]['rate']  + 
                                        $QuotationArray['pax']['cnb'] * $Budget['other']['PP']['cnb'][$key]['rate'])];
                    }
                }
            }
        }
        $dataBase[] = ["Total", "", "", "", "", currency_format($Budget['other']['cost']??0) . " " . $requested_currency];
        $dataBase[] = ["", ""];


        $dataBase[] = ["Accommodation", "", "", "", "", currency_format($TotalHotelCost) ?? 0 . " " . $requested_currency];
        $dataBase[] = ["Meal", "", "", "", "", currency_format($MealRate["cost"]["total"]) . " " . $requested_currency];
        $dataBase[] = ["Tickets", "", "", "", "", currency_format($Budget['attraction']['total_attraction']??0) . " " . $requested_currency];
        if($isLocal) {
            $dataBase[] = ["Transfers", "", "", "", "", $Budget['transport']['rates'] ? currency_format($Budget['transport']['rates']['total'] + ($Budget['transport']["meal_transfer"]['cost']??0 + $Cost['water_bottle']['cost']??0)) : currency_format($Cost["transport"]["cost"]["total"] + $Cost['water_bottle']['cost']??0) . " " . $requested_currency];
        } else {
            $dataBase[] = ["Tour Transfers", "", "", "", "", (currency_format($Budget['transport']["rates"]['total']??0) + $Budget['attraction']['total_none_attraction'] + $Budget['transport']['meal_transfer']["cost"])  . " " . $requested_currency];
        }
        $dataBase[] = ["Others", "", "", "", "", currency_format($Budget['other']['cost']??0) . " " . $requested_currency];

        $dataBase[] = ["Total Mega Cost", "", "", "", "", currency_format($Cost['total']) . " " . $requested_currency];
        $dataBase[] = ["Total Tour Cost Without Markup", "", "", "", "", currency_format($CostWM['total']) . " " . $requested_currency];
        $dataBase[] = ["Profit/Loss", "", "", "", "", currency_format($Cost['total'] - $CostWM['total']) . " " . $requested_currency];

        return [$dataBase,$dataStyles];
    }
}