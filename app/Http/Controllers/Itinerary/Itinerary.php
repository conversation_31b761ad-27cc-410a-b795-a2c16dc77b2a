<?php

namespace App\Http\Controllers\Itinerary;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Model\Quotation\QuotationTask;
use Illuminate\Http\Request;

/**
 * Class Itinerary
 * @package App\Http\Controllers\Itinerary
 */
class Itinerary extends Controller
{


    /**
     * @param Request $request
     * @throws \Exception
     * @throws \ReflectionException
     */
    function setAttractionText(Request $request)
    {
        $QuotationTask = new QuotationTask();

        $Changes = $request->input();

        $Data['type'] = 'attraction_text';
        $Data['attraction_id'] = $Changes['attraction_id'];
        $Data['attr_type'] = $Changes['type'];
        $Data['text'] = $Changes['value'];
        $Data['day'] = $Changes['day'];

        $QuotationTask->setSession('itinerary', $Data);

    }

    /**
     * @param Request $request
     * @throws \Exception
     * @throws \ReflectionException
     */
    function setAttractionDays(Request $request)
    {
        $QuotationTask = new QuotationTask();

        $Changes = $request->input();

        $Data['type'] = 'attraction_order';
        $Data['attraction'] = isset($Changes['attraction']) ? $Changes['attraction'] : [];
        $Data['excursion'] = isset($Changes['excursion']) ? $Changes['excursion'] : [];
        $Data['city_tour'] = isset($Changes['city_tour']) ? $Changes['city_tour'] : [];

        $QuotationTask->setSession('itinerary', $Data);

    }

    /**
     * @param Request $request
     * @throws \Exception
     * @throws \ReflectionException
     */
    function setDayText(Request $request)
    {

        $QuotationTask = new QuotationTask();

        $Changes = $request->input();
        $Data['type'] = 'day_text';
        $Data['day'] = $Changes['day'];
        $Data['text'] = $Changes['value'];

        $QuotationTask->setSession('itinerary', $Data);
    }

    /**
     * @param Request $request
     * @throws \Exception
     * @throws \ReflectionException
     */
    function setDayHeader(Request $request)
    {


        $QuotationTask = new QuotationTask();

        $Changes = $request->input();
        $Data['type'] = 'day_header';
        $Data['day'] = $Changes['day'];
        $Data['text'] = $Changes['value'];

        $QuotationTask->setSession('itinerary', $Data);
    }

    
    /**
     * @param Request $request
     * @throws \Exception
     * @throws \ReflectionException
     */
    function showItinerary(Request $request)
    {
        $QuotationTask = new QuotationTask();

        $Changes = $request->input();
        $Data['type'] = 'show_itinerary';
        $Data['show_itinerary'] = $Changes['show'];

        $QuotationTask->setSession('itinerary', $Data);
    }

}
