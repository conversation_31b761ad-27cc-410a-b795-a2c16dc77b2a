<?php

namespace App\Http\Controllers\Hotel;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Model\Hotel\HotelTBO as TBO;
use View;

use Session;


/**
 * Class HotelTBO
 * @package App\Http\Controllers\Hotel
 */
class HotelTBO extends Controller
{
    /**
     * @param $PlaceID
     * @param $HotelItemIndex
     * @param Request $request
     */
    function getChangeHotelList($PlaceID, $HotelItemIndex, Request $request)
    {

    }

    /**
     * @param Request $Data
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    function getSearchHotels(Request $Data)
    {
        return "";


        Session::put("quotation.api_hotel.filters", $Data->input());
        $TBO = new TBO();
        $Respond = $TBO->getLowestHotel($Data->input());


        if ($Respond['Status']['StatusCode'] == "01") {

            $Data['session_id'] = $Respond['SessionId'];
            $Data['rooms'] = $Respond['NoOfRoomsRequested'];
            $Data['check_in_date'] = $Respond['CheckInDate'];
            $Data['check_out_date'] = $Respond['CheckOutDate'];
            $Data['room_guests'] = $Respond['RoomGuests'];


            return view("element.hotel.TBO.hotel-search-result", ['HotelResult' => $Respond['HotelResultList']['HotelResult'], "Data" => $Data]);
        } else {

            return view("system.error.error-bootstrap", ["Header" => 'TBO', 'Message' => $Respond['Status']['Description']]);

        }


    }


    /**
     * @param Request $r
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelCancellationPolicy(Request $r)
    {

        $TBO = new TBO();
        $Data = $r->input();
        $Data['data'] = Session::get('quotation.api_hotel.hotel_tbo');

        $HotelDetails = $TBO->getHotelCancellationPolicy($Data);

        if ($HotelDetails['Status']['StatusCode'] == "01") {
            return view("element.hotel.TBO.hotel-room-cancellation-policy", ["HotelDetails" => $HotelDetails['CancelPolicies']['CancelPolicy'], 'DefaultPolicy' => $HotelDetails['CancelPolicies']['DefaultPolicy']]);
        } else
            return $HotelDetails['Status']['Description'];

    }


}
