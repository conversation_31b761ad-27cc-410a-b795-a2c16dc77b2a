<?php

namespace App\Http\Controllers\Hotel;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Model\Hotel\HotelBed as HotelBedModel;
use App\Model\Hotel\Rates;
use App\Model\Place\Place;
use App\Model\Quotation\QuotationHotel;
use Auth;
use Carbon\Carbon;
use Dompdf\Dompdf;
use Illuminate\Http\Request;
use Mail;
use Session;
use View;
use App\Model\QuotationManage\QuotationHotelBedsRateKeys;
use App\Http\Controllers\Hotel\Hotel as hotelController;


/**
 * Class HotelBed
 * @package App\Http\Controllers\Hotel
 */
class HotelBed extends Controller
{


    /**
     * @param Request $Data
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getSearchHotels(Request $Data)
    {

        Session::put("quotation.api_hotel.filters", $Data->input());


        $HotelBed = new HotelBedModel();
        $HotelResult = $HotelBed->getSearchHotels($Data->input());


        if ($HotelResult) {

            foreach ($HotelResult->hotels as $HotelItem) {

                $HotelDetails = $HotelBed->getHotelDetails($HotelItem->code);
                echo View::make("element.hotel.hotelbed.hotel-search-item", ["HotelItem" => $HotelItem, "HotelDetails" => $HotelDetails])->render();
            }

        }


    }

    /**
     * @return string
     */
    function getSignature()
    {
        $HotelBed = new HotelBedModel();
        return $HotelBed->getSignature();
    }

    /**
     * @param Request $r
     * @return array
     */
    function getCategoryChange(Request $r)
    {


        $RoomNum = $r->input('room_number');

        $RoomCat = $r->input('room_category')[$RoomNum];
        $Meal = $r->input('meal')[$RoomNum];
        $RateClass = $r->input('rate')[$RoomNum];
        $DataType = $r->input('data_type');


        $Data = objectToArray(Session::get('quotation.api_hotel.hotelbed.rooms'));
        $Filters = objectToArray(Session::get('quotation.api_hotel.filters'));


        $HotelBed = new HotelBedModel();
        $AvailableFilters = $HotelBed->getAvailableFilters($Data, $Filters);


        $RateItem = $HotelBed->getAvailableRates($RoomCat, $RateClass, $Meal, $DataType, $Filters, $RoomNum);


        $html = View::make("element.hotel.hotelbed.hotel-room-details", ['RateItem' => $RateItem])->render();

        return ["Rate" => $RateItem, "Availability" => $AvailableFilters[$RoomNum][$RoomCat], 'html' => $html];


    }


    /**
     * @param Request $Data
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \ReflectionException
     * @throws \Throwable
     */
    function getAvailability(Request $Data)
    {


        ini_set('max_execution_time', 0);
        Session::put("quotation.api_hotel.filters", $Data->input());

        $HotelBed = new HotelBedModel();
        $HotelResult = $HotelBed->Availability($Data->input());


        if ($HotelBed->ReturnStaus) {

            foreach ($HotelResult->hotels as $HotelItem) {
                $HotelDetails = $HotelBed->getHotelDetails($HotelItem['code']);
                echo View::make("element.hotel.hotelbed.hotel-search-item", ["HotelItem" => $HotelItem, "HotelDetails" => $HotelDetails])->render();
            }

        } else
            return view("system.error.error-bootstrap", ["Header" => 'Hotelbeds', 'Message' => $HotelBed->ErrorMsg]);


    }


    /**
     * @return array
     */
    function getCheckRate()
    {
        $HotelBed = new HotelBedModel();
        $RateKeyArray = $HotelBed->getRateKeys();

        return $HotelBed->CheckRate($RateKeyArray)->toArray();

    }


    /**
     * @param Request $r
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getConfirm(Request $r)
    {


        ini_set('max_execution_time', 0);
        $Pax = $r->input('pax');
        $Comment = $r->input('comment');


        $HotelBed = new HotelBedModel();
        $RateKeyArray = $HotelBed->getRateKeys();


        $CheckRateRs = $HotelBed->CheckRate($RateKeyArray);

        if (!$CheckRateRs)
            return '<div class="content-box invoice" id="hotelbed_invoice">' . $HotelBed->ErrorMsg . '</div>';


        $Paxes = $HotelBed->getPaxes($Pax);


        $Confirm = $HotelBed->BookingConfirm($Paxes, $Comment);
        $Recite = [];


        if ($HotelBed->ReturnStaus) {


            $Recite['reference'] = $Confirm->booking->reference;;
            $Recite['creationDate'] = $Confirm->booking->creationDate;
            $Recite['status'] = $Confirm->booking->status;
            $Recite['holder'] = $Confirm->booking->holder;
            $Recite['hotel'] = $Confirm->booking->hotel;


            $Recite['hotel_details'] = $HotelBed->getHotelDetails($Confirm->booking->hotel['code'], false);


            if (env("APP_DEBUG"))
                $CSS = file_get_contents(public_path('assets/css/apple.css'));
            else
                $CSS = file_get_contents(public_path('assets/css/apple.min.css'));

            $ReciteHTML = View::make("element.hotel.hotelbed.hotel-receipt", ['Data' => $Recite, 'Email' => true, 'Comment' => $Comment])->render();


//            $pdf = PDF::loadView('element.hotel.hotelbed.email-receipt', ['QuotationHTML' => $ReciteHTML, "CSS" => $CSS]);
//            return $pdf->stream('download.pdf');


            Mail::send('element.hotel.hotelbed.email-receipt', ['QuotationHTML' => $ReciteHTML, "CSS" => $CSS], function ($message) {

                $message->from("<EMAIL>", "Appleholidays System");
                $message->to(Auth::user()->email, Auth::user()->name);

                $message->subject("Hotel Receipt");


            });

            return view("element.hotel.hotelbed.hotel-receipt", ['Data' => $Recite, 'Comment' => $Comment]);
        } else
            return '<div class="content-box invoice" id="hotelbed_invoice">' . $HotelBed->ErrorMsg . '</div>';


    }


    /**
     * @param $Reference
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getBookDetails($Reference)
    {


        $HotelBed = new HotelBedModel();
        $Booking = $HotelBed->getBookDetails($Reference);


        $Booking['hotel_details'] = $HotelBed->getHotelDetails($Booking['hotel']['code'], false);
        $ReciteHTML = View::make("element.hotel.hotelbed.hotel-receipt", ['Data' => $Booking, 'PDF' => true, 'Comment' => "s"])->render();


        echo $ReciteHTML;


        $dompdf = new Dompdf();
        $dompdf->loadHtml("");
        $dompdf->setPaper('A4', 'landscape');
        $dompdf->render();
        $dompdf->stream();
    }


    /**
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    function test()
    {
        ini_set('max_execution_time', 0);

//        $GoogleCLient = new \Google_Client();
//        $GoogleCLient->setApplicationName("geocode");
//        $GoogleCLient->setDeveloperKey("AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc");
//
//
//        sd();


        $HotelBed = new HotelBedModel();
        $List = $HotelBed->rateCommentDetails();


    }


    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     * @throws \Throwable
     */
    function getHotelFilterList()
    {
        return "Hotels not found!";
        $Data = request()->all();

        $Data = json_decode($Data['data'], true);
        $Rate = new Rates();

        $HotelSettingsModified = arrayMapMulti('getActualDataType', $Data);
        $localHotelRatePlane = 0;
        $Quotation = new QuotationHotel();
        $hotelController = new hotelController();
        $session = session()->get('quotation');
        $hotelRate = $Quotation->getHotelRatePlaneCancel($session);

        if($hotelRate) {
            $index = $Data['index'];
            $hotelRateData = $hotelRate[$index];
            $localHotelRatePlane = $hotelController->getHotelRatePlane($hotelRateData);
        }

        #Night to checkout
        $CheckIn = Carbon::create($Data['check_in']['year'], $Data['check_in']['month'], $Data['check_in']['day']);
        $CheckIn->addDays($Data['nights']);
        $Data['check_out']['year'] = $CheckIn->year;
        $Data['check_out']['month'] = $CheckIn->month;
        $Data['check_out']['day'] = $CheckIn->day;
        $Data['count'] = $Data['count'] ?? 1;

        unset($Data["hotel_setting"]);
        $HotelSettingsModified = $Data;
        /*
        //filter city list
        $city_list = Place::having('ID', $Data['filter']['city'])->get()->pluck('A2');


        // comment for update table
       $HotelIDs = HotelBedModel::where("name", 'like', "%" . $Data['filter']['name'] . "%")
            ->having('destination_code', $city_list)
            ->get()
            ->pluck('id')
            ->toArray();
        if (empty($HotelIDs)) {
            foreach ($city_list as $a2) {
                HotelBedModel::updateDB($a2);
            }

            $HotelIDs = HotelBedModel::where("name", 'like', "%" . $Data['filter']['name'] . "%")
                ->having('destination_code', $city_list)
                ->get()
                ->pluck('id')
                ->toArray();
        }*/

        if(!empty($Data['filter']['name'])) {
            $HotelIDs = HotelBedModel::where("name", 'like', "%" . $Data['filter']['name'] . "%")
                ->pluck('id')
                ->toArray();
        } else {
            $placeId = $Data['place'];
            $HotelIDs = HotelBedModel::where('place_id',$placeId)->pluck('id')->toArray();
        }


        if (empty($HotelIDs)) {
            return "Hotels not found!";
        }

        return view('element.hotel.hotelbed.hotel-availability-list', ["HotelSettings" => $HotelSettingsModified, 'HotelIDs' => $HotelIDs, 'count' => $Data['count'] ?? 5, 'SelectedHotelRateValue' => $localHotelRatePlane]);
    }

    public function getPolicies(Request $request)
    {

        $HotelBed = new HotelBedModel();
        $rateKeyArray = json_decode($request->rateKey);
        $view = "";


        foreach ($rateKeyArray ?? [] as $rateObjKey => $rateKeyObj) {

            $rateKey = $rateKeyObj->rateKey;
            $recheckArr = [["rateKey" => $rateKey]];
            $rateComment = "";
            $recheck = $HotelBed->CheckRate($recheckArr, 1);
            if ($recheck) {
                $recheck = $recheck->toArray();
                $hotel = $recheck['hotel']->toArray();
                $cancelPolicies = array();
                $hotelInfo = array('destinationName' => $hotel['destinationName'], 'name' => $hotel['name'], 'currency' => $hotel['currency']);

                $noOfAdults = $hotel['rooms'][0]['rates'][0]['adults'];
                $noOfRooms = $hotel['rooms'][0]['rates'][0]['rooms'];
                $boardCode = $hotel['rooms'][0]['rates'][0]['boardCode'];
                $boardName = $hotel['rooms'][0]['rates'][0]['boardName'];

                if ($noOfAdults == 1) {
                    $roomType = $noOfRooms . " - SGL Room";
                } else if ($noOfAdults == 2) {
                    $roomType = $noOfRooms . " - DBL Room";
                } else if ($noOfAdults == 3) {
                    $roomType = $noOfRooms . " - TPL Room";
                }

                $rateComment = $hotel['rooms'][0]['rates'][0]['rateComments'] ?? "Car park YES (without additional debit notes)";
                $rateClass = $hotel['rooms'][0]['rates'][0]['rateClass'];
                $rateClassName = $HotelBed->getRateClass($rateClass);
                if (isset($hotel['rooms'][0]['rates'][0]['cancellationPolicies'])) {
                    foreach ($hotel['rooms'][0]['rates'][0]['cancellationPolicies'] as $key => $Value) {
                        $datObj = Carbon::parse($Value['from']);
                        $cancelPolicies[$key]['dateTime'] = $datObj->toDayDateTimeString();
                        $cancelPolicies[$key]['amount'] = $Value['amount'];

                    }
                }
                $view .= View::make('element.hotel.hotelbed.hotel_show_policies', compact('roomType', 'rateComment', 'cancelPolicies', 'hotelInfo','rateClass','rateClassName','boardCode','boardName'))->render();
            } else {
                $view .= View::make("system.error.error-ajax", ['Message' => "Hotel Data Not Available Due to System Failure"])->render();
                $view .= "<hr><br>";

            }

        }
        if (isset($hotelInfo['name'])) {
            $html = "<h4 class='margin-top-5'> " . $hotelInfo['name'] . "  - " . $hotelInfo['destinationName'] . " </h4>";
            $view = $html . $view;
            return array('status' => true, 'html' => $view, 'hotelInfo' => $hotelInfo);
        } else {
            return array('status' => false, 'html' => $view);
        }

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     * @throws \Throwable
     */
    function apiGetHotelFilterList($Data) {
        $Rate = new Rates();

        #get selected hotel rates
        if (!empty($Data['room_type'][2])) {
            $RoomType = 2;
        } elseif (!empty($Data['room_type'][1])) {
            $RoomType = 1;
        } elseif (!empty($Data['room_type'][3])) {
            $RoomType = 3;
        } else {
            $RoomType = 2;
        }

        $RateSelectedHotel = $Rate->getRate($Data['hotel'], $Data['check_in'] ?? false, $RoomType, $Data['meal_type'] ?? false, $Data['room_category'] ?? false);

        /*if (!empty($RateSelectedHotel[0])) {
            $SelectedHotelRateValue = $RateSelectedHotel[0]->rate;
        } else
            $SelectedHotelRateValue = 0;
        #end it*/

        #Night to checkout
        $CheckIn = Carbon::create($Data['check_in']['year'], $Data['check_in']['month'], $Data['check_in']['day']);
        $CheckIn->addDays($Data['nights']);
        $Data['check_out']['year'] = $CheckIn->year;
        $Data['check_out']['month'] = $CheckIn->month;
        $Data['check_out']['day'] = $CheckIn->day;
        $Data['count'] = $Data['count'] ?? 1;

        unset($Data["hotel_setting"]);
        $HotelSettingsModified = $Data;


        //filter city list
        $city_list = Place::having('ID', $Data['filter']['city'])->get()->pluck('A2');

        // comment for update table
        $name = isset($Data['filter']['name'])?$Data['filter']['name']:"";
        $HotelIDs = HotelBedModel::where("name", 'like', "%" . $name . "%")
            ->having('destination_code', $city_list)
            ->get()
            ->pluck('id')
            ->toArray();

        if (empty($HotelIDs)) {
            foreach ($city_list as $a2) {
                HotelBedModel::updateDB($a2);
            }

            $HotelIDs = HotelBedModel::where("name", 'like', "%" . $name . "%")
                ->having('destination_code', $city_list)
                ->get()
                ->pluck('id')
                ->toArray();
        }

        if (empty($HotelIDs)) {
            return "Hotels not found!";
        }

        $HotelBed = new \App\Model\Hotel\HotelBed();
        $QuotationArray=[];
        $QuotationArray['pax'] = $Data["pax"]??[];
        $HotelBedHotels = $HotelBed->AvailabilityCombine($HotelSettingsModified, $QuotationArray, $HotelIDs,$Data['count'] ?? 5, false, false);

        return $HotelBedHotels;
    }

}