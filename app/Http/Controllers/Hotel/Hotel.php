<?php

namespace App\Http\Controllers\Hotel;

use App\Http\Controllers\Controller;
use App\Model\Cruise\CruisePackage;
use App\Model\Hotel\Hotel as HotelModel;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\HotelClass;
use App\Model\Hotel\HotelTBO as TBO;
use App\Model\Hotel\Room;
use App\Model\Hotel\Messages;
use App\Model\Image\Image;
use App\Model\Place\Place;
use App\Model\Quotation\QuotationCruise;
use App\Model\Quotation\QuotationHotel;
use App\Model\Quotation\Quotation;
use App\Model\Text\Text;
use Carbon\Carbon;
use Illuminate\Http\Request;
use phpDocumentor\Reflection\Types\Object_;
use Session;
use Validator;
use View;
use App\Http\Controllers\Hotel\HotelBed as HotelBedsController;
use App\Model\Meal\MealPlan;
use App\Model\Rate\apple_cancel_charges_markup_rates;
/**
 * Class Hotel
 * @package App\Http\Controllers\Hotel
 */
class Hotel extends Controller
{


    public function index()
    {

    }


    /**
     * @param $HotelID
     * @return \Illuminate\Http\JsonResponse
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getHotel($HotelID)
    {
        return jsona(HotelModel::getHotelFromAll($HotelID));
    }


    /**
     * @param $HotelID
     * @return \Illuminate\Http\JsonResponse
     */
    function getHotelInfo($HotelID)
    {

        $Image = new Image();

        $HotelDetails = HotelModel::find($HotelID);
        $HotelImages = $Image->getImage($HotelID, '3x', "hotel", 10);

        return jsona(['hotel_details' => $HotelDetails, 'hotel_images' => $HotelImages]);
    }


    /**
     * @param $lng
     * @param $lat
     * @return \Illuminate\Http\JsonResponse
     */
    function getMapAreaPlaces($lng, $lat)
    {

        $Hotel = new HotelModel();
        $Hotels = $Hotel->getAreaHotels($lng, $lat);


        if ($Hotels)
            return response()->json($Hotels);
        else
            return jsona(['longitude' => $lat, 'latitude' => $lng]);

    }

    /**
     * @param $HotelID
     * @param string $Size
     * @param int $limit
     */
    function getImage($HotelID, $Size = '1x', $limit = 2)
    {

        $Image = new Image();

        print_r($Image->getHotelImage($HotelID, $Size, $limit));


    }

    /**
     * @param Request $request
     * @param string $Type
     * @return \Illuminate\Http\JsonResponse
     * @throws \ReflectionException
     */
    function getHotelSettings(Request $request, $Type = "All")
    {

        $Hotel = new HotelModel();
        $ArrivalDate = Session::get('quotation.arrival_date');

        Session::put('quotation.transport_only', $request->transportation); // default 0

        $BookDates = $Hotel->getHotelSettingsBookDatesChange($request->all(), $Type, $ArrivalDate);

        $Query = $Hotel->getHotelSettingsQuery($request->all(), $BookDates);

        $hData = "";
        $hotelIndex = "";
        if(isset($request->provider) && $request->provider === "hotelbeds") {

            $meal = $request->meal_type;
            $room  = $request->room_category;
            $hotelId = $request->hotel;

            $session = session()->get('quotation');
            $results = array();

                    if(isset($session['api']['hotelbeds']['hotel'])) {
                        $rateArray = array();
                        foreach ($session['api']['hotelbeds']['hotel'] as $hotelKey => $hotelValue){
                            $Data = $hotelValue['full'];
                            if($Data['id'] == $hotelId) {
                                if (isset($Data['combine'][$room][$meal])) {
                                    $boards = $Data['combine'][$room][$meal];
                                    Session::put("quotation.api.hotelbeds.hotel." . $hotelKey . ".selected", $boards);
                                    $hotelIndex = $hotelKey;
                                    $hData = json_encode($boards);
                                }
                            }
                        }

            }

        }


        return jsona(['query' => $Query['Query'], 'settings' => $BookDates, 'hotel_status' => $Query['HotelStatusArray'], 'can_apply' => $Query['CanApply'],'hdata' =>  $hData,'hotelIndex' =>  $hotelIndex]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \ReflectionException
     */
    function getHotelStatusSheet(Request $request)
    {


        $HotelSettings = $request->input();
        $Market = Session::get('quotation.market');
//        spd($HotelSettings);

        $SheetReturn = [];
        $dt = Carbon::create($HotelSettings['check_in']['year'], $HotelSettings['check_in']['month'], 1, 0);
        $HotelModel = new HotelModel();

        for ($c = 1; $c <= 90; $c++) {//for 3 month

            //details
            $BookDate = ['year' => $dt->year, 'month' => $dt->month, 'day' => $dt->day];

            $RateCurrent = $HotelModel->getStatus($HotelSettings['hotel'], $BookDate, $HotelSettings['room_type'], $HotelSettings['meal_type'], $HotelSettings['room_category'], $Market);
            $SheetReturn[$dt->year][$dt->month][$dt->day] = $RateCurrent;
            $dt->addDay();
        }

        return jsona($SheetReturn);
    }

    /**
     * @param Request $r
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getBookHotelApi(Request $r)
    {
        $Data = Session::get('quotation.api_hotel');


        if ($Data['api'] == 'tbo') {//TBO

            $TBO = new TBO();
            $Data = Session::get('quotation.api_hotel.hotel_tbo');
            $HotelDetails = $TBO->getHotelDetails($Data);


            return view("element.hotel.TBO.hotel-book", ["HotelDetails" => $HotelDetails]);
        } elseif ($Data['api'] == 'hotelbed') {//hotel beds

            $Hotelbeds = new HotelBed();
            $Data = Session::get('quotation.api_hotel.hotelbed');
            $HotelDetails = $Hotelbeds->getHotelDetails($Data['hotel_code'], false);

            return view("element.hotel.hotelbed.hotel-details", ["HotelDetails" => $HotelDetails]);

        }
    }

    /**
     * @param $Index
     * @return array
     */
    function getHotelRemove($Index)
    {

        Session::forget("quotation.hotel.$Index");//remove Hotel
        Session::forget("quotation.place.$Index");//remove Place


        Session::put("quotation.place", array_values(Session::get("quotation.place")));//reset places
        return [];

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelFilterList()
    {

        $Text = new Text();
        $QuotationHotel = new QuotationHotel();
        $QuotationCruise = new QuotationCruise();
        $Data = request()->all();
        
        $Type = $Data['acc_type']??1;
        parse_str($Data['data_local'], $Data);
        $check = 0;
        $HotelItemIndex = $Data['index'];
        $PlaceID = Session::get("quotation.place.$HotelItemIndex") ?? $Data['place'];

        if(isset($Data["cruise"])) {
            $HotelSettings = $QuotationCruise->getDecodeArray( $Data["cruise_setting"][$HotelItemIndex]);
        } else if(isset($Data["hotel"])) {
            $HotelSettings = $QuotationHotel->getDecodeArray( $Data["hotel_setting"][$HotelItemIndex]);
        } else if(isset($Data["hotel_setting"][$HotelItemIndex]) && !empty($Data["hotel_setting"][$HotelItemIndex])) {
            $HotelSettings = $QuotationHotel->getDecodeArray( $Data["hotel_setting"][$HotelItemIndex]);
        } else if(isset($Data["cruise_setting"][$HotelItemIndex]) && !empty($Data["cruise_setting"][$HotelItemIndex])) {
            $HotelSettings = $QuotationCruise->getDecodeArray( $Data["cruise_setting"][$HotelItemIndex]);
        } else {
            $check = 1;
            $HotelSettings = $QuotationHotel->getDecodeArray( $Data["hotel_setting"][$HotelItemIndex]);
        }

        #night to checkout
        $CheckIn = Carbon::create($HotelSettings['check_in']['year'], $HotelSettings['check_in']['month'], $HotelSettings['check_in']['day']);
        $CheckIn->addDays($HotelSettings['nights']);
        $HotelSettings['check_out']['year'] = $CheckIn->year;
        $HotelSettings['check_out']['month'] = $CheckIn->month;
        $HotelSettings['check_out']['day'] = $CheckIn->day;


        $HotelCurrentDetails = $Data["hotel_setting"];
        unset($Data["hotel_setting"]);
        $HotelSettingsModified = $Data;


        if (empty($HotelCurrentDetails[$HotelItemIndex])) {//if its not a delete hotel or empty palce
            $HotelSettings = $QuotationHotel->getCheckinCheckoutDatesFromHotelArray($HotelCurrentDetails, Session::get('quotation.arrival_date'))[0];
        }
        
        if(isset($Data["cruise"]) || $check == 1) {
            return view('element.cruise.cruise-filter-list', ['Text' => $Text, "PlaceID" => $PlaceID, "CruiseItemIndex" => $HotelItemIndex, "CruiseSettings" => $HotelSettings, "CruiseSettingsModified" => $HotelSettingsModified]);
        } else if($Type == "4") {
            return view('element.cruise.cruise-filter-list', ['Text' => $Text, "PlaceID" => $PlaceID, "CruiseItemIndex" => $HotelItemIndex, "CruiseSettings" => $HotelSettings, "CruiseSettingsModified" => $HotelSettingsModified]);
        } else {
            return view('element.hotel.hotel-filter-list', ['Text' => $Text, "PlaceID" => $PlaceID, "HotelItemIndex" => $HotelItemIndex, "HotelSettings" => $HotelSettings, "HotelSettingsModified" => $HotelSettingsModified]);
        }

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function ApiGetHotelFilterList($Data)
    {

        $Text = new Text();
        $QuotationHotel = new QuotationHotel();
        $QuotationCruise = new QuotationCruise();
        $Rate = new \App\Model\Hotel\Rates();
        $Image = new Image();

        $PlaceID = $Data['place'];

        $CheckIn = Carbon::create($Data['check_in']['year'], $Data['check_in']['month'], $Data['check_in']['day']);
        $CheckOut = Carbon::create($Data['check_out']['year'], $Data['check_out']['month'], $Data['check_out']['day']);

        $HotelSettingsModified = $Data;
        $HotelListL2M = $Rate->getLowestHotelPlace($PlaceID, $Data['check_in'], false, false, false, false, 8, $HotelSettingsModified);

        $NewHotelListL2M = [];
        $NewHotelList = [];
        foreach ($HotelListL2M as $Hotel) {
            $Hotel = objectToArray($Hotel);
            $Hotel['hotel_data'] = \App\Model\Hotel\Hotel::find($Hotel['hotel']);
            $Hotel['hotel_class'] = HotelClass::find($Hotel['hotel_data']['class']);
            $Hotel['city'] = Place::find($Hotel['hotel_data']['city']);

            $Hotel['hotel_data']['hotel_image'] = $Image->getImage($Hotel['hotel'],'3x',"hotel",1, $Hotel['name'])[0] or "";
            $Hotel['hotel_data']['hotel_image_large'] = $Image->getImage($Hotel['hotel'],'300',"hotel",1, $Hotel['name'])[0] or "";


            $NewHotelListL2M[] = $Hotel;
        }
        $HotelBed = new \App\Http\Controllers\Hotel\HotelBed();
        if(isset($Data["pax"]) && !empty($Data["pax"])) {
            $NewHotelBedListL2M = $HotelBed->apiGetHotelFilterList($Data);
        }

        $NewHotelList["local"] = $NewHotelListL2M;
        if(isset($NewHotelBedListL2M)) {
            $NewHotelList["hotelbed"] = $NewHotelBedListL2M->hotels??[];
        }

        return $NewHotelList;
    }

    function ApiGetCruiseFilterList($Data)
    {

        $Text = new Text();
        $QuotationHotel = new QuotationHotel();
        $QuotationCruise = new QuotationCruise();
        $Rate = new \App\Model\Cruise\CruiseCabinRate();

        $PlaceID = $Data['place'];

        $CheckIn = Carbon::create($Data['check_in']['year'], $Data['check_in']['month'], $Data['check_in']['day']);

        $HotelSettingsModified = $Data;
        $HotelListL2M = $Rate->getLowestCruisePlace($PlaceID, $Data['check_in'], false, false, false, false, 8, $HotelSettingsModified, false, false);

        $NewHotelListL2M = [];
        $NewHotelList = [];

        if(isset($HotelListL2M) && !empty($HotelListL2M)) {
            foreach ($HotelListL2M as $Hotel) {

                $Hotel = objectToArray($Hotel);
                $Hotel['cruise_data'] = \App\Model\Cruise\Cruise::find($Hotel['cruise']);
                $Hotel['cruise_class'] = HotelClass::find($Hotel['cruise_data']['class']);
                $Hotel['city'] = Place::find($Hotel['cruise_data']['city']);
                $Hotel['package_data'] = CruisePackage::find($Hotel['package']);

                $NewHotelListL2M[] = $Hotel;
            }
        } else {
            return false;
        }

        $NewHotelList["local"] = $NewHotelListL2M;

        return $NewHotelList;
    }


    function ApiGetHotelList($Data) {
        $Image = new Image();

        $validator = Validator::make($Data, [
            'id' => 'exists:apple_hotels,ID|max:10',
            'name' => 'string|max:255',
            'city' => 'exists:apple_places,ID|max:10',
            'class' => 'exists:apple_hotel_class,ID|max:10',
        ], config('api.error_messages.hotel'));

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }

        $id = $Data["filter"]['id'] ?? null;
        $name = $Data["filter"]['name'] ?? null;
        $city = $Data["filter"]['city'] ?? null;
        $class = $Data["filter"]['class'] ?? null;
        $limit = $Data["filter"]['limit'] ?? 100;

        $data = \App\Model\Hotel\Hotel::select("*");


        //Filters
        $id ? $data->find($id) : null;
        $name ? $data->where('name', "LIKE", "%$name%") : null;
        $city ? $data->whereIn('city', $city) : null;
        $class ? $data->whereIn('class', $class) : null;
        $data->orderBy('preferred', 'desc');//preferred first

        $data = $data->with(['class', 'Contact', 'City'])->paginate($limit);

        if ($data->isEmpty()) {
            $data = ["status"=>false, "msg" => "Hotel(s) not found!"];
        }

        if(isset($data)) {
            foreach ($data as $key => $hotel) {
                $data[$key]['hotel_image'] = $Image->getImage($hotel['ID'],'3x',"hotel",1, $hotel['name'])[0] or "";
                $data[$key]['hotel_image_large'] = $Image->getImage($hotel['hotel'],'300',"hotel",1, $hotel['name'])[0] or "";
            }
        }

        $data = json_encode($data);

        $data = str_replace('class', 'hotel_class', $data);

//        return $this->data = $data->toArray();
        return replaceNullWithEmptyString(json_decode($data, true));
    }

    function ApiGetCruiseList($Data) {

        $validator = Validator::make($Data, [
            'id' => 'exists:apple_cruise,ID|max:10',
            'name' => 'string|max:255',
            'city' => 'exists:apple_places,ID|max:10',
        ], config('api.error_messages.cruise'));

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }

        $id = $Data["filter"]['id'] ?? null;
        $name = $Data["filter"]['name'] ?? null;
        $city = $Data["filter"]['city'] ?? null;
        $limit = $Data["filter"]['limit'] ?? 100;

        $data = \App\Model\Cruise\Cruise::select("*");


        //Filters
        $id ? $data->find($id) : null;
        $name ? $data->where('name', "LIKE", "%$name%") : null;
        $city ? $data->whereIn('city', $city) : null;

        $data = $data->with(['City'])->paginate($limit);

        if ($data->isEmpty()) {
            $data = ["status"=>false, "msg" => "Cruise(s) not found!"];
        }

        $data = json_encode($data);

        $data = str_replace('class', 'hotel_class', $data);

//        return $this->data = $data->toArray();
        return replaceNullWithEmptyString(json_decode($data, true));
    }
    /**
     * @param $Type
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelHtml($Type)
    {
        return view("element.hotel.hotel-plain");
    }

    public function addOwnArrangement(Request $request)
    {
        $own = session()->get('quotation.own');

        if (isset($own))
        {
            $own += $request->own;
            session()->put('quotation.own', $own);
        }
        else
        {
            session()->put('quotation.own', 1);
        }

        return 'added own';
    }

    public function removeOwnArrangement(Request $request)
    {
        $own = session()->get('quotation.own');

        if (isset($own))
        {
            $own = $own - 1;
            session()->put('quotation.own', $own);

            return 'remove own';
        }


    }

    public function resetOwnArrangement(Request $request)
    {
        $own = session()->get('quotation.own');

        if (isset($own))
        {
            session()->forget('quotation.own');

            return 'reset';
        }

    }

    public function getCancelPolicies (Request $request){
        $policies = $request->policies;
        if(isset($request->hotelId)) {
            $messages = Messages::where(function($query) use ($request) {
                return $query->where('hotel', $request->hotelId)
                    ->where('start_date', '<=', $request->checkInDateHotel)
                    ->where('end_date', '>=', $request->checkInDateHotel);
            })->orWhere(function($query) use ($request) {
                return $query->where('hotel', $request->hotelId)
                    ->where('start_date', '<=', $request->checkOutDateHotel)
                    ->where('end_date', '>=', $request->checkOutDateHotel);
            })->get();
        }
        
        $view = View::make('element.hotel.hotel_show_policies', compact('policies', 'messages'))->render();
        return array('status' => true , 'html' => $view );
    }

    public function confirmCancelPolicies(){
        $HotelBedsController = new HotelBedsController();
        $Quotation = new QuotationHotel();
        $session = session()->get('quotation');
        $hotelRate = $Quotation->getHotelRatePlaneCancel($session);
        $html = "";
        if(isset($session['hotel'])) {
            foreach ($session['hotel'] as $hotelKey => $hotel) {
                if ($hotel['provider'] === "hotelbeds") {
                    if (isset($session['api']['hotelbeds']['hotel'][$hotelKey])) {
                        $rateArray = array();
                        $apiDataHotelSelected = $session['api']['hotelbeds']['hotel'][$hotelKey]['selected'];
                        $rateArray = json_encode($apiDataHotelSelected);
                        $HotelBedsCancelRequest = new Request();
                        $HotelBedsCancelRequest->replace(['rateKey' => $rateArray]);
                        $HotelBedsCancelViews = $HotelBedsController->getPolicies($HotelBedsCancelRequest);
                        $html .= $HotelBedsCancelViews['html'];
                    }
                } else {
                    // Making Cancel Detail Array
                    $data = [];
                    if (isset($hotelRate[$hotelKey])) {
                        $localHotelRatePlane = $this->getHotelRatePlane($hotelRate[$hotelKey]);
                        $rateWithmarkup = $this->localHotelCostWithMarkup($hotel['place'], $hotel['nights'], $localHotelRatePlane);
                        $checkInObj = Carbon::create($hotel['check_in']['year'], $hotel['check_in']['month'], $hotel['check_in']['day']);
                        $deadLine = $checkInObj->subDays(HotelModel::find($hotel['hotel'])->cancellation_days ?? 14)->toDayDateTimeString();
                        $data['deadLine'] = $deadLine;
                        $data['cost'] = $rateWithmarkup;
                        $data['hotelId'] = $hotel['hotel'];
                        $data['place'] = Place::find($hotel['place'])->name;
                        $data['hotelName'] = HotelModel::find($hotel['hotel'])->name;
                        $data['mealPlan'] = $hotel['meal_type'];
                        $data['mealPlanLongName'] = MealPlan::find($hotel['meal_type'])->long_name;
                        $data['mealPlanName'] = MealPlan::find($hotel['meal_type'])->plan;
                        $data['currency'] = "USD";
                        $data['notes'] = HotelModel::find($hotel['hotel'])->cancel_policy ?? "";
                        $roomTypeText = "";
                        foreach ($hotel['room_type'] as $RoomTypeID => $RoomCount) {
                            if ($RoomCount) {
                                $roomTypeText .= $RoomCount . ' ' . Room::find($RoomTypeID)->short_name . " - ";
                            }
                        }
                        $roomTypeText = rtrim($roomTypeText, "- ");
                        $data['roomTypeText'] = $roomTypeText;
                        $view = View::make('element.hotel.hotel_show_policies_local', compact('data'))->render();
                        $html .= $view;
                    } else {
                        $html .= "<h5>Own Arrangement</h5>";
                    }
                }
            }
        } else {
            $html = "Cancellation Policies Not Available For Transport Only Section";
        }
         return $html;
    }
    function localHotelCostWithMarkup($city,$nights,$currentRate){
       if(apple_cancel_charges_markup_rates::where('city',$city)->where('nights',$nights)->exists()){
            $markUpPercentage = apple_cancel_charges_markup_rates::where('city',$city)->where('nights',$nights)->first()->mark_up;
            $percentageRate = $currentRate * ($markUpPercentage / 100);
            $rate = $currentRate + $percentageRate;
            return $rate;
       } else {
           return $currentRate;
       }
    }

    function getHotelRatePlane($HotelRate) {

        $adultCost = 0;
        $childCost = 0;

            foreach ($HotelRate['adult'] as $RoomType => $RoomCost) {
                if ($RoomCost !== false) {
                    $adultCost += $RoomCost;
                }
            }
            #Child
            foreach ($HotelRate['child'] as $ChildType => $RoomCost) {
                $childCost += $RoomCost;
            }
            $totalRates = $adultCost + $childCost;
            return $totalRates;
        }
    public function getHotelDiscountList(Request $request) {

        $data = $request->all();
        $view = "";
        $view .= View::make('element.hotel.hotel-discount')->with('data', $data)->render();

        return array('status' => true , 'html' => $view );
    }

}
