<?php

namespace App\Http\Controllers\Element;
use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use  App\Model\Place\Place;

use Session;

/**
 * Class Tour
 * @package App\Http\Controllers\Element
 */
class Tour extends Controller
{


    /**
     * @param Request $request
     * @param $MarkerID
     * @param $PlaceID
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getPlaceItemIternery(Request $request, $MarkerID, $PlaceID){
 		
		$Place = new Place();
		
		$PlaceDetails = Place::find($PlaceID);
		$Index = 0;
		
		$PathDistance = false;
		if($request->input('place')){
			$PlacesList = $request->input('place');
			$LastPlace = end($PlacesList);
			$PathDistance = $Place->getPlaceDistance($LastPlace, $PlaceID);
			$Index = count($PlacesList);
		}
		
 		
		return view('element.place',["MarkerID"=>$MarkerID,"Place"=>$PlaceDetails,"PathDistance"=>$PathDistance,"Index"=>$Index]);
		
	}
	
	
}