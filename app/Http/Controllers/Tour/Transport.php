<?php
namespace App\Http\Controllers\Tour;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

/**
 * Class Transport
 * @package App\Http\Controllers\Tour
 */
class Transport extends Controller
{

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function searchGuideDriver(Request $request){
		
		$inputs = $request->input();
		
		return view('element.transport.search-result',["Inputes"=>$inputs]);
		
	}
	
}