<?php

namespace App\Http\Controllers\Tour;

use App\Http\Controllers\Controller;
use App\Model\Cruise\Cruise;
use App\Model\Cruise\CruiseCabinRate;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\HotelBed as HotelBedModel;
use App\Model\Hotel\Rates;
use App\Model\Place\Place;
use App\Model\Quotation\Quotation;
use App\Model\Quotation\QuotationCruise;
use App\Model\Quotation\QuotationHotel;
use App\Model\Text\Text;
use Illuminate\Http\Request;
use Session;

/**
 * Class ElementHtml
 * @package App\Http\Controllers\Tour
 */
class ElementHtml extends Controller
{


    /**
     * @param $HotelID
     * @param $HotelItemIndex
     * @param $HotelProvider
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \ReflectionException
     */
    function getHotelModify($HotelID, $HotelItemIndex, $HotelProvider, Request $request)
    {

        $QuotationHotel = new QuotationHotel();

        $HotelCurrentDetailsArray = $request->input("hotel_setting") or false;
        $HotelCurrentDetails = $HotelCurrentDetailsArray[$HotelItemIndex];
        $HotelSettingsArray = arrayMapMulti(['urldecode', 'urldecode_array'], $HotelCurrentDetailsArray);
// dd($HotelCurrentDetails);
        $AccommodationTypeArray = $request->input("accommodation");
        $AccommodationType = $AccommodationTypeArray[$HotelItemIndex];
        $HotelSettings = $QuotationHotel->getDecodeArray($HotelCurrentDetails) or false;

        if ($HotelProvider === 'hotelbeds') {
            $HotelbedsData = json_decode(request('hotel_beds_json')[$HotelItemIndex] ?? "{}", true);
            $HotelbedsDataSelected = json_decode(request('hotel_selected_data')[$HotelItemIndex] ?? "{}", true);
        }

        $Text = new Text();
        return view('element.hotel.hotel-modify', ['HotelbedsDataSelected' => $HotelbedsDataSelected ?? false,
                                                            'HotelbedsData' => $HotelbedsData ?? false,
                                                            'HotelProvider' => $HotelProvider,
                                                            'Text' => $Text,
                                                            "HotelID" => $HotelID,
                                                            "HotelItemIndex" => $HotelItemIndex,
                                                            "HotelSettingsArray" => $HotelSettingsArray,
                                                            "HotelSettings" => $HotelSettings,
                                                            "AccommodationType" => $AccommodationType,
                                                            'HotelAccommodationArray' => $AccommodationTypeArray,
                                                            'SelectedID' => Session::get('quotation.transport_only')                                                       ]);

    }

    /**
     * @param $HotelID
     * @param $HotelItemIndex
     * @param $HotelProvider
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \ReflectionException
     */
    function getCruiseModify($CruiseID, $CruiseItemIndex, Request $request)
    {
        if(isset($CruiseID)) {
            $QuotationCruise = new QuotationCruise();

            $CruiseCurrentDetailsArray = $request->input("cruise_setting") or false;
            $CruiseCurrentDetails = $CruiseCurrentDetailsArray[$CruiseItemIndex];
            $CruiseSettingsArray = arrayMapMulti(['urldecode', 'urldecode_array'], $CruiseCurrentDetailsArray);

            $AccommodationTypeArray = $request->input("accommodation");
            $AccommodationType = $AccommodationTypeArray[$CruiseItemIndex];
            $CruiseSettings = $QuotationCruise->getDecodeArray($CruiseCurrentDetails) or false;

            $Text = new Text();
            return view('element.cruise.cruise-modify', ['Text' => $Text,
                "CruiseID" => $CruiseID,
                "CruiseItemIndex" => $CruiseItemIndex,
                "CruiseSettingsArray" => $CruiseSettingsArray,
                "CruiseSettings" => $CruiseSettings,
                "AccommodationType" => $AccommodationType,
                'CruiseAccommodationArray' => $AccommodationTypeArray,
                'SelectedID' => Session::get('quotation.transport_only')
            ]);
        }
    }

    /**
     * @param $Slide
     * @param Request $request
     * @return mixed
     * @throws \ReflectionException
     */
    function getTotalCostUnit($Slide, Request $request){

        $QuotationArray = Session::get('quotation');
        $Data = $request->all();

        if ($Slide == 4) {
            $HotelSettingsArray = arrayMapMulti(['urldecode', 'urldecode_array'], $Data['hotel_setting']);
            $QuotationArray['hotel'] = arrayMapMulti('intval',$HotelSettingsArray);
            $QuotationArray['accommodation'] = arrayMapMulti(['intval'],$Data['accommodation']);



        } elseif ($Slide == 5) {

            unset($QuotationArray['attraction']);
            unset($QuotationArray['city_tour']);
            unset($QuotationArray['excursion']);

            if (isset($Data['attraction']))
                $QuotationArray['attraction'] = arrayMapMulti(['intval'], $Data['attraction']);

            if (isset($Data['city_tour']))
                $QuotationArray['city_tour'] = arrayMapMulti(['intval'], $Data['city_tour']);

            if (isset($Data['excursion']))
                $QuotationArray['excursion'] = arrayMapMulti(['intval'], $Data['excursion']);


        }
        elseif ($Slide == 6) {
            $QuotationArray['meal'] = $Data['meal'];
        }


        $Quotation = new Quotation();
        $Cost = $Quotation->getCost($QuotationArray);
//        dd($QuotationArray);
        return $Cost;
    }

    /**
     * @param $HotelID
     * @param $HotelItemIndex
     * @param int $HotelType
     * @param string $HotelProvider
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getHotel($HotelID, $HotelItemIndex, $HotelType = 0, $HotelProvider = 'local', Request $request)
    {
        $QuotationHotel = new QuotationHotel();
        $QuotationCruise = new QuotationCruise();
        $Rate = new Rates();
        $CruiseRate = new CruiseCabinRate();
        if($HotelProvider == 'local_special') {
            $room_category = $request->input("room_category");
            $nights = $request->input("nights");
        }

        $HotelCurrentDetails = $request->input("hotel_setting");
        $CruiseCurrentDetails = $request->input("cruise_setting");


        if ($HotelProvider == 'local') {//apple hotels
            $Availability = [];
            if (isset($HotelCurrentDetails[$HotelItemIndex]) && $HotelCurrentDetails[$HotelItemIndex]) {//if its not a delete hotel or empty palce

                $HotelSettings = $QuotationHotel->getDecodeArray($HotelCurrentDetails[$HotelItemIndex]);
                $HotelSettings["room_category"] = false; // Edited by Priyantha,(Hotel category rates need to update in backend....) more ask wasundara
                $HotelSettings['room_type'] = isset($HotelSettings['room_type']) ? $HotelSettings['room_type'] : false;
                $Query = $QuotationHotel->getEncodeArray($HotelID, $HotelSettings['check_in'], $HotelSettings['check_out'], $HotelSettings['room_category'], $HotelSettings['meal_type']??false, $HotelSettings['room_type'], $HotelSettings['extrabed'], $HotelSettings['place']);

            } else if(isset($CruiseCurrentDetails[$HotelItemIndex]) && $CruiseCurrentDetails[$HotelItemIndex]) {
                $HotelSettings = $QuotationCruise->getDecodeArray($CruiseCurrentDetails[$HotelItemIndex]);
                $HotelSettings["room_category"] = false; // Edited by Priyantha,(Hotel category rates need to update in backend....) more ask wasundara
                $HotelSettings['room_type'] = false;
                $Query = $QuotationHotel->getEncodeArray($HotelID, $HotelSettings['check_in'], $HotelSettings['check_out'], $HotelSettings['room_category'], $HotelSettings['meal_type']??false, $HotelSettings['room_type'], $HotelSettings['extrabed'], $HotelSettings['place']);
            }

            $Availability['hotel'] = $Rate->getAvailabilityHotelPlace($HotelSettings['place'], $HotelSettings['check_in']);
            $Availability['cruise'] = $CruiseRate->getAvailabilityCruisePlace($HotelSettings['place'], $HotelSettings['check_in']);
            $HotelSettings['hotel'] = $HotelID;

            $Session = Session::get('quotation');
            if(isset($Session['api']['hotelbeds']['hotel'][$HotelItemIndex])){
                session()->forget('quotation.api.hotelbeds.hotel.'.$HotelItemIndex);

            }

            if ($HotelType == 0)
                return view('element.hotel.hotel', ["HotelDetails" => Hotel::find($HotelID), "HotelItemIndex" => $HotelItemIndex, "Query" => $Query, "HotelSettings" => $HotelSettings, "CruiseQuery" => null, "CruiseSettings" => null, "Availability" => $Availability]);
            else
                return view('element.hotel.hotel', ["HotelDetails" => Hotel::find($HotelID), "HotelItemIndex" => $HotelItemIndex, "Query" => $Query, "CruiseQuery" => null, "CruiseSettings" => null, "Attributes" => array('QuotationHotel' => true), "HotelSettings" => $HotelSettings, "Availability" => $Availability]);
        } elseif ($HotelProvider == 'local_special') {

            if (isset($HotelCurrentDetails[$HotelItemIndex]) && $HotelCurrentDetails[$HotelItemIndex]) {

                $HotelSettings = $QuotationHotel->getDecodeArray($HotelCurrentDetails[$HotelItemIndex]);

                $HotelSettings["room_category"] = $room_category; // Edited by Priyantha,(Hotel category rates need to update in backend....) more ask wasundara
                $HotelSettings["nights"] = $nights;
                $HotelSettings["provider"] = "local_special";
                $HotelSettings['room_type'] = isset($HotelSettings['room_type']) ? $HotelSettings['room_type'] : false;
                $Query = $QuotationHotel->getEncodeArray($HotelID, $HotelSettings['check_in'], $HotelSettings['check_out'], $HotelSettings['room_category'], $HotelSettings['meal_type']??false, $HotelSettings['room_type'], $HotelSettings['extrabed'], $HotelSettings['place'], "local_special", $nights);

            }

            $Availability['hotel'] = $Rate->getAvailabilityHotelPlace($HotelSettings['place'], $HotelSettings['check_in']);
            $HotelSettings['hotel'] = $HotelID;

            return view('element.hotel.hotel', ["HotelDetails" => Hotel::find($HotelID), "HotelItemIndex" => $HotelItemIndex, "Query" => $Query, "HotelSettings" => $HotelSettings, "CruiseQuery" => null, "CruiseSettings" => null, "Availability" => $Availability]);

        }
        elseif ($HotelProvider == 'hotelbeds') { //Hotelbeds
            $HotelBeds = new HotelBed();
            $HotelBedsJson = request('data');
            $HotelBedsItem = json_decode($HotelBedsJson, true);



            $OldHotelSettings = $QuotationHotel->getDecodeArray($HotelCurrentDetails[$HotelItemIndex]);


            $session = session()->get('quotation');
            $results = array();



            $SelectedRate = collect(collect($HotelBedsItem['combine'])->last());
            $SelectedRate = $SelectedRate[1]??$SelectedRate->first();


            $rateKey = json_encode($SelectedRate);

            $rateKey = json_encode($SelectedRate);



            $Query = $QuotationHotel->getEncodeArrayHotelbeds($HotelBedsItem['id'], $OldHotelSettings['check_in'], $OldHotelSettings['check_out'], $OldHotelSettings['place'] , $SelectedRate);
            return view('element.hotel.hotelbeds',["rateKey" => $rateKey ,"SelectedRate" => json_encode($SelectedRate), "HotelItem" => $HotelBeds->getHotelDetails($HotelID), "HotelItemIndex" => $HotelItemIndex, "Query" => $Query, 'HotelBedsJson' => $HotelBedsJson]);
        }
    }

    /**
     * @param $HotelID
     * @param $HotelItemIndex
     * @param int $HotelType
     * @param string $HotelProvider
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getCruise($CruiseID, $CruiseItemIndex, $HotelType = 0, Request $request)
    {
        $QuotationCruise = new QuotationCruise();
        $QuotationHotel = new QuotationHotel();
        $Rate = new Rates();
        $CruiseRate = new CruiseCabinRate();
        $CruiseCurrentDetails = $request->input("cruise_setting");
        $HotelCurrentDetails = $request->input("hotel_setting");

        if (isset($CruiseCurrentDetails[$CruiseItemIndex]) && $CruiseCurrentDetails[$CruiseItemIndex]) {//if its not a delete hotel or empty palce
            $CruiseSettings = $QuotationCruise->getDecodeArray($CruiseCurrentDetails[$CruiseItemIndex]);
            $CruiseSettings["cabin_type"] = false; // Edited by Priyantha,(Hotel category rates need to update in backend....) more ask wasundara
            $Query = $QuotationCruise->getEncodeArray($CruiseID, $CruiseSettings['check_in'], $CruiseSettings['check_out'], $CruiseSettings['cabin_type'], $CruiseSettings['meal_type'], $CruiseSettings['cabin_occupancy_type'], $CruiseSettings['extrabed'], $CruiseSettings['place']);
        } else {
            $CruiseSettings = $QuotationHotel->getDecodeArray($HotelCurrentDetails[$CruiseItemIndex]);
            $CruiseSettings['cruise'] = $CruiseID;
            $CruiseSettings['cabin_type'] = false;
            $CruiseSettings['package'] = false;
            $CruiseSettings['provider'] = "local";
            $CruiseSettings['cabin_occupancy_type'] = $CruiseSettings['room_type'];
            $Query = $QuotationCruise->getEncodeArray($CruiseID, $CruiseSettings['check_in'], $CruiseSettings['check_out'], false, false, false, false, $CruiseSettings['place']);
        }

        $Availability['hotel'] = $Rate->getAvailabilityHotelPlace($CruiseSettings['place'], $CruiseSettings['check_in']);
        $Availability['cruise'] = $CruiseRate->getAvailabilityCruisePlace($CruiseSettings['place'], $CruiseSettings['check_in']);
        $CruiseSettings['cruise'] = $CruiseID;


        if ($HotelType == 0)
            return view('element.hotel.hotel', ["CruiseDetails" => Cruise::find($CruiseID), "HotelItemIndex" => $CruiseItemIndex, "CruiseQuery" => $Query, "CruiseSettings" => $CruiseSettings, "Query" => null, "HotelSettings" => null, 'AccommodationType' => 4, "Availability" => $Availability]);
        else
            return view('element.hotel.hotel', ["CrusieDetails" => Cruise::find($CruiseID), "HotelItemIndex" => $CruiseItemIndex, "CruiseQuery" => $Query, "Query" => null, "HotelSettings" => null, "Attributes" => array('QuotationHotel' => true), "CruiseSettings" => $CruiseSettings, 'AccommodationType' => 4, "Availability" => $Availability]);

    }

    /**
     * @param $HotelID
     * @param $HotelItemIndex
     * @param int $HotelType
     * @param string $HotelProvider
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getChangeHotel($HotelItemIndex, $HotelType = 0, $HotelProvider = 'local', Request $request)
    {

        $QuotationHotel = new QuotationHotel();
        $HotelCurrentDetails = $request->input("hotel")['hotel_setting'];
        $type = $request->input("type");
        $star = $request->input("star");

        Session::put("quotation.hotel_filter.return_star", $star);
        Session::put("quotation.hotel_filter.return_type", $type);

        if ($type == 'local') {//apple hotels

            if (isset($HotelCurrentDetails[$HotelItemIndex]) && $HotelCurrentDetails[$HotelItemIndex]) {//if its not a delete hotel or empty palce
                $HotelSettings = $QuotationHotel->getDecodeArray($HotelCurrentDetails[$HotelItemIndex]);

                $HotelID = ElementHtml::getLowestHotel($star, $HotelSettings, $type);
                $Query = $QuotationHotel->getEncodeArray($HotelID[0], $HotelSettings['check_in'], $HotelSettings['check_out'], $HotelSettings['room_category'], $HotelSettings['meal_type'], $HotelSettings['room_type'], $HotelSettings['extrabed'], $HotelSettings['place']);
            }

            $HotelSettings['hotel'] = $HotelID[0];

            if ($HotelType == 0)
                return view('element.hotel.hotel', ["HotelDetails" => Hotel::find($HotelID), "HotelItemIndex" => $HotelItemIndex, "Query" => $Query, "HotelSettings" => $HotelSettings, "CruiseDetails"=>null, "CruiseQuery"=>null] );
            else
                return view('element.hotel.hotel', ["HotelDetails" => Hotel::find($HotelID), "HotelItemIndex" => $HotelItemIndex, "Query" => $Query, "Attributes" => array('QuotationHotel' => true), "HotelSettings" => $HotelSettings, "CruiseDetails"=>null, "CruiseQuery"=>null]);

        } elseif ($type == 'hotelbeds') {//Hotelbeds

            $OldHotelSettings = $QuotationHotel->getDecodeArray($HotelCurrentDetails[$HotelItemIndex]);
            $HotelID = ElementHtml::getLowestHotel($star, $OldHotelSettings, $type);

            $HotelBed = new \App\Model\Hotel\HotelBed();
            $QuotationArray = Session::get('quotation');

            $HotelBedHotels = $HotelBed->AvailabilityCombine($OldHotelSettings, $QuotationArray, $HotelID,1);

            if(isset($HotelBedHotels) && isset($HotelBedHotels->hotels[0])) {
                $HotelBedsJson = HotelBed::extractRespond($HotelBedHotels->hotels[0],true,$QuotationArray['pax'],$QuotationArray);

                $HotelBeds = new HotelBed();
                $HotelBedsItem = json_decode($HotelBedsJson, true);


                $SelectedRate = collect(collect($HotelBedsItem['combine'])->first());
                $SelectedRate = $SelectedRate[1]??$SelectedRate->first();


                $rateKey = json_encode($SelectedRate);


                $Query = $QuotationHotel->getEncodeArrayHotelbeds($HotelBedsItem['id'], $OldHotelSettings['check_in'], $OldHotelSettings['check_out'], $OldHotelSettings['place'] , $SelectedRate);

                return view('element.hotel.hotelbeds', ["rateKey" => $rateKey ,"SelectedRate" => json_encode($SelectedRate), "HotelItem" => $HotelBeds->getHotelDetails($HotelID), "HotelItemIndex" => $HotelItemIndex, "Query" => $Query, 'HotelBedsJson' => $HotelBedsJson]);

            } else {
                return false;
            }

        }
    }

    static function getLowestHotel($star, $OldHotelSettings, $type) {

        if($type == "hotelbeds") {
            $city_list = Place::having('ID', $OldHotelSettings['place'])->get()->pluck('A2');

            $HotelIDs = HotelBedModel::having('destination_code', $city_list);
            if($star != 0) {
                $HotelIDs = $HotelIDs->where("class", 'like',  $star-1 . "%");
            }

            $HotelIDs = $HotelIDs->get();
            $HotelIDs = $HotelIDs->pluck('id');
            $HotelIDs = $HotelIDs->toArray();

            if (empty($HotelIDs)) {
                return "Hotels not found!";
            } else {
                return $HotelIDs;
            }
        } else {
            $Rate = new Rates();
            $HotelSettingsModified['filter']['class'] = [(int)$star];
            $LowestHotel = $Rate->getLowestHotelPlace($OldHotelSettings['place'], $OldHotelSettings['check_in'], false, false, false, false, 1, $HotelSettingsModified, false);

            $HotelIDs = [$LowestHotel[0]->hotel];

            if (empty($HotelIDs)) {
                return "Hotels not found!";
            } else {
                return $HotelIDs;
            }
        }
    }

    /**
     * @param $HotelItemIndex
     * @param $PlaceID
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getNoHotel($HotelItemIndex, $PlaceID)
    {

        return view('element.hotel.hotel', ["PlaceID" => $PlaceID, "HotelItemIndex" => $HotelItemIndex]);
    }


    /**
     * @param $Index
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getChangeHotelList($Index)
    {
        $PlaceID = Session::get("quotation.place.$Index");
        return view('element.hotel.hotel-list', ['PlaceID' => $PlaceID,'Index' => $Index]);
    }


    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getTransportModify()
    {
        $Text = new Text();
        return view('element.transport.advance-search', ['Text' => $Text]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getMealModify()
    {
        $Text = new Text();
        return view('element.meal.meal-modify', ['Text' => $Text]);

    }

    /**
     * @param int $GuideNumber
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getGuideRate($GuideNumber = 1)
    {

        $Text = new Text();
        return view('element.transport.add-guide', ['Text' => $Text, 'GuideNumber' => $GuideNumber]);

    }


}
