<?php

namespace App\Http\Controllers\Tour;

use App\Http\Controllers\Controller;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\HotelTBO as TBO;
use App\Model\Place\Place;
use App\Model\Quotation\QuotationTask;
use Illuminate\Http\Request;
use Session;
use Exception;


/**
 * Class Data
 * @package App\Http\Controllers\Tour
 */
class Data extends Controller
{
    /**
     * Data constructor.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('quotation_middleware');
    }

    /**
     * @param $viewID
     * @param bool $ShowTab
     * @return mixed
     */
    public function getView($viewID, $ShowTab = false)
    {
        $Data = Session::get('quotation');
        $EmptyData = [
            'code' => 500,
            'message' => ['Empty Quotation']
        ];

        if (!$Data && $viewID != 16)
            return jsona($EmptyData, 500);

        $Attributes = request()->all();

        switch ($viewID) {
            case 1:
                return view('tour.welcome', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 2:
                return view('tour.pax', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 3:
                return view('tour.transport', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 4:
                return view('tour.hotel', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 5:
                return view('tour.attraction', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 6:
                return view('tour.meal', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 7:
                return view('tour.flight', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 8:
                return view('tour.quotation', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 9:
                return view('tour.save', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 10:
                return view('tour.confirm', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 11:
                return view('tour.final', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 12:
                return view('flight.flight-result', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 13:
                return view('tour.hotel-only', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
            case 14:
                $Data = Session::get('quotation.api_hotel');
                if ($Data['api'] == 'hotelbed') {//hotel beds
                    $AvailableFilters = HotelBed::getAvailableFilters($Data['hotelbed']['rooms'], $Data['filters']);
                    return view('tour.hotel-book-hotelbed', ["show_tab" => $ShowTab, "Attributes" => $Attributes, 'AvailableFilters' => $AvailableFilters, 'Rooms' => $Data['hotelbed']['rooms']]);
                }
                break;
            case 15:
                $Data = Session::get('quotation.api_hotel');
                if ($Data['api'] == 'tbo') {//TBO
                    $TBO = new TBO();
                    $Data = Session::get('quotation.hotel_tbo');
                    $AvailablePriceHotelRooms = $TBO->getAvailablePriceHotelRooms($Data);
                    return view('tour.hotel-room-book-tbo', ["show_tab" => $ShowTab, "Attributes" => $Attributes, 'AvailablePriceHotelRooms' => $AvailablePriceHotelRooms, 'Status' => $AvailablePriceHotelRooms['AvailableForBook']]);
                }
                if ($Data['api'] == 'hotelbed') {//hotel beds
                    return view('tour.hotel-room-book-pax-hotelbed', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);
                }
                break;
            case 16:
                return view('tour.end-message', ["show_tab" => $ShowTab, "Attributes" => $Attributes, 'Message' => "You're all done!"]);

            case 17:
                return view('tour.transport-night', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);

            case 20:
                return view('tour.flight-book', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);

            case 21:
                return view('tour.flight-confirm', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);

            case 23:
                return view('flight.pnr-cancel', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);

            case 26:
                return view('flight.ticket-issue', ["show_tab" => $ShowTab, "Attributes" => $Attributes]);


        }
        return jsona($EmptyData, 500);
    }


    /**
     * @return array
     * @throws \ReflectionException
     */
    public function setTourArray()
    {

        $inputs = request()->all();

        $QuotationTask = new QuotationTask();

        $TourType = Session::get('quotation.tour_type');
        $TourArray = QuotationTask::getTourSlideArray($TourType);


        $CurrentSlide = intval($inputs['current_slide']);
        $NextSlide = $inputs['next_slide'] ?? $CurrentSlide;

        $CurrentIndex = array_search($CurrentSlide, $TourArray);
        $NextIndex = array_search($NextSlide, $TourArray);

        $Slides = QuotationTask::getCompletedSlides();

        if ($CurrentIndex > $NextIndex) {
            return ["active_slide" => $Slides];
        }

        $data = $QuotationTask->setSession($CurrentSlide, $inputs);
        $Slides = QuotationTask::getCompletedSlides();

        if (request('warn')) {
            $QuotationTask->warning = request('warn');
        }

        if(isset($QuotationTask->error) && $QuotationTask->error && isset($NextSlide) && $NextSlide == 10) {
            throw new Exception($QuotationTask->error);
        }


        $totalValue = 0;

        $customSession = Session::get('quotation.other_rate');


       if (isset($customSession)){
           foreach ($customSession as $custom) {

               if (isset($custom['details']['single_bed_room_count']) != null){
                   $singleValue = $custom['details']['single_bed_rate'] * $custom['details']['single_bed_room_count'];
                   $totalValue += $singleValue;
               }

               if (isset($custom['details']['double_bed_room_count']) != null){
                   $doubleValue = $custom['details']['double_bed_rate'] * $custom['details']['double_bed_room_count'];
                   $totalValue += $doubleValue;
               }

               if (isset($custom['details']['triple_bed_room_count']) != null){
                   $tripleValue = $custom['details']['triple_bed_rate'] * $custom['details']['triple_bed_room_count'];
                   $totalValue += $tripleValue;
               }
           }
       }

        $totalValue = currency($totalValue, 'LKR', 'USD', false);

        Session::put('quotation.day_use_room_total_value', $totalValue);

        return [
            "active_slide" => $Slides,
            "warn" => $QuotationTask->warning,
            "error" => $QuotationTask->error,
            "data" => $data,
        ];

    }

    /**
     * @param $Slide
     * @return mixed
     */
    function resetSlide($Slide)
    {
        return QuotationTask::resetSlide($Slide);
    }

    /**
     * @param $TourType
     * @param bool $force
     * @return \Illuminate\Http\JsonResponse
     * @throws \ReflectionException
     */
    function getTourSlideArray($TourType, $force = false)
    {
        if (!intval($force)) {
            $TourID = Session::get('quotation.tour_type');
            if (!empty($TourID))
                return jsona([
                    "message" => "You already have an unsaved quotation! Do you want continue or create new tour ?",
                    "type" => "exist",
                    "tour_id" => $TourID,
                ], 500);
        }

        $QuotationTask = new QuotationTask();
        $SlideNames = QuotationTask::getSlideNames();

        $TourArrayM['slide_array'] = QuotationTask::getTourSlideArray($TourType);
        $TourArrayM['tour_type'] = $TourType;
        $TourArrayM['transport_only'] = 0;
        $TourArrayM['slide'] = QuotationTask::getCompletedSlides($TourType);
        $QuotationTask->setSession(0, $TourArrayM);

        $Data = QuotationTask::getCompletedSlides();

        return jsona(["slide" => $TourArrayM, "slide_name" => $SlideNames, "status" => $Data]);


    }


}
