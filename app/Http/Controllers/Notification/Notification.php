<?php

namespace App\Http\Controllers\Notification;

use App\Http\Controllers\Controller;

/**
 * Class Notification
 * @package App\Http\Controllers\Notification
 */
class Notification extends Controller
{

    /**
     * @return array
     */
    function getStatus()
    {
        $Status = [];

        if (\request('id')) {
            $Data = \Auth::user()->notifications()->where('id', request('id'))->first();
            $Status['new'][] = [
                "id"=>$Data->id,
                "html"=> \View::make("element.notification.notification-item", ['NotificationItem' => $Data])->render()
            ];
        }
        $Status['unred'] = \Auth::user()->unreadNotifications->count();
        $Status['red'] = \Auth::user()->readNotifications->count();

        return $Status;
    }

    /**
     * @param $id
     */
    function markAsRead($id)
    {

    }

    /**
     * @return mixed
     */
    function markAsReadAll()
    {
        \Auth::user()->unreadNotifications->markAsRead();
        $Data['status'] = $this->getStatus();
        return $Data;
    }

    function delete()
    {

    }

    function deleteAll()
    {

    }

}
