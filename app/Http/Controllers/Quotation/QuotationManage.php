<?php


namespace App\Http\Controllers\Quotation;


use App\Events\QuotationEmail;
use App\Http\Controllers\Controller;
use App\Model\Image\Image;
use App\Model\Place\Distance;
use App\Model\Place\Place;
use App\Mail\HotelbedsInvoice;
use App\Model\Quotation\Quotation as QuotationModel;
use App\Model\QuotationManage\Quotation;
use App\Model\Vehicle\TransportCost;
use App\Model\Vehicle\Vehicle;
use App\Model\QuotationManage\QuotationHotelBedsRateKeys;
use App\Model\QuotationManage\QuotationHotelbedsVouchers;
use App\User;
use Auth;
use Event;
use Illuminate\Http\Request;
use Session;
use Validator;
use View;
use Mail;
use DB;
use App\Model\Place\Stop;
use App\Model\Country\AvailableCountry;

/**
 * Class QuotationManage
 * @package App\Http\Controllers\Quotation
 */
class QuotationManage extends Controller
{
    /**
     * @var array
     */
    protected $From = ['email' => '<EMAIL>', 'name' => 'Apple Holidays'];
    /**
     * @var string
     */
    public $ErrorMessage = "";

    /**
     * QuotationManage constructor.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('quotation_manage');
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     * @throws \Throwable
     */
    function save()
    {

        #GET QUOTATION SESSION
        $QuotationSession = Session::get('quotation');

        $Quotation = new Quotation();

        $QuotationSave = $Quotation->saveQuotation($QuotationSession);

        if (!$QuotationSave) {
            throw new \Exception($Quotation->error);
        }

        // Prev remove pri
        ########################################################################
        #  After Confirm
        // $Quotation->sendEmails($QuotationSession, $QuotationSave, $Quotation);
        // Prev remove pri

        // New added pri
        ########################################################################
        #Confirmation

        if ($QuotationSession['save_type'] == Quotation::CONFIRM && $QuotationSession['status'] != 3) {
            $Quotation->confirmQuotation($QuotationSave['reference_id'], $QuotationSave['quotation_no']);
        }

        if ($QuotationSession['save_type'] == Quotation::CANCEL) {
            $Quotation->cancelQuotation($QuotationSave['reference_id'], $QuotationSave['quotation_no']);
        }

        // Send tour confirmation voucher
        if ($QuotationSession['save_type'] == Quotation::CONFIRM) {

            $EmailData = $QuotationSave;
            $EmailData['to_emails'] = Auth::user()->email;
            $EmailData['type'] = "tour_confirmation";
            Event::fire(new QuotationEmail($EmailData));
            // $this->emailHotelVoucher($EmailData);
        }

        if ($QuotationSession['save_type'] == Quotation::SAVE) {
            //Sending quotation
            $EmailData = $QuotationSave;
            $EmailData['to_emails'] = Auth::user()->email;
            $EmailData['type'] = "quote";
            Event::fire(new QuotationEmail($EmailData));
        }

        //Send tour cancellation voucher
        if ($QuotationSession['save_type'] == Quotation::CANCEL) {

            $EmailData = $QuotationSave;
            $EmailData['to_emails'] = Auth::user()->email;
            $EmailData['type'] = "tour_confirmation";
            $EmailData['ID'] = $QuotationSession['ID'];
            Event::fire(new QuotationEmail($EmailData));
            // $this->emailHotelVoucher($EmailData);
        }
        // New added pri

        $html = View::make("quotation.respond.save", ['status' => $QuotationSave])->render();
        return jsona(['html' => $html, 'status' => $QuotationSave]);

    }

    /**
     * @param $reference_id
     * @param $quotation_no
     * @return array|\Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    function edit($reference_id, $quotation_no)
    {

        $validator = Validator::make(['reference_id' => $reference_id, 'quotation_no' => $quotation_no], [
            'quotation_no' => 'required|exists:apple_quotation,ID'
        ]);

        if ($validator->fails())
            return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);


        $SessionArray = Quotation::getQuotation($reference_id, $quotation_no);

        if ($SessionArray) {
            Session::put('quotation', $SessionArray);
            return ['reference_id' => $SessionArray['ID'], 'quotation_no' => $SessionArray['quotation_no'], 'tour_type' => $SessionArray['tour_type']];
        } else {
            return view("system.error.error-ajax", ["Message" => "Invalid tour no!"]);
        }


    }

    /**
     * @param $quotation_no
     * @return mixed
     * @throws \Exception
     */
    function cancel($quotation_no)
    {
        $Quot = Quotation::withTrashed()->where('quotation_no', $quotation_no)->orderBy('ID', 'desc')->first();
        if ($Quot->status == 2) {

            $this->edit($Quot->ID, $quotation_no);
            Session::put('quotation.save_type', 'cancel');
            return Session::get('quotation');

        } else
            throw new \Exception("Quotation is no Confirmed");

    }

    public function emailHotelVoucher($EmailData)
    {
        $QuotationArray = Session::get('quotation');
        $EmailList = [];

        if (isset($QuotationArray['user'])) {#check this is not the first quote
            $EmailList[] = User::find($QuotationArray['user'])->email;//quotation email
        }
        if (empty($QuotationArray['user']) || (isset($QuotationArray['user']) && $QuotationArray['user'] != Auth::user()->email)) {#check same users
            $EmailList[] = Auth::user()->email;//current log user
        }


        $EmailData['cc_emails'] = Auth::user()->email;
        $EmailData['quotation_no'] = $QuotationArray['quotation_no'];
        $EmailData['type'] = "hotel_vouchers";

        if ($QuotationArray['save_type'] == Quotation::CANCEL)
            $EmailData['voucher_type'] = "cancel";
        else
            $EmailData['voucher_type'] = "auto";

        Event::fire(new QuotationEmail($EmailData));

    }


    /**
     * @param $ReferenceID
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function emailResendBox($ReferenceID)
    {
        return view('quotation.email.email-resed-model', ['ReferenceID' => $ReferenceID]);
    }


    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \ReflectionException
     */
    public function viewEmailResend()
    {

        ini_set('max_execution_time', 0);

        $Data = request()->all();

        if (empty($Data['voucher_type']))
            $VoucherType = 'cancel';
        else
            $VoucherType = $Data['voucher_type'];

        if (isset($Data['ID']))
            $Quot = Quotation::withTrashed()->where('ID', $Data['ID']);
        else
            $Quot = Quotation::withTrashed()->where('quotation_no', $Data['quotation_no'])->orderBy('ID', 'desc');


        $quotation_no = $Data['quotation_no'];
        $LastQuotationID = $Quot->first();

        if ($LastQuotationID->quotation_no == $LastQuotationID->ID)
            $OldQuotationID = $LastQuotationID;
        else
            $OldQuotationID = $Quot->skip(1)->take(1)->first();//get before last one


        //if this is
        $QuotationModel = new QuotationModel();
        $HotelID = false;
        if (isset($Data['send_individual'])) {

            if ($Data['type'] == 'hotel_vouchers') {

                if (is_array($Data['hotel_id'])) {

                    if (empty($Data['hotel_id'][0]))
                        $HotelID = false;
                    else {
                        if (empty($Data['hotel_id'][0]))
                            unset($Data['hotel_id'][0]);
                        $HotelID = array_values(arrayMapMulti('getActualDataType', $Data['hotel_id']));
                    }
                } else
                    $HotelID = $Data['hotel_id'];

            } else if($Data['type'] == 'day_use_vouchers') {

                if (is_array($Data['dayused_id'])) {

                    if (empty($Data['dayused_id'][0]))
                        $HotelID = false;
                    else {
                        if (empty($Data['dayused_id'][0]))
                            unset($Data['dayused_id'][0]);
                        $HotelID = array_values(arrayMapMulti('getActualDataType', $Data['dayused_id']));
                    }
                } else
                    $HotelID = $Data['dayused_id'];

            }
        }

        if ($Data['type'] == 'hotel_vouchers') {
            foreach ($QuotationModel->getHotelVouchers($quotation_no, $LastQuotationID, $OldQuotationID, false, $VoucherType, $HotelID, "hotel_vouchers", $Data['send_individual']??null) as $Index => $VoucherArray) {

                foreach ($VoucherArray as $HotelID => $VoucherHtml) {

                    echo $VoucherHtml;

                }
            }
        } elseif ($Data['type'] == 'day_use_vouchers') {
            foreach ($QuotationModel->getHotelVouchers($quotation_no, $LastQuotationID, $OldQuotationID, false, $VoucherType, $HotelID, 'day_use_vouchers') as $Index => $VoucherArray) {

                foreach ($VoucherArray as $HotelID => $VoucherHtml) {

                    echo $VoucherHtml;

                }
            }
        } elseif ($Data['type'] == 'invoice') {

            $QuotationArray = Quotation::getQuotation($LastQuotationID->ID, $quotation_no);
            return view("quotation.invoice", ['QuotationArray' => $QuotationArray, 'requested_currency' => "USD"]);

        } elseif ($Data['type'] == 'tour_confirmation'  || $Data['type'] == 'quote') {

            $QuotationArray = Quotation::getQuotation($LastQuotationID->ID, $quotation_no);
            $QuotationHTML = View::make('quotation.voucher.tour-confirmation-voucher', ['QuotationArray' => $QuotationArray, 'Email' => true])->render();

            return $QuotationHTML;
        } elseif ($Data['type'] == 'tour_confirmation_voucher') {

            $QuotationArray = Quotation::getQuotation($LastQuotationID->ID, $quotation_no);
            $QuotationHTML = View::make('quotation.voucher.tour-confirmation-voucher', ['QuotationArray' => $QuotationArray, 'Email' => true, 'Client' => true])->render();
            return $QuotationHTML;
        } else  if($Data['type'] == 'hb_hotel_vouchers') {
            $inputData = $Data;
            $QuotationData = Quotation::where('quotation_no', $Data['quotation_no'])->orderBy('ID', 'desc')->first();
            $referenceID = $QuotationData->ID;
            $QuotationHotelbedsVouchersObj = QuotationHotelbedsVouchers::where('reference_id',$referenceID)->get();
            $voucher = "";
           if($QuotationHotelbedsVouchersObj) {

               foreach ($QuotationHotelbedsVouchersObj as $voucherKey => $voucherData) {
                $Data = json_decode($voucherData->json,true);
                if(isset($inputData['hb_hv_send_individual']) && isset($inputData['hotelBeds_hotel_id'])){
                    $hotelId = $Data['hotel']['code'];
                     if(!(in_array($hotelId,$inputData['hotelBeds_hotel_id']))) {
                         continue;
                     }
                }
                   if (env("APP_DEBUG"))
                       $CSS = file_get_contents(public_path('assets/css/apple.css'));
                   else
                       $CSS = file_get_contents(public_path('assets/css/apple.min.css'));

                   $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];

                   $quotation_no = $QuotationData->quotation_no;

                   $voucher .= \View::make("email.body-start")->render();
                   $voucher .= \View::make("element.hotel.hotelbed.hotel-booking-voucher", ['Data' => $Data, 'Comment' => ""])->render();
                   $voucher .= \View::make("email.body-end")->render();
                   $voucher.= "<div style='border: 1px #1d2124 solid;'></div>";

                   if (!($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {
                       $proforma = \View::make("email.body-start")->render();
                       $proforma.= \View::make("element.hotel.hotelbed.proforma-invoice", ['Data' => $Data, 'Comment' => ""])->render();
                       $proforma.= \View::make("email.body-end")->render();
                       $proforma.= "<div style='border: 1px #1d2124 solid;'></div>";
                       $voucher .= $proforma;
                   }

               }
               echo $voucher;
           }

        }

    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\Response|string
     */
    function resendEmails(Request $request)
    {


        $Validator = Validator::make($request->all(), [
            'quotation_no' => 'required|exists:apple_quotation,ID',
            'type' => 'required',
            'to_emails' => 'required'
        ]);


        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()], 500);
        }
        $type = $request->type;
        $EmailStatus = true;
        $EmailList['to'] = [];
        $EmailList['cc'] = [];
        $EmailList['bcc'] = [];

        $EmailList['cc'][] = "<EMAIL>";
        $EmailList['cc'][] = "<EMAIL>";
        $EmailList['cc'][] = "<EMAIL>";

        if($type === "hb_hotel_vouchers") {
            $Data = $request->all();
            $inputData = $Data;
            $QuotationData = Quotation::where('quotation_no', $Data['quotation_no'])->orderBy('ID', 'desc')->first();
            $referenceID = $QuotationData->ID;
            $QuotationHotelbedsVouchersObj = QuotationHotelbedsVouchers::where('reference_id',$referenceID)->get();

            if($QuotationHotelbedsVouchersObj) {

                  foreach ($QuotationHotelbedsVouchersObj as $voucherKey => $voucherData) {
                    $Data = json_decode($voucherData->json,true);
                    if(isset($inputData['hb_hv_send_individual']) && isset($inputData['hotelBeds_hotel_id'])){
                        $Validator = Validator::make($request->all(), [
                            'hotelBeds_hotel_id' => 'required|array'
                        ]);
                        if ($Validator->fails()) {
                            $messages = $Validator->messages();
                            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()], 500);
                        }
                        $hotelId = $Data['hotel']['code'];
                        if(!(in_array($hotelId,$inputData['hotelBeds_hotel_id']))) {
                            continue;
                        }
                    }
                    if (env("APP_DEBUG"))
                        $CSS = file_get_contents(public_path('assets/css/apple.css'));
                    else
                        $CSS = file_get_contents(public_path('assets/css/apple.min.css'));

                    $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];

                    $quotation_no = $QuotationData->quotation_no;
                    $voucher = "";
                    $voucher .= \View::make("email.body-start")->render();
                    $voucher .= \View::make("element.hotel.hotelbed.hotel-booking-voucher", ['Data' => $Data, 'Comment' => ""])->render();
                    $voucher .= \View::make("email.body-end")->render();

                    $subject = "Hotel Voucher : ".$Data['hotel']['name']."#".$quotation_no;

                    if (isset($inputData['to_emails'])) {
                        $EmailList['to'] = explode(",", $inputData['to_emails']);
                    }

                    if (isset($inputData['cc_emails'])) {
                        $EmailList['cc'] = explode(",", $inputData['cc_emails']);
                    }

                    if (isset($inputData['bcc_emails'])) {
                        $EmailList['bcc'] = explode(",", $inputData['bcc_emails']);
                    }

                    Mail::to($EmailList['to'])
                        ->cc($EmailList['cc'])
                        ->bcc($EmailList['bcc'])
                        ->send(new HotelbedsInvoice($voucher, $subject, $CSS));


                    if (!($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {
                        $proforma = \View::make("email.body-start")->render();
                        $proforma.= \View::make("element.hotel.hotelbed.proforma-invoice", ['Data' => $Data, 'Comment' => ""])->render();
                        $proforma.= \View::make("email.body-end")->render();


                        $subject = "Proforma Invoice : ".$Data['hotel']['name']."#".$quotation_no;
                        Mail::to($EmailList['to'])
                            ->cc($EmailList['cc'])
                            ->bcc($EmailList['bcc'])
                            ->send(new HotelbedsInvoice($proforma, $subject, $CSS));

                    }

                }

            }


        } else {
            $EmailStatus = Event::fire(new QuotationEmail($request->all()));
        }


        if ($EmailStatus)
            return View::make("system.success.success-bootstrap", ["Message" => "Email has been sent!"])->render();
        else
            return View::make("system.error.error-bootstrap", ["Message" => "Something wend wrong!"])->render();


    }

    /**
     *
     */
    function resetItinerary()
    {
        Session::forget('quotation.itinerary');
    }

    public function getAvailableCountries(){
        $avilableCountryObj = AvailableCountry::orderby('id','ASC')->first();

        return View::make("quotation.avialable_country_list", compact('avilableCountryObj'))->render();
    }

    public function getRouteTypeInfo(){
        $QuotationArray = Session::get('quotation');
        $Vehicle = new Vehicle();
        $Place = new Place();
        if(isset($QuotationArray['place_full']) &&  isset($QuotationArray['place_type']) && $QuotationArray['country'] != 189) {
            $PathPair = $Place->getPathPair($QuotationArray['place_full'], $QuotationArray['place_type']);
            $transportType = 2;
            $transportTypes = array();
            foreach ($PathPair as $index => $PathPairItem) {
                $Distance = Distance::where("from", $PathPairItem['from'])->where('to', $PathPairItem['to'])->first();
                if ($Distance) {
                    $DistanceID = $Distance->ID;
                    $VehicleType = $Vehicle->getPaxToVehicle($QuotationArray);
                    if (!empty($VehicleType)) {
                        $VehicleID = $VehicleType->ID;
                    } else {
                        $VehicleID = '-';
                    }
                    $transportType = TransportCost::where("distance_id", $DistanceID)
                            ->where('vehicle_type', $VehicleID)
                            ->first()->type ?? 2;
                    $transportTypes[$index]['from'] = $PathPairItem['from'];
                    $transportTypes[$index]['to'] = $PathPairItem['to'];
                    $transportTypes[$index]['transport_type'] = $transportType;
                } else {
                    $transportTypes[$index]['from'] = $PathPairItem['from'];
                    $transportTypes[$index]['to'] = $PathPairItem['to'];
                    $transportTypes[$index]['transport_type'] = 2;
                }
            }

            return View::make("quotation.RouteTypeInfo", compact('transportTypes'))->render();
        } else {
            return [];
        }
    }


}
