<?php

namespace App\Http\Controllers\Quotation;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Model\Hotel\Hotel;
use Session;
use App\Model\StaticPackages\StaticPackages as StaticPackagesModel;
/**
 * Class StaticPackage
 * @package App\Http\Controllers\Quotation
 */
class StaticPackage extends Controller
{

    /**
     * @return array
     */
    function getPackages() {

        $Quotation = Session::get('quotation');


        if($Quotation['pax'] && empty($Quotation['place']) && empty($Quotation['hotel'])){  // get packages according to pax

            $packages  = StaticPackagesModel::where('is_active',1)->where('country',$Quotation['country'])->limit(3)->get();
            return $packages;

        }elseif($Quotation['pax'] && $Quotation['place'] && empty($Quotation['hotel'])){   // get packages according to place

                $packages       = StaticPackagesModel::whereHas('place', function($query) {
                $Quotation      = Session::get('quotation');

                if(isset($Quotation['place'])){
                    $places     = implode(',',$Quotation['place']);
                    $placeIDs   = array_map('intval', explode(',', $places));
                } else {
                    $placeIDs   = NULL;
                }$query->whereIn('place',$placeIDs); })->where('is_active',1)->where('country',$Quotation['country'])->limit(3)->get();

            return $packages;

        } elseif($Quotation['pax'] &&  $Quotation['hotel'] && $Quotation['place']){ // get packages according to hotels

                $packages       = StaticPackagesModel::whereHas('place', function($query) {
                $Quotation      = Session::get('quotation');

                if(isset($Quotation['place'])){
                    $places     = implode(',',$Quotation['place']);
                    $placeIDs   = array_map('intval', explode(',', $places));
                } else {
                    $placeIDs   = NULL;
                }$query->whereIn('place',$placeIDs); })->where('is_active',1)->where('country',$Quotation['country'])->limit(3)->get();

                $packagesHotels  = StaticPackagesModel::whereHas('hotel', function($query) {
                $Quotation       = Session::get('quotation');
                $hotelsClasses   = [];


                foreach($Quotation['hotel'] as $key => $hotel){
                    if(isset($hotel['hotel']) && $hotel['hotel'] != 0) {
                        $class = $this->getHotelClasses($hotel['hotel']);

                        array_push( $hotelsClasses,$class[0]['class']);
                    }
                }

                $query->whereIn('hotel_class_id',$hotelsClasses); })->where('is_active',1)->where('country',$Quotation['country'])->limit(3)->get();

            $packages     = array_merge($packages->toArray(),$packagesHotels->toArray());
            $all_packages = json_decode(json_encode($packages), true);

            return $all_packages;
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */


    function template(){

        return view("static-packages.package");
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function templateFull($id){

        $Package = StaticPackagesModel::find($id);
        return view("static-packages.package-full",['Package'=>$Package]);
    }


    function getHotelClasses($id){

        $class = Hotel::where('ID', $id)->get(['class'])->toArray();

        return $class;
    }

}