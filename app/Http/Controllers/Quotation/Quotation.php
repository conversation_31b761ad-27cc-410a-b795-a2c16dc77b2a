<?php

namespace App\Http\Controllers\Quotation;

use App\Currency;
use App\Http\Controllers\Controller;
use App\Model\Cruise\CruiseCabinRate;
use App\Model\Hotel\Availability;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelClass;
use App\Model\Hotel\Rates;
use App\Model\Itinerary\Itinerary;
use App\Model\Meal\Meal;
use App\Model\Place\Place;
use App\Model\Quotation\Quotation as QuotationModel;
use App\Model\Quotation\QuotationCruise;
use App\Model\Quotation\QuotationHotel;
use App\Model\Quotation\QuotationTask;
use App\Model\QuotationManage\Quotation as QuotationManage;
use App\Model\QuotationManage\QuotationTransport;
use App\Model\Transport\Transport;
use App\User;
use App\UserHierarchy;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Session;
use View;
use App\Model\Image\Image;
use Dompdf\Dompdf;
use Dompdf\Options;

use App\Model\FdPackages\apple_fd_packages_quotation;

use App\Model\Hotel\apple_hotelbeds_destination_market as nationality;

/**
 * Class Quotation
 * @package App\Http\Controllers\Quotation
 */
class Quotation extends Controller
{

    /**
     * @var array
     */
    protected $RateType = ['transport' => 'transport_rate', 'hotel' => 'hotel_rate', 'cruise' => 'cruise_rate', 'attraction' => 'attraction_rate', 'meal' => 'meal_rate', 'other' => 'other_rate', 'transport_time' => 'transport_time', 'attraction_time' => 'attraction_time'];

    /**
     * @param $Type
     * @param bool $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    function getTemplate($Type, $id = false)
    {
        $data = request();

        switch ($Type) {

            case "quote":

                if (isset($data['reference_id']) && isset($data['quotation_no'])) {

                    $validator = \Validator::make(['reference_id' => $data['reference_id'], 'quotation_no' => $data['quotation_no']], [
                        'quotation_no' => 'required|exists:apple_quotation,ID'
                    ]);

                    if ($validator->fails())
                        return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);

                    $QuotationArray = QuotationManage::getQuotation($data['reference_id'], $data['quotation_no']);

                    if ($QuotationArray) {
                        $QuotationArray = QuotationManage::getQuotation($data['reference_id'], $data['quotation_no']);

                        return view("quotation.qoute", ['QuotationArray' => $QuotationArray]);

                    } else
                        return response()->view(" system.error.error-ajax", ["Message" => "Something Wrong with this quotation!"], 422);


                } else
                    return view("quotation.qoute", ['QuotationArray' => Session::get('quotation')]);


            case "pnl":

                if (isset($data['reference_id']) && isset($data['quotation_no'])) {
                    $QuotationArray = QuotationManage::getQuotation($data['reference_id'], $data['quotation_no']);
                    return view("quotation.lost-profit", ['QuotationArray' => $QuotationArray, 'requested_currency' => $data['currency']]);
                } else
                    return view("quotation.lost-profit", ['QuotationArray' => Session::get('quotation'), 'requested_currency' => $data['currency']]);

            case "cost":

                if (isset($data['reference_id']) && isset($data['quotation_no'])) {
                    $QuotationArray = QuotationManage::getQuotation($data['reference_id'], $data['quotation_no']);
                    return view("quotation.cost-sheet", ['QuotationArray' => $QuotationArray, 'requested_currency' => $data['currency']]);
                } else
                    return view("quotation.cost-sheet", ['QuotationArray' => Session::get('quotation'), 'requested_currency' => $data['currency']]);
            
            case "day-vice-itinerary":

                if (isset($data['reference_id']) && isset($data['quotation_no'])) {
                    $QuotationArray = QuotationManage::getQuotation($data['reference_id'], $data['quotation_no']);
                    if(isset($data['type']) && $data['type'] == 'pdf') {
                        $HTML = View::make("quotation.day-vice-itinerary", ['QuotationArray' => $QuotationArray])->render();
                        $CSS = file_get_contents(public_path('assets/css/apple.css'));

                        $html = View::make('quotation.email.main', ['QuotationHTML' => $HTML, "CSS" => $CSS])->render();

                        $dompdf = new Dompdf();
                        $dompdf->loadHtml($html);

                        $options = new Options();
                        $options->setIsRemoteEnabled(true);

                        $dompdf->setOptions($options);

                        $dompdf->render();
                        $dompdf->setPaper('A4', 'landscape');
                        $dompdf->stream();
                    }
                    return view("quotation.day-vice-itinerary", ['QuotationArray' => $QuotationArray, 'requested_currency' => $data['currency']]);
                } else
                    return view("quotation.day-vice-itinerary", ['QuotationArray' => Session::get('quotation'), 'requested_currency' => $data['currency']]);

            case "hotels":

                return view('tour.hotel', ["Attributes" => ['QuotationHotel' => true]]);

            case "attraction":

                return view('tour.attraction', ['Quote' => true]);

            case "meal":

                $QuotationArray = Session::get('quotation');
                $MealRates = Meal::getMeal($QuotationArray);
                return view('tour.meal', ["MealRates" => $MealRates, 'Quote' => true]);

            case "transport":

                $QuotationArray = Session::get('quotation');

                $TransportRates = Transport::getTransport($QuotationArray);

                $Guide = [];
                if (isset($QuotationArray['guide'])) {
                    $Guide = $QuotationArray['guide'];
                }
                return view('quotation.transport.transport', ['TransportRates' => $TransportRates, 'Guide' => $Guide]);

            case "nights":

                return view('tour.transport-night', ["Attributes" => ['Quote' => true]]);

            case "other":

                $Itinerary = new Itinerary();

                $OtherRate = Session::get('quotation.other_rate');
                $QuotationArray = Session::get('quotation');

                $DaysDetail = QuotationModel::getTourDaysDetail($QuotationArray);
                $DayList = $Itinerary->getDayList($DaysDetail);

                return view("quotation.custom", ["Data" => $OtherRate, "DaysDetail" => $DayList]);
            
            case "time":

                $QuotationArray = Session::get('quotation');
                $MealRates = Meal::getMeal($QuotationArray);
                return view('tour.time', ["MealRates" => $MealRates, 'Quote' => true]);

            case "flow":

                $QuotationArray = Session::get('quotation');
                $MealRates = Meal::getMeal($QuotationArray);
                return view('tour.flow', ["MealRates" => $MealRates, 'Quote' => true]);

            default:

                return "";
        }
    }


    /**
     * @return array|mixed
     */
    function getQuotation()
    {
        $QuotationData = Session::get('quotation');
        unset($QuotationData['rate']);

        return Session::get('quotation') ?? [];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection|static[]
     */
    function getQuotations(Request $request)
    {
        $Data = $request->all();
        $UserID = empty($Data['user']) ? Auth::user()->id : $Data['user'];



        $Quotes = QuotationManage::with('main')->whereHas('Main', function ($Main) use ($UserID, $Data) {

            //Arrival date
            if (!empty($Data['arrival_date'])) {

                $ArrivalDate = new Carbon($Data['arrival_date']);
                $Main->where('arrival_year', $ArrivalDate->year);
                $Main->where('arrival_month', $ArrivalDate->month);
                $Main->where('arrival_day', $ArrivalDate->day);
            }

        });

        //quotation number
        if (!empty($Data['id'])) {
            $Quotes->where('quotation_no', 'LIKE', $Data['id']);
        }

        $Quotes->limit(10);


        $Quotes->orderByDesc('updated_at');

        return $Quotes->get();

    }

    function getQuotationsForApi(Request $request)
    {
        $hotelColor = [0=>"Green", 1=>"Yellow", 2=>"Red"];
        $image = new Image();
        $Data = $request->all();
        $ExactQuot = false;

        // $user = Auth::user($Data['user']);
        $user =  User::where('id',$Data['user']??1)->first();

        $UserIDs = (new UserHierarchy())->getHierarchy($user);

        if (!empty($Data['quotation_no'])) {
            $pos = strpos($Data['quotation_no'], '$');
            if ($pos === 0)
                $ExactQuot = true;
        }

        if (!$ExactQuot) {

            $Quotes = QuotationManage::withTrashed()->with('main', 'Pax', 'Place', 'Hotel', 'Hotel.room', 'Hotel.Rate','Hotel.Rate.child' , 'Attraction', 'Attraction.Rate', 'CityTours', 'CityTours.Rate',
                                                'Excursion', 'Excursion.Rate',  'Meal', 'Meal.Rate', 'OtherRate', 'OtherRate.sightseeing', 'DayUseHotel', 'Itinerary', 'ItineraryAttraction', 'ItineraryExcursion', 'ItineraryCityTour', 'Status', 'Confirm', 'Confirm.Hotel', 'Confirm.DayUsedHotel')
                ->whereHas('Main', function ($Main) use ($UserIDs, $Data, $ExactQuot) {

                //user or users
                if (is_array($UserIDs))
                    $Main->whereIn('user', $UserIDs);
                else
                    $Main->where('user', $UserIDs);

                //Arrival date
                if (!empty($Data['arrival_date'])) {
                    $ArrivalDate = new Carbon($Data['arrival_date']);
                    $Main->where('arrival_year', $ArrivalDate->year);
                    $Main->where('arrival_month', $ArrivalDate->month);
                    $Main->where('arrival_day', $ArrivalDate->day);
                }
            });


            //quotation status
            if (!empty($Data['status']) && !$ExactQuot) {
                $Quotes->whereIn('status', $Data['status']);
            }


            //quotation number
            if (!empty($Data['quotation_no'])) {
                $Quotes->where('quotation_no', $Data['quotation_no']);
            }

            //quotation number
            if (!empty($Data['reference_id'])) {
                $Quotes->where('ID', $Data['reference_id']);
            }


            $Quotes->limit(100);

        }
        else {
            $Quotes = QuotationManage::withTrashed()->where('quotation_no', str_replace("$", "", $Data['id']))->whereHas('Main', function ($Main) use ($UserID, $Data, $ExactQuot) {
                $Main->whereIn('user', $UserID);
            });
        }

        $Quotes->orderByDesc('created_at');

        $Quotes = $Quotes->get()->toArray();

        $Quotation = new \App\Model\Quotation\Quotation();
        $data = [];
        $QuotationCount=[];

        foreach ($Quotes as $q){
            if(!in_array($q['quotation_no'],$QuotationCount)) {
                $QuotationCount[] = $q['quotation_no'];
            }

            if(count($QuotationCount) > 10) {
                break;
            }
        }

        foreach ($Quotes as $q){
            if(in_array($q['quotation_no'],$QuotationCount)) {
                $q['update_number'] = QuotationManage::withTrashed()->where('quotation_no', $q['quotation_no'])->where('ID', '<=', $q['ID'])->count();
                $q['reference'] = QuotationManage::withTrashed()->where('quotation_no', $q['quotation_no'])->get()->toArray();

                $hotelAllArr=[];
                $hotelCommanArr=[];
                $hotelsCommanArr=[];
                $market = $q['pax']["market"];
                if(isset($q['hotel']) && !empty($q['hotel'])) {
                    foreach ($q['hotel'] as $index => $hotel) {
                        $hotelArr["hotel"] = $hotel["hotel"];
                        $hotelArr["room_category"] = $hotel["room_category"];
                        $hotelArr["meal_type"] = $hotel["meal"];
                        $hotelArr["check_in"]["year"] = $hotel["check_in_year"];
                        $hotelArr["check_in"]["month"] = $hotel["check_in_month"];
                        $hotelArr["check_in"]["day"] = $hotel["check_in_day"];
                        $hotelArr["check_out"]["year"] = $hotel["check_out_year"];
                        $hotelArr["check_out"]["month"] = $hotel["check_out_month"];
                        $hotelArr["check_out"]["day"] = $hotel["check_out_day"];
                        $hotelArr["room_type"] = array_column($hotel["room"], 'room_type');
                        $hotelArr["provider"] = "local";

                        $hotelAllArr["hotel"][] = $hotelArr;

                    }
                    $hotelAllArr["market"] = $market;
                    $Rates = QuotationHotel::getAPIHotelCost($hotelAllArr);
                    $q['hotel_default_rates'] = $Rates;

                    foreach ($hotelAllArr["hotel"] as $index => $hotelArr) {
                        $hotelCommanArr['hotel_availability_status'] = Availability::find((Hotel::getStatus($hotelArr['hotel'], $hotelArr['check_in'], $hotelArr['room_type'], $hotelArr['meal_type'], $hotelArr['room_category'], $market)[0]))->status;

                        $availabilityCount = Hotel::getStatus($hotelArr['hotel'], $hotelArr['check_in']);
                        $hotelCommanArr['hotel_availability_count'] = $availabilityCount[1] ?? 0;
                        $hotelCommanArr['hotel_image'] = $image->getImage($hotelArr['hotel'],'3x',"hotel",1,Hotel::find($hotelArr['hotel'])->name ?? "")[0] or "";
                        $hotelCommanArr['hotel_image_large'] = $image->getImage($hotelArr['hotel'],'300',"hotel",1,Hotel::find($hotelArr['hotel'])->name ?? "")[0] or "";


                        if(isset($availabilityCount[0]) && $availabilityCount[0] == 1 && isset($availabilityCount[1]) &&  $availabilityCount[1] > 0 && isset($Rates[$index])) {
                            $hotelCommanArr['hotel_color'] = $hotelColor[0];
                        } else if(isset($availabilityCount[0]) && $availabilityCount[0] == 1 && isset($availabilityCount[1]) && $availabilityCount[1] <= 0 && isset($Rates[$index])) {
                            $hotelCommanArr['hotel_color'] = $hotelColor[1];
                        } else if(isset($availabilityCount[0]) && $availabilityCount[0] == 2) {
                            $hotelCommanArr['hotel_color'] = $hotelColor[1];
                        } else {
                            $hotelCommanArr['hotel_color'] = $hotelColor[2];
                        }

                        $hotelsCommanArr[] = $hotelCommanArr;
                    }

                    $q['hotel_common_data'] = $hotelsCommanArr;
                }

                $dayUsedArray = array();
                $i = 0;
                if(isset($q['other_rate']) && !empty($q['other_rate'])) {
                    foreach ($q['other_rate'] as $index => $other) {
                        if($other["type"] == 3) {
                            $q["confirm"]["day_used_hotel"][$index]["hotel_id"] = $q["day_use_hotel"][$i]["hotel_id"];
                            $i++;
                        } else {
                            unset($q["confirm"]["day_used_hotel"][$index]);
                        }
                    }
                }

                if(isset($Data['id'])) {
                    $QuotationArray = \App\Model\QuotationManage\Quotation::getQuotation($Data["reference_id"], $Data["id"]);
                    $CostBreak = $Quotation->getCost($QuotationArray);
                    $q['cost_breakdown'] = $CostBreak;
                }

                // Add place names to place data
                if(isset($q['place']) && is_array($q['place'])) {
                    foreach ($q['place'] as $index => $placeData) {
                        if(isset($placeData['place'])) {
                            $placeDetails = \App\Model\Place\Place::getPlaceBYID($placeData['place']);
                            if($placeDetails) {
                                $q['place'][$index]['place_name'] = $placeDetails->name;
                            } else {
                                $q['place'][$index]['place_name'] = '';
                            }
                        }
                    }
                }

                // Add hotel names and related data to hotel data
                if(isset($q['hotel']) && is_array($q['hotel'])) {
                    foreach ($q['hotel'] as $index => $hotelData) {
                        // Add hotel name
                        if(isset($hotelData['hotel']) && !empty($hotelData['hotel'])) {
                            $hotel = \App\Model\Hotel\Hotel::find($hotelData['hotel']);
                            $q['hotel'][$index]['hotel_name'] = $hotel ? $hotel->name : '';
                        } else {
                            $q['hotel'][$index]['hotel_name'] = '';
                        }

                        // Add room category name
                        if(isset($hotelData['room_category']) && !empty($hotelData['room_category'])) {
                            $roomCategory = \App\Model\Hotel\RoomCategory::find($hotelData['room_category']);
                            $q['hotel'][$index]['room_category_name'] = $roomCategory ? $roomCategory->name : '';
                        } else {
                            $q['hotel'][$index]['room_category_name'] = '';
                        }

                        // Add meal type name
                        if(isset($hotelData['meal']) && !empty($hotelData['meal'])) {
                            $mealType = \App\Model\Hotel\Meal::find($hotelData['meal']);
                            $q['hotel'][$index]['meal_name'] = $mealType ? $mealType->type : '';
                        } else {
                            $q['hotel'][$index]['meal_name'] = '';
                        }

                        // Add room type names to room data
                        if(isset($hotelData['room']) && is_array($hotelData['room'])) {
                            foreach ($hotelData['room'] as $roomIndex => $roomData) {
                                if(isset($roomData['room_type']) && !empty($roomData['room_type'])) {
                                    $roomType = \App\Model\Hotel\RoomType::find($roomData['room_type']);
                                    $q['hotel'][$index]['room'][$roomIndex]['room_type_name'] = $roomType ? $roomType->type : '';
                                } else {
                                    $q['hotel'][$index]['room'][$roomIndex]['room_type_name'] = '';
                                }
                            }
                        }
                    }
                }

                // Add attraction names to attraction data
                if(isset($q['attraction']) && is_array($q['attraction'])) {
                    foreach ($q['attraction'] as $index => $attractionData) {
                        if(isset($attractionData['attraction']) && !empty($attractionData['attraction'])) {
                            $attraction = \App\Model\Place\Attraction::find($attractionData['attraction']);
                            $q['attraction'][$index]['attraction_name'] = $attraction ? $attraction->name : '';
                        } else {
                            $q['attraction'][$index]['attraction_name'] = '';
                        }
                    }
                }

                // Add city tour names to city tour data
                if(isset($q['city_tours']) && is_array($q['city_tours'])) {
                    foreach ($q['city_tours'] as $index => $cityTourData) {
                        if(isset($cityTourData['city_tour']) && !empty($cityTourData['city_tour'])) {
                            $cityTour = \App\Model\Place\CityTour::find($cityTourData['city_tour']);
                            $q['city_tours'][$index]['city_tour_name'] = $cityTour ? $cityTour->name : '';
                        } else {
                            $q['city_tours'][$index]['city_tour_name'] = '';
                        }
                    }
                }

                // Add excursion names to excursion data
                if(isset($q['excursion']) && is_array($q['excursion'])) {
                    foreach ($q['excursion'] as $index => $excursionData) {
                        if(isset($excursionData['excursion']) && !empty($excursionData['excursion'])) {
                            $excursion = \App\Model\Place\Excursion::find($excursionData['excursion']);
                            $q['excursion'][$index]['excursion_name'] = $excursion ? $excursion->name : '';
                        } else {
                            $q['excursion'][$index]['excursion_name'] = '';
                        }
                    }
                }

                array_push($data, $q);
            }
        }

        return replaceNullWithEmptyString($data);
    }


    /**
     * @param bool $reference_id
     * @param bool $quotation_no
     * @param string $requested_currency
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getInvoice($reference_id = false, $quotation_no = false, $requested_currency = 'USD')
    {

        if (!empty($reference_id)) {
            $QuotationArray = QuotationManage::getQuotation($reference_id, $quotation_no);
            return view("quotation.invoice", ['QuotationArray' => $QuotationArray, 'requested_currency' => $requested_currency]);
        } else
            return view("quotation.invoice", ['QuotationArray' => Session::get('quotation'), 'requested_currency' => $requested_currency]);

    }

    /**
     * @param $reference_id
     * @param $quotation_no
     * @param $update_number
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getSaveRespond($reference_id, $quotation_no, $update_number)
    {
        return view("quotation.respond.save", ['reference_id' => $reference_id, 'quotation_no' => $quotation_no, 'update_number' => $update_number]);
    }


    /**
     * @param $HotelItemIndex
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \ReflectionException
     */
    function getQuotationHotelRate($HotelItemIndex)
    {
        $QuotationHotel = new QuotationHotel();
        $Rates = new Rates();
        parse_str(request()->all()["params"], $HotelSettings);
        $showDriverAcc = true;

        // $HotelSettings = arrayMapMulti('getActualDataType',$output);

        if (Session::has("quotation.rate.hotel.$HotelItemIndex")) {


            $RateArray = objectToArray(Session::get("quotation.rate.hotel.$HotelItemIndex"));
            
            if (!$Rates->searchInRateArrayHotelSettings(objectToArray($RateArray), $HotelSettings)) {
                $RateArray = objectToArray($QuotationHotel->getHotelCost(Session::get("quotation"), $HotelSettings));
            }
            
            if(!isset($RateArray['driver_accommodation'])) {
                $RateArray['driver_accommodation'] = objectToArray($QuotationHotel->setAccRates(Session::get("quotation"), $HotelSettings));
            }
        } else {
            $RateArray = objectToArray($QuotationHotel->getHotelCost(Session::get("quotation"), $HotelSettings));

        }
        $showDriverAcc = Session::has("quotation.hotel.$HotelItemIndex.driver_accommodation") ? (Session::get("quotation.hotel.$HotelItemIndex.driver_accommodation") == 1) ? true : false : true; 

        return view("element.hotel.hotel-rate", ['RateArray' => $RateArray, "HotelItemIndex" => $HotelItemIndex, "HotelSettings" => $HotelSettings, "showDriverAcc" => $showDriverAcc]);
    }

    /**
     * @param $HotelItemIndex
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \ReflectionException
     */
    function getQuotationCruiseRate($HotelItemIndex)
    {
        $QuotationHotel = new QuotationCruise();
        $Rates = new CruiseCabinRate();

        parse_str(request()->all()["params"], $HotelSettings);
        // $HotelSettings = arrayMapMulti('getActualDataType',request()->all());

        if (Session::has("quotation.rate.cruise.$HotelItemIndex")) {

            $RateArray = objectToArray(Session::get("quotation.rate.cruise.$HotelItemIndex"));

            if (!$Rates->searchInRateArrayCruiseSettings(objectToArray($RateArray), $HotelSettings)) {
                $RateArray = objectToArray($QuotationHotel->getCruiseCost(Session::get("quotation"), $HotelSettings));
            }
        } else {
            $RateArray = objectToArray($QuotationHotel->getCruiseCost(Session::get("quotation"), $HotelSettings));
        }

        return view("element.cruise.cruise-rate", ['RateArray' => $RateArray, "HotelItemIndex" => $HotelItemIndex, "HotelSettings" => $HotelSettings]);
    }


    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws \ReflectionException
     */
    function getRate()
    {

        $QuotationModel = new QuotationModel();


        $Rates = $QuotationModel->getCost(Session::get("quotation"));
        $Rates['return'] = "none";


        return response()->json($Rates);
    }

    /**
     * @param Request $request
     * @param $Type
     * @return bool|\Illuminate\Http\JsonResponse|string
     * @throws \Exception
     * @throws \ReflectionException
     */
    public function setRate(Request $request, $Type)
    {
        $QuotationTask = new QuotationTask();
        $QuotationModel = new QuotationModel();

        $ChangeRate = arrayMapMulti('getActualDataType', $request->all());

        $Return = [];

        if (\Entrust::can('change_rates')) {//permission
            if (!isset($this->RateType[$Type]))
                return false;

            if (!$Return = $QuotationTask->setSession($this->RateType[$Type], $ChangeRate))//set to session
                return '';

        }
        
        $HotelRate = $QuotationModel->getCost(Session::get("quotation"));
        $HotelRate['return'] = $Return;
        return jsona($HotelRate);
    }


    /**
     * @param $Index
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getCustomField($Index)
    {
        $Quotation = new QuotationModel();
        $Itinerary = new Itinerary();

        $QuotationArray = Session::get('quotation');

        $DaysDetail = $Quotation->getTourDaysDetail($QuotationArray);
        $DayList = $Itinerary->getDayList($DaysDetail);

        return view("quotation.custom_field", ["Index" => $Index, "DaysDetail" => $DayList]);

    }


    /**
     *
     */
    function makeNew()
    {
        Session::forget('quotation');

        Session::forget('pnr_data');
        Session::forget('pnr_ref_id');
        Session::forget('flight_data');
    }

    /**
     * @param Request $request
     * @return array
     */
    function getQuotationList(Request $request)
    {

        $Data = $request->input();
        $ExactQuot = false;

        //hirachi
        $UserID = (new UserHierarchy())->getHierarchy(Auth::user());
        $AuthId = Auth::id();

        if (!empty($Data['id'])) {
            $pos = strpos($Data['id'], '$');
            if ($pos === 0)
                $ExactQuot = true;
        }

        if (!$ExactQuot) {

            $Quotes = QuotationManage::whereHas('Main', function ($Main) use ($AuthId, $UserID, $Data, $ExactQuot) {


                //user or users
                if($AuthId != 1) {
                    if (is_array($UserID))
                        $Main->whereIn('user', $UserID);
                    else
                        $Main->where('user', $UserID);
                    
                }

                //Arrival date
                if (!empty($Data['arrival_date'])) {

                    $ArrivalDate = new Carbon($Data['arrival_date']);
                    $Main->where('arrival_year', $ArrivalDate->year);
                    $Main->where('arrival_month', $ArrivalDate->month);
                    $Main->where('arrival_day', $ArrivalDate->day);
                }


            });
            // FD Quotes
            $fdQuotes = apple_fd_packages_quotation::where('created_at','<>','');
            if (is_array($UserID)){
                $fdQuotes->whereIn('user_id', $UserID);
            } else {
                $fdQuotes->where('user_id', $UserID);
            }

            if (!empty($Data['arrival_date'])) {
                $fdQuotes->where('arrival_date', $Data['arrival_date']);

            }


            //quotation status
            if (!empty($Data['status']) && !$ExactQuot) {
                $Quotes->whereIn('status', $Data['status']);
                $fdQuotes->whereIn('status', $Data['status']);

            }


            //quotation number
            if (!empty($Data['id'])) {
                $Quotes->where('quotation_no', $Data['id']);
                $fdQuotes->where('quotation_no', $Data['id']);
            }

            //is number
            if (!empty($Data['is_number'])) {
                $Quotes = $Quotes->whereHas('IsNumber', function ($IS) use ($UserID, $Data, $ExactQuot) {
                    $IS->where('is_number', $Data['is_number']);
                });
            }


            $Quotes->limit(16);
            $fdQuotes->limit(10);

        }  else {
            $Quotes = QuotationManage::where('quotation_no', str_replace("$", "", $Data['id']))->whereHas('Main', function ($Main) use ($UserID, $Data, $ExactQuot) {
                $Main->whereIn('user', $UserID);
            });
            $fdQuotes = apple_fd_packages_quotation::where('created_at','<>','')
                ->where('quotation_no',str_replace("$", "", $Data['id']))
                ->whereIn('user_id', $UserID);
        }

        $Quotes->orderByDesc('created_at');
        $fdQuotes->orderByDesc('created_at')
        ->whereRaw('id IN (select MAX(id) FROM apple_fd_packages_quotations GROUP BY quotation_no)');

        $QuotationReturnList = [];


        foreach ($Quotes->get() as $k => $QuotationItem) {


            if ($QuotationItem) {
                $QuotationReturnList[] =  View::make('quotation.item.item', ['Data' => $QuotationItem])->render();
            }
        }
        /*
        $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
        $Image = new Image();
        foreach ($fdQuotes->get() as $key => $fdQuoteItem) {
            if ($fdQuoteItem) {
                if(isset($fdQuoteItem->findFdPackage) && !empty($fdQuoteItem->findFdPackage)) {
                    $image = $Image->getGoogleGenDirectionPathStatic($fdQuoteItem->findFdPackage->findPlaces);
                    $historyItems = apple_fd_packages_quotation::where('quotation_no',$fdQuoteItem->quotation_no)->get();
                    // $QuotationReturnList[] =  View::make('fd_packages.fd_packages_quotation_search',compact('roleId','fdQuoteItem','historyItems','image'))->render(); 
                }
            }
        }
        */

        return $QuotationReturnList;
    }


    /**
     * @param $quotation_no
     * @param $reference_id
     * @return string
     */
    function printFile($quotation_no, $reference_id)
    {
        $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
        $Quot = QuotationManage::withTrashed()->where('quotation_no', $quotation_no)->orderBy('ID', 'desc');

        $LastQuotationID = $Quot->first();
        $OldQuotationID = $Quot->skip(1)->take(1)->first();//get before last one

        $QuotationArray = QuotationManage::getQuotation($reference_id, $quotation_no);

        $request_currency = $QuotationArray["base_currency"] ?? 142;
        $request_currency = Currency::find($request_currency)->code;

        $QuotationModel = new QuotationModel();

        $FinalHTML = "<script>window.print();</script>";


        //tour confirmation
        $FinalHTML .= View::make('quotation.voucher.tour-confirmation-voucher', ['QuotationArray' => $QuotationArray, 'Print' => true])->render();
        $FinalHTML .= '<div style="page-break-after: always;"></div>';


        //tour confirmation
        $FinalHTML .= View::make('quotation.voucher.tour-confirmation-voucher', ['QuotationArray' => $QuotationArray, 'Print' => true, "Client" => true])->render();
        $FinalHTML .= '<div style="page-break-after: always;"></div>';

        //Iternery
        $FinalHTML .= '<div id="quotation" class="clearfix email_format" >';
        $FinalHTML .= View::make('quotation.itinerary.itinerary', ['QuotationArray' => $QuotationArray, 'Print' => true])->render();
        $FinalHTML .= '</div><div style="page-break-after: always;"></div>';


        //PnL
        $FinalHTML .= View::make("quotation.lost-profit", ['QuotationArray' => $QuotationArray, 'Email' => true, 'requested_currency' => $request_currency])->render();
        $FinalHTML .= '<div style="page-break-after: always;"></div>';

        //Hotelbeds
        foreach(QuotationManage::find($reference_id)->HotelBeds()->get() as $HotelbedsItem){

            $Data = json_decode($HotelbedsItem->hotelbedsVouchers->json,true);
            if (!($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {
            $FinalHTML .= View::make("element.hotel.hotelbed.proforma-invoice", ['Data' => $Data, 'Comment' => ""])->render();
            $FinalHTML .= '<div style="page-break-after: always;"></div>';
            }

            $FinalHTML .= View::make("element.hotel.hotelbed.hotel-booking-voucher", ['Data' => $Data, 'Comment' => ""])->render();
            $FinalHTML .= '<div style="page-break-after: always;"></div>';
        }




        //print voucher
        foreach ($QuotationModel->getHotelVouchers($quotation_no, $LastQuotationID, $OldQuotationID, false, 'reservation') as $Index => $VoucherArray) {

            foreach ($VoucherArray as $HotelID => $VoucherHtml) {
                $HotelItem = Hotel::where("ID", $HotelID)->first();

                if ($HotelItem->provider == 1) {//if it's a local hotel
                    $FinalHTML .= $VoucherHtml;
                    $FinalHTML .= '<div style="page-break-after: always;"></div>';
                }
            }
        }


        if (env("APP_DEBUG"))
            $CSS = file_get_contents(public_path('assets/css/apple.css'));
        else
            $CSS = file_get_contents(public_path('assets/css/apple.min.css'));
        $FinalHTML = View::make('quotation.email.main', ['QuotationHTML' => $FinalHTML, "CSS" => $CSS])->render();

        return $FinalHTML;

    }

    /**
     * @param Request $r
     * @return array
     */
    function getCurrentPath(Request $r)
    {
        $LastOrder = [];

        if (Session::has('quotation.place')) {

            $Place = new Place();
            $PlaceIndex = array_flip($Place->getCombinedMarkerPlace($r->get('markers'), $r->get('place')));

            $CurrentPlaces = Session::get('quotation.place');

            foreach ($CurrentPlaces as $PlaceID) {
                $LastOrder[] = $PlaceIndex[$PlaceID];
            }
        }


        return [
            "index" => $LastOrder
        ];

    }

    /**
     * @return int|mixed
     */
    static function getnUpdateCurrency() {
        $Quotation = Session::get('quotation');

        $returnCurrency = [];

        $Currency = 142;
        if(isset($Quotation['country'])) {
            $Currency = \App\Model\Place\Place::find($Quotation['country'])->currency;
            if(isset($Quotation['place'])) {
                $CurrencyList = \App\Model\Quotation\Quotation::getCurrency($Quotation);
                if (count($CurrencyList) > 1)
                    $Currency = 142;
            }

        }

        $baseCurrencyCode = $Currency;
        $baseCurrency = Currency::find($Currency)->code;
        if(Session::has('quotation.ch_currency')) {
            $Currency = Session::get('quotation.ch_currency');
        }

        $returnCurrency["currency"] = $Currency;
        $returnCurrency["base_currency"] = $baseCurrency;

        Session::forget('quotation.currency');
        Session::forget('quotation.ch_currency');
        Session::forget('quotation.base_currency');
        Session::put('quotation.currency', $Currency);
        Session::put('quotation.ch_currency', $Currency);
        Session::put('quotation.base_currency', $baseCurrencyCode);

        return $returnCurrency;
    }

    /**
     * @param Request $request
     */
    static function setCurrency(Request $request) {
        Session::forget('quotation.ch_currency');
        $currency = $request->input("currency");
        Session::put('quotation.ch_currency', $currency);
    }

    public function setMarketByNationality(Request $request){
        $nationality = $request->nationality;
        $market = nationality::where('place_id',$nationality)->first();
        if($market) {
            $market = $market->market_id;
        } else {
            $market = 1 ;
        }
        return $market;
    }
}
