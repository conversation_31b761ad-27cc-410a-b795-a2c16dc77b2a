<?php


namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Auth;
use DB;
use DatePeriod;
use DateTime;
use DateInterval;
use Illuminate\Http\Request;
use Carbon\Carbon;


/**
 * Class FollowUPController
 * @package App\Http\Controllers\Flight
 */
class AllotmentController extends Controller {

    function allotmentSearch(Request $request) {
        /*$allotmentSearch = DB::table('apple_hotel_room_allotment')
            ->leftJoin('apple_hotels', 'apple_hotels.ID', '=', 'apple_hotel_room_allotment.hotel')
            ->leftJoin('apple_hotel_room_allotment_used', 'apple_hotel_room_allotment_used.hotel', '=', 'apple_hotel_room_allotment.hotel')

            ->select(
                'apple_hotel_room_allotment.*',
                'apple_hotels.name AS hotel_name',
                'apple_hotel_room_allotment_used.use_room AS use_room',
                DB::raw("CONCAT(apple_hotel_room_allotment_used.`year`, '-', apple_hotel_room_allotment_used.`month`, '-', apple_hotel_room_allotment_used.`day`) AS dates")
            );
        if (!empty($request->hotel)) {
            $allotmentSearch->where("apple_hotel_room_allotment.hotel", "=", $request->hotel);
        }
        if (!empty($request->start_date)) {
            $allotmentSearch->where(DB::raw("STR_TO_DATE( CONCAT(apple_hotel_room_allotment.`start_year`, '-', apple_hotel_room_allotment.`start_month`, '-', apple_hotel_room_allotment.`start_day`), '%Y-%m-%d')"), '>=', $request->start_date);
        }
        if (!empty($request->end_date)) {
            $allotmentSearch->where(DB::raw("STR_TO_DATE( CONCAT(apple_hotel_room_allotment.`end_year`, '-', apple_hotel_room_allotment.`end_month`, '-', apple_hotel_room_allotment.`end_day`), '%Y-%m-%d')"), '<=', $request->end_date);
        }
        $allotmentSearch->orderBy('created_at', 'desc');
        $allotmentSearch = $allotmentSearch->get()->toArray();

        $arrData = array();
        $total = array();
        
        foreach ($allotmentSearch as $allotment) {

            $period = new DatePeriod(
                new DateTime($allotment->start_year . "-". $allotment->start_month . "-". $allotment->start_day),
                new DateInterval('P1D'),
                new DateTime($allotment->end_year . "-". $allotment->end_month . "-". $allotment->end_day)
            );

            foreach ($period as $key => $value) {
                $date = $value->format('Y-m-d');

                $arrData[$date]['name'] = $allotment->hotel_name;
                $arrData[$date]['start_date'] = $date;
                $arrData[$date]['end_date'] = $date;
                $arrData[$date]['room_count'] = $allotment->room;

                $date_stamp = Carbon::parse($date)->timestamp;
                $dates_stamp = Carbon::parse($allotment->dates)->timestamp;

                if ($date_stamp == $dates_stamp) {
                    $value = array_key_exists($key, $total) ? $total[$key] : 0;
                    $total[$date] = $value + $allotment->use_room;
                    $arrData[$date]['used'] = $total[$date];
                }

                if(isset($arrData[$date]['used'])) {
                    $arrData[$date]['remain_count'] = $arrData[$date]['room_count'] - $arrData[$date]['used'];
                } else {
                    $arrData[$date]['remain_count'] = $arrData[$date]['room_count'];
                }

            }
        }*/

        $arrData = array();
        $arrAllotRange = array();
        $total = array();
        $i = 0;

        $period = new DatePeriod(
            new DateTime($request->start_date),
            new DateInterval('P1D'),
            new DateTime($request->end_date)
        );

        foreach ($period as $key => $value) {
            $date = $value->format('Y-m-d');

            $allotmentSearch = DB::table('apple_hotel_room_allotment')
                ->leftJoin('apple_hotels', 'apple_hotels.ID', '=', 'apple_hotel_room_allotment.hotel')

                ->select(
                    'apple_hotel_room_allotment.*',
                    'apple_hotels.name AS hotel_name'
                );
            if (!empty($request->hotel)) {
                $allotmentSearch->where("apple_hotel_room_allotment.hotel", "=", $request->hotel);
            }

            $allotmentSearch->where(DB::raw("STR_TO_DATE( CONCAT(apple_hotel_room_allotment.`start_year`, '-', apple_hotel_room_allotment.`start_month`, '-', apple_hotel_room_allotment.`start_day`), '%Y-%m-%d')"), '<=', $date);
            $allotmentSearch->where(DB::raw("STR_TO_DATE( CONCAT(apple_hotel_room_allotment.`end_year`, '-', apple_hotel_room_allotment.`end_month`, '-', apple_hotel_room_allotment.`end_day`), '%Y-%m-%d')"), '>=', $date);

            $allotmentSearch = $allotmentSearch->get()->toArray();

            foreach ($allotmentSearch as $allotmentSearchItem) {
                $start_date = $allotmentSearchItem->start_year . "-" . $allotmentSearchItem->start_month . "-" . $allotmentSearchItem->start_day;

                $arrAllotRange[$start_date]['name'] = $allotmentSearchItem->hotel_name;
                $arrAllotRange[$start_date]['start_date'] = date('Y-m-d',strtotime($start_date));
                $arrAllotRange[$start_date]['end_date'] = date('Y-m-d',strtotime($allotmentSearchItem->end_year . "-" . $allotmentSearchItem->end_month . "-" . $allotmentSearchItem->end_day));
                $arrAllotRange[$start_date]['room_count'] = $allotmentSearchItem->room;
            }

                // var_dump($allotmentSearch);exit();
            $allotmentSearchItem1 = end($allotmentSearch);
            $arrData[$i]['name'] = $allotmentSearchItem1->hotel_name;
            $arrData[$i]['date'] = $date;
            $arrData[$i]['room_count'] = $allotmentSearchItem1->room;

            $allotmentInnerSearch = DB::table('apple_hotel_room_allotment_used')
                ->select(
                    '*',
                    DB::raw("SUM(use_room) as total_use_room")
                );
            $allotmentInnerSearch->where(DB::raw("STR_TO_DATE( CONCAT(`year`, '-', `month`, '-', `day`), '%Y-%m-%d')"), '=', $date);
            $allotmentInnerSearch->where("hotel", "=", $request->hotel);

            $allotmentInnerSearch = $allotmentInnerSearch->get()->toArray();

            $arrData[$i]['used'] = $allotmentInnerSearch[0]->total_use_room;
            $arrData[$i]['remain_count'] = $arrData[$i]['room_count'] - $arrData[$i]['used'];
            $i++;
        }

        return view("allotments.allotment-items", compact(['arrData','arrAllotRange']));

    }

    function stopSalesSearch(Request $request, $id) {
        $array = array();
        /*$array[] = array('title' => 'Event Title1','start' => '2019-01-17','end' => '2019-01-17');
        $array[] = array('title' => 'Event Title2','start' => '2019-01-18','end' => '2019-01-20');
        $array[] = array('title' => 'Event Title3','start' => '2019-01-25','end' => '2019-01-26');*/

        $stopSalesSearch = DB::table('apple_hotel_room_allotment_stop_sale')
            ->leftJoin('apple_hotels', 'apple_hotels.ID', '=', 'apple_hotel_room_allotment_stop_sale.hotel')
            ->leftJoin('apple_hotel_room_category', 'apple_hotel_room_category.ID', '=', 'apple_hotel_room_allotment_stop_sale.room_category')
            ->select('apple_hotel_room_allotment_stop_sale.*', 'apple_hotels.name AS hotel_name', 'apple_hotel_room_category.name AS room_category_name');

        if (!empty($id)) {
            $stopSalesSearch->where("apple_hotel_room_allotment_stop_sale.hotel", "=", $id);
        }

        if (!empty($_GET['start'])) {
            $stopSalesSearch->where(DB::raw("STR_TO_DATE( CONCAT(apple_hotel_room_allotment_stop_sale.`start_year`, '-', apple_hotel_room_allotment_stop_sale.`start_month`, '-', apple_hotel_room_allotment_stop_sale.`start_day`), '%Y-%m-%d')"), '>=', $_GET['start']);
        }

        if (!empty($_GET['end'])) {
            $stopSalesSearch->where(DB::raw("STR_TO_DATE( CONCAT(apple_hotel_room_allotment_stop_sale.`end_year`, '-', apple_hotel_room_allotment_stop_sale.`end_month`, '-', apple_hotel_room_allotment_stop_sale.`end_day`), '%Y-%m-%d')"), '<=', $_GET['end']);
        }

        $stopSalesSearch->orderBy('apple_hotel_room_allotment_stop_sale.created_at', 'desc');
        $stopSalesSearch = $stopSalesSearch->get()->toArray();

        $stopSalesArr = array();
        foreach ($stopSalesSearch as $key => $stopSales) {
            $endDate = new DateTime($stopSales->end_year . "-". $stopSales->end_month . "-". $stopSales->end_day);
            $period = new DatePeriod(
                new DateTime($stopSales->start_year . "-". $stopSales->start_month . "-". $stopSales->start_day),
                new DateInterval('P1D'),
                $endDate->add(new DateInterval('P1D'))
            );

            foreach ($period as $key => $value) {
                $date = $value->format('Y-m-d');

                $stopSalesArr[$date . $stopSales->room_category_name]['ID'] = $stopSales->ID;
                $stopSalesArr[$date . $stopSales->room_category_name]['title'] = $stopSales->hotel_name . " / " . $stopSales->room_category_name;
                $stopSalesArr[$date . $stopSales->room_category_name]['start'] = $date;
                $stopSalesArr[$date . $stopSales->room_category_name]['end'] = $date;
            }
        }

        $stopSalesInc = array();
        $i=0;
        foreach ($stopSalesArr as $stopSales) {
            $stopSalesInc[$i]['title'] = $stopSales['title'];
            $stopSalesInc[$i]['start'] = $stopSales['start'];
            $stopSalesInc[$i]['end'] = $stopSales['end'];
            $i++;
        }

        return $stopSalesInc;
    }
}
?>
