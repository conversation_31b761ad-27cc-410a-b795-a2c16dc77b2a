<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\Market;
use App\Model\Hotel\Rates;
use App\Model\Hotel\RatesChild;
use App\Model\Hotel\RoomCategory;
use App\Model\Hotel\RoomType;
use App\Model\Meal\MealPlan;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Yajra\Datatables\Datatables;


/**
 * Class DatatablesModel
 * @package App\Http\Controllers\Admin
 */
class DatatablesModel extends Controller
{
    var $TableType = [
        "rates"=>Rates::class,
        "child_rates"=>RatesChild::class,
    ];

    var $TableColumn = [];


    /**
     * @param $table
     * @return mixed
     */
    function getColumnList($table){

        $ColumnList['rates'] = [
            DB::raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) as start_date"),
            DB::raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) as end_date"),
            "meal",
            "hotel",
            "rate",
            "room_type",
            "room_category",
            "market"];

        $ColumnList['child_rates'] = [
            DB::raw("IF(age_from = '0','CWB', 'CNB') as child_type"),
            DB::raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) as start_date"),
            DB::raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) as end_date"),
            "meal",
            "hotel",
            "rate",
            "room_type",
            "room_category",
            "market"];

        return $ColumnList[$table];


    }

    /**
     * @param $table
     * @return mixed
     */
    function getValidateRules($table){

         $Rules['rates'] = [
            "hotel" => "required|exists:apple_hotels,ID",
            "start_date" => "required|date",
            "end_date" => "required|date",
            "meal" => "required|exists:apple_meal_plan,ID",
            "room_type" => "required|exists:apple_hotel_room_type,ID",
            "room_category" => "required|exists:apple_hotel_room_category,ID",
            "market" => "required|exists:apple_market,ID",
            "rate" => "required"
        ];
         $Rules['child_rates'] = [
            "hotel" => "required|exists:apple_hotels,ID",
            "start_date" => "required|date",
            "end_date" => "required|date",
            "meal" => "required|exists:apple_meal_plan,ID",
            "room_type" => "required|exists:apple_hotel_room_type,ID",
            "room_category" => "required|exists:apple_hotel_room_category,ID",
            "market" => "required|exists:apple_market,ID",
            "rate" => "required"
        ];

        return $Rules[$table];
    }


    /**
     * @return mixed
     */
    public function getBasic()
    {
        return view('datatables.eloquent.basic')->limit(100);
    }

    /**
     * @param Request $request
     * @return mixed
     */
    public function getRatesAdult(Request $request)
    {
        $Data = $request->all();


        $rates = Rates::select(
            'id',
            DB::raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) as start_date"),
            DB::raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) as end_date"),
            "meal",
            "hotel",
            "rate",
            "room_type",
            "room_category",
            "market",
            "updated_at",
            "updated_by",
            "upload_id"
        );

        $Column =  clone $rates;
        $Column =  array_keys($Column->limit(1)->first()->toArray());


        if(isset($Data['columns_filters'])){
            foreach($Data['columns_filters'] as $k=>$ColumnItem){
                if(!empty($ColumnItem['value'])) {
                    $rates->havingRaw($ColumnItem['name'] . "='" . $ColumnItem['value']."'");
                }
            }
        }

        $rates->limit(300);

        $DataArray = $rates->get();



        return Datatables::of($DataArray)
            ->editColumn('updated_at', function ($Item) {
                return $Item->updated_at->format('Y/m/d');
            })
            ->editColumn('meal', function ($Item) {
                if (!MealPlan::find($Item->meal))
                    return $Item->meal;
                else
                    return MealPlan::find($Item->meal)->plan;
            })
            ->editColumn('hotel', function ($Item) {
                if (!Hotel::find($Item->hotel))
                    return $Item->hotel;
                else
                    return Hotel::find($Item->hotel)->name." ($Item->hotel)";
            })
            ->editColumn('room_type', function ($Item) {
                if (!RoomType::find($Item->room_type))
                    return $Item->room_type;
                else
                    return RoomType::find($Item->room_type)->type;
            })
            ->editColumn('room_category', function ($Item) {
                if (!RoomCategory::find($Item->room_category))
                    return $Item->room_category;
                else
                    return RoomCategory::find($Item->room_category)->name;
            })
            ->editColumn('market', function ($Item) {
                if (!Market::find($Item->market))
                    return $Item->market;
                else
                    return Market::find($Item->market)->market;
            })
            ->make();
    }

    /**
     * @param Request $request
     * @return mixed
     */
    public function getRatesChild(Request $request)
    {
        $Data = $request->all();


        $rates = RatesChild::select(
            'id',
            DB::raw("IF(age_from = '0','CWB', 'CNB') as child_type"),
            DB::raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) as start_date"),
            DB::raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) as end_date"),
            "meal",
            "hotel",
            "rate",
            "room_type",
            "room_category",
            "market",
            "updated_at",
            "updated_by",
            "upload_id"
        );

        $Column =  clone $rates;
        $Column =  array_keys($Column->limit(1)->first()->toArray());


        if(isset($Data['columns_filters'])){
            foreach($Data['columns_filters'] as $k=>$ColumnItem){
                if(!empty($ColumnItem['value'])) {
                    $rates->havingRaw($ColumnItem['name'] . "='" . $ColumnItem['value']."'");
                }
            }
        }

        $rates->limit(300);

        $DataArray = $rates->get();



        return Datatables::of($DataArray)
            ->editColumn('updated_at', function ($Item) {
                return $Item->updated_at->format('Y/m/d');
            })
            ->editColumn('meal', function ($Item) {
                if (!MealPlan::find($Item->meal))
                    return $Item->meal;
                else
                    return MealPlan::find($Item->meal)->plan;
            })
            ->editColumn('hotel', function ($Item) {
                if (!Hotel::find($Item->hotel))
                    return $Item->hotel;
                else
                    return Hotel::find($Item->hotel)->name." ($Item->hotel)";
            })
            ->editColumn('room_type', function ($Item) {
                if (!RoomType::find($Item->room_type))
                    return $Item->room_type;
                else
                    return RoomType::find($Item->room_type)->type;
            })
            ->editColumn('room_category', function ($Item) {
                if (!RoomCategory::find($Item->room_category))
                    return $Item->room_category;
                else
                    return RoomCategory::find($Item->room_category)->name;
            })
            ->editColumn('market', function ($Item) {
                if (!Market::find($Item->market))
                    return $Item->market;
                else
                    return Market::find($Item->market)->market;
            })
            ->make();
    }

    /**
     * @param $table
     * @param $id
     * @return array|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function select($table, $id){

        if(empty($this->TableType[$table]))
            return [];

        $Model = $this->TableType[$table]::select($this->getColumnList($table));

        if(!$Model->where('ID',$id)->first())
            return [];

        $Data = $Model->where('ID',$id)->first()->toArray();

        return view("admin.database.table-edit",['Data'=>$Data,'ID'=>$id,'table'=>$table]);

    }
    function insert(){

    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function update(Request $request){

        $Data = $request->input();
        $ReturnData['status'] = false;

        $table = $request->get('table');
        $id = $request->get('table_id');

        $rules = $this->getValidateRules($table);


        $Validator = \Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $ReturnData['data'] = $messages;
            return view("system.error.error-bootstrap", ["Message" => $messages->toArray()]);

        }

        if(empty($this->TableType[$table]))
            return view("system.error.error-bootstrap", ["Message" => "Invalid Table"]);


        $Model = new $this->TableType[$table];

        if(!$Model->where('ID',$id)->first())
            return view("system.error.error-bootstrap", ["Message" => "Invalid ID"]);


        if($table=='rates'){
            return $this->updateAdultRate($Data,$table,$id);
        }
        elseif($table=='child_rates'){
            return $this->updateChildRate($Data,$table,$id);

        }





    }


    /**
     * @param $Data
     * @param $table
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function updateAdultRate($Data, $table, $id){

        $StartDate = Carbon::parse($Data['start_date']);
        $EndDate = Carbon::parse($Data['end_date']);


        $Rates = Rates::where('ID',$id)->update(
            [
                'start_year' => $StartDate->year,
                'start_month' => $StartDate->month,
                'start_day' => $StartDate->day,

                'end_year' => $EndDate->year,
                'end_month' => $EndDate->month,
                'end_day' => $EndDate->day,

                'rate' => $Data['rate'],

                'meal' => $Data['meal'],
                'hotel' => $Data['hotel'],
                'room_type' => $Data['room_type'],
                'room_category' => $Data['room_category'],
                'market' => $Data['market'],
                'created_by' => \Auth::user()->id
            ]
        );

        $ReturnData['status'] = $Rates;

        if($Rates)
            $ReturnData['data'] = $Rates;


        if($ReturnData['status'])
            return view("system.success.success-bootstrap", ["Message" => "Updated"]);
        else
            return view("system.error.error-bootstrap", ["Message" => "Opps Something went wrong"]);
    }

    /**
     * @param $Data
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function updateChildRate($Data){

        $StartDate = Carbon::parse($Data['start_date']);
        $EndDate = Carbon::parse($Data['end_date']);


        $Rates = RatesChild::where('ID',$Data['table_id'])->update(
            [
                'start_year' => $StartDate->year,
                'start_month' => $StartDate->month,
                'start_day' => $StartDate->day,

                'end_year' => $EndDate->year,
                'end_month' => $EndDate->month,
                'end_day' => $EndDate->day,

                'age_from' => ($Data['child_type']=='CNB')?0:2,
                'age_to' => ($Data['child_type']=='CNB')?2:12,

                'rate' => $Data['rate'],

                'meal' => $Data['meal'],
                'hotel' => $Data['hotel'],
                'room_type' => $Data['room_type'],
                'room_category' => $Data['room_category'],
                'market' => $Data['market'],
                'created_by' => \Auth::user()->id
            ]
        );

        $ReturnData['status'] = $Rates;




        if($Rates)
            $ReturnData['data'] = $Rates;


        if($ReturnData['status'])
            return view("system.success.success-bootstrap", ["Message" => "Updated"]);
        else
            return view("system.error.error-bootstrap", ["Message" => "Opps Something went wrong"]);
    }


    function delete(){

    }


}
