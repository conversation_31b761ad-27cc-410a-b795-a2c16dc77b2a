<?php

namespace App\Http\Controllers\Admin;

use App\Model\Admin\HotelierRoomRates;
use App\Model\Admin\HotelierRoomRatesChild;
use App\Model\Admin\HotelierStopSales;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\Market;
use App\Model\Hotel\Room;
use App\Model\Hotel\RoomCategory;
use App\Model\Meal\MealPlan;
use Illuminate\Http\Request;

use Carbon\Carbon;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

/**
 * Class AdminHotelier
 * @package App\Http\Controllers\Admin
 */
class AdminHotelier extends Controller
{

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelierAdmin(){

        $hotels = Hotel::all();
        $meal = MealPlan::all();
        $room_type = Room::all();
        $room_category = RoomCategory::all();
        $market = Market::all();


        return View('admin/home',['page' => 'Hotelier.hotel_rates', 'hotels'=>$hotels, 'meals' => $meal, 'room_types' => $room_type, 'room_category' => $room_category, 'markets' => $market]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelierStopSalesAdmin(){

        $stop_sales = HotelierStopSales::where('hotel',2)->get();
        return View('admin.blade.php/home',['page' => 'Hotelier.stop_sales', 'stop_sales' => $stop_sales ]);

    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelRateHotelier(Request $request){

        $this->validate($request, [

            'active_from' => 'required',
            'active_to' => 'required',
            'meal' => 'required',
            'rooms' => 'required',
            'price' => 'required',
            'room_type' => 'required',
            'room_category' => 'required',
            'market' => 'required',

        ]);

        $date_start = $request->active_from;
        $date_end = $request->active_to;

        $start_dt = Carbon::parse($date_start);
        $end_dt = Carbon::parse($date_end);

        $st_yr = $start_dt->year;
        $st_mnth = $start_dt->month;
        $st_dt = $start_dt->day;

        $end_yr = $end_dt->year;
        $end_mnth = $end_dt->month;
        $end_dt = $end_dt->day;

        $rate = new HotelierRoomRates();

        $rate->start_year = $st_yr;
        $rate->start_month = $st_mnth;
        $rate->start_day = $st_dt;
        $rate->end_year = $end_yr;
        $rate->end_month = $end_mnth;
        $rate->end_day = $end_dt;
        $rate->meal = $request->meal;
        $rate->hotel = 2;
        $rate->rooms = $request->rooms;
        $rate->rate = $request->price;
        $rate->room_type = $request->room_type;
        $rate->room_category = $request->room_category;
        $rate->market = $request->market;

        if ($rate->save()){

            return 1;
        }else{

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelChildRateHotelier(Request $request){

        $this->validate($request, [

            'active_from' => 'required',
            'active_to' => 'required',
            'age_from' => 'required',
            'age_to' => 'required',
            'meal' => 'required',
            'rooms' => 'required',
            'price' => 'required',
            'room_type' => 'required',
            'room_category' => 'required',
            'market' => 'required',

        ]);

        $date_start = $request->active_from;
        $date_end = $request->active_to;

        $start_dt = Carbon::parse($date_start);
        $end_dt = Carbon::parse($date_end);

        $st_yr = $start_dt->year;
        $st_mnth = $start_dt->month;
        $st_dt = $start_dt->day;

        $end_yr = $end_dt->year;
        $end_mnth = $end_dt->month;
        $end_dt = $end_dt->day;

        $rate = new HotelierRoomRatesChild();

        $rate->start_year = $st_yr;
        $rate->start_month = $st_mnth;
        $rate->start_day = $st_dt;
        $rate->end_year = $end_yr;
        $rate->end_month = $end_mnth;
        $rate->end_day = $end_dt;
        $rate->meal = $request->meal;
        $rate->hotel = 2;
        $rate->rooms = $request->rooms;
        $rate->age_from = $request->age_from;
        $rate->age_to = $request->age_to;
        $rate->rooms = $request->rooms;
        $rate->rate = $request->price;
        $rate->room_type = $request->room_type;
        $rate->room_category = $request->room_category;
        $rate->market = $request->market;

        if ($rate->save()){

            return 1;
        }else{

            return 0;
        }


    }

    /**
     * @param $type
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelRates($type){



        if($type == "adult"){

            $rates = DB::table('apple_hotel_room_rates_hotelier')
                ->join('apple_hotel_room_category','apple_hotel_room_category.ID','=','apple_hotel_room_rates_hotelier.room_category')
                ->join('apple_hotel_room_type','apple_hotel_room_type.ID','=','apple_hotel_room_rates_hotelier.room_type')
                ->join('apple_market','apple_market.ID','=','apple_hotel_room_rates_hotelier.market')
                ->join('apple_hotels','apple_hotels.ID','=','apple_hotel_room_rates_hotelier.hotel')
                ->join('apple_meal_plan','apple_meal_plan.ID','=','apple_hotel_room_rates_hotelier.meal')
                ->where('apple_hotel_room_rates_hotelier.hotel',2)
                ->select('apple_hotel_room_rates_hotelier.ID as rate_id','apple_hotel_room_rates_hotelier.*','apple_hotel_room_category.name as room_category_name','apple_hotel_room_type.type as room_type_name','apple_market.market as market_name','apple_hotels.name as hotel_name','apple_meal_plan.long_name as meal_plan_name')
                ->get();

            return View('admin.blade.php/home',['page' => 'Hotelier.view_rates', 'rates'=>$rates]);

        }elseif ($type == "child"){

            $rates = DB::table('apple_hotel_room_rates_child_hotelier')
                ->join('apple_hotel_room_category','apple_hotel_room_category.ID','=','apple_hotel_room_rates_child_hotelier.room_category')
                ->join('apple_hotel_room_type','apple_hotel_room_type.ID','=','apple_hotel_room_rates_child_hotelier.room_type')
                ->join('apple_market','apple_market.ID','=','apple_hotel_room_rates_child_hotelier.market')
                ->join('apple_hotels','apple_hotels.ID','=','apple_hotel_room_rates_child_hotelier.hotel')
                ->join('apple_meal_plan','apple_meal_plan.ID','=','apple_hotel_room_rates_child_hotelier.meal')
                ->where('apple_hotel_room_rates_child_hotelier.hotel',2)
                ->select('apple_hotel_room_rates_child_hotelier.ID as rate_id','apple_hotel_room_rates_child_hotelier.*','apple_hotel_room_category.name as room_category_name','apple_hotel_room_type.type as room_type_name','apple_market.market as market_name','apple_hotels.name as hotel_name','apple_meal_plan.long_name as meal_plan_name')
                ->get();

            return View('admin.blade.php/home',['page' => 'Hotelier.view_rates_child', 'rates'=>$rates]);

        }else{

            echo "URL Not found";
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelStopSalesHotelier(Request $request){

        $this->validate($request, [

            'active_from' => 'required',
            'active_to' => 'required',

        ]);

        $date_start = $request->active_from;
        $date_end = $request->active_to;

        $start_dt = Carbon::parse($date_start);
        $end_dt = Carbon::parse($date_end);

        $st_yr = $start_dt->year;
        $st_mnth = $start_dt->month;
        $st_dt = $start_dt->day;

        $end_yr = $end_dt->year;
        $end_mnth = $end_dt->month;
        $end_dt = $end_dt->day;

        $rate = new HotelierStopSales();

        $rate->start_year = $st_yr;
        $rate->start_month = $st_mnth;
        $rate->start_day = $st_dt;
        $rate->end_year = $end_yr;
        $rate->end_month = $end_mnth;
        $rate->end_day = $end_dt;

        if ($rate->save()){

            return 1;
        }else{

            return 0;
        }


    }
}
