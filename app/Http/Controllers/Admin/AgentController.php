<?php


namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Model\Admin\SellerAgentPerson;
use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use App\Model\Admin\SellerAgent;
use App\Model\Admin\SalesTrack;
use Maatwebsite\Excel\Facades\Excel;


/**
 * Class FollowUPController
 * @package App\Http\Controllers\Flight
 */
class AgentController extends Controller {

    function agentSave(Request $request) {
        try{
            $SellerAgent = new SellerAgent();

            $SellerAgent->agency_name = $_POST['agency-name'];
            $SellerAgent->city = $_POST['city'];
            $SellerAgent->q1 = $_POST['q1'];
            $SellerAgent->q2 = $_POST['q2'];
            $SellerAgent->q3 = $_POST['q3'];
            $SellerAgent->q4 = $_POST['q4'];
            $SellerAgent->q11 = $_POST['q11'];
            $SellerAgent->q12 = $_POST['q12'];
            $SellerAgent->q13 = $_POST['q13'];
            $SellerAgent->q14 = $_POST['q14'];
            $SellerAgent->q15 = $_POST['q15'];
            $SellerAgent->q16 = $_POST['q16'];
            $SellerAgent->q17 = $_POST['q17'];
            $SellerAgent->q18 = $_POST['q18'];
            $SellerAgent->q19 = $_POST['q19'];
            $SellerAgent->q20 = $_POST['q20'];
            $SellerAgent->q21 = $_POST['q21'];
            $SellerAgent->q22 = $_POST['q22'];
            $SellerAgent->q23 = $_POST['q23'];
            $SellerAgent->q24 = $_POST['q24'];
            $SellerAgent->q25 = $_POST['q25'];
            $SellerAgent->q26 = $_POST['q26'];
            $SellerAgent->user_id = Auth::user()->id;
            $SellerAgent->save();

            foreach ($_POST['q6'] as $key => $_post) {
                if($key == 0) continue;
                $SellerAgentPerson = new SellerAgentPerson();

                $SellerAgentPerson->q6 = $_POST['q6'][$key];
                $SellerAgentPerson->q7 = $_POST['q7'][$key];
                $SellerAgentPerson->q8 = $_POST['q8'][$key];
                $SellerAgentPerson->q9 = $_POST['q9'][$key];
                $SellerAgentPerson->q10 = $_POST['q10'][$key];
                $SellerAgentPerson->seller_agent_id = $SellerAgent->ID;
                $SellerAgentPerson->save();
            }

            return json_encode(['status'=>'success', 'msg' => 'Successfully Addded..!!!']);
        } catch(\Exception $e){
            return json_encode(['status'=>'error', 'msg' => $e->getMessage()]);
        }
    }

    function agentUpdate(Request $request) {
        try{
            // var_dump($_POST);exit();
            $SellerAgent = SellerAgent::find($_POST['ID']);

            $SellerAgent->city = $_POST['city'];
            $SellerAgent->q1 = $_POST['q1'];
            $SellerAgent->q2 = $_POST['q2'];
            $SellerAgent->q3 = $_POST['q3'];
            $SellerAgent->q4 = $_POST['q4'];
            $SellerAgent->q11 = $_POST['q11'];
            $SellerAgent->q12 = $_POST['q12'];
            $SellerAgent->q13 = $_POST['q13'];
            $SellerAgent->q14 = $_POST['q14'];
            $SellerAgent->q15 = $_POST['q15'];
            $SellerAgent->q16 = $_POST['q16'];
            $SellerAgent->q17 = $_POST['q17'];
            $SellerAgent->q18 = $_POST['q18'];
            $SellerAgent->q19 = $_POST['q19'];
            $SellerAgent->q20 = $_POST['q20'];
            $SellerAgent->q21 = $_POST['q21'];
            $SellerAgent->q22 = $_POST['q22'];
            $SellerAgent->q23 = $_POST['q23'];
            $SellerAgent->q24 = $_POST['q24'];
            $SellerAgent->q25 = $_POST['q25'];
            $SellerAgent->q26 = $_POST['q26'];
            $SellerAgent->user_id = Auth::user()->id;

            $SellerAgent->save();

            SellerAgentPerson::where("seller_agent_id", "=", $SellerAgent->ID)->delete();

            foreach ($_POST['q6'] as $key => $_post) {
                if($key == 0) continue;
                $SellerAgentPerson = new SellerAgentPerson();

                $SellerAgentPerson->q6 = $_POST['q6'][$key];
                $SellerAgentPerson->q7 = $_POST['q7'][$key];
                $SellerAgentPerson->q8 = $_POST['q8'][$key];
                $SellerAgentPerson->q9 = $_POST['q9'][$key];
                $SellerAgentPerson->q10 = $_POST['q10'][$key];
                $SellerAgentPerson->seller_agent_id = $SellerAgent->ID;
                $SellerAgentPerson->save();
            }

            return json_encode(['status'=>'success', 'msg' => 'Successfully Updated..!!!']);
        } catch(\Exception $e){
            return json_encode(['status'=>'error', 'msg' => $e->getMessage()]);
        }

    }

    function salesTrackSave(Request $request) {
        try{
            $SalesTrack = new SalesTrack();

            $SalesTrack->sales_track_type = $_POST['sales_track_type'];
            $SalesTrack->company_agent = $_POST['company_agent'];
            $SalesTrack->tracking_type = $_POST['tracking_type'];
            $SalesTrack->contact_person = $_POST['contact_person'];
            $SalesTrack->contact_no = $_POST['contact_no'];
            $SalesTrack->date_time = $_POST['date_time'];

            $SalesTrack->email = $_POST['email'];
            $SalesTrack->remark = $_POST['remark'];
            $SalesTrack->destination = $_POST['destination'];
            $SalesTrack->city = $_POST['city'];
            $SalesTrack->sales_track_id = $_POST['sales_track_id'];

            $SalesTrack->quotation_status = $_POST['quotation_status'];
            $SalesTrack->quotation_no = $_POST['quotation_no'];
            $SalesTrack->user_id = Auth::user()->id;

            $SalesTrack->save();

            return json_encode(['status'=>'success', 'msg' => 'Successfully Addded..!!!']);
        } catch(\Exception $e){
            return json_encode(['status'=>'error', 'msg' => $e->getMessage()]);
        }
    }

    function salesTrackSearch(Request $request) {

        $salesTrack = DB::table('apple_sales_track')
            ->leftjoin('users', 'users.id', '=', 'apple_sales_track.user_id')
            ->select('apple_sales_track.*', 'users.name AS user_name');

        if (!empty($_POST["company_agent"])) {
            $salesTrack->where("company_agent", "=", $_POST["company_agent"]);
        }

        if (!empty($_POST["sales_track_type"])) {
            if($_POST["sales_track_type"] == "New Sales") {
                $salesTrack->where("sales_track_type", "=", $_POST["sales_track_type"]);
                // $salesTrack->orwhere("sales_track_type", "=", null);
            } else {
                $salesTrack->where("sales_track_type", "=", $_POST["sales_track_type"]);
            }

        }

        if(!isset($_POST["user"]))
            return false;

        if (!empty($_POST["user"])) {
            $salesTrack->whereIn("user_id", $_POST["user"]);
        }

        if (!empty($request->from_date) && !empty($request->to_date)) {
            $salesTrack->whereBetween('date_time', [new Carbon($request->from_date), new Carbon($request->to_date)]);
        }

        if (!empty($_POST["destination"])) {
            $salesTrack->where("destination", "like", $_POST["destination"] . "%");
        }

       /* if (!empty($_POST["no_of_inquiries"])) {
            $salesTrack->where("no_inquiry", $_POST["no_of_inquiries"]);
        }*/

        if (!empty($_POST["no_of_inquiries"])) {
            $salesTrack->where("no_inquiry", '>=', 1);
        }

        $salesTrack->orderBy('date_time', 'asc');

        $salesTrack = $salesTrack->get()->toArray();

        /*foreach ($salesTrack as $key => $sales) {
            $userId = intval($sales->user_id);
            foreach ($request->user as $user) {
                if($userId != $user) {
                    unset($salesTrack[$key]);
                }
            }
        }*/


        if($_POST["sales_track_type"] == "New Sales") {
            return view("reports.sales-track-report-items", compact('salesTrack'));
        } else {
            return view("reports.sales-track-quotation-report-items", compact('salesTrack'));
        }

    }

    public function salesTrackDownloadToExcel(Request $request) {

        $salesTrack = DB::table('apple_sales_track')
                        ->join('users', 'users.id', '=', 'apple_sales_track.user_id')
                        ->select('apple_sales_track.*', 'users.name AS user_name');

        if (!empty($request->company_agent)) {
            $salesTrack->where("company_agent", "=", $request->company_agent);
        }
        if (!empty($request->sales_track_type)) {
            if($request->sales_track_type == "New Sales") {
                $salesTrack->where("sales_track_type", "=", $request->sales_track_type);
                // $salesTrack->orwhere("sales_track_type", "=", null);
            } else {
                $salesTrack->where("sales_track_type", "=", $request->sales_track_type);
            }

        }

        if(!isset($request->user))
            return false;

        if (!empty($request->user)) {
            $salesTrack->whereIn("user_id", $request->user);
        }

        if (!empty($request->from_date) && !empty($request->to_date)) {
            $salesTrack->whereBetween('date_time', [$request->from_date, $request->to_date]);
        }

        if (!empty($request->destination)) {
            $salesTrack->where("destination", "like", $request->destination . "%");
        }
        if (!empty($request->no_of_inquiries)) {
            $salesTrack->where("no_inquiry", '>=', 1);
        }


        $salesTrack->orderBy('date_time', 'desc');

        $salesTrack = $salesTrack->get();

        /*foreach ($salesTrack as $key => $sales) {
            $userId = intval($sales->user_id);
            foreach ($request->user as $user) {
                if($userId != $user) {
                    unset($salesTrack[$key]);
                }
            }
        }*/

        $data = [];

        foreach ($salesTrack as $key => $sales) {

            $data[$key]['sales_track_id'] = $sales->sales_track_id;
            $data[$key]['company_agent'] = $sales->company_agent;
            $data[$key]['tracking_type'] = $sales->tracking_type;
            $data[$key]['contact_person'] = $sales->contact_person;
            $data[$key]['contact_no'] = $sales->contact_no;
            $data[$key]['user_name'] = $sales->user_name;
            $data[$key]['date_time'] = $sales->date_time;
             if(!$request->sales_track_type == "New Sales") {
                 $data[$key]['quotation_status'] = $sales->quotation_status;
                 $data[$key]['quotation_no'] = $sales->quotation_no;
             }
            $data[$key]['email'] = $sales->email;
            $data[$key]['destination'] = $sales->destination;
            $data[$key]['city'] = $sales->city;
            $data[$key]['remark'] = $sales->remark;

        }

        Excel::create('Sales Track Data', function($excel) use ($data) {

            $excel->sheet('Sheet', function($sheet) use ($data) {

                $sheet->fromArray($data);
            });
        })->download('xls');

        return redirect('#sales_track_report_container');
    }

    function agentAgencySearch() {

        $agencyQuestions = DB::table('apple_seller_agent_person')
            ->leftJoin('apple_seller_agent', 'apple_seller_agent_person.seller_agent_id' ,'=','apple_seller_agent.ID')
            ->rightJoin('apple_agent', 'apple_agent.ID' ,'=','apple_seller_agent.agency_name')
            ->select('apple_seller_agent.*', 'apple_seller_agent_person.*', 'apple_agent.name', 'apple_seller_agent.ID AS seller_id');

        if (!empty($_POST["agency_name"])) {
            $agencyQuestions->where("agency_name", "=", $_POST["agency_name"]);
        }
        if (!empty($_POST["sales_person"]) && $_POST["sales_person"] > 0) {
            $agencyQuestions->where("user_id", "=", $_POST["sales_person"]);
        }

        if (!empty($_POST['from_date']) && !empty($_POST['to_date'])) {

            $fromDate = Carbon::createFromFormat('Y-m-d H:i:s', $_POST['from_date'] . ' 00:00:00')->format('Y-m-d H:i:s');
            $toDate = Carbon::createFromFormat('Y-m-d H:i:s', $_POST['to_date'] . ' 00:00:00')->format('Y-m-d H:i:s');

            $agencyQuestions->whereBetween('apple_seller_agent_person.created_at', [$fromDate, $toDate]);
        }

        $agencyQuestions->orderBy('apple_seller_agent.created_at', 'desc');
        $agencyQuestions->groupBy('apple_agent.ID');

        $agencyQuestions = $agencyQuestions->get();

        return view("reports.agency-profile-question-reports", compact('agencyQuestions'));
    }

    public function getAgencyQuestionDetails(Request $request)
    {
        $agencyQuestions = DB::table('apple_seller_agent_person')
                                ->leftJoin('apple_seller_agent',
                                            'apple_seller_agent_person.seller_agent_id' ,
                                        '=',
                                        'apple_seller_agent.ID')
                                ->rightJoin('apple_agent',
                                            'apple_agent.ID' ,
                                        '=',
                                        'apple_seller_agent.agency_name')
                                ->select('apple_seller_agent.*',
                                                'apple_seller_agent_person.*',
                                                'apple_agent.name',
                                                'apple_seller_agent.ID AS seller_id')
                                ->where('apple_agent.ID', '=', $request->agency_id)
                                ->get();

        return view("reports.agency-profile-question-agency", compact('agencyQuestions'));
    }

    public function agencyQuestionDownloadToExcel(Request $request)
    {
        $agencyQuestions = DB::table('apple_seller_agent_person')
                                ->leftJoin('apple_seller_agent',
                                            'apple_seller_agent_person.seller_agent_id' ,
                                        '=',
                                        'apple_seller_agent.ID')
                                ->rightJoin('apple_agent',
                                            'apple_agent.ID' ,
                                        '=',
                                        'apple_seller_agent.agency_name')
                                ->select('apple_seller_agent.*',
                                                'apple_seller_agent_person.*',
                                                'apple_agent.name',
                                                'apple_seller_agent.ID AS seller_id')
                                ->where('apple_agent.ID', '=', $request->agency_id)
                                ->groupBy('apple_agent.ID')
                                ->get();

        $request->session()->put('agency', $agencyQuestions);

        $data = [];

        foreach ($agencyQuestions as $key => $agency){

            $data[$key]['name'] = $agency->name;
            $data[$key]['seller_id'] = $agency->seller_id;
            $data[$key]['city'] = $agency->city;
            $data[$key]['q1'] = $agency->q1;
            $data[$key]['q2'] = $agency->q2;
            $data[$key]['q3'] = $agency->q3;
            $data[$key]['q4'] = $agency->q4;
            $data[$key]['q11'] = $agency->q11;
            $data[$key]['q12'] = $agency->q12;
            $data[$key]['q13'] = $agency->q13;
            $data[$key]['q14'] = $agency->q14;
            $data[$key]['q15'] = $agency->q15;
            $data[$key]['q16'] = $agency->q16;
            $data[$key]['q17'] = $agency->q17;
            $data[$key]['q18'] = $agency->q18;
            $data[$key]['q19'] = $agency->q19;
            $data[$key]['q20'] = $agency->q20;
            $data[$key]['q21'] = $agency->q21;
            $data[$key]['q22'] = $agency->q22;
            $data[$key]['q23'] = $agency->q23;
            $data[$key]['q24'] = $agency->q24;
            $data[$key]['q25'] = $agency->q25;
            $data[$key]['q26'] = $agency->q26;

        }

        Excel::create('Agency Questions Data', function($excel) use ($data) {

            $excel->sheet('Sheet', function($sheet) use ($data) {

                $sheet->fromArray($data);
            });
        })->download('xls');

        return redirect('#agent_profile_report_container');
    }

    function agentNameSearch() {

        $agencyQuestions = DB::table('apple_seller_agent_person')
            ->leftJoin('apple_seller_agent', 'apple_seller_agent_person.seller_agent_id' ,'=','apple_seller_agent.ID')
            ->leftJoin('apple_agent', 'apple_agent.ID' ,'=','apple_seller_agent.agency_name')
            ->select('apple_seller_agent_person.*', 'apple_agent.name');

        if (!empty($_POST["name"])) {
            $agencyQuestions->where("apple_seller_agent_person.ID", "=", $_POST["name"]);
        }

        $agencyQuestions = $agencyQuestions->get()->toArray();

        return view("reports.agency-profile-question-name", compact('agencyQuestions'));
    }

    function salesTrackFilters() {
        return view('reports.sales-track-report');
    }

    function agentQuestionFilters() {
        return view('reports.agent-profile-question-report');
    }

    function salesTrackSuggest(Request $request) {
        $saleTrack = SalesTrack::select("ID", "company_agent", "contact_person", "contact_no", "sales_track_id", 'city')->where("company_agent", "LIKE", "%".$request->q."%")->groupBy('company_agent')->get()->toArray();

        return $saleTrack;
    }

    function salesTrackSuggestName(Request $request) {
        $saleTrack = SalesTrack::select("ID", "contact_person", "contact_no", "sales_track_id")
            ->where("contact_person", "LIKE", "%".$request->q."%")
            ->where("company_agent", "=", $request->agency)->get()->toArray();

        return $saleTrack;
    }

    function salesTrackSuggestID(Request $request) {
        $saleTrack = SalesTrack::select("*")
            ->where("sales_track_id", "LIKE", "%".$request->q."%")->groupBy('sales_track_id')->get()->toArray();

        return $saleTrack;
    }

}
?>
