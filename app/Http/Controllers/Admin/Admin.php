<?php

namespace App\Http\Controllers\Admin;

use App\Events\ImportRateHotel;
use App\Events\ImportRateCruise;
use App\Http\Controllers\Controller;
use App\Model\Costcut\Costcut;
use App\Model\Costcut\CostcutRate;
use App\Model\Hotel\Allotment;
use App\Model\Hotel\Contact;
use App\Model\Hotel\ContactType;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\HotelClass;
use App\Model\Hotel\Market;
use App\Model\Hotel\OutsideRestaurant;
use App\Model\Hotel\Rates;
use App\Model\Hotel\RatesChild;
use App\Model\Cruise\CruiseCabinRate;
use App\Model\Cruise\CruiseCabinRateChild;
use App\Model\Cruise\Cruise;
use App\Model\Cruise\CruiseCabinType;
use App\Model\Cruise\CruiseMeal;
use App\Model\Cruise\CruiseOccupancyType;
use App\Model\Cruise\CruisePackage;
use App\Model\Hotel\Room;
use App\Model\Hotel\RoomCategory;
use App\Model\Hotel\RoomType;
use App\Model\Hotel\Supplement;
use App\Model\Image\Image;
use App\Model\Meal\MealPlan;
use App\Model\Notice\Notice;
use App\Model\Notice\NoticeRead;
use App\Model\Notice\NoticeUser;
use App\Model\Place\Attraction;
use App\Model\Place\AttractionRate;
use App\Model\Place\CityTour;
use App\Model\Place\Excursion;
use App\model\Place\ExtraMileage;
use App\Model\Place\Place;
use App\Model\Profile\Profile;
use App\Model\Reports;
use App\Model\Restaurant\Restaurant;
use App\Model\Restaurant\RestaurantMealRate;
use App\Model\Restaurant\RestaurantMealRateTemp;
use App\Model\Vehicle\VehicleType;
use App\Permission;
use App\Role;
use App\User;
use App\UserHierarchy;
use Carbon\Carbon;
use DB;
use Event;
use Illuminate\Http\Request;
use Validator;
use View;
use App\Model\StaticPackages\StaticPackages;
use App\Model\FdPackages\apple_fd_packages;
use App\Model\dashboard\apple_dashboard_teamleads;
use App\Model\dashboard\apple_dashboard_user_hierarchy;
use App\Model\dashboard\apple_dashboard_user_transfer_history;
use App\Model\Country\AvailableCountry;



//test
/**
 * Class Admin
 * @package App\Http\Controllers\Admin
 */
class Admin extends Controller
{



    var $RateTableColumn = ["start_date", "end_date", "meal", "hotel", "price", "child_with_bed", "child_with_no_bed", "room_type", "room_category", "market", "special"];

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function index()
    {
        return view("admin.admin");
    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function view(Request $request)
    {

        $Data = $request->input();

        if ($Data['type'] == 'hotel_rate')
            return view("admin.hotel.hotel-rates");

        elseif ($Data['type'] == 'user_manage')
            return view("admin.user.manage");

        elseif ($Data['type'] == 'user_permission')
            return view("admin.user.permission");

        elseif ($Data['type'] == 'new_user')
            return view("admin.user.add");
       elseif ($Data['type'] == 'user_hierarchy')
            return view("admin.user.hierarchy");
        elseif ($Data['type'] == 'update_user') {
            $Data = User::find($request->input('ID'));
            return view("admin.user.add", ['Data' => $Data]);
        } elseif ($Data['type'] == 'hotel_add') {
            return view("admin.hotel.add");
        } elseif ($Data['type'] == 'attraction_manage') {
            return view("admin.attraction.attraction-manage");
        } elseif ($Data['type'] == 'city_tour_manage') {
            return view("admin.attraction.city-tour-manage");
        } elseif ($Data['type'] == 'excursion_manage') {
            return view("admin.attraction.excursion-manage");
        } elseif ($Data['type'] == 'rate_add') {
            return view("admin.hotel.add-rate");
        } elseif ($Data['type'] == 'extra_mileage') {
            return view("admin.transport.extra-mileage");
        } elseif ($Data['type'] == 'restaurants_add_rate') {
            return view("admin.meal.restaurants-add-rate");
        } elseif ($Data['type'] == 'restaurants') {
            return view("admin.meal.restaurants");
        } elseif ($Data['type'] == 'pending_restaurants_add_rate') {
            return view("admin.meal.pending-restaurants-add-rate");
        } elseif ($Data['type'] == 'markup') {
            return view("admin.other.markup");
        } elseif ($Data['type'] == 'sync') {
            return view("admin.sync");
        } elseif ($Data['type'] == 'hotelbeds_booking_list') {
            return view("admin.hotelbeds.bookings");
        } elseif ($Data['type'] == 'manage_rate') {
            return view("admin.hotel.manage-rate");
        } elseif ($Data['type'] == 'statistics_dashboard') {
            return view("admin.statistics.dashboard");
        } elseif ($Data['type'] == 'user-media-object') {
            return view("admin.user.media-object");
        } elseif ($Data['type'] == 'notice_generate') {
            return view("admin.notice.generate");
        } elseif ($Data['type'] == 'static_packages_generate') {
            return view("admin.static_packages.generate");
        } elseif ($Data['type'] == 'follow_up_report') {
            return view("follow-up.list");
        } /*elseif ($Data['type'] == 'allotments_report') {
            return view("follow-up.list");
        }*/
          elseif ($Data['type'] == 'add_hotel_images') {
            return view('admin.add_images.add_hotel_images');

        } elseif ($Data['type'] == 'add_place_images') {
            return view('admin.add_images.add_place_image');

        } elseif ($Data['type'] == 'add_fd_packages') {
              $Places = Place::where('type',1)->get();
              $users = User::whereNotNull('remember_token')->get();
              $countries =  Place::where('type',2)->get();
            return view('admin.fd_packages.add_fd-package',compact('Places','users','countries'));

        } elseif ($Data['type'] == 'view_edit_fd_packages') {
              $fdPackages = apple_fd_packages::all();
            return view('admin.fd_packages.view_edit_fd-package',compact('fdPackages'));

        }  elseif ($Data['type'] == 'dashboard_hierarchy') {
            $exitingTeamLeadsObj = apple_dashboard_teamleads::with('findUser','findTeamMembers')->get();
            $existingTeamLead = array();
                if($exitingTeamLeadsObj->count() > 0){
                    foreach ($exitingTeamLeadsObj as $existing) {
                        array_push($existingTeamLead,$existing->user_id);
                    }
                }
            $existingAllMembers = apple_dashboard_user_hierarchy::all();
            if($existingAllMembers->count() > 0){
                foreach ($existingAllMembers as $existing) {
                    array_push($existingTeamLead,$existing->user_id);
                }
            }

            $users = User::whereNotIn('id',$existingTeamLead)->get();
            return view('admin.dashboard.dashboard_user_hierarchy',compact('users','exitingTeamLeadsObj'));
        } elseif ($Data['type'] == 'cruise_rate_add') {
            return view("admin.cruise.add-rate");
        }



        /*elseif ($Data['type'] == 'add_attraction_images') {
            return view('admin.add_images.add_attraction_images');

        } elseif ($Data['type'] == 'add_city_images') {
            return view('admin.add_images.add_city_images');

        } elseif ($Data['type'] == 'add_excursion_images') {
            return view('admin.add_images.add_excursion_images');
        }*/
        elseif ($Data['type'] == 'cost_cutting_add') {
            return view('admin.cost_cut.cost_cut_add');
        } elseif ($Data['type'] == 'available_country') {
            $avilableCountryObj = AvailableCountry::orderby('id','ASC')->first();
            return view('admin.available_countries.available_countries',compact('avilableCountryObj'));

        }


    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getRateTable(Request $request)
    {
        $Data = $request->input();
        return view("admin.hotel.rate-table", ['Data' => $Data]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getRateDetailsAdult(Request $request)
    {
        $RateID = $request->input('RateID');
        $RateItem = Rates::where('ID', $RateID)->first()->toArray();
        $RateItem['type'] = 'adult';
        return view('admin.hotel.rate-item-details', ['Data' => $RateItem]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getRateDetailsChild(Request $request)
    {
        $RateID = $request->input('RateID');
        $RateItem = RatesChild::where('ID', $RateID)->first()->toArray();
        $RateItem['type'] = 'child';

        return view('admin.hotel.rate-item-details', ['Data' => $RateItem]);
    }

    /**
     * @param Request $request
     * @return Rates|\Illuminate\Database\Eloquent\Model|null|object
     */
    function updateRateHotelAdult(Request $request)
    {

        $Data = $request->input();
        $UpdateStatus = Rates::where('ID', $Data['ID'])->update([
            'rate' => $Data['rate'],
            'updated_by' => \Auth::user()->id
        ]);
        return Rates::where('ID', $Data['ID'])->first();
    }

    /**
     * @param Request $request
     * @return bool|null
     * @throws \Exception
     */
    function deleteRateHotelAdult(Request $request)
    {
        $Data = $request->input();
        return Rates::where('ID', intval($Data['ID']))->delete();
    }


    /**
     * @param Request $request
     * @return RatesChild|\Illuminate\Database\Eloquent\Model|null|object
     */
    function updateRateHotelChild(Request $request)
    {
        $Data = $request->input();
        $UpdateStatus = RatesChild::where('ID', $Data['ID'])->update([
            'rate' => $Data['rate'],
            'updated_by' => \Auth::user()->id
        ]);
        return RatesChild::where('ID', $Data['ID'])->first();
    }

    /**
     * @param Request $request
     * @return bool|null
     * @throws \Exception
     */
    function deleteRateHotelChild(Request $request)
    {
        $Data = $request->input();
        return RatesChild::where('ID', $Data['ID'])->delete();
    }


    /**
     * @param Request $request
     * @return mixed
     */
    function addRateHotel(Request $request)
    {
        $Data = $request->input();

        $rules = [
            "hotel" => "required|exists:apple_hotels,ID",
            "from" => "required|date",
            "to" => "required|date",
            "pax_type" => "required",
            "meal_type" => "required|exists:apple_meal_plan,ID",
            "room_type" => "required|exists:apple_hotel_room_type,ID",
            "category" => "required|exists:apple_hotel_room_category,ID",
            "market" => "required|exists:apple_market,ID",
            "rate" => "required"

        ];


        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = 'invalid';
            return $Status;
        }

        $ReturnStatus = false;
        $Data['pax_type'] = intval($Data['pax_type']);

        if ($Data['pax_type'] == 1) {//adult


            $StartDate = Carbon::parse($Data['from']);
            $EndDate = Carbon::parse($Data['to']);


            $Rates = new Rates();
            $Rates->start_year = $StartDate->year;
            $Rates->start_month = $StartDate->month;
            $Rates->start_day = $StartDate->day;

            $Rates->end_year = $EndDate->year;
            $Rates->end_month = $EndDate->month;
            $Rates->end_day = $EndDate->day;

            $Rates->meal = $Data['meal_type'];
            $Rates->hotel = $Data['hotel'];
            $Rates->rate = $Data['rate'];
            $Rates->room_type = $Data['room_type'];
            $Rates->room_category = $Data['category'];
            $Rates->market = $Data['market'];
            $Rates->created_by = \Auth::user()->id;

            $ReturnStatus = $Rates->save();


        } elseif ($Data['pax_type'] == 2) {

            $StartDate = Carbon::parse($Data['from']);
            $EndDate = Carbon::parse($Data['to']);


            $Rates = new RatesChild();
            $Rates->start_year = $StartDate->year;
            $Rates->start_month = $StartDate->month;
            $Rates->start_day = $StartDate->day;

            $Rates->end_year = $EndDate->year;
            $Rates->end_month = $EndDate->month;
            $Rates->end_day = $EndDate->day;

            $Rates->age_from = 2;
            $Rates->age_to = 12;

            $Rates->meal = $Data['meal_type'];
            $Rates->hotel = $Data['hotel'];
            $Rates->rate = $Data['rate'];
            $Rates->room_type = $Data['room_type'];
            $Rates->room_category = $Data['category'];
            $Rates->market = $Data['market'];
            $Rates->created_by = \Auth::user()->id;

            $ReturnStatus = $Rates->save();

        } elseif ($Data['pax_type'] == 3) {

            $StartDate = Carbon::parse($Data['from']);
            $EndDate = Carbon::parse($Data['to']);


            $Rates = new RatesChild();
            $Rates->start_year = $StartDate->year;
            $Rates->start_month = $StartDate->month;
            $Rates->start_day = $StartDate->day;

            $Rates->end_year = $EndDate->year;
            $Rates->end_month = $EndDate->month;
            $Rates->end_day = $EndDate->day;

            $Rates->age_from = 0;
            $Rates->age_to = 2;

            $Rates->meal = $Data['meal_type'];
            $Rates->hotel = $Data['hotel'];
            $Rates->rate = $Data['rate'];
            $Rates->room_type = $Data['room_type'];
            $Rates->room_category = $Data['category'];
            $Rates->market = $Data['market'];
            $Rates->created_by = \Auth::user()->id;

            $ReturnStatus = $Rates->save();
        }


        $Status['status'] = $ReturnStatus ? 'OK' : 'Error';
        $Status['error'] = $ReturnStatus;
        return $Status;


    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function importRateHotel(Request $request)
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '-1');


        $Errors = [];
        $FileRate = $request->file('file_rate');
        $AddRate = $request->input('add_to_system');


        if ($FileRate && $FileRate->isValid()) {

            $FilePath = $FileRate->path();
            $Excel = \App::make('excel');


            $Data = $Excel->load($FilePath, function ($reader) {
            });


            $DataArray = [];
            $DataArrayColumn = [];
            $DataArrayData = [];


            $DataArray = $Data->get()->toArray();


            foreach ($DataArray[0] as $ColumnName => $value)
                $DataArrayColumn[] = $ColumnName;

            foreach ($DataArray as $RowIndex => $Row) {

                $hasError = false;
                foreach ($Row as $ColumnName => $RowValue) {
                    if ($ColumnName === "start_date" || $ColumnName === "end_date") {#date

                        if (is_date($RowValue))
                            $DataArrayData[$RowIndex][] = Carbon::parse($RowValue)->format("Y-m-d");
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid date">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }

                    } #meals
                    elseif ($ColumnName === 'meal') {
                        if ($MealPlan = MealPlan::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $MealPlan->plan;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Meal>' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }

                    } #Hotels
                    elseif ($ColumnName === 'hotel') {
                        if ($Hotel = Hotel::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $Hotel->name;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Hotel ">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }
                    } #room type
                    elseif ($ColumnName === 'room_type') {
                        if ($RoomType = RoomType::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $RoomType->type;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Room type ">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }
                    } #Room catogory
                    elseif ($ColumnName === 'room_category') {
                        if ($RoomCategory = RoomCategory::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $RoomCategory->name;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Room Category">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }
                    } #Market
                    elseif ($ColumnName === 'market') {
                        if ($Market = Market::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $Market->market;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Market ">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }
                    } elseif ($ColumnName === 'special') {


                        if (trim($RowValue) == "Y")
                            $DataArrayData[$RowIndex][] = '<span class="text-success" ><i class="fas fa-check" ></i></span>';
                        elseif (trim($RowValue) == "N")
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" ><i class="fas fa-times" ></i></span>';
                        else {

                            $DataArrayData[$RowIndex][] = '<span class="text-danger">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName, ";
                        }
                    } elseif ($ColumnName === 'price') {
                        if(is_null($RowValue)) {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName, ";
                        } else {
                            $DataArrayData[$RowIndex][] = currency_format($RowValue);
                        }
                    } elseif ($ColumnName === 'child_with_bed') {
                        if(is_null($RowValue)) {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName, ";
                        } else {
                            $DataArrayData[$RowIndex][] = currency_format($RowValue);
                        }

                    } elseif ($ColumnName === 'child_with_no_bed') {
                        if(is_null($RowValue)) {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName, ";
                        } else {
                            $DataArrayData[$RowIndex][] = currency_format($RowValue);
                        }

                    }

                }
                if (!$hasError) {
                    unset($DataArrayData[$RowIndex]);
                }


            }


            if ($Errors) {
                return view('system.error.error-excel-upload', ["columns" => $DataArrayColumn, "rows" => $DataArrayData, 'Errors' => $Errors]);
            } else {

                if ($AddRate) {
                    $DataEvent = Event::fire(new ImportRateHotel($Data->all()))[0];

                    return view("system.success.success-bootstrap", ["Message" => "Rate has added. Update ID: " . $DataEvent['upload_id'] . "<br> Adult:" . $DataEvent['adult'] . ", CWB:" . $DataEvent['cwb'] . ", CNB:" . $DataEvent['cnb']]);

                }

                return view("system.success.success-bootstrap", ["Message" => "Finally! No Error Found!"]);
            }

        } else {
            return view("system.error.error-bootstrap", ["Message" => "Please input valid Excel file!"]);
        }

    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function importRateCruise(Request $request)
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '-1');


        $Errors = [];
        $FileRate = $request->file('file_rate');
        $AddRate = $request->input('add_to_system');


        if ($FileRate && $FileRate->isValid()) {

            $FilePath = $FileRate->path();
            $Excel = \App::make('excel');


            $Data = $Excel->load($FilePath, function ($reader) {
            });


            $DataArray = [];
            $DataArrayColumn = [];
            $DataArrayData = [];


            $DataArray = $Data->get()->toArray();


            foreach ($DataArray[0] as $ColumnName => $value)
                $DataArrayColumn[] = $ColumnName;

            foreach ($DataArray as $RowIndex => $Row) {

                $hasError = false;
                foreach ($Row as $ColumnName => $RowValue) {
                    if ($ColumnName === 'cruise') {
                        if ($Cruise = Cruise::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $Cruise->name;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Hotel ">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }
                    } #room type
                    else if ($ColumnName === "start_date" || $ColumnName === "end_date") {#date

                        if (is_date($RowValue))
                            $DataArrayData[$RowIndex][] = Carbon::parse($RowValue)->format("Y-m-d");
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid date">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }

                    } #meals
                    elseif ($ColumnName === 'meal') {
                        if ($MealPlan = CruiseMeal::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $MealPlan->plan;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Meal>' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }

                    } #Hotels
                    
                    elseif ($ColumnName === 'cabin_type') {
                        if ($RoomType = CruiseCabinType::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $RoomType->name;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Room type ">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }
                    } #Room catogory
                    elseif ($ColumnName === 'cabin_occupancy_type') {
                        if ($RoomCategory = CruiseOccupancyType::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $RoomCategory->type;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Room Category">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }
                    } #Market
                    elseif ($ColumnName === 'market') {
                        if ($Market = Market::where("ID", $RowValue)->first())
                            $DataArrayData[$RowIndex][] = $Market->market;
                        else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid Market ">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }
                    } elseif ($ColumnName === 'package') {
                        if ($Package = CruisePackage::where("ID", $RowValue)->first()) {
                            $DataArrayData[$RowIndex][] = $Package->package;
                        } else {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger" title="This is a invalid CruisePackage ">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName";
                        }

                        
                    } elseif ($ColumnName === 'price') {
                        if(is_null($RowValue)) {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName, ";
                        } else {
                            $DataArrayData[$RowIndex][] = currency_format($RowValue);
                        }
                    } elseif ($ColumnName === 'child_with_bed') {
                        if(is_null($RowValue)) {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName, ";
                        } else {
                            $DataArrayData[$RowIndex][] = currency_format($RowValue);
                        }
                    } elseif ($ColumnName === 'child_with_no_bed') {
                        if(is_null($RowValue)) {
                            $DataArrayData[$RowIndex][] = '<span class="text-danger">' . $RowValue . "</span>";
                            $hasError = true;
                            $Errors[] = "Has an error in row:" . ($RowIndex + 2) . " and column: $ColumnName, ";
                        } else {
                            $DataArrayData[$RowIndex][] = currency_format($RowValue);
                        }

                    }

                }
                if (!$hasError) {
                    unset($DataArrayData[$RowIndex]);
                }


            }


            if ($Errors) {
                return view('system.error.error-excel-upload', ["columns" => $DataArrayColumn, "rows" => $DataArrayData, 'Errors' => $Errors]);
            } else {

                if ($AddRate) {
                    $DataEvent = Event::fire(new ImportRateCruise($Data->all()))[0];

                    return view("system.success.success-bootstrap", ["Message" => "Rate has added. Update ID: " . $DataEvent['upload_id'] . "<br> Adult:" . $DataEvent['adult'] . ", CWB:" . $DataEvent['cwb'] . ", CNB:" . $DataEvent['cnb']]);

                }

                return view("system.success.success-bootstrap", ["Message" => "Finally! No Error Found!"]);
            }

        } else {
            return view("system.error.error-bootstrap", ["Message" => "Please input valid Excel file!"]);
        }

    }


    /**
     * @param Request $request
     * @return array
     */
    function userSearch(Request $request)
    {

        $Data = $request->input();

        $List = new User();

        if (!empty($Data['name'])) {//name
            $List = $List->where('name', 'like', '%' . $Data['name'] . '%')->orWhere('email', 'like', '%' . $Data['name'] . '%');
        }


        $List = $List->get();


        $HtmlList = [];

        foreach ($List as $UserItem) {

            if (!empty($Data['role']) && !$UserItem->roles()->where('id', $Data['role'])->first()) {
                continue;
            }
            $HtmlList[$UserItem->id] = View::make("admin.user.user-item", ['Data' => $UserItem])->render();
        }

        return $HtmlList;


    }

    /**
     * @param Request $request
     * @return mixed
     */
    function userUpdate(Request $request)
    {

        $rules = [
            "id" => "required|exists:users",
            "first_name" => "required",
            "last_name" => "required",
            "email" => "required|email",
            "role" => "required",
            "company" => "required",
            "position" => "required",
            "tel" => "",
            "nic" => ""

        ];

        $Data = $request->input();
        $ProPic = $request->file('pro_pic');
        $Validator = Validator::make($Data, $rules);


        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = 'invalid';
            return $Status;
        }
        try {

            $UserUpdate = User::find($Data['id']);
            $UserUpdate->name = trim($Data['first_name'] . ' ' . $Data['last_name']);
            $UserUpdate->email = $Data['email'];
            $UserUpdate->save();


            #PROfile
            $UpdateData['user'] = $Data['id'];
            $UpdateData['first_name'] = $Data['first_name'];
            $UpdateData['designation'] = $Data['position'];
            $UpdateData['company'] = $Data['company'];
            $UpdateData['last_name'] = $Data['last_name'];
            $UpdateData['nic'] = $Data['nic'];
            $UpdateData['dob'] = $Data['dob'];

            $ProfileUpdate = Profile::where('user', $Data['id']);

            if ($ProfileUpdate->first()) {
                Profile::where('user', $Data['id'])->update($UpdateData);
            } else {
                Profile::insert($UpdateData);
            }

            //ROle
            User::find($Data['id'])->roles()->detach();
            User::find($Data['id'])->roles()->attach($Data['role']);

            //profile picture
            if ($ProPic && $ProPic->isValid()) {
                //image upload
                \File::deleteDirectory(public_path('assets/image/user/' . $Data['id']));
                \Storage::disk('image')->put('/user/' . $Data['id'] . '/0.jpg', file_get_contents($request->file('pro_pic')));

                #profile
                User::where('id', $Data['id'])->update(['updated_at' => Carbon::now()]);

            }
            $img['1x'] = Image::getImageDirect($Data['id'], '1x', 'user', 1, User::find($Data['id'])->name)[0] . '?' . time();
            $img['2x'] = Image::getImageDirect($Data['id'], '2x', 'user', 1, User::find($Data['id'])->name)[0] . '?' . time();
            $img['3x'] = Image::getImageDirect($Data['id'], '3x', 'user', 1, User::find($Data['id'])->name)[0] . '?' . time();


            $Status['pro_pic'] = Image::getImageDirect($Data['id'], '3x', 'user', 1, $UserUpdate->first()->first_name, true)[0] . "?" . time();


            $Status['user'] = User::where('ID', $Data['id'])->first();
            $Status['status'] = 'OK';
            return $Status;
        } catch (\Exception $e) {
            $Status['error'] = $e->getMessage();
            $Status['status'] = 'Error';
            return $Status;
        }


    }

    /**
     * @param Request $request
     * @return mixed
     * @throws \Exception
     */
    function userAdd(Request $request)
    {


        $rules = [
            "first_name" => "required",
            "last_name" => "required",
            "email" => "required|email|unique:users",
            "password" => "required|min:3'",
            "password_confirmation" => "required|same:password",
            "role" => "required",
            "company" => "required",
            "position" => "required",
            "tel" => "",
            "nic" => ""

        ];

        $Data = $request->input();
        $ProPic = $request->file('pro_pic');
        $Validator = Validator::make($Data, $rules);


        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = 'invalid';
            return $Status;
        }

        DB::beginTransaction();
        try {


            $Data['name'] = $Data['first_name'] . ' ' . $Data['last_name'];
            $Data['password'] = bcrypt($Data['password']);


            $Status['user'] = User::create($Data);


            if ($ProPic && $ProPic->isValid()) {
                \Intervention\Image\Facades\Image::make($ProPic)->encode('jpg', 100)->save(public_path('assets/image/user/' . $Status['user']->first()->id . '/0.jpg'));
            }


            $Profile = new Profile();
            $Profile->user = $Status['user']->id;
            $Profile->first_name = $Data['first_name'];
            $Profile->designation = intval($Data['position']);
            $Profile->company = intval($Data['company']);
            $Profile->last_name = $Data['last_name'];
            $Profile->nic = $Data['nic'];
            $Profile->dob = '1991-04-12';
            $Profile->save();


            User::where('ID', $Status['user']->id)->first()->roles()->detach();
            User::where('ID', $Status['user']->id)->first()->roles()->attach($Data['role']);

            DB::commit();


            $Status['status'] = 'OK';
            return $Status;
        } catch (\Exception $e) {

            DB::rollback();
            $Status['error'] = $e->getMessage();
            $Status['status'] = 'Error';
            return $Status;
        }


    }

    /**
     * @param Request $request
     * @return mixed
     */
    function roleRegister(Request $request)
    {


        $rules = [
            "name" => "required",
            "description" => "required"
        ];

        $Data = $request->input();
        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = 'invalid';
            return $Status;
        }


        try {


            $owner = new Role();
            $owner->name = strtolower(str_replace(' ', '_', $Data['name']));
            $owner->display_name = $Data['name']; // optional
            $owner->description = $Data['description'];

            $Status['user'] = $owner->save();
            $Status['status'] = 'OK';
            return $Status;
        } catch (\Exception $e) {
            $Status['error'] = $e->getMessage();
            $Status['status'] = 'Error';
            return $Status;
        }
    }


    /**
     * @param Request $request
     * @return mixed
     */
    function permissionRegister(Request $request)
    {


        $rules = [
            "name" => "required",
            "description" => "required"
        ];

        $Data = $request->input();
        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = 'invalid';
            return $Status;
        }


        try {

            $Permission = new Permission();
            $Permission->name = strtolower(str_replace(' ', '_', $Data['name']));
            $Permission->display_name = $Data['name']; // optional
            $Permission->description = $Data['description'];
            $Permission->save();


            $Status['user'] = $Permission->save();
            $Status['status'] = 'OK';
            return $Status;
        } catch (\Exception $e) {
            $Status['error'] = $e->getMessage();
            $Status['status'] = 'Error';
            return $Status;
        }
    }


    /**
     * @param Request $request
     * @return mixed
     */
    function addPermissionToRole(Request $request)
    {

        $Data = $request->input();
        $Role = Role::find($Data['RoleID']);
        $Permission = Permission::find($Data['PermissionID']);
        $OptionStatus = $Data['Status'];


        try {
            if ($OptionStatus == 'true') {
                $Status['user'] = $Role->attachPermission($Permission);
                $Status['status'] = 'Permission assigned to the ' . $Role->display_name;
                return $Status;
            } else {
                $Status['user'] = $Role->detachPermission($Permission);
                $Status['status'] = 'Permission removed from the ' . $Role->display_name;
                return $Status;
            }


        } catch (\Exception $e) {
            $Status['error'] = $e->getMessage();
            $Status['status'] = 'Error';
            return $Status;
        }


    }


    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function newHotelAdmin()
    {

        $places = Place::all();
        $classes = HotelClass::all();
        $obj = new Hotel();

        $hotels = $obj
            ->join('apple_hotel_class', 'apple_hotel_class.ID', '=', 'apple_hotels.class')
            ->join('apple_places', 'apple_places.ID', '=', 'apple_hotels.city')
            ->select('apple_hotel_class.class as hotel_class', 'apple_hotels.*', 'apple_places.name as place')
            ->get();

        return View('admin.blade.php/home', ['page' => 'Hotel.hotels', 'classes' => $classes, 'places' => $places, 'hotels' => $hotels]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function newHotelRatesAdmin()
    {

        $hotels = Hotel::all();
        $meal = MealPlan::all();
        $room_type = Room::all();
        $room_category = RoomCategory::all();
        $market = Market::all();


        return View('admin.blade.php/home', ['page' => 'Hotel.new_rates', 'hotels' => $hotels, 'meals' => $meal, 'room_types' => $room_type, 'room_category' => $room_category, 'markets' => $market]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelRatesAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'Hotel.view_rates']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getAllotmentAdmin()
    {

        $hotels = Hotel::all();
        $allotments = Allotment::select(DB::raw('CONCAT(start_year, "/", start_month,"/", start_day) AS start_date'), 'ID')
            ->join('apple_hotels', 'apple_hotels.ID', '=', 'apple_hotel_room_allotment.hotel')
            ->select('apple_hotel_room_allotment.*', 'apple_hotels.name as hotel_name')
            ->get();

        return View('admin.blade.php/home', ['page' => 'Hotel.allotment', 'hotels' => $hotels, 'allotments' => $allotments]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getSupplementAdmin()
    {

        $hotels = Hotel::all();
        $meal = MealPlan::all();
        $supplements = Supplement::all();

        return View('admin.blade.php/home', ['page' => 'Hotel.supplement', 'hotels' => $hotels, 'meals' => $meal, 'supplements' => $supplements]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getStopSalesAdmin()
    {

        $hotels = Hotel::all();

        return View('admin.blade.php/home', ['page' => 'Hotel.stop_sale', 'hotels' => $hotels]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getRoomCategoryAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'Hotel.room_category']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getRoomTypeAdmin()
    {

        return View('admin.home', ['page' => 'Hotel.room_type']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getOutsideRestaurantAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'Hotel.outside_restaurant']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelClassAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'Hotel.hotel_class']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getPerTypeAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'Hotel.per_type']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getHotelContactAdmin()
    {

        $hotels = Hotel::all();
        $contact_type = ContactType::all();
        return View('admin.blade.php/home', ['page' => 'Hotel.hotel_contact', 'hotels' => $hotels, 'types' => $contact_type]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getMarkupAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'Hotel.markup']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getAttractionAdmin()
    {

        $places = Place::all();
        $markets = Market::all();

        return View('admin.blade.php/home', ['page' => 'Attraction.attraction', 'places' => $places, 'markets' => $markets]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getAttractionRateAdmin()
    {

        $attraction = Attraction::all();
        $markets = Market::all();

        return View('admin.blade.php/home', ['page' => 'Attraction.attraction_rate', 'attractions' => $attraction, 'markets' => $markets]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getAdditionalCostAdmin()
    {

        $attraction = Attraction::all();
        $markets = Market::all();

        return View('admin.blade.php/home', ['page' => 'Attraction.attraction_rate', 'attractions' => $attraction, 'markets' => $markets]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function newUserAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'user.user']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getAllUsersAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'user.user_profile']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getCompanyAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'user.company']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getDesignationAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'user.designation']);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getAgentsAdmin()
    {

        return View('admin.blade.php/home', ['page' => 'user.agents']);

    }

    /**
     * @param Request $request
     * @return int
     */
    function createNewHotel(Request $request)
    {

        $this->validate($request, [

            'class' => 'required',
            'place' => 'required',
            'name' => 'required',

        ]);

        $hotel = new Hotel();

        $hotel->name = $request->get('name');
        $hotel->class = $request->get('class');
        $hotel->city = $request->get('place');
        $hotel->longitude = $request->get('longitude');
        $hotel->latitude = $request->get('latitude');
        $hotel->description = $request->get('description');

        if ($hotel->save()) {

            return 1;

        } else {

            return 0;
        }

    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelRate(Request $request)
    {

        $this->validate($request, [

            'active_from' => 'required',
            'active_to' => 'required',
            'meal' => 'required',
            'hotel' => 'required',
            'price' => 'required',
            'room_type' => 'required',
            'room_category' => 'required',
            'market' => 'required',

        ]);

        $date_start = $request->active_from;
        $date_end = $request->active_to;

        $start_dt = Carbon::parse($date_start);
        $end_dt = Carbon::parse($date_end);

        $st_yr = $start_dt->year;
        $st_mnth = $start_dt->month;
        $st_dt = $start_dt->day;

        $end_yr = $end_dt->year;
        $end_mnth = $end_dt->month;
        $end_dt = $end_dt->day;

        $rate = new Rates();

        $rate->start_year = $st_yr;
        $rate->start_month = $st_mnth;
        $rate->start_day = $st_dt;
        $rate->end_year = $end_yr;
        $rate->end_month = $end_mnth;
        $rate->end_day = $end_dt;
        $rate->meal = $request->meal;
        $rate->hotel = $request->hotel;
        $rate->rate = $request->price;
        $rate->room_type = $request->room_type;
        $rate->room_category = $request->room_category;
        $rate->market = $request->market;

        if ($rate->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelChildRate(Request $request)
    {

        $this->validate($request, [

            'active_from' => 'required',
            'active_to' => 'required',
            'age_from' => 'required',
            'age_to' => 'required',
            'meal' => 'required',
            'hotel' => 'required',
            'price' => 'required',
            'room_type' => 'required',
            'room_category' => 'required',
            'market' => 'required',

        ]);

        $date_start = $request->active_from;
        $date_end = $request->active_to;

        $start_dt = Carbon::parse($date_start);
        $end_dt = Carbon::parse($date_end);

        $st_yr = $start_dt->year;
        $st_mnth = $start_dt->month;
        $st_dt = $start_dt->day;

        $end_yr = $end_dt->year;
        $end_mnth = $end_dt->month;
        $end_dt = $end_dt->day;

        $rate = new RatesChild();

        $rate->start_year = $st_yr;
        $rate->start_month = $st_mnth;
        $rate->start_day = $st_dt;
        $rate->end_year = $end_yr;
        $rate->end_month = $end_mnth;
        $rate->end_day = $end_dt;
        $rate->age_from = $request->age_from;
        $rate->age_to = $request->age_to;
        $rate->meal = $request->meal;
        $rate->hotel = $request->hotel;
        $rate->rate = $request->price;
        $rate->room_type = $request->room_type;
        $rate->room_category = $request->room_category;
        $rate->market = $request->market;

        if ($rate->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelRoomAllotment(Request $request)
    {

        $this->validate($request, [

            'active_from' => 'required',
            'active_to' => 'required',
            'room' => 'required',
            'release_period' => 'required',
            'hotel' => 'required',

        ]);

        $date_start = $request->active_from;
        $date_end = $request->active_to;

        $start_dt = Carbon::parse($date_start);
        $end_dt = Carbon::parse($date_end);

        $st_yr = $start_dt->year;
        $st_mnth = $start_dt->month;
        $st_dt = $start_dt->day;

        $end_yr = $end_dt->year;
        $end_mnth = $end_dt->month;
        $end_dt = $end_dt->day;

        $allotment = new Allotment();

        $allotment->start_year = $st_yr;
        $allotment->start_month = $st_mnth;
        $allotment->start_day = $st_dt;
        $allotment->end_year = $end_yr;
        $allotment->end_month = $end_mnth;
        $allotment->end_day = $end_dt;
        $allotment->hotel = $request->hotel;
        $allotment->release_period = $request->release_period;
        $allotment->room = $request->room;

        if ($allotment->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelSupplement(Request $request)
    {

        $this->validate($request, [

            'active_from' => 'required',
            'active_to' => 'required',
            'hotel' => 'required',
            'meal_type' => 'required',

        ]);

        $date_start = $request->active_from;
        $date_end = $request->active_to;

        $start_dt = Carbon::parse($date_start);
        $end_dt = Carbon::parse($date_end);

        $st_yr = $start_dt->year;
        $st_mnth = $start_dt->month;
        $st_dt = $start_dt->day;

        $end_yr = $end_dt->year;
        $end_mnth = $end_dt->month;
        $end_dt = $end_dt->day;

        $allotment = new Supplement();

        $allotment->start_year = $st_yr;
        $allotment->start_month = $st_mnth;
        $allotment->start_day = $st_dt;
        $allotment->end_year = $end_yr;
        $allotment->end_month = $end_mnth;
        $allotment->end_day = $end_dt;
        $allotment->hotel = $request->hotel;
        $allotment->adult_rate = $request->adult_rate;
        $allotment->child_rate = $request->child_rate;
        $allotment->additional = $request->additional;

        if ($allotment->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelStopSales(Request $request)
    {

        $this->validate($request, [

            'active_from' => 'required',
            'active_to' => 'required',
            'hotel' => 'required',

        ]);

        $date_start = $request->active_from;
        $date_end = $request->active_to;

        $start_dt = Carbon::parse($date_start);
        $end_dt = Carbon::parse($date_end);

        $st_yr = $start_dt->year;
        $st_mnth = $start_dt->month;
        $st_dt = $start_dt->day;

        $end_yr = $end_dt->year;
        $end_mnth = $end_dt->month;
        $end_dt = $end_dt->day;

        $allotment = new Supplement();

        $allotment->start_year = $st_yr;
        $allotment->start_month = $st_mnth;
        $allotment->start_day = $st_dt;
        $allotment->end_year = $end_yr;
        $allotment->end_month = $end_mnth;
        $allotment->end_day = $end_dt;
        $allotment->hotel = $request->hotel;

        if ($allotment->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newRoomCategory(Request $request)
    {

        $this->validate($request, [

            'category' => 'required',

        ]);

        $category = new RoomCategory();

        $category->name = $request->category;

        if ($category->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newRoomType(Request $request)
    {

        $this->validate($request, [

            'room_type' => 'required',
            'short_name' => 'required',

        ]);

        $type = new RoomType();

        $type->type = $request->type;
        $type->short_name = $request->short_name;

        if ($type->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newOutsideRestaurant(Request $request)
    {

        $this->validate($request, [

            'type' => 'required',
            'cost' => 'required',

        ]);

        $type = new OutsideRestaurant();

        $type->type = $request->type;
        $type->cost = $request->cost;

        if ($type->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelClass(Request $request)
    {

        $this->validate($request, [

            'class' => 'required',
            'star' => 'required',

        ]);

        $type = new HotelClass();

        $type->class = $request->class;
        $type->star = $request->star;

        if ($type->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelContact(Request $request)
    {

        $this->validate($request, [

            'hotel' => 'required',
            'type' => 'required',
            'contact_id' => 'required',

        ]);

        $contact = new Contact();

        $contact->hotel = $request->hotel;
        $contact->type = $request->type;
        $contact->contact_id = $request->type;
        $contact->note = $request->note;

        if ($contact->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newHotelMarkup(Request $request)
    {

        $this->validate($request, [

            'company' => 'required',
            'markup' => 'required',

        ]);

        $contact = new Contact();

        $contact->hotel = $request->hotel;
        $contact->type = $request->type;
        $contact->contact_id = $request->type;
        $contact->note = $request->note;

        if ($contact->save()) {

            return 1;
        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newAttraction(Request $request)
    {

        $this->validate($request, [

            'place' => 'required',
            'child_rate' => 'required',
            'adult_rate' => 'required',
            'market' => 'required',

        ]);

        $attraction = new Attraction();

        $attraction->place = $request->place;
        $attraction->address = $request->address;
        $attraction->description = $request->description;
        $attraction->point = $request->point;
        $attraction->name = $request->name;
        $attraction->operning = $request->operning;
        $attraction->closing = $request->closing;
        $attraction->distance = $request->distance;
        $attraction->time = $request->time;
        $attraction->longitude = $request->longitude;
        $attraction->latitude = $request->latitude;

        if ($attraction->save()) {

            $attr_rate = new AttractionRate();

            $attr_rate->attraction = $attraction->id;
            $attr_rate->market = $request->market;
            $attr_rate->adult = $request->adult_rate;
            $attr_rate->child = $request->child_rate;

            if ($attr_rate->save()) {

                return 1;

            } else {

                return 0;
            }

        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     */
    function newAttractionRate(Request $request)
    {

        $this->validate($request, [

            'attraction' => 'required',
            'child_rate' => 'required',
            'adult_rate' => 'required',
            'market' => 'required',

        ]);

        $attraction_rate = new AttractionRate();

        $attraction_rate->attraction = $request->attraction;
        $attraction_rate->adult = $request->adult_rate;
        $attraction_rate->child = $request->child_rate;
        $attraction_rate->market = $request->market;


        if ($attraction_rate->save()) {

            return 1;

        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    function addHotel(Request $request)
    {


        $Validator = Validator::make($request->all(), [
            'name' => 'required',
            'place' => 'required',
            'class' => 'required',
            'description' => 'required',

            'lat' => 'required',
            'lng' => 'required',
            'hotelier' => 'required'
        ]);


        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()]);
        }

        $Hotel = new Hotel();
        $Hotel->city = $request->get('place');
        $Hotel->class = $request->get('class');
        $Hotel->name = $request->get('name');
        $Hotel->description = $request->get('description');
        $Hotel->latitude = $request->get('lat');
        $Hotel->longitude = $request->get('lng');
        $Hotel->provider = 1;
        $Hotel->cancellation_days = 14;
        $Hotel->preferred = empty($request->get('preferred')) ? 0 : 1;
        $Hotel->tripadvisor = $request->get('tripadvisor');
        $Hotel->save();

        return view("system.success.success-bootstrap", ["Message" => 'Hotel Has Added']);


    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    function manageAttraction(Request $request)
    {

        //sd($request->all());

        $Validator = Validator::make($request->all(), [
            'name' => 'required',
            'place' => 'required',
            'description' => 'required',
            'address' => 'required',

            'lat' => 'required',
            'lng' => 'required',

            'operning' => 'required',
            'closing' => 'required',

            'distance' => 'required',
            'duration' => 'required',

            'saarc_adult' => 'required',
            'saarc_child' => 'required',
            'non_saarc_adult' => 'required',
            'non_saarc_child' => 'required',
        ]);


        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()]);
        }

        if (!empty($request->input('attr_id'))) {


            Attraction::where('ID', $request->input('attr_id'))->update([
                'place' => $request->input('place'),
                'duration' => $request->input('duration'),
                'address' => $request->input('address'),
                'description' => $request->input('description'),
                'name' => $request->input('name'),
                'operning' => $request->input('operning'),
                'closing' => $request->input('closing'),
                'distance' => $request->input('distance'),
                'longitude' => $request->input('lng'),
                'latitude' => $request->input('lat')
            ]);

            AttractionRate::where("attraction", $request->input('attr_id'))->where("market", 1)->update([
                'adult' => $request->input('saarc_adult'),
                'child' => $request->input('saarc_child')
            ]);
            AttractionRate::where("attraction", $request->input('attr_id'))->where("market", 2)->update([
                'adult' => $request->input('non_saarc_adult'),
                'child' => $request->input('non_saarc_child')
            ]);


        } else {
            #details
            $Attraction = new Attraction();
            $Attraction->place = $request->input('place');
            $Attraction->duration = $request->input('duration');
            $Attraction->address = $request->input('address');
            $Attraction->description = $request->input('description');
            $Attraction->name = $request->input('name');
            $Attraction->operning = $request->input('operning');
            $Attraction->closing = $request->input('closing');
            $Attraction->distance = $request->input('distance');
            $Attraction->time = $request->input('time');
            $Attraction->longitude = $request->input('longitude');
            $Attraction->latitude = $request->input('latitude');
            $Attraction->save();

            #Rates
            #saark
            $AttractionRate = new AttractionRate();
            $AttractionRate->attraction = $Attraction->id;
            $AttractionRate->market = 1;
            $AttractionRate->adult = $request->input('saarc_adult');
            $AttractionRate->child = $request->input('saarc_child');
            $AttractionRate->save();

            #non saark
            $AttractionRate = new AttractionRate();
            $AttractionRate->attraction = $Attraction->id;
            $AttractionRate->market = 2;
            $AttractionRate->adult = $request->input('non_saarc_adult');
            $AttractionRate->child = $request->input('non_saarc_child');
            $AttractionRate->save();
        }


    }


    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    function manageExtraMileage(Request $request)
    {

        $Validator = Validator::make($request->all(), [
            'mileage' => 'required',
            'extra' => 'required',
            'city' => 'required'
        ]);


        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()]);
        }


        if (ExtraMileage::where('city', $request->input('city'))->first()) {

            ExtraMileage::where('city', $request->input('city'))->update([
                "city" => $request->input('city'),
                "mileage" => $request->input('mileage'),
                "extra" => $request->input('extra')
            ]);

            return view("system.success.success-bootstrap", ["Message" => "Updated"]);
        } else {

            $ExtraMileage = new ExtraMileage();
            $ExtraMileage->city = $request->input('city');
            $ExtraMileage->mileage = $request->input('mileage');
            $ExtraMileage->extra = $request->input('extra');
            $ExtraMileage->save();


            return view("system.success.success-bootstrap", ["Message" => "Added"]);

        }

    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    function addRestaurant(Request $request)
    {


        $Validator = Validator::make($request->all(), [
            'name' => "required",
            'city' => "required",
            'expensive' => "required",
            'description' => "required",
            'restaurant_contact' => "required",
            'restaurant_tel' => "required",
            'restaurant_email' => "required",
            'address' => "required",
            'web' => "required",
            'sales_person' => "required",
            'tel' => "required",
            'sales_email' => "required"
        ]);


        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()], 500);
        }

        try {

            #details
            $Restaurant = new Restaurant();
            $Restaurant->place = $request->input('city');
            $Restaurant->name = $request->input('name');
            $Restaurant->expensive = $request->input('expensive');
            $Restaurant->description = $request->input('description');
            $Restaurant->remarks = $request->input('remarks');
            $Restaurant->sales_person = $request->input('sales_person');
            $Restaurant->tel = $request->input('tel');
            $Restaurant->email = $request->input('sales_email');
            $Restaurant->restaurant_contact = $request->input('restaurant_contact');
            $Restaurant->restaurant_tel = $request->input('restaurant_tel');
            $Restaurant->restaurant_email = $request->input('restaurant_email');
            $Restaurant->address = $request->input('address');
            $Restaurant->web = $request->input('web');
            $Restaurant->save();

            return response()->view("system.success.success-bootstrap", ['Message' => "Restaurant id: " . $Restaurant->id]);

        } catch (\Exception $e) {
            return response()->view("system.error.error-bootstrap", ['Message' => $e->getMessage()], 500);
        }


    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    function addRestaurantRate(Request $request)
    {


        $Validator = Validator::make($request->all(), [
            'restaurant' => "required",
            'meal_time' => "required",
            'rate' => "required",
            'from' => "required",
            'to' => "required"
        ]);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()], 500);
        }


        try {

            $Company = \Auth::user()->Profile()->first()->company;


            if ($Company == 1) {//if appleholidays


                $RestaurantMealRate = new RestaurantMealRate();
                $RestaurantMealRate->restaurant = $request->input('restaurant');
                $RestaurantMealRate->meal_time = $request->input('meal_time');
                $RestaurantMealRate->rate = $request->input('rate');
                $RestaurantMealRate->from = $request->input('from');
                $RestaurantMealRate->user = \Auth::user()->id;
                $RestaurantMealRate->to = $request->input('to');
                $RestaurantMealRate->save();

                return response()->view("system.success.success-bootstrap", ['Message' => "New offer Added!"]);


            } else {

                $RestaurantMealRateTemp = new RestaurantMealRateTemp();
                $RestaurantMealRateTemp->restaurant = $request->input('restaurant');
                $RestaurantMealRateTemp->meal_time = $request->input('meal_time');
                $RestaurantMealRateTemp->rate = $request->input('rate');
                $RestaurantMealRateTemp->from = $request->input('from');
                $RestaurantMealRateTemp->to = $request->input('to');
                $RestaurantMealRateTemp->user = \Auth::user()->id;
                $RestaurantMealRateTemp->save();

                return response()->view("system.success.success-bootstrap", ['Message' => "New offer Added!"]);


            }


        } catch (\Exception $e) {
            return response()->view("system.error.error-bootstrap", ['Message' => $e->getMessage()], 500);
        }

    }

    /**
     * @param $ID
     * @return RestaurantMealRateTemp|\Illuminate\Database\Eloquent\Model|null|object
     * @throws \Exception
     */
    function acceptRestaurantRate($ID)
    {

        if ($ConfirmRow = RestaurantMealRateTemp::where("ID", $ID)->first()) {

            $RestaurantMealRate = new RestaurantMealRate();
            $RestaurantMealRate->restaurant = $ConfirmRow->restaurant;
            $RestaurantMealRate->meal_time = $ConfirmRow->meal_time;
            $RestaurantMealRate->rate = $ConfirmRow->rate;
            $RestaurantMealRate->from = $ConfirmRow->from;
            $RestaurantMealRate->user = $ConfirmRow->user;
            $RestaurantMealRate->to = $ConfirmRow->to;
            $RestaurantMealRate->save();

            RestaurantMealRateTemp::where("ID", $ID)->delete();
        }

        return $ConfirmRow;

    }

    /**
     * @param $Table
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getTableComparedDetails($Table)
    {

        return view("admin.database.table-compare-details", ['Table' => $Table]);


    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    function getHotelbedsBookings(Request $request)
    {


        $Validator = Validator::make($request->all(), [
            'reference' => "required|exists:apple_quotation,ID",
            'start' => "required|date",
            'end' => "required|date",
            'status' => "required",
            'filterType' => "required",
            'limit' => "required|numeric|min:1|max:100"
        ]);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()], 500);
        }

        $HotelBed = new HotelBed();
        $BookingList = $HotelBed->BookingList($request->input('start'), $request->input('end'), $request->input('filterType'),request('reference'));

        if (!$BookingList or empty($BookingList['bookings'])) {
            return response()->view("system.error.error-bootstrap", ['Message' => $HotelBed->ErrorMsg], 500);
        } else {
            foreach ($BookingList['bookings'] as $BookingItem) {
                $Data = $HotelBed->getBookDetails($BookingItem['reference']);

                if($Data['status'] == false){
                    $msg = $Data['result']['error']['code']." | ".$Data['result']['error']['message'];
                    return response()->view("system.error.error-bootstrap", ['Message' => $msg], 500);
                } else {
                    echo View::make("element.hotel.hotelbed.proforma-invoice",['Data' => $Data, 'Comment' => ""])->render();
                    echo View::make("element.hotel.hotelbed.hotel-booking-voucher",['Data' => $Data, 'Comment' => ""])->render();
                }
            }
        }

    }


    /**
     * @param $UploadID
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function viewImportHotelRate($UploadID)
    {

        return view("admin.hotel.view-rate-table", ['UploadID' => $UploadID]);

    }

    /**
     * @param $UploadID
     * @throws \Exception
     */
    function deleteImportHotelRate($UploadID)
    {

        Rates::where('upload_id', $UploadID)->delete();
        RatesChild::where('upload_id', $UploadID)->delete();

    }


        /**
     * @param $UploadID
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function viewImportCruiseRate($UploadID)
    {

        return view("admin.cruise.view-rate-table", ['UploadID' => $UploadID]);

    }

    /**
     * @param $UploadID
     * @throws \Exception
     */
    function deleteImportCruiseRate($UploadID)
    {

        CruiseCabinRate::where('upload_id', $UploadID)->delete();
        CruiseCabinRateChild::where('upload_id', $UploadID)->delete();

    }

    //chart

    /**
     * @return array
     */
    function getDashboardChartData()
    {

        $Data = request()->all();
        $Type = false;
        $Date = Carbon::parse($Data['date'] ?? Carbon::now()->toDateString());
        $returnData = [];

        switch ($Data['type']) {

            case 'DAILY':
                $Type = Reports::DAILY;
                break;

            case 'MONTHLY':
                $Type = Reports::MONTHLY;
                break;

            case 'YEARLY':
                $Type = Reports::YEARLY;
                break;

            case 'ALL_THE_TIME':
                $Type = Reports::ALl_THE_TIME;
                break;

        }

        $ReportData = Reports::getQuotationReport($Type, $Date, $Data['user'] ?? false);

        $returnData['statics'] = Reports::getQuotationReportUserCount($Type, $Date, $Data['user'] ?? false);

        //return $returnData['statics'];

        $returnData['total']['confirm'] = $returnData['statics']->sum('count_confirm');
        $returnData['total']['pending'] = $returnData['statics']->sum('count_pending');
        $returnData['total']['cancel'] = $returnData['statics']->sum('count_cancel');


        $returnData['data'] = Reports::quotationReportFormatter($ReportData, $Type);

        return $returnData;

    }

    /**
     * @param $id
     * @return string
     * @throws \Exception
     */
    function Login($id)
    {
        if (\Entrust::can('manage_admin')) {//permission
            \Auth::loginUsingId($id);
            return \URL::to('/');
        } else {
            throw new \Exception("You don't have permission!");
        }
    }


    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    function sendNotice()
    {

        $rules = [
            "notice" => "required",
            "type" => "required",

        ];

        $Validator = Validator::make(\request()->all(), $rules);

        if ($Validator->fails()) {
            return view("system.error.error-ajax", ["Message" => $Validator->messages()->toArray()]);

        }

        $Notice = new Notice();
        $Notice->note = request('notice');
        $Notice->type = request('type');
        $Notice->save();

        if (request('user')) {

            foreach ((request('user') ?? []) as $UserID) {

                $NoticeUser = new NoticeUser();
                $NoticeUser->user = $UserID;
                $NoticeUser->notice_id = $Notice->id;
                $NoticeUser->save();
            }


        }

        event(new \App\Events\Notice(['id'=>$Notice->id]));
        return "Successfully sent!";

    }

    /**
     * @return array
     */
    function getNotice()
    {

        $User = \Auth::user();

        $UnreadNotice = Notice::getUnread($User);
        $Data = [
            'status'=> false,
            'html'=>''
        ];

        foreach($UnreadNotice as $NoticeItem){

            $Data['id_list'][] = $NoticeItem->id;
            $Data['html'] .= ($NoticeItem->note);
            $Data['status'] = true;

            $Data['html'] .= '<br><br>';

            $Data['html'] .= '<div class="text-right text-muted">'.$NoticeItem->created_at->toDayDateTimeString().' by '.$NoticeItem->by()->first()->name.'</div>';
            $Data['html'] .= '<hr>';
        }


        return $Data;

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    function readNotice()
    {

        $rules = [
            "notice_ids" => "required|array",
            "notice_ids.*" => "required|exists:notice,id",

        ];

        $Validator = Validator::make(\request()->all(), $rules);

        if ($Validator->fails()) {
            return view("system.error.error-ajax", ["Message" => $Validator->messages()->toArray()]);

        }
        foreach(\request()->get('notice_ids') as $id){
            $NoticeRead = new NoticeRead();
            $NoticeRead->user = \Auth::user()->id;
            $NoticeRead->notice_id = $id;
            $NoticeRead->save();
        }

        return "Done";

    }

    /**
     * @param int $user
     * @return void
     */
    function getTreeItems($user = 1){
        $UserHierarchy = new UserHierarchy();
        foreach($UserHierarchy->getUnder(User::find($user)) as $userItem){
            echo View::make('admin.user.user-tree-items',['user'=>User::find($userItem)])->render();
        }
    }

    function addStaticPackage(Request $request)

    {
        $static_package_details = $request->all();

        $rules = [
            "name"                   => "required",
            "description"            => "required",
            "number_of_nights"       => "required",
            "validity_period_start"  => "required",
            "validity_period_end"    => "required",
            "adult"                  => "required",
            "cwb"                    => "required",
            "cnb"                    => "required" ];

        $Validator = Validator::make( $static_package_details , $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error']  = $messages;
            $Status['status'] = 'invalid';
            return $Status;

        } else {

            $static_package                        = new StaticPackages();
            $static_package->name                  = $static_package_details['name'];
            $static_package->description           = $static_package_details['description'];
            $static_package->number_of_nights      = $static_package_details['number_of_nights'];
            $static_package->validity_period_start = $static_package_details['validity_period_start'];
            $static_package->validity_period_end   = $static_package_details['validity_period_end'];
            $static_package->adult                 = $static_package_details['adult'];
            $static_package->cwb                   = $static_package_details['cwb'];
            $static_package->cnb                   = $static_package_details['cnb'];
            $static_package->save();

            $img = $request->file('static_package_image');

            if($img && $img->isValid()) {
                \Storage::disk('image')->put('/static_packages/'.$static_package->id.'/0.jpg', file_get_contents($img));
            }

            $images['4x']  = Image::getImageDirect($static_package->id, '4x', 'static_packages', 1, $static_package->id)[0] . '?' . time();
            return response()->json(['success' => true]);
        }
    }

    static function getLowestRateRoomCategory($hotelId, $startDate, $endDate)
    {

        $lowestRates = DB::table('apple_hotel_room_rates')->select('*');

        $lowestRates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $startDate);
        $lowestRates->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $endDate);

        $lowestRates->where('hotel', $hotelId);

        $lowestRates->groupBy('room_category');
        $lowestRates->orderBy('rate', 'asc'); // order by lowest rates
        $lowestRates->whereNull('deleted_at');

        $result = $lowestRates->get();

        return $result;
    }

    static function getLowestRateCruiseCabinCategory($cruiseId, $startDate, $endDate)
    {

        $lowestRates = DB::table('apple_cruise_cabin_rates')->select('*');

        $lowestRates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $startDate);
        $lowestRates->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $endDate);

        $lowestRates->where('cruise', $cruiseId);

        $lowestRates->groupBy('cabin_type');
        $lowestRates->orderBy('rate', 'asc'); // order by lowest rates

        $result = $lowestRates->get();

        return $result;
    }

    static function selectedRoomCategory($hotelId, $startDate, $endDate){

        $roomCategoryNames = [];

        $roomCategoryId = Admin::getLowestRateRoomCategory($hotelId, $startDate, $endDate);

        foreach ($roomCategoryId as $key1 => $roomCatId){
            $id = $roomCatId->room_category;
            $roomCategoryNames[$id] = RoomCategory::find($id)->name;
        }

        $array = $roomCategoryNames;

        reset($array);

        $first_key = key($array);

        $data = [
            $roomCategoryNames,
            $first_key
        ];

        return $data;
    }

    public function uploadHotelImage(Request $request) {

        $rules = [
            "hotel_id" => "required",
        ];

        $Validator = Validator::make($request->toArray() , $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error']  = $messages;
            $Status['status'] = 'invalid';
            return $Status;

        } else {

            $img = $request->file('hotel_img');

            if($img && $img->isValid()) {
                \Storage::disk('image')->put('/hotel/'.$request->hotel_id.'/0.jpg', file_get_contents($img));
            }

//            $images['4x']  = Image::getImageDirect($request->hotel_id, '4x', 'hotel_images', 1, $request->hotel_id)[0];

            return response()->json(['success' => true]);
        }
    }

    public function uploadPlaceImage(Request $request) {

        $rules = [
            "place_id" => "required",
            "place_type" => "required",
            "place_img" => "required",
        ];

        $Validator = Validator::make($request->toArray() , $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error']  = $messages;
            $Status['status'] = 'invalid';
            return $Status;

        } else {

            $img = $request->file('place_img');

            if($img && $img->isValid()) {
                \Storage::disk('image')->put('/'.$request->place_type.'/'.$request->place_id.'/0.jpg', file_get_contents($img));
            }

            return response()->json(['success' => true]);
        }
    }

    function costData(Request $request) {
        $data = $request->all();
        $data['vehicle_name'] = VehicleType::find($data["vehicle_type"])->name;
        $data['citytour_name'] = CityTour::find($data["city_tour"])->name;

        $RowData = Costcut::where("city_tour", "=", $data['city_tour'])
            ->where("from", "=", $data['place_from'])
            ->where("to", "=", $data['place_to'])
            ->with(["Rate"])
            ->whereHas("Rate",  function($query) use ($data) {
                $query->where("vehicle_type", "=", $data['vehicle_type']);
            })->get();

        foreach ($RowData as $key => $Data) {
            $TempArray = explode("," ,$Data["template"]);
            $TemplateArray = [];
            foreach ($TempArray as $TempItem) {
                if(strpos($TempItem, "PP-") !== false) {
                    $TemplateArray[] = 1;
                }
                if(strpos($TempItem, "C-") !== false) {
                    $TemplateArray[] = 2;
                }
                if(strpos($TempItem, "MT-B") !== false) {
                    $TemplateArray[] = 3;
                }
                if(strpos($TempItem, "MT-L") !== false) {
                    $TemplateArray[] = 4;
                }
                if(strpos($TempItem, "MT-D") !== false) {
                    $TemplateArray[] = 5;
                }
            }
            $RowData[$key]['TemplateArray'] = $TemplateArray;
        }

        return view("admin.cost_cut.cost-pck-data", ['data' => $data, 'RowData' => $RowData]);
    }

    function saveCostData(Request $request) {
        $data = $request->all();

        foreach ($data["cost_packages"]??[] as $index => $pkg) {
            $template = "";
            $title = "";
            $price = 0;

            foreach ($pkg as $no => $item) {
                if($no == 1 && $item =="on") {
                    $template .= "[PP-".$data["place_from"]."/".$data["place_to"]."],";
                }
                if($no == 2 && $item =="on") {
                    $template .= "[C-".$data["city_tour"]."],";
                }
                if($no == 3 && $item =="on") {
                    $template .= "[MT-B],";
                }
                if($no == 4 && $item =="on") {
                    $template .= "[MT-L],";
                }
                if($no == 5 && $item =="on") {
                    $template .= "[MT-D],";
                }
                if($no == 6 && !empty($item)) {
                    $price = (int)$item;
                }
                if($no == 7 && !empty($item)) {
                    $title = $item;
                }

            }
            $template = rtrim($template, ",");

            if(isset($data["cost_id"][$index]) && !empty($data["cost_id"][$index])) {
                $referenceID = $data["cost_id"][$index];
                DB::table('apple_costcut')
                    ->where("ID",$data["cost_id"][$index])
                    ->update([
                        "title" => $title,
                        "template" => $template,
                        "city_tour" => $data["city_tour"],
                        "from" => $data["place_from"],
                        "to" => $data["place_to"],
                        "index" => $index
                    ]);

            } else {
                $Costcut = new Costcut();
                $Costcut->title = $title;
                $Costcut->template = $template;
                $Costcut->city_tour = $data["city_tour"];
                $Costcut->from = $data["place_from"];
                $Costcut->to = $data["place_to"];
                $Costcut->index = $index;
                $referenceID = $Costcut->save();

                $referenceID = $Costcut->id;
            }

            $this->saveCostRates($data, $price, $referenceID, $index);
        }

        return ["status"=>"1", "msg"=>"Successfully Added"];
    }

    function deleteCostData(Request $request) {
        $data = $request->all();

        $Costcut = Costcut::where("ID",$data["id"]);
        $Costcut->delete();
        $CostcutRate = CostcutRate::where("costcut",$data["id"]);
        $CostcutRate->delete();

        return ["status"=>"1", "msg"=>"Successfully Deleted"];
    }

    public function saveCostRates($data, $price, $referenceID, $index) {
        if(isset($data["cost_id"][$index]) && !empty($data["cost_id"][$index])) {
            DB::table('apple_costcut_rate')
                ->where("costcut",$referenceID)
                ->update([
                    "costcut" => $referenceID,
                    "vehicle_type" => $data["vehicle_type"],
                    "rate" => $price
                ]);
        } else {
            $CostcutRate = new CostcutRate();
            $CostcutRate->costcut = $referenceID;
            $CostcutRate->vehicle_type = $data["vehicle_type"];
            $CostcutRate->rate = $price;
            $referenceID = $CostcutRate->save();
        }
    }

    public function available_countries(Request $request){
        $Data = $request->input();
        $rules = [
            "available_countries_text_area" => "required",
            "upcoming_countries_text_area" => "required",
        ];
        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }
        if(AvailableCountry::where('id',1)->exists()){
            $AvailableCountryObj = AvailableCountry::find(1);
        } else {
            $AvailableCountryObj =  new AvailableCountry();
        }
        $AvailableCountryObj->available_countries_text = $request->available_countries_text_area;
        $AvailableCountryObj->upcoming_countries_text = $request->upcoming_countries_text_area;
        $AvailableCountryObj->save();
        return [];

    }

}
