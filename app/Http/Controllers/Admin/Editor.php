<?php

namespace App\Http\Controllers\Admin;


use App\CountryCurrency;
use App\Currency;
use App\Http\Controllers\Controller;
use App\Model\Admin\OutboundVehicleRate;
use App\Model\Agent\Agent;
use App\Model\Sales\SalesPerson;
use App\Model\Costcut\Costcut;
use App\Model\Costcut\CostcutRate;
use App\Model\Country\Country;
use App\Model\Cruise\Cruise;
use App\Model\Cruise\CruiseCabinRate;
use App\Model\Cruise\CruiseCabinRateChild;
use App\Model\Cruise\CruiseCabinType;
use App\Model\Cruise\CruisePackage;
use App\Model\Hotel\Allotment;
use App\Model\Hotel\Contact;
use App\Model\Hotel\DiscountRates;
use App\Model\Hotel\DiscountRatesChild;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\OutsideRestaurant;
use App\Model\Hotel\Rates;
use App\Model\Hotel\RatesChild;
use App\Model\Hotel\RoomCategory;
use App\Model\Hotel\StopSale;
use App\Model\Hotel\Supplement;
use App\Model\Hotel\Messages;
use App\Model\Markup;
use App\Model\Meal\MealPriceDefault;
use App\Model\Meal\MealRestaurant;
use App\Model\Meal\MealTransfer;
use App\Model\Meal\MealType;
use App\Model\Operation\VehicleInsurance;
use App\Model\Place\Attraction;
use App\Model\Place\AttractionType;
use App\Model\Place\AttractionRate;
use App\Model\Place\AttractionTime;
use App\Model\Place\CityTour;
use App\Model\Place\CityTourRate;
use App\Model\Place\CitytourTime;
use App\Model\Place\Distance;
use App\Model\Place\DistanceCharges;
use App\Model\Place\Excursion;
use App\Model\Place\ExcursionRate;
use App\Model\Place\ExcursionTime;
use App\model\Place\ExtraMileage;
use App\model\Place\ExtraRate;
use App\Model\Place\Place;
use App\Model\Place\Stop;
use App\Model\Profile\Company;
use App\Model\Profile\Designation;
use App\Model\Profile\Profile;
use App\Model\Quotation\QuotationExclude;
use App\Model\Quotation\QuotationInclude;
//use App\Model\Quotation\QuotationTransport;
use App\Model\Quotation\TermsConditions;
use App\Model\Quotation\ValueAddedServices;
use App\Model\QuotationManage\QuotationTransport;
use App\Model\Restaurant\Restaurant;
use App\Model\Restaurant\RestaurantMealRate;
use App\Model\StaticPackages\StaticPackages;
use App\Model\StaticPackages\StaticPackagesPlaces;
use App\Model\Vehicle\HotelTransportRate;
use App\Model\Vehicle\SubTransportCost;
use App\Model\Vehicle\SubVehicle;
use App\Model\Vehicle\SubVehicleCity;
use App\Model\Vehicle\SubVehicleType;
use App\Model\Vehicle\TransportCost;
use App\Model\Vehicle\TransportType;
use App\Model\Vehicle\Vehicle;
use App\Model\Vehicle\VehicleCityRate;
use App\Model\Vehicle\VehicleType;
use App\User;
use App\UserHierarchy;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use App\Model\StaticPackages\StaticPackagesHotels;
use App\Model\DriverAccomodation\DriverAccmomodation;
use Spatie\Activitylog\Models\Activity;
use App\Model\Rate\apple_hotel_rate_source;
use App\Model\Rate\apple_hotel_rate_vendor;
use App\Model\UserTargets\apple_userTargets;
use App\Model\Hotel\apple_hotelbeds_destination_market;
use App\Model\Transport\InnerHotelTransfer;
use App\Model\Rate\apple_cancel_charges_markup_rates;
use App\Model\Country\AvailableCountry;
use App\Model\FdPackages\apple_fd_packages;
use App\Model\FdPackages\apple_fd_packages_user;
use App\Model\FdPackages\apple_fd_packages_attractions;
use App\Model\FdPackages\apple_fd_packages_hotels;
use App\Model\FdPackages\apple_fd_packages_images;
use App\Model\FdPackages\apple_fd_packages_other_costs;
use App\Model\FdPackages\apple_fd_packages_package_price;
use App\Model\FdPackages\apple_fd_packages_transport_cost;
use App\Model\FdPackages\apple_fd_packages_places;
use App\Model\FdPackages\apple_fd_packages_hotel_allotments;
use App\Model\FdPackages\apple_fd_packages_group_hotel_allotments;
use App\Model\FdPackages\apple_fd_packages_localhotels;
use App\Model\FdPackages\apple_fd_packages_meal_cost;
use App\Model\FdPackages\apple_fd_packages_suppliments;
use App\Model\Hotel\HotelBed;


//test
/**
 * Class Editor
 * @package App\Http\Controllers\Admin
 */
class Editor extends Controller
{
    static $TableTypeCategory = [

        "hotel" => [
            "hotel" => Hotel::class,
            "hotel_contact" => Contact::class,
            "rates" => Rates::class,
            "rate_child" => RatesChild::class,
            "allotment" => Allotment::class,
            "stop_sales" => StopSale::class,
            "room_category" => RoomCategory::class,
            "supplement" => Supplement::class,
            "hotel_transport_rate" => HotelTransportRate::class,
            "driver_accommodations_default_rate" => DriverAccmomodation::class,
            "hotelBeds_destinations" => apple_hotelbeds_destination_market::class,
            "hotel_cancel_charges_markup_rates" => apple_cancel_charges_markup_rates::class,
            "hotel_messages" => Messages::class,
            "hotel_Beds" => HotelBed::class,
        ],
        "discount_hotel" => [
            "discount_rates" => DiscountRates::class,
            "discount_rate_child" => DiscountRatesChild::class,
        ],
        "cruise" => [
            "cruise" => Cruise::class,
            "cruise_package" => CruisePackage::class,
            "cruise_rates" => CruiseCabinRate::class,
            "cruise_rate_child" => CruiseCabinRateChild::class,
            "cruise_cabin_category" => CruiseCabinType::class,
        ],
        'sightseeing' => [
            "attraction_type" => AttractionType::class,
            "attraction" => Attraction::class,
            "attraction_rate" => AttractionRate::class,
            "attraction_time" => AttractionTime::class,
            "city_tour" => CityTour::class,
            "city_tour_rate" => CityTourRate::class,
            "city_tour_time" => CitytourTime::class,
            "excursion" => Excursion::class,
            "excursion_rate" => ExcursionRate::class,
            "excursion_time" => ExcursionTime::class,
        ],
        "quotation" => [
            "agent" => Agent::class,
            "sales_person" => SalesPerson::class,
            "designation" => Designation::class,
            "quotation_exclude" => QuotationExclude::class,
            "quotation_include" => QuotationInclude::class,
            "quotation_value_added_service" => ValueAddedServices::class,
            "quotation_terms_conditions" => TermsConditions::class,
            "markup" => Markup::class,
            "quotation_transport" => QuotationTransport::class,
        ],
        "meal" => [
            "meal_transfer" => MealTransfer::class,
            "apple_meal_price_default" => MealPriceDefault::class,
            "apple_meal_type" => MealType::class,
            "apple_places_stops" => Stop::class,
            "apple_meal_type_restaurant" => MealRestaurant::class
        ],
        "restaurant" => [
            "restaurant" => Restaurant::class,
            "apple_hotel_outside_rest" => OutsideRestaurant::class,
            "apple_restaurant_meal_rate" => RestaurantMealRate::class,

        ],
        "place" => [
            "countries" => Country::class,
            "place" => Place::class,
            "transport_rate" => TransportCost::class,
            "sub_transport_rate" => SubTransportCost::class,
            "distance" => Distance::class,
            "place_distance_charges" => DistanceCharges::class,
            "Available_Countries" => AvailableCountry::class,
        ],
        "operation" => [
            "vehicle_type" => VehicleType::class,
            "vehicle_type_rates" => Vehicle::class,
            "sub_vehicle_type" => SubVehicleType::class,
            "sub_vehicle_type_rates" => SubVehicle::class,
            "sub_vehicle_city" => SubVehicleCity::class,
            "vehicle_city_rates" => VehicleCityRate::class,
            "insurance" => VehicleInsurance::class,
            "hotel_transport" => HotelTransportRate::class,
            "country_transport_type" => TransportType::class,
            "outbound_vehicle_rate" => OutboundVehicleRate::class,
	        "extra_milage" => ExtraMileage::class,
            "extra_rate" => ExtraRate::class,
            "inner_hotel_transfer_country" => InnerHotelTransfer::class
        ],
        'users' => [
            'user' => User::class,
            'user_profile' => Profile::class,
            'user_hierarchy' => UserHierarchy::class,
	        'Login History' => Activity::class
        ],
        'currency' => [
            'currency' => Currency::class,
            'country_currency' => CountryCurrency::class
        ],
        'static_packages' => [
            'static_packages'        => StaticPackages::class,
            'static_packages_places' => StaticPackagesPlaces::class,
            'static_packages_hotels' => StaticPackagesHotels::class
        ],
        'company' => [
            'company' => Company::class
        ],
        'cost_cutting' => [
            'cost_cutting' => Costcut::class,
            'cost_cutting_rate' => CostcutRate::class
        ],
        'User_Targets' => [
            'apple_userTargets' => apple_userTargets::class
        ],
        'FD_Packages' => [
            'fd_packages' => apple_fd_packages::class,
            'apple_fd_packages_user' => apple_fd_packages_user::class,
            'apple_fd_packages_attractions' => apple_fd_packages_attractions::class,
            'apple_fd_packages_hotels' => apple_fd_packages_hotels::class,
            'apple_fd_packages_images' => apple_fd_packages_images::class,
            'apple_fd_packages_package_price' => apple_fd_packages_package_price::class,
            'apple_fd_packages_transport_cost' => apple_fd_packages_transport_cost::class,
            'apple_fd_packages_places' => apple_fd_packages_places::class,
            'apple_fd_packages_hotel_allotments' => apple_fd_packages_hotel_allotments::class,
            'apple_fd_packages_group_hotel_allotments' => apple_fd_packages_group_hotel_allotments::class,
            'apple_fd_packages_localhotels' => apple_fd_packages_localhotels::class,
            'apple_fd_packages_meal_cost' => apple_fd_packages_meal_cost::class,
            'apple_fd_packages_suppliments' => apple_fd_packages_suppliments::class,
        ]
//        'Rate_Source'=> [
//            'apple_hotel_rate_vendors'=> apple_hotel_rate_vendor::class,
//            'apple_hotel_rate_sources'=> apple_hotel_rate_source::class
//        ]
    ];


    static $readOnlyColumns = [
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
        'deleted_at',
        'upload_id',
    ];


    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function index()
    {

        $this->getTableArray();
        return view('admin.editor.index');
    }


    /**
     * @return array
     */
    static function getTableArray()
    {

        $returnArray = [];
        foreach (self::$TableTypeCategory as $ItemArray) {
            foreach ($ItemArray as $key => $Item) {
                $returnArray [$key] = $Item;
            }
        }
        return $returnArray;
    }

    /**
     * @param Request $request
     * @return array
     */
    function Load(Request $request)
    {
        $class_path = self::getTableArray()[$request->input('table') . ""];

        $ClassInstance = with(new $class_path);
        $TableName = $ClassInstance->getTable();

        $TableDetails = \DB::select("DESCRIBE $TableName");

        $Model = $ClassInstance::where(function ($query) use ($request, $TableDetails) {

            $q = $request->input('q');
            $words = explode(",", $q);

            if (count(explode(":", $words[0])) > 1) {
                foreach ($words as $word_pair) {
                    $words_split = explode(":", $word_pair);
                    if (isset($words_split[0]) && isset($words_split[1]) && search_multidimensional($TableDetails, $words_split[0]))
                        $query->where($words_split[0], $words_split[1]);
                }
            } else {

                foreach ($TableDetails as $ColumnItem) {
                    #sd($TableDetails);
                    $query->orWhere(function ($query) use ($request, $TableDetails, $ColumnItem) {
                        $q = $request->input('q');
                        $query->orWhere($ColumnItem->Field, 'LIKE', '%' . $q . '%');
                    });
                }

            }

        });

        $PG = $Model->paginate();


        if ($PG->count()) {

            $Data = $PG->toArray();
            $Column = $this->getColumn($Model);
            array_unshift($Data['data'], array_combine($Column, $Column));

            $Data['link'] = empty($PG->links()) ? "" : $PG->links()->toHtml();
            $Data['column'] = $TableDetails;
            $Data['status'] = true;


            return $Data;
        } else
            return ['status' => false];
    }

    /**
     * @param Request $request
     * @return array
     */
    function Save(Request $request)
    {


        $Data = $request->input('data');
        $Column = $Data[0];
        unset($Data[0]);
        $Status = [];

        $class_path = self::getTableArray()[$request->input('table')];
        $ClassInstance = with(new $class_path);
        $ColumnList = self::getActualColumn($class_path);

        foreach ($Data as $Row) {

            $DataArray = [];
            $ID = $Row[0];
            unset($Row[0]);

            foreach ($Row as $Index => $RowItem) {
                if (array_search($Column[$Index], self::$readOnlyColumns) || !array_search($Column[$Index], $ColumnList)) {
                    continue;
                }
                $DataArray[$Column[$Index]] = $RowItem;

            }

            if ($ID) {

                $Extra = [
                    'updated_by' => \Auth::user()->id,
                    'updated_at' => Carbon::now()];

                $Status['status'] = $ClassInstance::where("ID", $ID)->update($DataArray);

            } elseif (array_filter($DataArray)) {

                $Extra = ['created_by' => \Auth::user()->id,
                    'updated_by' => \Auth::user()->id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()];

                $DataArray = array_combine2($Extra, $DataArray);

                $Status['status'] = $ClassInstance::insert($DataArray);

            }

        }

        return $Status;

    }

    /**
     * @param Request $request
     * @return array
     */
    function Delete(Request $request)
    {

        $class_path = self::getTableArray()[$request->input('table')];
        $ClassInstance = with(new $class_path);

        $Data = $request->all();
        $Status = ['status' => false];
        foreach ($Data['data'] as $ID) {
            $Status['status'] = $ClassInstance::where("ID", $ID)->delete();
        }

        return $Status;
    }


    /**
     * @param $Table
     * @return array
     */
    function getColumn($Table)
    {
        return array_keys($Table->limit(1)->first()->toArray());

    }

    /**
     * @param $Table
     * @return array
     */
    function getActualColumn($Table)
    {
        return DB::getSchemaBuilder()->getColumnListing(with(new $Table)->getTable());

    }

    /**
     * @param $TableName
     * @return array
     */
    function getColumnRaw($TableName)
    {
        $class_path = self::getTableArray()[$TableName];
        $ClassInstance = with(new $class_path);

        $TableName = $ClassInstance->getTable();

        $TableDescribe = objectToArray(DB::select("DESCRIBE $TableName"));

        return array_column($TableDescribe, 'Field');

    }


    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function import(Request $request)
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '256M');


        $Errors = [];
        $DataForm = $request->all();
        $FileRate = $request->file('file_rate');
        $AddRate = $request->input('add_to_system');
        $UpdateID = substr(time() . \Auth::user()->id, -9);

        $TableColumn = $this->getColumnRaw($DataForm['table']);


        if ($FileRate && $FileRate->isValid()) {

            $FilePath = $FileRate->path();
            $Excel = \App::make('excel');


            $Data = $Excel->load($FilePath, function ($reader) {
            });


            $DataArray = $Data->get()->toArray();

            foreach ($DataArray as $key => $value ) {
                foreach ($value as $key1 => $value1) {
                    if($key1 == 0 && !isset($value1)) {
                        unset($DataArray[$key][$key1]);
                    }
                }
            }

            $class_path = self::getTableArray()[$DataForm['table']];
            $ClassInstance = with(new $class_path);

            $FileColumn = array_keys($DataArray[0]);

            foreach ($FileColumn as $ColumnName) {
                if (!preg_grep("/$ColumnName/i", $TableColumn)) {
                    $Errors = [
                        'status' => false,
                        'message' => $ColumnName . " is not found!",
                        'data' => $TableColumn

                    ];
                    break;
                }
            }

            $DataToInsert = [];


            if (empty($Errors)) {

                foreach ($DataArray as $DataIteItem) {//data row

                    $CurrentDataItem = [];

                    foreach ($DataIteItem as $DataKey => $DataValue) {//data value
                        $CurrentDataItem[$DataKey] = $DataValue;
                    }
                    //add upload ID
                    $CurrentDataItem['upload_id'] = $UpdateID;
                    $CurrentDataItem['created_by'] = \Auth::id();

                    $DataToInsert[] = $CurrentDataItem;

                }


                //insert
                $ClassInstance::insert($DataToInsert);

                return view("system.success.success-bootstrap", ["Message" => "Upload ID: $UpdateID"]);


            } else {
                return view("system.error.error-bootstrap", ["Message" => $Errors]);

            }


        } else {
            return view("system.error.error-bootstrap", ["Message" => "Please input valid Excel file!"]);
        }

    }
}

