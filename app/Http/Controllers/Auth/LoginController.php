<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;

use Auth;
use App\User;


/**
 * Class LoginController
 * @package App\Http\Controllers\Auth
 */
class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }


    /**
     * @param Request $request
     * @param $user
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    protected function authenticated(Request $request, $user)
    {

        $userId=Auth::user()->id;

        $userItem = User::find($userId);
        $status =(int) $userItem->disabled;

        if ($status == 1) {
            
           $msg = env("DISABLE_ERROR_MSG","Please Contact Your Supervisor");
           $this->guard()->logout();
           $request->session()->flush();
           $request->session()->regenerate();
           return redirect()->route('login')->with('error', $msg);
        }

        if (!$user->can('create_tour')) {
            if ($user->can('admin_access')) {
                return redirect('/#admin_container_view');
            }
            elseif ($user->can('manage_transport')) {
                return redirect('/#operation_container');
            }
            elseif ($user->can('view_reports')) {
                return redirect('/#operation_container');
            }
        }
	
	    activity()
		    ->performedOn(Auth::user())
		    ->causedBy(Auth::user()->id)
		    ->withProperties("Ip" . \Request::ip())
		    ->log(Auth::user()->email." Logged");

        return redirect('/');
    }
}
