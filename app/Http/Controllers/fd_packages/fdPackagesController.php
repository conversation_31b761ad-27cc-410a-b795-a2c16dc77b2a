<?php

namespace App\Http\Controllers\fd_packages;

use App\Mail\fdHotelVouchers;
use App\Mail\fdTourConfrimation;
use App\Mail\fdQuotationNotification;

use App\Model\Place\Place;
use DB;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Model\Hotel\Hotel;
use App\Model\Vehicle\VehicleType;
use App\Model\Vehicle\Vehicle;
use App\Model\Hotel\Room;
use App\Model\Hotel\Rates;
use App\Model\Hotel\RatesChild;
use Mail;
use Validator;
use Image;
use File;
use Illuminate\Support\Facades\Input;
use App\Model\FdPackages\apple_fd_packages;
use App\Model\FdPackages\apple_fd_packages_user;
use App\Model\FdPackages\apple_fd_packages_attractions;
use App\Model\FdPackages\apple_fd_packages_hotels;
use App\Model\FdPackages\apple_fd_packages_images;
use App\Model\FdPackages\apple_fd_packages_other_costs;
use App\Model\FdPackages\apple_fd_packages_package_price;
use App\Model\FdPackages\apple_fd_packages_transport_cost;
use App\Model\FdPackages\apple_fd_packages_places;
use App\Model\FdPackages\apple_fd_packages_hotel_allotments;
use App\Model\FdPackages\apple_fd_packages_group_hotel_allotments;
use App\Model\FdPackages\apple_fd_packages_localhotels;
use App\Model\FdPackages\apple_fd_packages_meal_cost;

use Auth;
use Carbon\Carbon;
use App\Model\Image\Image as ImgaeGenerator;

use App\Model\Place\Attraction as AttractionModel;
use App\Model\Place\CityTour;
use App\Model\Place\Excursion;
use App\Model\Hotel\RoomCategory;
use App\Model\Hotel\Meal;
use App\User;

use App\Model\FdPackages\apple_fd_packages_suppliments;

// Quotation Data
use App\Model\FdPackages\apple_fd_packages_quotation;
use App\Model\FdPackages\apple_fd_packages_quotation_cost;
use App\Model\FdPackages\apple_fd_packages_quotation_hotel_rates;
use App\Model\FdPackages\apple_fd_packages_quotation_pax;
use App\Model\FdPackages\apple_fd_packages_quotation_hotel_vouchers_info;
use App\Model\FdPackages\apple_fd_packages_quotation_cancelled_hotels;

use App\Model\QuotationManage\Quotation;
use App\Model\Quotation\Quotation as Quote;
use View;


class fdPackagesController extends Controller
{
    public function __construct()
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 0);
    }

    public function getHotels(Request $request)
    {

        $city = $request->placeId;
        $hotels = Hotel::where('city', $city)->with('class')->get();
        return $hotels;

    }

    public function set_rates(Request $request)
    {
        $Data = $request->input();
        $ListObj = array();
        $ListAllotmentObj = array();
        $ListLocalHotelsObj = array();

        $rules = [
            "fd_package_name" => "required",
            "fd-valid-from" => "required|date",
            "fd-valid-to" => "required|date",
            "fd_user" => "required",
            "fd_city" => "required",
            "fd_hotel" => "required"
        ];
        $groupPackage = $request->fd_group_package;
        if ($groupPackage == 1) {
            $rules['fd_group_arrival_date'] = "required";
            $rules['fd_user'] = "required|array|min:1";

        }


        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }

        $fdPlaceArray = array();
        $fdPlaces = $request->fd_city;
        $fdNights = $request->fd_no_of_nights;

        foreach ($fdPlaces as $key => $place) {
            $fdPlaceArray[$key]['place'] = $place;
            $fdPlaceArray[$key]['no_of_nights'] = $fdNights[$key];
        }

        $allotmentTable = "";

        if ($groupPackage == 0) {
            $allotmentTable = apple_fd_packages_hotel_allotments::class;
        } else {
            $allotmentTable = apple_fd_packages_group_hotel_allotments::class;
        }


        $hotels = $request->fd_hotel;
        $noOfnights = array_sum($request->fd_no_of_nights) + 1;

        foreach ($hotels as $hotelKey => $hotelValue) {
            $hotelObj = Hotel::where('ID', $hotelValue)->with('City')->first();
            foreach ($fdPlaceArray as $placeArr) {
                if ($placeArr['place'] == $hotelObj->city) {
                    $ListObj[$hotelObj->city]['noOfNights'] = $placeArr['no_of_nights'];
                    break;
                }
            }
            $ListObj[$hotelObj->city]['cityName'] = $hotelObj->City->name;
            $ListObj[$hotelObj->city]['city'] = $hotelObj->city;
            $ListObj[$hotelObj->city]['hotels'][$hotelKey]['hotelId'] = $hotelObj->ID;
            $ListObj[$hotelObj->city]['hotels'][$hotelKey]['hotelName'] = $hotelObj->name;

            if (!$allotmentTable::where('hotel_id', '=', $hotelValue)->exists()) {
                $ListAllotmentObj[$hotelObj->city]['cityName'] = $hotelObj->City->name;
                $ListAllotmentObj[$hotelObj->city]['city'] = $hotelObj->city;
                $ListAllotmentObj[$hotelObj->city]['hotels'][$hotelKey]['hotelId'] = $hotelObj->ID;
                $ListAllotmentObj[$hotelObj->city]['hotels'][$hotelKey]['hotelName'] = $hotelObj->name;
            }

        }
        foreach ($ListObj as $key => $hotels) {
            $existingHotels = array();
            $hotelNames = "";
            foreach ($hotels['hotels'] as $hotelKey => $hotel) {
                array_push($existingHotels, $hotel['hotelId']);
                $hotelNames .= $hotel['hotelName'] . ",";
            }
            $hotelNames = rtrim($hotelNames, ",");
            $localHotels = Hotel::where('city', '=', $hotels['city'])
                ->whereNotIn('ID', $existingHotels)
                ->select('ID', 'name')
                ->orderBy('preferred', 'DESC')
                ->get();

            $ListLocalHotelsObj[$key]['localHotels'] = $localHotels;
            $ListLocalHotelsObj[$key]['cityId'] = $key;
            $ListLocalHotelsObj[$key]['cityName'] = $hotels['cityName'];
            $ListLocalHotelsObj[$key]['hotelNames'] = $hotelNames;
        }
        $array = array('fd_hotels' => $ListObj, 'fd_allotment_hotels' => $ListAllotmentObj, 'noOfDays' => $noOfnights, 'localHotels' => $ListLocalHotelsObj);
        return $array;

    }

    public function createPackageSaveData(Request $request)
    {

        $Data = $request->input();
        $ListObj = array();

        $rules = [
            "fd_package_name" => "required",
            "fd-valid-from" => "required|date",
            "fd-valid-to" => "required|date",
            "fd_user" => "required",
            "fd_city" => "required",
            "fd_hotel" => "required",
            "fd_hotel_adult_single_rate.*" => "required",
            "fd_hotel_adult_double_rate.*" => "required",
            "fd_hotel_adult_triple_rate.*" => "required",
            "fd_hotel_child_rate.*" => "required",
            "fd_hotel_c_wout_rate.*" => "required",
            "fd_adult_total_cost_1" => "required",
            "fd_adult_total_cost_2" => "required",
            "fd_adult_total_cost_3" => "required",
            "fd_adult_total_cost" => "required",
            "fd_adult_total_cost_6" => "required",
            "fd_adult_total_cost_7" => "required",
            "fd_adult_total_cost_8" => "required",
            "fd_adult_total_cost_9" => "required",
            "fd_adult_total_cost_10" => "required",
            "fd_adult_total_cost_11" => "required",
            "fd_adult_total_cost_12" => "required",
            "fd_adult_total_cost_13" => "required",
            "fd_adult_total_cost_14" => "required",
            "fd_adult_total_cost_15" => "required",
            "fd_child_total_cost" => "required",
            "fd_child_w_bed_total_cost" => "required",
            "fd_adult_total_cost_profit" => "required",
            "fd_child_total_cost_profit" => "required",
            "fd_child_w_bed_total_cost_profit" => "required",
            "fd_distance" => "required|not_in:0",
            "fd_distance_price" => "required|not_in:0",
            "fd_bata" => "required|not_in:0",
            "fd_no_of_days" => "required|not_in:0",
            "fd_package_inclusions" => "required",
            "fd_package_exclusions" => "required",
            "break_first_adult_meal_rate" => "required|not_in:0|numeric",
            "break_first_child_meal_rate" => "required|not_in:0|numeric",
            "lunch_adult_meal_rate" => "required|not_in:0|numeric",
            "lunch_child_meal_rate" => "required|not_in:0|numeric",
            "dinner_adult_meal_rate" => "required|not_in:0|numeric",
            "dinner_child_meal_rate" => "required|not_in:0|numeric",

        ];


        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }


        $apple_fd_packages = new apple_fd_packages();
        $apple_fd_packages->package_name = $request->fd_package_name;
        $apple_fd_packages->date_from = $request['fd-valid-from'];
        $apple_fd_packages->date_to = $request['fd-valid-to'];
        $apple_fd_packages->itinerary_desc = $request->fd_itineray_desc;
        $apple_fd_packages->includes = $request->fd_package_inclusions;
        $apple_fd_packages->excludes = $request->fd_package_exclusions;
        $apple_fd_packages->pick_up = $request->fd_pick_up_location;
        $apple_fd_packages->drop_off = $request->fd_drop_off_location;
        $apple_fd_packages->country = $request->fd_country;
        $apple_fd_packages->group_package = $request->fd_group_package;
        if($request->fd_group_package == 1) {
            $apple_fd_packages->arrival_date = $request->fd_group_arrival_date;
            $apple_fd_packages->max_pax = $request->fd_group_max_pax;
        }
        $apple_fd_packages->user_id = Auth::user()->id;
        $apple_fd_packages->status = 1;
        $apple_fd_packages->save();

        foreach ($request->fd_city as $key => $fdCity) {
            $apple_fd_packages_places = new apple_fd_packages_places();
            $apple_fd_packages_places->fd_package_id = $apple_fd_packages->id;
            $apple_fd_packages_places->place_id = $fdCity;
            $apple_fd_packages_places->no_of_nights = $request->fd_no_of_nights[$key];
            $apple_fd_packages_places->save();
        }

        foreach ($request->fd_hotel_id as $key => $hotelID) {
            $apple_fd_packages_hotels = new apple_fd_packages_hotels();
            $apple_fd_packages_hotels->fd_package_id = $apple_fd_packages->id;
            $apple_fd_packages_hotels->place_id = $request->fd_city_id[$key];
            $apple_fd_packages_hotels->hotel_id = $hotelID;
            $apple_fd_packages_hotels->no_of_nights = $request->fd_nights[$key];
            $apple_fd_packages_hotels->meal_type = $request->fd_hotel_meal_type[$key];
            $apple_fd_packages_hotels->room_category = $request->fd_hotel_room_category[$key];
            $apple_fd_packages_hotels->adult_rate_single_room = $request->fd_hotel_adult_single_rate[$key];
            $apple_fd_packages_hotels->adult_rate_double_room = $request->fd_hotel_adult_double_rate[$key];
            $apple_fd_packages_hotels->adult_rate_triple_room = $request->fd_hotel_adult_triple_rate[$key];
            $apple_fd_packages_hotels->child_rate = $request->fd_hotel_child_rate[$key];
            $apple_fd_packages_hotels->child_w_out_bed_rate = $request->fd_hotel_c_wout_rate[$key];
            $apple_fd_packages_hotels->save();
        }

        $apple_fd_packages_package_price = new apple_fd_packages_package_price();
        $apple_fd_packages_package_price->fd_package_id = $apple_fd_packages->id;
        $apple_fd_packages_package_price->adult_rate = $request->fd_adult_total_cost;
        $apple_fd_packages_package_price->adult_rate_1 = $request->fd_adult_total_cost_1;
        $apple_fd_packages_package_price->adult_rate_2 = $request->fd_adult_total_cost_2;
        $apple_fd_packages_package_price->adult_rate_3 = $request->fd_adult_total_cost_3;
        $apple_fd_packages_package_price->adult_rate_4 = $request->fd_adult_total_cost_4;
        $apple_fd_packages_package_price->adult_rate_5= $request->fd_adult_total_cost_6;
        $apple_fd_packages_package_price->adult_rate_6 = $request->fd_adult_total_cost_7;
        $apple_fd_packages_package_price->adult_rate_7 = $request->fd_adult_total_cost_8;
        $apple_fd_packages_package_price->adult_rate_8 = $request->fd_adult_total_cost_9;
        $apple_fd_packages_package_price->adult_rate_9 = $request->fd_adult_total_cost_10;
        $apple_fd_packages_package_price->adult_rate_10 = $request->fd_adult_total_cost_11;
        $apple_fd_packages_package_price->adult_rate_11 = $request->fd_adult_total_cost_12;
        $apple_fd_packages_package_price->adult_rate_12 = $request->fd_adult_total_cost_13;
        $apple_fd_packages_package_price->adult_rate_13 = $request->fd_adult_total_cost_14;
        $apple_fd_packages_package_price->adult_rate_14 = $request->fd_adult_total_cost_15;
        $apple_fd_packages_package_price->child_rate = $request->fd_child_total_cost;
        $apple_fd_packages_package_price->child_w_out_bed_rate = $request->fd_child_w_bed_total_cost;
        $apple_fd_packages_package_price->adult_profit = $request->fd_adult_total_cost_profit;
        $apple_fd_packages_package_price->child_profit = $request->fd_child_total_cost_profit;
        $apple_fd_packages_package_price->child_w_out_profit = $request->fd_child_w_bed_total_cost_profit;
        $apple_fd_packages_package_price->save();

        $apple_fd_packages_meal_cost = new apple_fd_packages_meal_cost();
        $apple_fd_packages_meal_cost->fd_package_id = $apple_fd_packages->id;
        $apple_fd_packages_meal_cost->bf_adult_meal_rate = $request->break_first_adult_meal_rate;
        $apple_fd_packages_meal_cost->bf_child_meal_rate = $request->break_first_child_meal_rate;
        $apple_fd_packages_meal_cost->lunch_adult_meal_rate = $request->lunch_adult_meal_rate;
        $apple_fd_packages_meal_cost->lunch_child_meal_rate = $request->lunch_child_meal_rate;
        $apple_fd_packages_meal_cost->dinner_adult_meal_rate = $request->dinner_adult_meal_rate;
        $apple_fd_packages_meal_cost->dinner_child_meal_rate = $request->dinner_child_meal_rate;
        $apple_fd_packages_meal_cost->save();

        foreach ($request->fd_distance as $key => $distance) {
            $apple_fd_packages_transport_cost = new apple_fd_packages_transport_cost();
            $apple_fd_packages_transport_cost->fd_package_id = $apple_fd_packages->id;
            $apple_fd_packages_transport_cost->distance = $request->fd_distance[$key];
            $apple_fd_packages_transport_cost->distance_price = $request->fd_distance_price[$key];
            $apple_fd_packages_transport_cost->bata = $request->fd_bata[$key];
            $apple_fd_packages_transport_cost->no_of_days = $request->fd_no_of_days[$key];
            $apple_fd_packages_transport_cost->grand_total = $request->total_transport_cost[$key];
            $apple_fd_packages_transport_cost->paging = $request->total_paging_cost[$key];
            $apple_fd_packages_transport_cost->type = $key + 1;
            if(($key + 1) == 4) {
                $apple_fd_packages_transport_cost->driver_accmomodation_charges = $request->total_driver_acc_cost;
                $apple_fd_packages_transport_cost->guide_fee = $request->total_guide_fee_cost;
            }
            $apple_fd_packages_transport_cost->save();
        }


        foreach ($request->fd_user as $key => $userId) {
            if ($key == 0) {
                continue;
            }
            $apple_fd_packages_user = new apple_fd_packages_user();
            $apple_fd_packages_user->fd_package_id = $apple_fd_packages->id;
            $apple_fd_packages_user->user_id = $userId;
            $apple_fd_packages_user->save();
        }

        if (isset($request->fd_other_cost)) {
            foreach ($request->fd_other_cost as $key => $value) {
                $apple_fd_packages_other_costs = new apple_fd_packages_other_costs();
                $apple_fd_packages_other_costs->fd_package_id = $apple_fd_packages->id;
                $apple_fd_packages_other_costs->cost_name = $value;
                $apple_fd_packages_other_costs->cost_type = $request->fd_cost_type[$key];
                $apple_fd_packages_other_costs->adult_rate = $request->fd_other_cost_adult_cost[$key];
                $apple_fd_packages_other_costs->child_rate = $request->fd_other_cost_child_cost[$key];
                $apple_fd_packages_other_costs->child_w_out_bed_rate = $request->fd_other_cost_w_child_cost[$key];
                $apple_fd_packages_other_costs->save();
            }
        }

        if (isset($request->fd_attractions)) {
            foreach ($request->fd_attractions as $key => $value) {
                $apple_fd_packages_attractions = new apple_fd_packages_attractions();
                $apple_fd_packages_attractions->fd_package_id = $apple_fd_packages->id;
                $apple_fd_packages_attractions->place_id = $request->fd_city_attractions[$key];
                $apple_fd_packages_attractions->attraction_id = $value;
                $apple_fd_packages_attractions->attraction_type = $request->attraction_type[$key];
                $apple_fd_packages_attractions->day = $request->fd_attractions_day[$key];
                $apple_fd_packages_attractions->adult_rate = $request->fd_attraction_adult_cost[$key];
                $apple_fd_packages_attractions->child_rate = $request->fd_attraction_child_cost[$key];
                $apple_fd_packages_attractions->child_w_out_bed_rate = $request->fd_attraction_w_child_cost[$key];
                $apple_fd_packages_attractions->save();
            }
        }

        $folderPath = public_path('assets/image/fd_package_images/' . $apple_fd_packages->id);

        if (!File::exists($folderPath)) {
            File::makeDirectory($folderPath);
        }
        $files = Input::file('fd_package_images');

        foreach ($files as $key => $file) {

            $filenameWithExt = $file->getClientOriginalName();
            $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            $extension = $file->getClientOriginalExtension();
            $fileNameToStore = $filename . '_' . str_random(20) . '.' . $extension;

            $image_resize = Image::make($file->getRealPath());
            $image_resize->resize(500, null, function ($constraint) {
                $constraint->aspectRatio();
            });
            $image_resize->save($folderPath . '/' . $fileNameToStore);
            //$upload_success = $file->move($folderPath, $fileNameToStore);

            $dbFileName = "assets/image/fd_package_images/" . $apple_fd_packages->id . "/" . $fileNameToStore;

            $apple_fd_packages_images = new apple_fd_packages_images();
            $apple_fd_packages_images->fd_package_id = $apple_fd_packages->id;
            $apple_fd_packages_images->image_path = $dbFileName;
            $apple_fd_packages_images->priority = $key + 1;
            $apple_fd_packages_images->save();

        }
        $groupPackage = $request->fd_group_package;
        $allotmentTable = "";

        if ($groupPackage == 0) {
            $allotmentTable = apple_fd_packages_hotel_allotments::class;
        } else {
            $allotmentTable = apple_fd_packages_group_hotel_allotments::class;
        }
        if (isset($request->fd_allotment_hotel_id)) {
            foreach ($request->fd_allotment_hotel_id as $key => $hotelId) {
                if (!$allotmentTable::where('hotel_id', '=', $hotelId)->exists()) {
                    $apple_fd_packages_hotel_allotments = new $allotmentTable();
                    $apple_fd_packages_hotel_allotments->hotel_id = $hotelId;
                    $apple_fd_packages_hotel_allotments->no_of_rooms = $request->fd_hotel_allotment_room_count[$key];
                    $apple_fd_packages_hotel_allotments->start_date = $request->fd_hotel_allotment_start_date[$key];
                    $apple_fd_packages_hotel_allotments->end_date = $request->fd_hotel_allotment_end_date[$key];
                    $apple_fd_packages_hotel_allotments->release_period = $request->fd_hotel_allotment_releast_period[$key];
                    $apple_fd_packages_hotel_allotments->save();
                }
            }
        }
        $packages = apple_fd_packages::where('id', '=', $apple_fd_packages->id)->with('findImages')->first();
        return array('id' => $apple_fd_packages->id, 'packages' => $packages);
    }

    public function imagesReorder(Request $request)
    {
        $positions = $request->position;
        $packageId = $request->id;
        foreach ($positions as $key => $position) {
            $apple_fd_packages_images = apple_fd_packages_images::find($position);
            $apple_fd_packages_images->priority = $key + 1;
            $apple_fd_packages_images->save();
        }

        $packages = apple_fd_packages::where('id', '=', $packageId)->with('findImages')->first();
        return array('id' => $packageId, 'packages' => $packages);
    }

    public function editPackage(Request $request)
    {

        $packageId = $request->fd_package_edit_pack_id;
        $status = $request->fd_package_status;

        $apple_fd_packages = apple_fd_packages::find($packageId);
        $apple_fd_packages->status = $status;
        $apple_fd_packages->save();

        if (isset($request->edit_package_info)) {
            $apple_fd_packages = apple_fd_packages::find($packageId);
            $apple_fd_packages->package_name = $request->fd_package_name_edit;
            $apple_fd_packages->date_from = $request->fd_package_from_edit;
            $apple_fd_packages->date_to = $request->fd_package_to_edit;
            $apple_fd_packages->includes = $request->fd_edit_package_inclusions;
            $apple_fd_packages->excludes = $request->fd_edit_package_exclusions;
            $apple_fd_packages->save();
        }


        if (isset($request->edit_price)) {
            $apple_fd_packages_package_price = new apple_fd_packages_package_price();
            $apple_fd_packages_package_price->fd_package_id = $packageId;
            $apple_fd_packages_package_price->adult_rate = $request->fd_adult_total_cost;
            $apple_fd_packages_package_price->adult_rate_1 = $request->fd_adult_total_cost_1;
            $apple_fd_packages_package_price->adult_rate_2 = $request->fd_adult_total_cost_2;
            $apple_fd_packages_package_price->adult_rate_3 = $request->fd_adult_total_cost_3;
            $apple_fd_packages_package_price->adult_rate_4 = $request->fd_adult_total_cost_4;
            $apple_fd_packages_package_price->adult_rate_5= $request->fd_adult_total_cost_6;
            $apple_fd_packages_package_price->adult_rate_6 = $request->fd_adult_total_cost_7;
            $apple_fd_packages_package_price->adult_rate_7 = $request->fd_adult_total_cost_8;
            $apple_fd_packages_package_price->adult_rate_8 = $request->fd_adult_total_cost_9;
            $apple_fd_packages_package_price->adult_rate_9 = $request->fd_adult_total_cost_10;
            $apple_fd_packages_package_price->adult_rate_10 = $request->fd_adult_total_cost_11;
            $apple_fd_packages_package_price->adult_rate_11 = $request->fd_adult_total_cost_12;
            $apple_fd_packages_package_price->adult_rate_12 = $request->fd_adult_total_cost_13;
            $apple_fd_packages_package_price->adult_rate_13 = $request->fd_adult_total_cost_14;
            $apple_fd_packages_package_price->adult_rate_14 = $request->fd_adult_total_cost_15;
            $apple_fd_packages_package_price->child_rate = $request->fd_child_total_cost;
            $apple_fd_packages_package_price->child_w_out_bed_rate = $request->fd_child_w_bed_total_cost;
            $apple_fd_packages_package_price->adult_profit = $request->adult_profit;
            $apple_fd_packages_package_price->child_profit = $request->child_profit;
            $apple_fd_packages_package_price->child_w_out_profit = $request->child_w_out_bed_profit;
            $apple_fd_packages_package_price->save();
        }
        if (isset($request->fd_package_edit_allowd_users)) {
            $deletedRows = apple_fd_packages_user::where('fd_package_id', $packageId)->delete();
            foreach ($request->fd_package_edit_allowd_users as $key => $userId) {
                $apple_fd_packages_user = new apple_fd_packages_user();
                $apple_fd_packages_user->fd_package_id = $packageId;
                $apple_fd_packages_user->user_id = $userId;
                $apple_fd_packages_user->save();
            }
        }


        $folderPath = public_path('assets/image/fd_package_images/' . $packageId);
        $packages = apple_fd_packages::where('id', '=', $packageId)->with('findImages')->first();
        $pr = $packages->findImages()->count();


        if (!File::exists($folderPath)) {
            File::makeDirectory($folderPath);
        }
        $files = Input::file('fd_package_images');
        if (isset($files)) {
            foreach ($files as $key => $file) {

                $filenameWithExt = $file->getClientOriginalName();
                $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
                $extension = $file->getClientOriginalExtension();
                $fileNameToStore = $filename . '_' . str_random(20) . '.' . $extension;

                $image_resize = Image::make($file->getRealPath());
                $image_resize->resize(500, null, function ($constraint) {
                    $constraint->aspectRatio();
                });
                $image_resize->save($folderPath . '/' . $fileNameToStore);
                //$upload_success = $file->move($folderPath, $fileNameToStore);

                $dbFileName = "assets/image/fd_package_images/" . $packageId . "/" . $fileNameToStore;

                $apple_fd_packages_images = new apple_fd_packages_images();
                $apple_fd_packages_images->fd_package_id = $packageId;
                $apple_fd_packages_images->image_path = $dbFileName;
                $apple_fd_packages_images->priority = $pr + 1 ?? 1;
                $apple_fd_packages_images->save();

                $pr++;
            }
        }


        $packages = apple_fd_packages::where('id', '=', $packageId)->with('findImages', 'findPackagePrice')->first();
        return array('id' => $packageId, 'packages' => $packages, 'latestPrice' => $packages->findPackagePrice{0});
    }

    public function removeImage(Request $request)
    {
        $packageId = $request->packageId;
        $imageId = $request->imageId;
        $path = apple_fd_packages_images::where('id', $imageId)->first()->image_path;
        $path = public_path($path);

        if (File::exists($path)) {
            apple_fd_packages_images::where('id', $imageId)->delete();
            File::delete($path);
        }
        $packages = apple_fd_packages::where('id', '=', $packageId)->with('findImages')->first();
        return array('id' => $packageId, 'packages' => $packages, 'path' => $path);
    }

    public function retrieveFdPackage(Request $request)
    {
        $packageId = $request->id;
        $packages = apple_fd_packages::where('id', '=', $packageId)->with('findImages', 'findPackagePrice', 'findPlaces','findAllowedPersons')->first();
        $places = $packages->findPlaces;
        $countryDesc = $packages->findPlaces{0}->findPlace->FindCountry->description ?? null;
        $countryId = $packages->findPlaces{0}->findPlace->FindCountry->ID;
        $path = $this->getGoogleGenDirectionPathStatic($places);
        $hotelArray = array();
        $hotels = $packages->findHotels;

        $allowedUsersArray = array();
        $allowedUsers =  apple_fd_packages_user::where('fd_package_id','=',$packageId)->get();
        foreach ($allowedUsers as $key => $user) {
            array_push($allowedUsersArray,$user->user_id);
        }
        $userArray = array();
        $users = User::all();
        foreach ($users as $key => $user) {
            array_push($userArray,$user->id);
        }
        $diff = array_diff($userArray,$allowedUsersArray);
        $userhtml = "";
        if(count($diff) > 0){
            $allowUsers = User::whereIn('id',$allowedUsersArray)->get();
            foreach ($allowUsers as $key => $user) {
                $userhtml .= "<option value='".$user->id."' selected>".$user->name."</option>";
            }
            $users = User::whereIn('id',$diff)->get();
            foreach ($users as $key => $user) {
                $userhtml .= "<option value='".$user->id."' >".$user->name."</option>";
            }
        } else {
            $users = User::all();
            foreach ($users as $key => $user) {
                $userhtml .= "<option value='".$user->id."' selected>".$user->name."</option>";
            }
        }

        foreach ($hotels as $hotelKey => $hotelValue) {
            $hotelArray[$hotelKey]['hotelName'] = $hotelValue->findHotel->first()->name;
            $hotelArray[$hotelKey]['hotelId'] = $hotelValue->hotel_id;
            $hotelArray[$hotelKey]['placeId'] = $hotelValue->place_id;
            $hotelArray[$hotelKey]['placeName'] = $hotelValue->findPlace()->first()->name;
            $hotelArray[$hotelKey]['no_of_nights'] = $hotelValue->no_of_nights;
            $hotelArray[$hotelKey]['mealType'] = $hotelValue->findMealType->first()->plan;
            $hotelArray[$hotelKey]['roomCategory'] = $hotelValue->findRoomCategory->first()->name;
            $hotelArray[$hotelKey]['singleRoomRate'] = $hotelValue->adult_rate_single_room;
            $hotelArray[$hotelKey]['doubleRoomRate'] = $hotelValue->adult_rate_double_room;
            $hotelArray[$hotelKey]['tripleRoomRate'] = $hotelValue->adult_rate_triple_room;
            $hotelArray[$hotelKey]['cwbRate'] = $hotelValue->child_rate;
            $hotelArray[$hotelKey]['cnbRate'] = $hotelValue->child_w_out_bed_rate;
        }
        $finalArray = array('id' => $packageId, 'packages' => $packages, 'latestPrice' => $packages->findPackagePrice{0},
            'places' => $places, 'countryDesc' => $countryDesc, 'path' => $path, 'countryId' => $countryId, 'hotels' => $hotelArray,
            'userHtml' => $userhtml);

        return $finalArray;
    }

    function getGoogleGenDirectionPathStatic($PlaceList, $zoom = 7, $size = "500x500")
    {
        $con_list = array();
        foreach ($PlaceList as $PlaceItem) {

            $PlaceDetails = Place::where('ID', $PlaceItem->place_id)->get()->first();

            $con_name = $PlaceDetails->FindCountry->first()->name;
            $con_list[] = $PlaceDetails->name . '+' . $con_name;
        }

        $way_point = implode("|", $con_list);


        $path = "//maps.googleapis.com/maps/api/staticmap?&zoom=$zoom&center=$con_name&size=$size&style=element:labels|visibility:on&style=element:geometry.stroke|visibility:off&style=feature:landscape|element:geometry|saturation:-100&style=feature:water|saturation:-100|invert_lightness:true&key=AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc&markers=color:green|$way_point";
        return $path;
    }

    //  --------------------------------------------------------front end --------------------------------------------------

    public function getHotelData()
    {
        $roomCategories = RoomCategory::all(['ID', 'name']);
        $meals = Meal::all('ID', 'long_name', 'plan');
        $mealSelect = '<select name="fd_hotel_meal_type[]" id="fd_hotel_meal_type" class="form-control selectpicker fd_hotel_meal_type" data-live-search="true">';
        foreach ($meals as $meal) {
            $mealSelect .= '<option value="' . $meal->ID . '">' . $meal->plan . ' - ' . $meal->long_name . '</option>';
        }
        $mealSelect .= '</select>';

        $roomCategorySelect = '<select name="fd_hotel_room_category[]" id="fd_hotel_room_category" class="form-control selectpicker fd_hotel_room_category" data-live-search="true">';
        foreach ($roomCategories as $roomCategory) {
            $roomCategorySelect .= '<option value="' . $roomCategory->ID . '">' . $roomCategory->name . '</option>';
        }
        $roomCategorySelect .= '</select>';
        return array('mealSelect' => $mealSelect, 'roomCategory' => $roomCategorySelect);
    }

    public function showPackages(Request $request)
    {

        $Data = $request->input();
        $placeModel = new Place();

        $rules = [
            'fd_front_pax' => 'required|array',
            'fd_front_arrival_date' => 'required|array',
            'fd_front_pax.adult' => 'numeric|required|min:1'
        ];


        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }
        $Image = new ImgaeGenerator();

        $country = $request->fd_country;

        $year = $request->fd_front_arrival_date['year'];
        $month = $request->fd_front_arrival_date['month'];
        $date = $request->fd_front_arrival_date['day'];

        $noOfAdults = $request->fd_front_pax['adult'];
        $noOfCwb = $request->fd_front_pax['cwb'];
        $noOfCnb = $request->fd_front_pax['cnb'];
        $noOfAdults = 2;
        $arrivalDate = Carbon::create($year, $month, $date);

        $adultPackageChargesColumn = "";
        if($noOfAdults == 1) {
            $adultPackageChargesColumn = "adult_rate";
        } else if ($noOfAdults  == 2) {
            $adultPackageChargesColumn = "adult_rate_1";
        } else if ($noOfAdults == 3) {
            $adultPackageChargesColumn = "adult_rate_2";
        } else if ($noOfAdults == 4) {
            $adultPackageChargesColumn = "adult_rate_3";
        } else if ($noOfAdults == 5) {
            $adultPackageChargesColumn = "adult_rate_4";
        } else if ($noOfAdults == 6) {
            $adultPackageChargesColumn = "adult_rate_5";
         }else if ($noOfAdults == 7) {
            $adultPackageChargesColumn = "adult_rate_6";
        } else if ($noOfAdults == 8) {
            $adultPackageChargesColumn = "adult_rate_7";
        } else if ($noOfAdults == 9) {
            $adultPackageChargesColumn = "adult_rate_8";
        } else if ($noOfAdults == 10) {
            $adultPackageChargesColumn = "adult_rate_9";
        } else if ($noOfAdults == 11) {
            $adultPackageChargesColumn = "adult_rate_10";
        } else if ($noOfAdults == 12) {
            $adultPackageChargesColumn = "adult_rate_11";
        } else if ($noOfAdults == 13) {
            $adultPackageChargesColumn = "adult_rate_12";
        } else if ($noOfAdults == 14) {
            $adultPackageChargesColumn = "adult_rate_13";
        } else if ($noOfAdults == 15) {
            $adultPackageChargesColumn = "adult_rate_14";
        } else if($adultPackageChargesColumn > 15){
            $adultPackageChargesColumn = "adult_rate_14";
        }

        $fdPackages = apple_fd_packages::where('status', '=', 1)
            ->where('date_from', '<', $arrivalDate->toDateString())
            ->where('date_to', '>', $arrivalDate->toDateString())
            ->where('country', '=', $country)
            ->where('group_package', '=', 0)
            ->get();


        $rateColumnName = "";
        if ($noOfAdults == 1) {
            $rateColumnName = "adult_rate_double_room";
        } else {
            $rateColumnName = "adult_rate_double_room";
        }
        $currentUser = Auth::user()->id;

        foreach ($fdPackages as $key => $fdPackage) {
            $packagePlaces = $fdPackage->findPlaces;
            $hotelRate = 0;
            foreach ($packagePlaces as $key => $place) {

                $packageHotelsObj = apple_fd_packages_hotels::where('fd_package_id', '=', $place->fd_package_id)
                    ->where('place_id', '=', $place->place_id)->first();
                $hotelRate += $packageHotelsObj->$rateColumnName * $packageHotelsObj->no_of_nights;

            }
            if ($noOfAdults > 1) {
                $hotelRate = $hotelRate / 2;
            }
            $adultSupplementCost = 0;
            $cwbSupplementCost = 0;
            $cnbSupplementCost = 0;
            if(count($fdPackage->findSuppliment) > 0) {
                foreach ($fdPackage->findSuppliment as $supplementCostKey => $supplement){
                    $fromDate = Carbon::parse($supplement->from);
                    $toDate = Carbon::parse($supplement->to);
                    $valid = $arrivalDate->between($fromDate, $toDate);
                    if($valid){
                        $adultSupplementCost += $supplement->adult_cost;
                        $cwbSupplementCost += $supplement->cwb_cost;
                        $cnbSupplementCost += $supplement->cnb_cost;
                    }
                }
            }


            $fdPackage->hotelRate = $hotelRate;
            $fdPackage->packageCost = $fdPackage->findPackagePrice{0}->$adultPackageChargesColumn + $adultSupplementCost;
            $fdPackage->totalCost = number_format(($fdPackage->findPackagePrice{0}->$adultPackageChargesColumn + $adultSupplementCost) + $hotelRate, 2);

            if (count($fdPackage->findPackagePrice) > 1) {
                $fdPackage->DiscountTotalCost = number_format(($fdPackage->findPackagePrice{1}->$adultPackageChargesColumn + $adultSupplementCost) + $hotelRate, 2);
            }

            $noOfNights = 0;
            foreach ($fdPackage->findPlaces as $placeKey => $placesValue) {
                $noOfNights += $placesValue->no_of_nights;
            }
            $days = $noOfNights + 1;
            $fdPackage->DayNight = $noOfNights . " Nights " . $days . " Days";


            if ($fdPackage->group_package == 1) {
                $fdPackage->arrival_date_format = Carbon::parse($fdPackage->arrival_date)->toFormattedDateString();
            }

            // package access

            $allowedUsers = collect($fdPackage->findAllowedPersons);
            $filteredItems = $allowedUsers->where('user_id', '=', $currentUser);
            $fdPackage->allow = $filteredItems->count();
        }
        foreach ($fdPackages as $key => $subArr) {
            if ($subArr->allow == 0) {
                unset($fdPackages[$key]);
            }
        }

        if ($fdPackages->count() == 0) {
            $Status['status'] = false;
            $messages['msgs'][0] = "No Packages Available";
            $Status['error'] = $messages;
            return $Status;
        }


        $groupFdPacks = apple_fd_packages::where('arrival_date', '=', $arrivalDate->toDateString())
            ->where('country','=',$country)
            ->first();

        $allowedUsers = collect($fdPackage->findAllowedPersons);
        $filteredItems = $allowedUsers->where('user_id', '=', $currentUser);

        if($filteredItems->count() > 0) {

            if (!empty($groupFdPacks)) {
                $fdQuotation = apple_fd_packages_quotation::where('fd_package_id', '=', $groupFdPacks->id)
                    ->where('status', '=', 2)
                    ->where('user_id', '=', $currentUser)
                    ->orderBy('id', 'DESC')->first();

                if (!empty($fdQuotation)) {
                    $existingPax =  $fdQuotation->findPax->toArray();
                    $totalExistingPax = $existingPax['no_of_adults'] + $existingPax['no_of_cwb'] + $existingPax['no_of_cnb'];
                    $maximumPaxAllowed = $fdQuotation->findFdPackage->max_pax;
                    $remainingPax = $maximumPaxAllowed - $totalExistingPax;
                    $fdQuotation->imagePath = $fdQuotation->findFdPackage->findImages{0}->image_path;
                    $fdQuotation->package_name = $fdQuotation->findFdPackage->package_name;
                    $fdQuotation->pax = $existingPax;
                    $fdQuotation->remainingPax = $remainingPax ?? 0;
                    $fdQuotation->count = apple_fd_packages_quotation::where('fd_package_id', '=', $groupFdPacks->id)
                        ->where('status', '=', 2)->orderBy('id', 'DESC')->get()->count();

                    $Status['groupFdPackHtml'] = View::make('fd_packages.fd_group_packages_info', compact('fdQuotation', 'adultPackageChargesColumn'))->render();


                } else {

                    $adultSupplementCost = 0;
                    $cwbSupplementCost = 0;
                    $cnbSupplementCost = 0;
                    if(count($groupFdPacks->findSuppliment) > 0) {
                        foreach ($groupFdPacks->findSuppliment as $supplementCostKey => $supplement){
                            $fromDate = Carbon::parse($supplement->from);
                            $toDate = Carbon::parse($supplement->to);
                            $valid = $arrivalDate->between($fromDate, $toDate);
                            if($valid){
                                $adultSupplementCost += $supplement->adult_cost;
                                $cwbSupplementCost += $supplement->cwb_cost;
                                $cnbSupplementCost += $supplement->cnb_cost;
                            }
                        }
                    }

                    $hotelRate = 0;
                    $fdGroupPackPlaces = $groupFdPacks->findPlaces;
                    foreach ($fdGroupPackPlaces as $key => $place) {

                        $packageHotelsObj = apple_fd_packages_hotels::where('fd_package_id', '=', $place->fd_package_id)
                            ->where('place_id', '=', $place->place_id)->first();
                        $hotelRate += $packageHotelsObj->$rateColumnName * $packageHotelsObj->no_of_nights;

                    }
                    if ($noOfAdults > 1) {
                        $hotelRate = $hotelRate / 2;
                    }

                    $groupFdPacks->hotelRate = $hotelRate;
                    $groupFdPacks->packageCost = $groupFdPacks->findPackagePrice{0}->$adultPackageChargesColumn + $adultSupplementCost;
                    $groupFdPacks->totalCost = number_format(($groupFdPacks->findPackagePrice{0}->$adultPackageChargesColumn + $adultSupplementCost) + $hotelRate, 2);

                    if (count($groupFdPacks->findPackagePrice) > 1) {
                        $groupFdPacks->DiscountTotalCost = number_format(($groupFdPacks->findPackagePrice{1}->$adultPackageChargesColumn + $adultSupplementCost ) + $hotelRate, 2);
                    }

                    $noOfNights = 0;
                    foreach ($groupFdPacks->findPlaces as $placeKey => $placesValue) {
                        $noOfNights += $placesValue->no_of_nights;
                    }
                    $days = $noOfNights + 1;
                    $groupFdPacks->DayNight = $noOfNights . " Nights " . $days . " Days";


                    $groupFdPacks->arrival_date_format = $arrivalDate->toFormattedDateString();

                    $Status['groupFdPackHtml'] = View::make('fd_packages.fd_show_group_packages', compact('groupFdPacks', 'adultPackageChargesColumn'))->render();
                }

                $Status['status'] = "group";
                $Status['existingPackagesHtml'] = View::make('fd_packages.fd_show_packages', compact('fdPackages', 'adultPackageChargesColumn'))->render();
                return $Status;
            }
        }
        return view('fd_packages.fd_show_packages', compact('fdPackages', 'adultPackageChargesColumn'));
    }

    public function generateQuotation(Request $request)
    {

        $Data = $request->input();
        $placeModel = new Place();

        $rules = [
            'fd_front_pax' => 'required|array',
            'fd_front_arrival_date' => 'required|array',
            'fd_front_pax.adult' => 'numeric|required|min:1'
        ];


        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }

        $fdPackageId = $request->fd_front_package_id;
        $countryId = $request->fd_country;
//        return $request->all();
        $apple_fd_packages = apple_fd_packages::where('id', '=', $fdPackageId)->with('findPackagePrice', 'findHotels', 'findPlaces', 'findImages')->first();

        $noOfNights = 0;
        foreach ($apple_fd_packages->findPlaces as $placeKey => $placesValue) {
            $noOfNights += $placesValue->no_of_nights;
        }

        $pickUpLocation = Place::where('ID', '=', $apple_fd_packages->pick_up)->first()->name;
        $pickUpLocationId = Place::where('ID', '=', $apple_fd_packages->pick_up)->first()->ID;
        $dropOffLocation = Place::where('ID', '=', $apple_fd_packages->drop_off)->first()->name;
        $dropOffLocationID = Place::where('ID', '=', $apple_fd_packages->drop_off)->first()->ID;


        $year = $request->fd_front_arrival_date['year'];
        $month = $request->fd_front_arrival_date['month'];
        $date = $request->fd_front_arrival_date['day'];

        $PackageValidFrom = Carbon::parse($apple_fd_packages->date_from);
        $PackageValidTo = Carbon::parse($apple_fd_packages->date_to)->subDays($noOfNights);

        $arrivalDate = Carbon::create($year, $month, $date);
        $tourStartDate = Carbon::create($year, $month, $date);
        $AStartDate = Carbon::create($year, $month, $date);
        $valid = $arrivalDate->between($PackageValidFrom, $PackageValidTo);

        if ($valid == false) {
            $Status['status'] = false;
            $messages['msgs'][0] = "Arrival Date is Invalid For Selected Package";
            $Status['error'] = $messages;
            return $Status;
        }

        $noOfAdults = $request->fd_front_pax['adult'];
        $noOfCwb = $request->fd_front_pax['cwb'];
        $noOfCnb = $request->fd_front_pax['cnb'];
        $totalPax = $noOfAdults + $noOfCwb + $noOfCnb;


        $vehicle = $this->getPaxToVehicle($countryId, $totalPax, $arrivalDate);
        if (!$vehicle) {
            $Status['status'] = false;
            $messages['msgs'][0] = "Coudn't Identify The Vehicle Type";
            $Status['error'] = $messages;
            return $Status;
        }

        $vehicleName = VehicleType::where('ID', '=', $vehicle->vehicle)->first()->name;
        // initial Cost without Hotel Charges
        $totalHotelCost = 0;

        $adultCost = 0;
        $cwdCost = 0;
        $cnbCost = 0;

        $adultPackageChargesColumn = "";
        if($noOfAdults == 1) {
            $adultPackageChargesColumn = "adult_rate";
        } else if ($noOfAdults  == 2) {
            $adultPackageChargesColumn = "adult_rate_1";
        } else if ($noOfAdults == 3) {
            $adultPackageChargesColumn = "adult_rate_2";
        } else if ($noOfAdults == 4) {
            $adultPackageChargesColumn = "adult_rate_3";
        } else if ($noOfAdults == 5) {
            $adultPackageChargesColumn = "adult_rate_4";
        } else if ($noOfAdults == 6) {
            $adultPackageChargesColumn = "adult_rate_5";
        }else if ($noOfAdults == 7) {
            $adultPackageChargesColumn = "adult_rate_6";
        } else if ($noOfAdults == 8) {
            $adultPackageChargesColumn = "adult_rate_7";
        } else if ($noOfAdults == 9) {
            $adultPackageChargesColumn = "adult_rate_8";
        } else if ($noOfAdults == 10) {
            $adultPackageChargesColumn = "adult_rate_9";
        } else if ($noOfAdults == 11) {
            $adultPackageChargesColumn = "adult_rate_10";
        } else if ($noOfAdults == 12) {
            $adultPackageChargesColumn = "adult_rate_11";
        } else if ($noOfAdults == 13) {
            $adultPackageChargesColumn = "adult_rate_12";
        } else if ($noOfAdults == 14) {
            $adultPackageChargesColumn = "adult_rate_13";
        } else if ($noOfAdults == 15) {
            $adultPackageChargesColumn = "adult_rate_14";
        } else if($adultPackageChargesColumn > 15){
            $adultPackageChargesColumn = "adult_rate_14";
        }

        $prices = $apple_fd_packages->findPackagePrice{0};
        $packagePlaces = $apple_fd_packages->findPlaces;
        $packageImages = $apple_fd_packages->findImages;
        $desc = $apple_fd_packages->itinerary_desc ?? "";
        $includes = $apple_fd_packages->includes ?? "";
        $excludes = $apple_fd_packages->excludes ?? "";

        $adultSupplementCost = 0;
        $cwbSupplementCost = 0;
        $cnbSupplementCost = 0;
        if(count($apple_fd_packages->findSuppliment) > 0) {
            foreach ($apple_fd_packages->findSuppliment as $supplementCostKey => $supplement){
                $fromDate = Carbon::parse($supplement->from);
                $toDate = Carbon::parse($supplement->to);
                $valid = $arrivalDate->between($fromDate, $toDate);
                if($valid){
                    $adultSupplementCost += $supplement->adult_cost;
                    $cwbSupplementCost += $supplement->cwb_cost;
                    $cnbSupplementCost += $supplement->cnb_cost;
                }
            }
        }


        $adultCost = ($prices->$adultPackageChargesColumn + $adultSupplementCost) * $noOfAdults;
        $cwdCost = ($prices->child_rate + $cwbSupplementCost) * $noOfCwb;
        $cnbCost = ($prices->child_w_out_bed_rate + $cnbSupplementCost) * $noOfCnb;
        $packageCostArray = array('adultCost' => $prices->$adultPackageChargesColumn + $adultSupplementCost, 'cwb' => $prices->child_rate + $cwdCost, 'cnb' => $prices->child_w_out_bed_rate + $cnbCost);

        $Hotel = new Hotel();
        $RoomType = $Hotel->getHotelRoomCountWithPax($request->fd_front_pax);

        $hotelCostArray = array();
        $mealPlanArray = array();
        $noOfNightsArray = array();
        $roomTypeText = "";

        foreach ($RoomType as $RoomTypeID => $RoomCount) {
            if ($RoomCount) {
                $roomTypeText .= $RoomCount . ' ' . Room::find($RoomTypeID)->short_name . " ";
            }
        }
        $image = new ImgaeGenerator();

        foreach ($packagePlaces as $key => $place) {

            $packageHotelsObj = apple_fd_packages_hotels::where('fd_package_id', '=', $place->fd_package_id)
                ->where('place_id', '=', $place->place_id)->first();


            // meal data
            $mealPlan = $packageHotelsObj->findMealType->first()->plan . " - " . $packageHotelsObj->findMealType->first()->long_name;
            $mealPlanCode = $packageHotelsObj->findMealType->first()->plan;


            // hotel data
            if ($key == 0) {
                $hotelCostArray[$key]['checkIn'] = $tourStartDate->toDateString();
                $hotelCostArray[$key]['CheckOut'] = $tourStartDate->addDays($packageHotelsObj->no_of_nights)->toDateString();

                $hotelCostArray[$key]['checkInFormatted'] = Carbon::parse($hotelCostArray[$key]['checkIn'])->toFormattedDateString();
                $hotelCostArray[$key]['CheckOutFormatted'] = Carbon::parse($hotelCostArray[$key]['CheckOut'])->toFormattedDateString();
            } else {
                $hotelCostArray[$key]['checkIn'] = $hotelCostArray[$key - 1]['CheckOut'];
                $hotelCostArray[$key]['CheckOut'] = $this->generateDates($hotelCostArray[$key - 1]['CheckOut'], $packageHotelsObj->no_of_nights);

                $hotelCostArray[$key]['checkInFormatted'] = Carbon::parse($hotelCostArray[$key - 1]['CheckOut'])->toFormattedDateString();
                $hotelCostArray[$key]['CheckOutFormatted'] = Carbon::parse($this->generateDates($hotelCostArray[$key - 1]['CheckOut'], $packageHotelsObj->no_of_nights))->toFormattedDateString();

                if (isset($placeModel->getPlaceDistance($hotelCostArray[$key - 1]['placeId'], $place->place_id)->distance)) {
                    $hotelCostArray[$key]['Distance'] = $placeModel->getPlaceDistance($hotelCostArray[$key - 1]['placeId'], $place->place_id)->distance / 1000 . " KM";
                    $hotelCostArray[$key]['Duration'] = formatDuration($placeModel->getPlaceDistance($hotelCostArray[$key - 1]['placeId'], $place->place_id)->time);
                }
                $placeObj = Place::where('ID', '=', $hotelCostArray[$key - 1]['placeId'])->first();
                $hotelCostArray[$key]['LasePlaceName'] = $placeObj->name;
                $hotelCostArray[$key]['LasePlaceId'] = $hotelCostArray[$key - 1]['placeId'];

            }

            $hotelCostArray[$key]['hotelName'] = $packageHotelsObj->findHotel->first()->name ?? "";
            $hotelCostArray[$key]['provider'] = "package_hotel";
            $hotelCostArray[$key]['hotelImage'] = $image->getImage($packageHotelsObj->hotel_id, '3x', "hotel", 1, Hotel::find($packageHotelsObj->hotel_id)->name)[0];
            $hotelCostArray[$key]['class'] = $packageHotelsObj->findHotel->first()->class()->first()->class ?? "";
            $hotelCostArray[$key]['classStar'] = $packageHotelsObj->findHotel->first()->class()->first()->star ?? "";
            $hotelCostArray[$key]['hotelId'] = $packageHotelsObj->hotel_id;
            $hotelCostArray[$key]['mealType'] = $mealPlan;
            $hotelCostArray[$key]['mealTypeId'] = $packageHotelsObj->meal_type;
            $hotelCostArray[$key]['roomCategory'] = $packageHotelsObj->findRoomCategory->name;
            $hotelCostArray[$key]['roomCategoryId'] = $packageHotelsObj->room_category;
            $EmailsList = "";
            if (Hotel::find($packageHotelsObj->hotel_id))
                $EmailsList = implode(",", Hotel::find($packageHotelsObj->hotel_id)->Contact()->where('type', 2)->pluck('contact_id')->toArray());
            $hotelCostArray[$key]['contactEmail'] = $EmailsList;

            $hotelCostArray[$key]['mealPlanCode'] = $mealPlanCode;
            array_push($mealPlanArray, $mealPlanCode);
            array_push($noOfNightsArray, $packageHotelsObj->no_of_nights);

            $hotelCostArray[$key]['noOfNights'] = $packageHotelsObj->no_of_nights;
            $hotelCostArray[$key]['placeId'] = $place->place_id;

            $placeObj = Place::where('ID', '=', $place->place_id)->first();
            $hotelCostArray[$key]['placeName'] = $placeObj->name;
            $hotelCostArray[$key]['placeDesc'] = $placeObj->description ?? "";
            $hotelCostArray[$key]['placeImage'] = $packageImages{$key}->image_path ?? "assets/image/no_image/N_5x.jpg";
            $hotelCostArray[$key]['roomTypeText'] = $roomTypeText;

            if ($noOfCwb > 0) {
                $hotelCostArray[$key]['cost']['cwb'] = $packageHotelsObj->child_rate * $packageHotelsObj->no_of_nights * $noOfCwb;
            }
            if ($noOfCnb > 0) {
                $hotelCostArray[$key]['cost']['cnb'] = $packageHotelsObj->child_w_out_bed_rate * $packageHotelsObj->no_of_nights * $noOfCnb;
            }

            // hotel room and rates data
            foreach ($RoomType as $RoomTypeID => $RoomCount) {
                $roomColumn = "";
                if ($RoomCount) {
                    if ($RoomTypeID == 1) {
                        $roomColumn = "adult_rate_single_room";
                    } else if ($RoomTypeID == 2) {
                        $roomColumn = "adult_rate_double_room";
                    } else if ($RoomTypeID == 3) {
                        $roomColumn = "adult_rate_triple_room";
                    }
                    $hotelCostArray[$key]['cost'][$roomColumn]['adultCostPP'] = round(($packageHotelsObj->$roomColumn * $packageHotelsObj->no_of_nights) / $RoomTypeID, 2);
                    $hotelCostArray[$key]['cost'][$roomColumn]['adultCost'] = $packageHotelsObj->$roomColumn * $packageHotelsObj->no_of_nights;
                    $hotelCostArray[$key]['cost'][$roomColumn]['roomCount'] = $RoomCount;
                    $totalHotelCost += $hotelCostArray[$key]['cost'][$roomColumn]['adultCost'] * $RoomCount;
                }
            }
            if ($noOfCwb > 0) {
                $totalHotelCost += $hotelCostArray[$key]['cost']['cwb'];
            }
            if ($noOfCnb > 0) {
                $totalHotelCost += $hotelCostArray[$key]['cost']['cnb'];
            }
        }


        $adultPpHotelCost = 0;
        $cwbPpHotelCost = 0;
        $cnbPpHotelCost = 0;

        $roomCostBreakDown = array();
        $adultSingleRoomCost = 0;
        $adultDoubleRoomCost = 0;
        $adultTripleRoomCost = 0;


        foreach ($hotelCostArray as $hotelCostKey => $hotelCostValues) {

            if (isset($hotelCostValues['cost']['adult_rate_single_room'])) {
                $adultSingleRoomCost += $hotelCostValues['cost']['adult_rate_single_room']['adultCostPP'];
            }
            if (isset($hotelCostValues['cost']['adult_rate_double_room'])) {
                $adultDoubleRoomCost += $hotelCostValues['cost']['adult_rate_double_room']['adultCostPP'];
            }
            if (isset($hotelCostValues['cost']['adult_rate_triple_room'])) {
                $adultTripleRoomCost += $hotelCostValues['cost']['adult_rate_triple_room']['adultCostPP'];
            }
            if (isset($hotelCostValues['cost']['cwb'])) {
                $cwbPpHotelCost += $hotelCostValues['cost']['cwb'] / $noOfCwb;
            }
            if (isset($hotelCostValues['cost']['cnb'])) {
                $cnbPpHotelCost += $hotelCostValues['cost']['cnb'] / $noOfCnb;
            }
        }


        if ($adultSingleRoomCost > 0) {
            $roomCostBreakDown['adultSingleRoomCost']['cost'] = round($adultSingleRoomCost, 2);
            $roomCostBreakDown['adultSingleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
            $roomCostBreakDown['adultSingleRoomCost']['totalCost'] = $roomCostBreakDown['adultSingleRoomCost']['cost'] +  $roomCostBreakDown['adultSingleRoomCost']['packageCost'] ;
        }
        if ($adultDoubleRoomCost > 0) {
            $roomCostBreakDown['adultDoubleRoomCost']['cost'] = round($adultDoubleRoomCost, 2);
            $roomCostBreakDown['adultDoubleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost ;
            $roomCostBreakDown['adultDoubleRoomCost']['totalCost'] = $roomCostBreakDown['adultDoubleRoomCost']['cost'] + $roomCostBreakDown['adultDoubleRoomCost']['packageCost'];
        }
        if ($adultTripleRoomCost > 0) {
            $roomCostBreakDown['adultTripleRoomCost']['cost'] = round($adultTripleRoomCost, 2);
            $roomCostBreakDown['adultTripleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
            $roomCostBreakDown['adultTripleRoomCost']['totalCost'] = $roomCostBreakDown['adultTripleRoomCost']['cost'] + $roomCostBreakDown['adultTripleRoomCost']['packageCost'];
        }


        $totCost = 0;
        if(isset($roomCostBreakDown['adultSingleRoomCost'])){
            $totCost += $roomCostBreakDown['adultSingleRoomCost']['totalCost']  * ($RoomType[1] * 1);
        }
        if(isset($roomCostBreakDown['adultDoubleRoomCost'])){
            $totCost += $roomCostBreakDown['adultDoubleRoomCost']['totalCost']  * ($RoomType[2] * 2);
        }
        if(isset($roomCostBreakDown['adultTripleRoomCost'])){
            $totCost += $roomCostBreakDown['adultTripleRoomCost']['totalCost']  * ($RoomType[3] * 3);
        }

        if($noOfCwb > 0){
            $totCost += ($prices->child_rate + $cwbSupplementCost + $cwbPpHotelCost) * $noOfCwb;
        }

        if($noOfCnb > 0){
            $totCost += ($prices->child_w_out_bed_rate + $cnbSupplementCost + $cnbPpHotelCost) * $noOfCnb;
        }

        $finalCostArray = array(
            'adultCostBreakDown' => $roomCostBreakDown,
            'cwbHotelPpCost' => $cwbPpHotelCost,
            'cwbPackagePpCost' => $prices->child_rate + $cwbSupplementCost,
            'cwbPpTotalCost' => $prices->child_rate + $cwbSupplementCost + $cwbPpHotelCost,
            'cnbHotelPpCost' => $cnbPpHotelCost,
            'cnbPackagePpCost' => $prices->child_w_out_bed_rate + $cnbSupplementCost ,
            'cnbPpTotalCost' => $prices->child_w_out_bed_rate + $cnbSupplementCost + $cnbPpHotelCost,
            'grandCost' => $totCost
        );

        // itineraries
        $itinerariesArray = array();
        $day = 1;
        $itenaryLastIndex = 0;
        foreach ($hotelCostArray as $hotelCostKey => $hotelCostValues) {

            if (isset($hotelCostValues['LasePlaceName'])) {
                $itinerariesArray[$hotelCostKey]['LastPlaceName'] = $hotelCostValues['LasePlaceName'];
                $itinerariesArray[$hotelCostKey]['LastPlaceId'] = $hotelCostValues['LasePlaceId'];
                $itinerariesArray[$hotelCostKey]['Distance'] = $hotelCostValues['Distance'];
                $itinerariesArray[$hotelCostKey]['Duration'] = $hotelCostValues['Duration'];
            } else {
                $itinerariesArray[$hotelCostKey]['LastPlaceName'] = $pickUpLocation;
                $itinerariesArray[$hotelCostKey]['LastPlaceId'] = $pickUpLocationId;
                $itinerariesArray[$hotelCostKey]['Distance'] = $placeModel->getPlaceDistance($pickUpLocationId, $hotelCostValues['placeId'])->distance / 1000 . " KM";
                $itinerariesArray[$hotelCostKey]['Duration'] = formatDuration($placeModel->getPlaceDistance($pickUpLocationId, $hotelCostValues['placeId'])->time);

            }

            $itinerariesArray[$hotelCostKey]['placeID'] = $hotelCostValues['placeId'];
            $itinerariesArray[$hotelCostKey]['placeName'] = $hotelCostValues['placeName'];
            $itinerariesArray[$hotelCostKey]['placeDesc'] = $hotelCostValues['placeDesc'];
            $itinerariesArray[$hotelCostKey]['placeImage'] = $hotelCostValues['placeImage'];
            $itinerariesArray[$hotelCostKey]['roomTypeText'] = $hotelCostValues['roomTypeText'];
            $itinerariesArray[$hotelCostKey]['noOfNights'] = $hotelCostValues['noOfNights'];
            $itinerariesArray[$hotelCostKey]['checkIn'] = $hotelCostValues['checkIn'];
            $itinerariesArray[$hotelCostKey]['checkInFormatted'] = $hotelCostValues['checkInFormatted'];

            $startDate = Carbon::parse($hotelCostValues['checkIn']);
            $endDate = Carbon::parse($hotelCostValues['CheckOut']);
            $dateRange = $this->generateDateRange($startDate, $endDate);


            foreach ($dateRange as $dateKey => $dateValue) {
                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['day'] = $day;
                // attractions
                $attractionsObj = apple_fd_packages_attractions::where('fd_package_id', '=', $fdPackageId)
                    ->where('place_id', '=', $hotelCostValues['placeId'])->get();
                if (count($attractionsObj) > 0) {

                    foreach ($attractionsObj as $attractionKey => $attractionValue) {
                        if (($dateKey + 1) == $attractionValue->day) {

                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_id'] = $attractionValue->attraction_id;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['type'] = $attractionValue->attraction_type;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['adult_rate'] = $attractionValue->adult_rate;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['cwb_rate'] = $attractionValue->child_rate;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['cnb_rate'] = $attractionValue->child_w_out_bed_rate;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];

                            if ($attractionValue->attraction_type == "attraction") {

                                $attraction = AttractionModel::where('ID', '=', $attractionValue->attraction_id)->first();
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $attraction->point;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $attraction->description;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "attraction", 1, $attraction->point)[0];

                            } else if ($attractionValue->attraction_type == "city_tour") {

                                $cityTour = CityTour::where('ID', '=', $attractionValue->attraction_id)->first();
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $cityTour->name;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $cityTour->description;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "city_tour", 1, $cityTour->name)[0];

                            } else {
                                $excursion = Excursion::where('ID', '=', $attractionValue->attraction_id)->first();
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $excursion->name;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $excursion->description;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "place", 1, $excursion->name)[0];
                            }

                        } else {
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['text'] = "Day at leisure at the Hotel!";
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];
                        }
                    }
                } else {
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['text'] = "Day at leisure at the Hotel!";
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];
                }


                $day++;

            }
            $itenaryLastIndex = $hotelCostKey;
        }


        //  drop off array data
        $itenaryLastIndex = $itenaryLastIndex + 1;
        $lastArrObj = end($itinerariesArray);
        $lastDateObj = end($lastArrObj['dates']);
        $itinerariesArray[$itenaryLastIndex]['LastPlaceName'] = $lastArrObj['placeName'];
        $itinerariesArray[$itenaryLastIndex]['LastPlaceId'] = $lastArrObj['placeID'];
        $itinerariesArray[$itenaryLastIndex]['Distance'] = $placeModel->getPlaceDistance($lastArrObj['placeID'], $dropOffLocationID)->distance / 1000 . " KM";;
        $itinerariesArray[$itenaryLastIndex]['Duration'] = formatDuration($placeModel->getPlaceDistance($lastArrObj['placeID'], $dropOffLocationID)->time);
        $itinerariesArray[$itenaryLastIndex]['placeID'] = $dropOffLocationID;
        $itinerariesArray[$itenaryLastIndex]['checkInFormatted'] = Carbon::parse($lastDateObj['date'])->addDay()->toFormattedDateString();
        $itinerariesArray[$itenaryLastIndex]['placeName'] = $dropOffLocation;
        $itinerariesArray[$itenaryLastIndex]['dates'][0]['date'] = Carbon::parse($lastDateObj['date'])->addDay()->toDateString();
        $itinerariesArray[$itenaryLastIndex]['dates'][0]['day'] = $day;


        $mealPlanArray = array_unique($mealPlanArray);
        $nightsSum = array_sum($noOfNightsArray);
        $days = (int)$nightsSum + 1;
        $DaysNightsText = $nightsSum . " Nights " . $days . " Days";
        $includesText = "";
        foreach($hotelCostArray as $key => $value) {
            $text = $value['mealType']." at " . $value['hotelName'];
            $includesText.= $this->getIncludesTemplate($text);
        }
        $includesText.= "</ul>";
        $includes = str_replace("</ul>",$includesText,$includes);

        $textArray = array('desc' => $desc, 'inclutions' => $includes, 'exclusions' => $excludes, 'nightsDaysCount' => $DaysNightsText);
        $arrivalDateArray = array('year' => $AStartDate->year, 'month' => $AStartDate->month, 'day' => $AStartDate->day);

        $groupPackage = $apple_fd_packages->group_package;
        $allotmentTable = "";

        if ($groupPackage == 0) {
            $allotmentTable = apple_fd_packages_hotel_allotments::class;
        } else {
            $allotmentTable = apple_fd_packages_group_hotel_allotments::class;
        }

        $hotelCostArray = $this->getAllotment($hotelCostArray, $allotmentTable);

        $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
        $confirmData = array(
            'user' => Auth::user()->id,
            'roleId' => $roleId,
        );


        $quotationData = array('textData' => $textArray,
            'packageId' => $fdPackageId,
            'pax' => $request->fd_front_pax,
            'mealPlanTypes' => implode(' / ', $mealPlanArray),
            'arrivalDate' => $AStartDate->year . " / " . $AStartDate->month . " / " . $AStartDate->day,
            'arrivalDateFormat' => $AStartDate->year . "-" . $AStartDate->month . "-" . $AStartDate->day,
            'arrival_date_obj' => $arrivalDateArray,
            'deadLineDate' => Carbon::create($AStartDate->year, $AStartDate->month, $AStartDate->day)->addDays(14)->toDayDateTimeString(),
            'vehicleName' => $vehicleName,
            'RoomTypes' => $RoomType,
            'packageCost' => $packageCostArray,
            'hotelData' => $hotelCostArray,
            'itineraries' => $itinerariesArray,
            'finalCost' => $finalCostArray,
            'totalHotelCost' => $totalHotelCost,
            'packageName' => $apple_fd_packages->package_name,
            'packageType' => $apple_fd_packages->group_package,
            'packageId' => $apple_fd_packages->id,
            'confirmData' => $confirmData,
            'QuotationCurrency' => "USD"
        );
//            return json_encode($quotationData);
        return view('fd_packages.fd_packages_quotation', compact('quotationData'));

    }

    function getPaxToVehicle($country, $totalPax, $arrival_date)
    {

        $Vehicle = Vehicle::where('pax_max', '>=', $totalPax);
        $Vehicle->where('pax_min', '<=', $totalPax);
        $Vehicle->where("country", $country);
        $Vehicle->where("start_date", "<=", $arrival_date);
        $Vehicle->where("end_date", ">=", $arrival_date);
        $Vehicle = $Vehicle->first();

        return $Vehicle;

    }

    function generateDates($date, $noOfDays)
    {

        $date = Carbon::parse($date)->addDays($noOfDays);
        return $date->toDateString();
    }

    function generateDateRange(Carbon $start_date, Carbon $end_date)
    {
        $dates = [];
        $end_date = $end_date->subDays(1);

        for ($date = $start_date->copy(); $date->lte($end_date); $date->addDay()) {
            $dates[] = $date->format('Y-m-d');
        }

        return $dates;

    }

    public function getAllotment($hotelCostArray, $allotmentTable)
    {
        $newHotelCostArray = [];
        if (isset($hotelCostArray)) {
            foreach ($hotelCostArray as $Hotel) {
                $Hotel["allotment"] = $this->checkAllotmentItem($Hotel, $allotmentTable);
                $newHotelCostArray[] = $Hotel;
            }
        }
        return $newHotelCostArray;
    }

    public function checkAllotmentItem($Hotel, $allotmentTable, $HotelID = null)
    {
        $HotelID = isset($HotelID) ? $HotelID : $Hotel["hotelId"];
        $allotments = apple_fd_packages_quotation_hotel_rates::where("hotel_id", $HotelID)
            ->where("check_in", "<=", $Hotel["checkIn"])
            ->where("check_out", ">=", $Hotel["checkIn"])
            ->groupBy("reference_id")
            ->get();

        $roomCount = 0;
        foreach ($allotments as $allotment) {
            $roomCount = $roomCount + $allotment["adult_single_room_count"] ?? 0 + $allotment["adult_double_room_count"] ?? 0 + $allotment["adult_triple_room_count"] ?? 0;
        }

        return $this->checkAllotments($Hotel, $roomCount, $HotelID, $allotmentTable);

    }

    public function checkAllotments($Hotel, $roomCount, $HotelID, $allotmentTable)
    {
        $allotmentRec = $allotmentTable::where("hotel_id", $HotelID)
            ->where("start_date", "<=", $Hotel["checkIn"])
            ->where("end_date", ">=", $Hotel["checkIn"])
            ->first();

        $date = Carbon::parse($Hotel["checkIn"]);
        $now = Carbon::now();

        $diff = $date->diffInDays($now);

        if (isset($allotmentRec) && !empty($allotmentRec)) {
            if (($allotmentRec["no_of_rooms"] - $roomCount) > 0) {
                if ($diff > $allotmentRec["release_period"]) {
                    return array("status" => 1, "status_class" => "available", "status_name" => "Available", "available" => $allotmentRec["no_of_rooms"] - $roomCount, "room_count" => $roomCount);
                } else {
                    return array("status" => 2, "status_class" => "on_request", "status_name" => "On Request", "available" => $allotmentRec["no_of_rooms"] - $roomCount, "room_count" => $roomCount);
                }
            } else {
                return array("status" => 2, "status_class" => "on_request", "status_name" => "On Request", "available" => $allotmentRec["no_of_rooms"] - $roomCount, "room_count" => $roomCount);
            }
        } else {
            return array("status" => 2, "status_class" => "on_request", "status_name" => "On Request", "available" => 0, "room_count" => $roomCount);
        }
    }

    public function getPackageHotelList(Request $request)
    {
        $image = new ImgaeGenerator();
        $packageId = $request->packageId;
        $placeId = $request->placeId;
        $selectedHotelId = $request->selectedHotelId;
        $type = $request->type;
        $index = $request->index;
        $hotelArray = array();
        $localHotelsArray = array();
        $quotationArray = $request->currentJson;
        $quotationArray = json_decode($quotationArray, true);
        $arrivalDate = $quotationArray['arrivalDateFormat'];

        $apple_fd_packages = apple_fd_packages::where('id', '=', $packageId)->first();
        $groupPackage = $apple_fd_packages->group_package;

        $allotmentTable = "";

        if ($groupPackage == 0) {
            $allotmentTable = apple_fd_packages_hotel_allotments::class;
        } else {
            $allotmentTable = apple_fd_packages_group_hotel_allotments::class;
        }


        $packageHotelsObj = apple_fd_packages_hotels::where('fd_package_id', '=', $packageId)
            ->where('place_id', '=', $placeId)->get();

        $selectedHotelobj = apple_fd_packages_hotels::where('fd_package_id', '=', $packageId)
            ->where('place_id', '=', $placeId)
            ->where('hotel_id', '=', $selectedHotelId)
            ->first();
        $selectedHotelDoubleRoomCharge = $selectedHotelobj->adult_rate_double_room;
        $existingHotels = array();

        foreach ($packageHotelsObj as $packageHotelKey => $packageHotelsValue) {
            $hotelArray[$packageHotelKey]['image'] = $image->getImage($packageHotelsValue->hotel_id, '3x', "hotel", 1, Hotel::find($packageHotelsValue->hotel_id)->name)[0];
            $hotelArray[$packageHotelKey]['hotelName'] = $packageHotelsValue->findHotel->first()->name ?? "";
            $hotelArray[$packageHotelKey]['hotelId'] = $packageHotelsValue->hotel_id;
            $hotelArray[$packageHotelKey]['allotments'] = $this->checkAllotmentItem($quotationArray["hotelData"][$index], $allotmentTable, $packageHotelsValue->hotel_id);
            array_push($existingHotels, $packageHotelsValue->hotel_id);
            $hotelArray[$packageHotelKey]['class'] = $packageHotelsValue->findHotel->first()->class()->first()->class ?? "";
            $hotelArray[$packageHotelKey]['star'] = $packageHotelsValue->findHotel->first()->class()->first()->star ?? "";
            $hotelArray[$packageHotelKey]['placeId'] = $packageHotelsValue->place_id;
            $difference = $selectedHotelDoubleRoomCharge - $packageHotelsValue->adult_rate_double_room;
            if ($difference == 0) {
                $difference = 0;
            } else if ($difference < 0) {
                $difference = "+" . str_replace("-", "", $difference);
            } else {
                $difference = "-" . str_replace("-", "", $difference);;
            }
            $hotelArray[$packageHotelKey]['difference'] = $difference;
        }
        $localHotels = apple_fd_packages_localhotels::where('place_id', '=', $placeId)
            ->whereNotIn('hotel_id', $existingHotels)
            ->where('fd_package_id', '=', $packageId)
            ->get();

        if ($localHotels->count() > 0) {

            foreach ($localHotels as $localHotelKey => $localHotelValue) {

                $hotelInfo = Hotel::where('ID', '=', $localHotelValue->hotel_id)->first();

                $rateDiff = Rates::where('hotel', '=', $localHotelValue->id)
                    ->where('room_type', '=', 2)
                    ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $arrivalDate)
                    ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $arrivalDate)
                    ->orderBy('ID', "DESC")
                    ->get();

                if ($rateDiff->count() > 0) {

                    $singleRoomRate = $this->getChangedHotelRateAdults($selectedHotelId, 1, $arrivalDate);
                    $tripleRoomRate = $this->getChangedHotelRateAdults($selectedHotelId, 3, $arrivalDate);
                    $cwbRate = $this->getChangedHotelRateChild($selectedHotelId, 1, $arrivalDate, 2, 12);
                    $cnbRate = $this->getChangedHotelRateChild($selectedHotelId, 1, $arrivalDate, 0, 2);

                    if (!($singleRoomRate == false or $cwbRate == false or $cnbRate == false)) {


                        $localHotelsArray[$localHotelKey]['image'] = $image->getImage($localHotelValue->hotel_id, '3x', "hotel", 1, $hotelInfo->name)[0];
                        $localHotelsArray[$localHotelKey]['hotelName'] = $hotelInfo->name ?? "";
                        $localHotelsArray[$localHotelKey]['hotelId'] = $localHotelValue->hotel_id;
                        $localHotelsArray[$localHotelKey]['class'] = $hotelInfo->class()->first()->class ?? "";
                        $localHotelsArray[$localHotelKey]['star'] = $hotelInfo->class()->first()->star ?? "";
                        $localHotelsArray[$localHotelKey]['placeId'] = $localHotelValue->place_id;

                        $difference = $selectedHotelDoubleRoomCharge - $rateDiff[0]->rate;
                        if ($difference == 0) {
                            $difference = 0;
                        } else if ($difference < 0) {
                            $difference = "+" . str_replace("-", "", $difference);
                        } else {
                            $difference = "-" . str_replace("-", "", $difference);
                        }

                        $localHotelsArray[$localHotelKey]['difference'] = $difference;
                    }
                }

            }
        }
        $RoomTypes = $quotationArray['RoomTypes'];

//        return json_encode($localHotelsArray);
        if ($type == "new") {
            return view('fd_packages.fd_packages_quotation_hotel_list', compact('hotelArray', 'selectedHotelId', 'localHotelsArray', 'RoomTypes'));
        } else {
            return view('fd_packages.edit.fd_packages_quotation_hotel_list', compact('hotelArray', 'selectedHotelId', 'localHotelsArray', 'RoomTypes'));
        }
    }

    function getChangedHotelRateAdults($hotelId, $roomType, $arrivalDate)
    {
        $rates = Rates::where('hotel', '=', $hotelId)
            ->where('room_type', '=', $roomType)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $arrivalDate)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $arrivalDate)
            ->orderBy('ID', "DESC")
            ->get();

        if ($rates->count() > 0) {
            return $rates[0]->rate;
        } else {
            return false;
        }
    }

    function getChangedHotelRateChild($hotelId, $roomType, $arrivalDate, $ageFrom, $ageTo)
    {
        $rates = RatesChild::where('hotel', '=', $hotelId)
            ->where('room_type', '=', $roomType)
            ->where('age_from', '=', $ageFrom)
            ->where('age_to', '=', $ageTo)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $arrivalDate)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $arrivalDate)
            ->orderBy('ID', "DESC")
            ->get();

        if ($rates->count() > 0) {
            return $rates[0]->rate;
        } else {
            return false;
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    public function changeHotel(Request $request)
    {
        $image = new ImgaeGenerator();
        $statusClass = $request->statusClass;
        $quotationData = $request->currentJson;
        $quotationData = json_decode($quotationData, true);
        $selectedHotelId = $request->selectedHotelId;
        $placeId = $request->placeId;
        $packageId = $request->packageId;
        $type = $request->type;
        $arrivalDate = Carbon::parse($quotationData['arrivalDateFormat']);

        $existingHotelId = "";
        foreach ($quotationData['hotelData'] as $hotelKey => $hotelValues) {
            if ($hotelValues['placeId'] == $placeId) {
                $existingHotelId = $hotelValues['hotelId'];
                break;
            }
        }

        $packageHotelsObj = apple_fd_packages_hotels::where('fd_package_id', '=', $packageId)
            ->where('hotel_id', '=', $selectedHotelId)->first();

        if (!(apple_fd_packages_hotels::where('fd_package_id', '=', $packageId)
            ->where('hotel_id', '=', $selectedHotelId)->exists())) {

            $singleRoomRate = $this->getChangedHotelRateAdults($selectedHotelId, 1, $arrivalDate);
            $DoubleRoomRate = $this->getChangedHotelRateAdults($selectedHotelId, 2, $arrivalDate);
            $tripleRoomRate = $this->getChangedHotelRateAdults($selectedHotelId, 3, $arrivalDate);
            $cwbRate = $this->getChangedHotelRateChild($selectedHotelId, 1, $arrivalDate, 2, 12);
            $cnbRate = $this->getChangedHotelRateChild($selectedHotelId, 1, $arrivalDate, 0, 2);

            $existingHotelInfo = apple_fd_packages_hotels::where('fd_package_id', '=', $packageId)
                ->where('hotel_id', '=', $existingHotelId)->first();
            $noOfNights = $existingHotelInfo->no_of_nights;
            $mealType = $existingHotelInfo->meal_type;
            $room_category = $existingHotelInfo->room_category;

            $apple_fd_packages_hotels = new apple_fd_packages_hotels();
            $apple_fd_packages_hotels->fd_package_id = $packageId;
            $apple_fd_packages_hotels->place_id = $placeId;
            $apple_fd_packages_hotels->hotel_id = $selectedHotelId;
            $apple_fd_packages_hotels->no_of_nights = $noOfNights;
            $apple_fd_packages_hotels->meal_type = $mealType;
            $apple_fd_packages_hotels->room_category = $room_category;
            $apple_fd_packages_hotels->adult_rate_single_room = $singleRoomRate;
            $apple_fd_packages_hotels->adult_rate_double_room = $DoubleRoomRate;
            $apple_fd_packages_hotels->adult_rate_triple_room = $tripleRoomRate;
            $apple_fd_packages_hotels->child_rate = $cwbRate;
            $apple_fd_packages_hotels->child_w_out_bed_rate = $cnbRate;
            $apple_fd_packages_hotels->save();

            $packageHotelsObj = apple_fd_packages_hotels::where('fd_package_id', '=', $packageId)
                ->where('hotel_id', '=', $selectedHotelId)->first();

        }
        // meal data
        $mealPlan = $packageHotelsObj->findMealType->first()->plan . " - " . $packageHotelsObj->findMealType->first()->long_name;
        $mealPlanCode = $packageHotelsObj->findMealType->first()->plan;

        $totalHotelCost = 0;

        $noOfAdults = $quotationData['pax']['adult'];
        $noOfCwb = $quotationData['pax']['cwb'];
        $noOfCnb = $quotationData['pax']['cnb'];

        $existingHotelTotalCost = 0;

        if (isset($quotationData['cancelHotelData'])) {
            $cancelHotelData = $quotationData['cancelHotelData'];
        } else {
            $cancelHotelData = array();
        }
        foreach ($quotationData['hotelData'] as $hotelKey => $hotelValues) {
            if ($hotelValues['placeId'] == $placeId) {

                if ($type == "update") {
                    $cancellVoucherDataCount = apple_fd_packages_quotation_hotel_rates::where('reference_id', $quotationData['confirmData']['referenceNo'])
                        ->where('hotel_id', $hotelValues['hotelId'])->count();
                    if ($cancellVoucherDataCount > 0) {
                        array_push($cancelHotelData, $hotelValues);
                    }
                }

                if (isset($hotelValues['cost']['cwb'])) {
                    $existingHotelTotalCost += $hotelValues['cost']['cwb'];
                }
                if (isset($hotelValues['cost']['cnb'])) {
                    $existingHotelTotalCost += $hotelValues['cost']['cnb'];
                }
                if (isset($hotelValues['cost']['adult_rate_single_room'])) {
                    $existingHotelTotalCost += $hotelValues['cost']['adult_rate_single_room']['adultCost'];
                }
                if (isset($hotelValues['cost']['adult_rate_double_room'])) {
                    $existingHotelTotalCost += $hotelValues['cost']['adult_rate_double_room']['adultCost'];
                }
                if (isset($hotelValues['cost']['adult_rate_triple_room'])) {
                    $existingHotelTotalCost += $hotelValues['cost']['adult_rate_triple_room']['adultCost'];
                }
            }
        }

        foreach ($quotationData['hotelData'] as $hotelKey => $hotelValues) {
            if ($hotelValues['placeId'] == $placeId) {
                $quotationData['hotelData'][$hotelKey]['hotelName'] = $packageHotelsObj->findHotel->first()->name ?? "";
                $quotationData['hotelData'][$hotelKey]['provider'] = "package_hotel";
                $quotationData['hotelData'][$hotelKey]['hotelImage'] = $image->getImage($packageHotelsObj->hotel_id, '3x', "hotel", 1, Hotel::find($packageHotelsObj->hotel_id)->name)[0];
                $quotationData['hotelData'][$hotelKey]['class'] = $packageHotelsObj->findHotel->first()->class()->first()->class ?? "";
                $quotationData['hotelData'][$hotelKey]['classStar'] = $packageHotelsObj->findHotel->first()->class()->first()->star ?? "";
                $quotationData['hotelData'][$hotelKey]['hotelId'] = $packageHotelsObj->hotel_id;
                $quotationData['hotelData'][$hotelKey]['mealType'] = $mealPlan;
                $quotationData['hotelData'][$hotelKey]['mealTypeId'] = $packageHotelsObj->meal_type;
                $quotationData['hotelData'][$hotelKey]['mealPlanCode'] = $mealPlanCode;
                $quotationData['hotelData'][$hotelKey]['roomCategory'] = $packageHotelsObj->findRoomCategory->name;
                $quotationData['hotelData'][$hotelKey]['roomCategoryId'] = $packageHotelsObj->room_category;

                $EmailsList = "";
                if (Hotel::find($packageHotelsObj->hotel_id))
                    $EmailsList = implode(",", Hotel::find($packageHotelsObj->hotel_id)->Contact()->where('type', 2)->pluck('contact_id')->toArray());

                $quotationData['hotelData'][$hotelKey]['contactEmail'] = $EmailsList;

                if ($noOfCwb > 0) {
                    $quotationData['hotelData'][$hotelKey]['cost']['cwb'] = $packageHotelsObj->child_rate * $packageHotelsObj->no_of_nights * $noOfCwb;
                }
                if ($noOfCnb > 0) {
                    $quotationData['hotelData'][$hotelKey]['cost']['cnb'] = $packageHotelsObj->child_w_out_bed_rate * $packageHotelsObj->no_of_nights * $noOfCnb;
                }
                // hotel room and rates data
                foreach ($quotationData['RoomTypes'] as $RoomTypeID => $RoomCount) {
                    $roomColumn = "";
                    if ($RoomCount) {
                        if ($RoomTypeID == 1) {
                            $roomColumn = "adult_rate_single_room";
                        } else if ($RoomTypeID == 2) {
                            $roomColumn = "adult_rate_double_room";
                        } else if ($RoomTypeID == 3) {
                            $roomColumn = "adult_rate_triple_room";
                        }
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCostPP'] = round(($packageHotelsObj->$roomColumn * $packageHotelsObj->no_of_nights) / $RoomTypeID, 2);
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCost'] = $packageHotelsObj->$roomColumn * $packageHotelsObj->no_of_nights;
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['roomCount'] = $RoomCount;
                        $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCost'];
                    }
                }
                if ($noOfCwb > 0) {
                    $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost']['cwb'];
                }
                if ($noOfCnb > 0) {
                    $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost']['cnb'];
                }

            }
        }
        $apple_fd_packages = apple_fd_packages::where('id', '=', $packageId)->first();
        $groupPackage = $apple_fd_packages->group_package;
        $allotmentTable = "";

        if ($groupPackage == 0) {
            $allotmentTable = apple_fd_packages_hotel_allotments::class;
        } else {
            $allotmentTable = apple_fd_packages_group_hotel_allotments::class;
        }
        $quotationData['hotelData'] = $this->getAllotment($quotationData['hotelData'], $allotmentTable);

        $includesText = "";
        $includes = $apple_fd_packages->includes ?? "";
        foreach($quotationData['hotelData'] as $key => $value) {
            $text = $value['mealType']." at " . $value['hotelName'];
            $includesText.= $this->getIncludesTemplate($text);
        }
        $includesText.= "</ul>";
        $includes = str_replace("</ul>",$includesText,$includes);
        $quotationData['textData']['inclutions'] = $includes;

//        return $totalHotelCost;
        $finalCost = $quotationData['totalHotelCost'] - $existingHotelTotalCost;
        $finalCost = $finalCost + $totalHotelCost;
        $quotationData['totalHotelCost'] = $finalCost;

        $adultPpHotelCost = 0;
        $cwbPpHotelCost = 0;
        $cnbPpHotelCost = 0;

        $roomCostBreakDown = array();
        $adultSingleRoomCost = 0;
        $adultDoubleRoomCost = 0;
        $adultTripleRoomCost = 0;

        foreach ($quotationData['hotelData'] as $hotelCostKey => $hotelCostValues) {

            if (isset($hotelCostValues['cost']['adult_rate_single_room'])) {
                $adultSingleRoomCost += $hotelCostValues['cost']['adult_rate_single_room']['adultCostPP'];
            }
            if (isset($hotelCostValues['cost']['adult_rate_double_room'])) {
                $adultDoubleRoomCost += $hotelCostValues['cost']['adult_rate_double_room']['adultCostPP'];
            }
            if (isset($hotelCostValues['cost']['adult_rate_triple_room'])) {
                $adultTripleRoomCost += $hotelCostValues['cost']['adult_rate_triple_room']['adultCostPP'];
            }
            if (isset($hotelCostValues['cost']['cwb'])) {
                $cwbPpHotelCost += $hotelCostValues['cost']['cwb'] / $noOfCwb;
            }
            if (isset($hotelCostValues['cost']['cnb'])) {
                $cnbPpHotelCost += $hotelCostValues['cost']['cnb'] / $noOfCnb;
            }
        }

        $apple_fd_packages = apple_fd_packages::where('id', '=', $packageId)->with('findPackagePrice', 'findHotels', 'findPlaces', 'findImages')->first();
        $prices = $apple_fd_packages->findPackagePrice{0};

        $adultPackageChargesColumn = "";
        if($noOfAdults == 1) {
            $adultPackageChargesColumn = "adult_rate";
        } else if ($noOfAdults  == 2) {
            $adultPackageChargesColumn = "adult_rate_1";
        } else if ($noOfAdults == 3) {
            $adultPackageChargesColumn = "adult_rate_2";
        } else if ($noOfAdults == 4) {
            $adultPackageChargesColumn = "adult_rate_3";
        } else if ($noOfAdults == 5) {
            $adultPackageChargesColumn = "adult_rate_4";
        } else if ($noOfAdults == 6) {
            $adultPackageChargesColumn = "adult_rate_5";
        } else if ($noOfAdults == 7) {
            $adultPackageChargesColumn = "adult_rate_6";
        } else if ($noOfAdults == 8) {
            $adultPackageChargesColumn = "adult_rate_7";
        } else if ($noOfAdults == 9) {
            $adultPackageChargesColumn = "adult_rate_8";
        } else if ($noOfAdults == 10) {
            $adultPackageChargesColumn = "adult_rate_9";
        } else if ($noOfAdults == 11) {
            $adultPackageChargesColumn = "adult_rate_10";
        } else if ($noOfAdults == 12) {
            $adultPackageChargesColumn = "adult_rate_11";
        } else if ($noOfAdults == 13) {
            $adultPackageChargesColumn = "adult_rate_12";
        } else if ($noOfAdults == 14) {
            $adultPackageChargesColumn = "adult_rate_13";
        } else if ($noOfAdults == 15) {
            $adultPackageChargesColumn = "adult_rate_14";
        } else if($adultPackageChargesColumn > 15){
            $adultPackageChargesColumn = "adult_rate_14";
        }

        $adultSupplementCost = 0;
        $cwbSupplementCost = 0;
        $cnbSupplementCost = 0;
        if(count($apple_fd_packages->findSuppliment) > 0) {
            foreach ($apple_fd_packages->findSuppliment as $supplementCostKey => $supplement){
                $fromDate = Carbon::parse($supplement->from);
                $toDate = Carbon::parse($supplement->to);
                $valid = $arrivalDate->between($fromDate, $toDate);
                if($valid){
                    $adultSupplementCost += $supplement->adult_cost;
                    $cwbSupplementCost += $supplement->cwb_cost;
                    $cnbSupplementCost += $supplement->cnb_cost;
                }
            }
        }

        if ($adultSingleRoomCost > 0) {
            $roomCostBreakDown['adultSingleRoomCost']['cost'] = round($adultSingleRoomCost, 2);
            $roomCostBreakDown['adultSingleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
            $roomCostBreakDown['adultSingleRoomCost']['totalCost'] = $roomCostBreakDown['adultSingleRoomCost']['cost'] + $prices->$adultPackageChargesColumn + $adultSupplementCost;
        }
        if ($adultDoubleRoomCost > 0) {
            $roomCostBreakDown['adultDoubleRoomCost']['cost'] = round($adultDoubleRoomCost, 2);
            $roomCostBreakDown['adultDoubleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
            $roomCostBreakDown['adultDoubleRoomCost']['totalCost'] = $roomCostBreakDown['adultDoubleRoomCost']['cost'] + $roomCostBreakDown['adultDoubleRoomCost']['packageCost'];
        }
        if ($adultTripleRoomCost > 0) {
            $roomCostBreakDown['adultTripleRoomCost']['cost'] = round($adultTripleRoomCost, 2);
            $roomCostBreakDown['adultTripleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
            $roomCostBreakDown['adultTripleRoomCost']['totalCost'] = $roomCostBreakDown['adultTripleRoomCost']['cost'] + $roomCostBreakDown['adultTripleRoomCost']['packageCost'];
        }
        $adultCost = ($prices->$adultPackageChargesColumn + $adultSupplementCost)* $noOfAdults;
        $cwdCost = ($prices->child_rate + $cwbSupplementCost)* $noOfCwb;
        $cnbCost = ($prices->child_w_out_bed_rate + $cnbSupplementCost) * $noOfCnb;

        $totCost = 0;
        if(isset($roomCostBreakDown['adultSingleRoomCost'])){
            $totCost += $roomCostBreakDown['adultSingleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][1] * 1);
        }
        if(isset($roomCostBreakDown['adultDoubleRoomCost'])){
            $totCost += $roomCostBreakDown['adultDoubleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][2] * 2);
        }
        if(isset($roomCostBreakDown['adultTripleRoomCost'])){
            $totCost += $roomCostBreakDown['adultTripleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][3] * 3);
        }

        if($quotationData['pax']['cwb'] > 0){
            $totCost += ($prices->child_rate + $cwbSupplementCost + $cwbPpHotelCost) * $quotationData['pax']['cwb'];
        }

        if($quotationData['pax']['cnb'] > 0){
            $totCost += ($prices->child_w_out_bed_rate + $cnbSupplementCost + $cnbPpHotelCost) * $quotationData['pax']['cnb'];
        }


        $finalCostArray = array(
            'adultCostBreakDown' => $roomCostBreakDown,
            'cwbHotelPpCost' => $cwbPpHotelCost,
            'cwbPackagePpCost' => $prices->child_rate + $cwbSupplementCost,
            'cwbPpTotalCost' => $prices->child_rate + $cwbSupplementCost + $cwbPpHotelCost,
            'cnbHotelPpCost' => $cnbPpHotelCost,
            'cnbPackagePpCost' => $prices->child_w_out_bed_rate + $cnbSupplementCost ,
            'cnbPpTotalCost' => $prices->child_w_out_bed_rate + $cnbSupplementCost + $cnbPpHotelCost,
//            'grandCost' => $finalCost + $adultCost + $cwdCost + $cnbCost
            'grandCost' => $totCost
        );

        $quotationData['finalCost'] = $finalCostArray;
        $jsonData = json_encode($quotationData);
        $roomType = array_slice($quotationData['RoomTypes'], 0, 3,true);
        if ($type == "new") {
            return view('fd_packages.fd_packages_quotation', compact('quotationData'));

        } else {

            foreach ($cancelHotelData as $cancelHotelDatKey => $cancelHotelDataValue) {
                if ($cancelHotelDataValue['hotelId'] == $selectedHotelId) {
                    unset($cancelHotelData[$cancelHotelDatKey]);
                    break;
                }
            }
            $quotationData['cancelHotelData'] = $cancelHotelData;
            return view('fd_packages.edit.fd_packages_edit_quotation', compact('quotationData'));

        }
    }

    public function saveQuotation(Request $request)
    {
       return DB::transaction(function() use ($request) {
        $quotationData = $request->fd_quotation_front_json;
        $quotationData = json_decode($quotationData, true);
        $agentId = $request->fd_agent;
        $clientHonorific = $request->client_title;
        $clientName = $request->fd_front_quotation_client_name;

        $confirmationData = $request->all();
        $quotationData["confirmationData"] = $confirmationData;

        $quotationNo = Quotation::max('quotation_no') + 1;
        if (apple_fd_packages_quotation::where('quotation_no', '=', $quotationNo)->exists()) {
            $quotationNo = apple_fd_packages_quotation::max('quotation_no') + 1;
        }
        $quotationData["quotationNo"] = $quotationNo;

        $apple_fd_packages_quotation = new apple_fd_packages_quotation();
        $apple_fd_packages_quotation->quotation_no = $quotationNo;
        $apple_fd_packages_quotation->status = 2;
        $apple_fd_packages_quotation->currency = 142;
        $apple_fd_packages_quotation->fd_package_id = $quotationData['packageId'];
        $apple_fd_packages_quotation->meal_plan_types = $quotationData['mealPlanTypes'];
        $apple_fd_packages_quotation->arrival_date = $quotationData['arrivalDateFormat'];
        $apple_fd_packages_quotation->nights_days_count = $quotationData['textData']['nightsDaysCount'];
        $apple_fd_packages_quotation->honorific_id = $clientHonorific;
        $apple_fd_packages_quotation->client_name = $clientName;
        $apple_fd_packages_quotation->agent_id = $agentId;
        $apple_fd_packages_quotation->user_id = Auth::user()->id;
        $apple_fd_packages_quotation->save();

        $referenceId = $apple_fd_packages_quotation->id;

        foreach ($quotationData['hotelData'] as $hotelKey => $hotelValue) {
            $apple_fd_packages_quotation_hotel_rates = new apple_fd_packages_quotation_hotel_rates();
            $apple_fd_packages_quotation_hotel_rates->reference_id = $referenceId;
            $apple_fd_packages_quotation_hotel_rates->place_id = $hotelValue['placeId'];
            $apple_fd_packages_quotation_hotel_rates->hotel_id = $hotelValue['hotelId'];
            $apple_fd_packages_quotation_hotel_rates->check_in = $hotelValue['checkIn'];
            $apple_fd_packages_quotation_hotel_rates->check_out = $hotelValue['CheckOut'];
            $apple_fd_packages_quotation_hotel_rates->provider = $hotelValue['provider'];
            $apple_fd_packages_quotation_hotel_rates->roomTypeText = $hotelValue['roomTypeText'];
            $apple_fd_packages_quotation_hotel_rates->no_of_nights = $hotelValue['noOfNights'];
            $apple_fd_packages_quotation_hotel_rates->meal_type = $hotelValue['mealTypeId'];
            $apple_fd_packages_quotation_hotel_rates->room_category = $hotelValue['roomCategoryId'];

            if (isset($hotelValue['cost']['adult_rate_single_room'])) {
                $apple_fd_packages_quotation_hotel_rates->adult_single_room_rate_pp = $hotelValue['cost']['adult_rate_single_room']['adultCostPP'];
                $apple_fd_packages_quotation_hotel_rates->adult_single_room_rate_total = $hotelValue['cost']['adult_rate_single_room']['adultCost'];
                $apple_fd_packages_quotation_hotel_rates->adult_single_room_count = $hotelValue['cost']['adult_rate_single_room']['roomCount'];
            }

            if (isset($hotelValue['cost']['adult_rate_double_room'])) {
                $apple_fd_packages_quotation_hotel_rates->adult_double_room_rate_pp = $hotelValue['cost']['adult_rate_double_room']['adultCostPP'];
                $apple_fd_packages_quotation_hotel_rates->adult_double_room_rate_total = $hotelValue['cost']['adult_rate_double_room']['adultCost'];
                $apple_fd_packages_quotation_hotel_rates->adult_double_room_count = $hotelValue['cost']['adult_rate_double_room']['roomCount'];
            }

            if (isset($hotelValue['cost']['adult_rate_triple_room'])) {
                $apple_fd_packages_quotation_hotel_rates->adult_triple_room_rate_pp = $hotelValue['cost']['adult_rate_triple_room']['adultCostPP'];
                $apple_fd_packages_quotation_hotel_rates->adult_triple_room_rate_total = $hotelValue['cost']['adult_rate_triple_room']['adultCost'];
                $apple_fd_packages_quotation_hotel_rates->adult_triple_room_count = $hotelValue['cost']['adult_rate_triple_room']['roomCount'];
            }

            if (isset($hotelValue['cost']['cwb'])) {
                $apple_fd_packages_quotation_hotel_rates->child_rate = $hotelValue['cost']['cwb'];
            }
            if (isset($hotelValue['cost']['cnb'])) {
                $apple_fd_packages_quotation_hotel_rates->child_w_bed_rate = $hotelValue['cost']['cnb'];
            }

            $apple_fd_packages_quotation_hotel_rates->email_status = 0;
            $apple_fd_packages_quotation_hotel_rates->save();
        }

        $apple_fd_packages_quotation_cost = new apple_fd_packages_quotation_cost();
        $apple_fd_packages_quotation_cost->reference_id = $referenceId;
        $apple_fd_packages_quotation_cost->adult_package_cost = $quotationData['packageCost']['adultCost'];
        $apple_fd_packages_quotation_cost->cwb_package_cost = $quotationData['packageCost']['cwb'];
        $apple_fd_packages_quotation_cost->cnb_package_cost = $quotationData['packageCost']['cnb'];

        if (isset($quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost'])) {
            $apple_fd_packages_quotation_cost->adult_single_room_cost = $quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost']['cost'];
        }

        if (isset($quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost'])) {
            $apple_fd_packages_quotation_cost->adult_double_room_cost = $quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost']['cost'];
        }

        if (isset($quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost'])) {
            $apple_fd_packages_quotation_cost->adult_triple_room_cost = $quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost']['cost'];
        }
        $apple_fd_packages_quotation_cost->cwb_cost = $quotationData['finalCost']['cwbHotelPpCost'];
        $apple_fd_packages_quotation_cost->cnb_cost = $quotationData['finalCost']['cnbHotelPpCost'];
        $apple_fd_packages_quotation_cost->total_hotel_cost = $quotationData['totalHotelCost'];
        $apple_fd_packages_quotation_cost->grand_cost = $quotationData['finalCost']['grandCost'];
        $apple_fd_packages_quotation_cost->save();

        foreach ($confirmationData['remark'] as $remarkKey => $remark) {
            $apple_fd_packages_quotation_hotel_vouchers_info = new apple_fd_packages_quotation_hotel_vouchers_info();
            $apple_fd_packages_quotation_hotel_vouchers_info->reference_id = $referenceId;
            $apple_fd_packages_quotation_hotel_vouchers_info->place_id = $confirmationData['hotel_voucher_place_id'][$remarkKey];
            $apple_fd_packages_quotation_hotel_vouchers_info->hotel_id = $confirmationData['hotel_voucher_hotel_id'][$remarkKey];
            $apple_fd_packages_quotation_hotel_vouchers_info->remarks = $remark;
            $apple_fd_packages_quotation_hotel_vouchers_info->confirmation_notes = $confirmationData['confirmation_note'][$remarkKey];
            $apple_fd_packages_quotation_hotel_vouchers_info->emails = $confirmationData['emails'][$remarkKey];
            $apple_fd_packages_quotation_hotel_vouchers_info->email_status = 0;
            $apple_fd_packages_quotation_hotel_vouchers_info->save();
        }

        $apple_fd_packages_quotation_pax = new apple_fd_packages_quotation_pax();
        $apple_fd_packages_quotation_pax->reference_id = $referenceId;
        $apple_fd_packages_quotation_pax->no_of_adults = $quotationData['pax']['adult'];
        $apple_fd_packages_quotation_pax->no_of_cwb = $quotationData['pax']['cwb'];
        $apple_fd_packages_quotation_pax->no_of_cnb = $quotationData['pax']['cnb'];
        $apple_fd_packages_quotation_pax->vehicle_name = $quotationData['vehicleName'];
        $apple_fd_packages_quotation_pax->save();

        $quotationData = $this->getQuotation($referenceId, $quotationNo);
        $this->sendTourConfirmationVouchers($quotationData);
        $this->sendNotification($quotationData, 1);
        $voutureData = $this->generateHotelVoutures($quotationData);
        $updateCount = 1;
        $msg = "Your Quotation has been saved!";
        $state = "new";
        $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];

        $pnlRequest = new Request();
        $pnlRequest->replace(['fdQuotationReferenceNo' => $referenceId , 'fdQuotationNo' => $quotationNo ]);
        $lostProfitHtml = $this->lostProfit($pnlRequest);
        return view('fd_packages.fd_packages_quotation_save', compact('referenceId', 'quotationNo', 'voutureData', 'updateCount', 'msg', 'state', 'roleId','lostProfitHtml'));
        });
    }

    public function generateHotelVoutures($quotationData)
    {
        // Send tour confirmation voucher
        /*if (isset($quotationData)) {

            $EmailData = $quotationData;
            $EmailData['to_emails'] = Auth::user()->email;
            $EmailData['type'] = "tour_confirmation";
            Event::fire(new QuotationEmail($EmailData));

            $EmailData['to_emails'] = Auth::user()->email;
            $EmailData['type'] = "quote";
            Event::fire(new QuotationEmail($EmailData));
        }*/
        $VoucherList = $this->getHotelVouchers($quotationData);
        $VoucherHotelList = ["All"];
        $VoucherHtml = "";

        if (!empty($VoucherList)) {
            foreach ($VoucherList as $HTMLArray) {
                foreach ($HTMLArray as $HotelID => $HTML) {
                    $VoucherHotelList[$HotelID] = Hotel::find($HotelID)->name;
                    $VoucherHtml .= $HTML['html'];

                }
            }
        }
        // $html = View::make("fd_packages.respond.hotel_voutures", ['status' => $quotationData])->render();
        return array("VoucherHtml" => $VoucherHtml, "VoucherHotelList" => $VoucherHotelList, 'VoucherList' => $VoucherList);
    }

    public function getHotelVouchers($quotationData, $HotelID = false, $StatusId = null)
    {
        $Email = null;
        /*if($QuotationLast->country == 64) { // Singapore
            $logoUrl[] = "http://applev2.appletechlabs.com/assets/image/logo/mega.jpg";
            $Address = "181 , Kitchener road #01 – 09/10 </br> New Park Hotel shopping arcade </br> Singapore 208533 </br>";
        } else if($QuotationLast->country == 146) { // Indonesia
            $logoUrl[] = "http://applev2.appletechlabs.com/assets/image/logo/bayu-buana.png";
            $Address = "No:148 </br> Aluthmawatha Road </br> Colombo 15 </br>";
        } else if($QuotationLast->country == 63) { // Malaysia
            $logoUrl[] = "http://applev2.appletechlabs.com/assets/image/logo/two-logos.jpg";
            $Address = "Pinnacle Tower A , 21st floor No 09, </br> Jalan 51 A /223, Pjs 52, 46100 Petaling Jaya, </br> Selangor, Malaysia. </br>";
        } else {*/
        $logoUrl[] = "https://applev2.appletechlabs.com/assets/image/logo/log-long.png";
        $Address = "No:148 </br> Aluthmawatha Road </br> Colombo 15 </br>";
        // }
        $VoucherHTML = [];

        //get hotels
        //amendment and reservation

        foreach (($quotationData['hotelData'] ?? []) as $Index => $HotelSettings) {
            if (isset($HotelID) && !empty($HotelID)) {
                if (!in_array(0, $HotelID)) {
                    if (!in_array($HotelSettings["hotelId"], $HotelID)) {
                        continue;
                    }
                }
            }
            if (isset($StatusId)) {
                $Status = $StatusId;
            } else {
                $Status = 1;
            }


            $ConfirmNote = isset($quotationData['confirmationData']["confirmation_note"][$Index]) ? $quotationData['confirmationData']["confirmation_note"][$Index] : "";
            $Remarks = isset($quotationData['confirmationData']["remark"][$Index]) ? $quotationData['confirmationData']["remark"][$Index] : "";

            $VoucherHTML[$Index][$HotelSettings['hotelId']]['html'] = View::make('fd_packages.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $quotationData, 'Logo' => $logoUrl, 'Address' => $Address])->render();
            $VoucherHTML[$Index][$HotelSettings['hotelId']]['emails'] = $HotelSettings['contactEmail'];
            $VoucherHTML[$Index][$HotelSettings['hotelId']]['type'] = "reservation";
            $VoucherHTML[$Index][$HotelSettings['hotelId']]['hotelId'] = $HotelSettings['hotelId'];
            $VoucherHTML[$Index][$HotelSettings['hotelId']]['hotelName'] = $HotelSettings['hotelName'];

        }
        if (isset($quotationData['cancelHotelData'])) {
            $count = count($VoucherHTML);
            foreach (($quotationData['cancelHotelData'] ?? []) as $Index => $HotelSettings) {
                if (isset($HotelID) && !empty($HotelID)) {
                    if (!in_array(0, $HotelID)) {
                        if (!in_array($HotelSettings["hotelId"], $HotelID)) {
                            continue;
                        }
                    }
                }
                $Status = 3;

                $ConfirmNote = $HotelSettings['voucherConfirmationNotes'] ?? "";
                $Remarks = $HotelSettings['voucherRemarks'] ?? "";

                $VoucherHTML[$count][$HotelSettings['hotelId']]['html'] = View::make('fd_packages.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $quotationData, 'Logo' => $logoUrl, 'Address' => $Address])->render();
                $VoucherHTML[$count][$HotelSettings['hotelId']]['emails'] = $HotelSettings['contactEmail'];
                $VoucherHTML[$count][$HotelSettings['hotelId']]['type'] = "cancel";
                $VoucherHTML[$count][$HotelSettings['hotelId']]['hotelId'] = $HotelSettings['hotelId'];
                $VoucherHTML[$count][$HotelSettings['hotelId']]['hotelName'] = $HotelSettings['hotelName'];
                $count++;
            }
        }
        return $VoucherHTML;
    }

    public function RetrieveQuotation(Request $request)
    {
        $referenceNo = $request->fdQuotationReferenceNo;
        $quotationNo = $request->fdQuotationNo;

        $quotationData = $this->getQuotation($referenceNo, $quotationNo);

        return view('fd_packages.edit.fd_packages_edit_quotation', compact('quotationData'));
    }

    public function getQuotation($referenceNo, $quotationNo, $statusId = "")
    {
        $placeModel = new Place();
        $image = new ImgaeGenerator();
        $Quote = new Quote();

        $FdPackagesQuotationObj = apple_fd_packages_quotation::where('id', '=', $referenceNo)->where('quotation_no', '=', $quotationNo)
            ->with('findFdPackage', 'findFdCost', 'findHotelRates', 'findHotelVoucher', 'findPax', 'findStatus')->first();
        $fdPackageId = $FdPackagesQuotationObj->fd_package_id;
        $updateCount = apple_fd_packages_quotation::where('quotation_no', '=', $quotationNo)->get()->count();

        $apple_fd_package = apple_fd_packages::where('id', $fdPackageId)->first();

        $desc = $apple_fd_package->itinerary_desc ?? "";
        $includes = $apple_fd_package->includes ?? "";
        $excludes = $apple_fd_package->excludes ?? "";
        $packageName = $apple_fd_package->package_name;
        $packageType =  $apple_fd_package->group_package;
        $DaysNightsText = $FdPackagesQuotationObj->nights_days_count;

        $pickUpLocation = Place::where('ID', '=', $apple_fd_package->pick_up)->first()->name;
        $pickUpLocationId = Place::where('ID', '=', $apple_fd_package->pick_up)->first()->ID;
        $dropOffLocation = Place::where('ID', '=', $apple_fd_package->drop_off)->first()->name;
        $dropOffLocationID = Place::where('ID', '=', $apple_fd_package->drop_off)->first()->ID;

        $packageImages = $apple_fd_package->findImages;


        $paxArray = array('adult' => $FdPackagesQuotationObj->findPax->no_of_adults,
            'cwb' => $FdPackagesQuotationObj->findPax->no_of_cwb,
            'cnb' => $FdPackagesQuotationObj->findPax->no_of_cnb);

        $packageCostArray = array('adultCost' => $FdPackagesQuotationObj->findFdCost->adult_package_cost,
            'cwb' => $FdPackagesQuotationObj->findFdCost->cwb_package_cost,
            'cnb' => $FdPackagesQuotationObj->findFdCost->cnb_package_cost);

        $hotelCostArray = array();
        $hotels = $FdPackagesQuotationObj->findHotelRates;
        $hotelVoucherInfo = $FdPackagesQuotationObj->findHotelVoucher;
        $single = 0;
        $double = 0;
        $triple = 0;

        foreach ($hotels as $key => $hotel) {
            if ($key != 0) {
                if (isset($placeModel->getPlaceDistance($hotelCostArray[$key - 1]['placeId'], $hotel->place_id)->distance)) {
                    $hotelCostArray[$key]['Distance'] = $placeModel->getPlaceDistance($hotelCostArray[$key - 1]['placeId'], $hotel->place_id)->distance / 1000 . " KM";
                    $hotelCostArray[$key]['Duration'] = formatDuration($placeModel->getPlaceDistance($hotelCostArray[$key - 1]['placeId'], $hotel->place_id)->time);
                }
                $placeObj = Place::where('ID', '=', $hotelCostArray[$key - 1]['placeId'])->first();
                $hotelCostArray[$key]['LasePlaceName'] = $placeObj->name;
                $hotelCostArray[$key]['LasePlaceId'] = $hotelCostArray[$key - 1]['placeId'];
            }
            // meal data
            $mealPlan = $hotel->findMealType->first()->plan . " - " . $hotel->findMealType->first()->long_name;
            $mealPlanCode = $hotel->findMealType->first()->plan;

            $hotelCostArray[$key]['checkIn'] = $hotel->check_in;
            $hotelCostArray[$key]['CheckOut'] = $hotel->check_out;
            $hotelCostArray[$key]['checkInFormatted'] = Carbon::parse($hotel->check_in)->toFormattedDateString();
            $hotelCostArray[$key]['CheckOutFormatted'] = Carbon::parse($hotel->check_out)->toFormattedDateString();
            $hotelCostArray[$key]['hotelName'] = $hotel->findHotel->first()->name;
            $hotelCostArray[$key]['provider'] = $hotel->provider;
            $hotelCostArray[$key]['hotelImage'] = $image->getImage($hotel->hotel_id, '3x', "hotel", 1, Hotel::find($hotel->hotel_id)->name)[0];
            $hotelCostArray[$key]['class'] = $hotel->findHotel->first()->class()->first()->class;
            $hotelCostArray[$key]['classStar'] = $hotel->findHotel->first()->class()->first()->class;
            $hotelCostArray[$key]['hotelId'] = $hotel->hotel_id;
            $hotelCostArray[$key]['mealType'] = $mealPlan;
            $hotelCostArray[$key]['mealTypeId'] = $hotel->meal_type;
            $hotelCostArray[$key]['roomCategory'] = $hotel->findRoomCategory->name;
            $hotelCostArray[$key]['roomCategoryId'] = $hotel->room_category;
            $hotelCostArray[$key]['mealPlanCode'] = $mealPlanCode;
            $hotelCostArray[$key]['noOfNights'] = $hotel->no_of_nights;
            $hotelCostArray[$key]['placeId'] = $hotel->place_id;
            $hotelCostArray[$key]['placeName'] = $hotel->findPlace()->first()->name;
            $hotelCostArray[$key]['placeDesc'] = $hotel->findPlace()->first()->description;
            $hotelCostArray[$key]['placeImage'] = $packageImages{$key}->image_path ?? "assets/image/no_image/N_5x.jpg";
            $hotelCostArray[$key]['roomTypeText'] = $hotel->roomTypeText;
            $hotelCostArray[$key]['voucherRemarks'] = $hotelVoucherInfo{$key}->remarks ?? "";
            $hotelCostArray[$key]['voucherConfirmationNotes'] = $hotelVoucherInfo{$key}->confirmation_notes ?? "";
            $hotelCostArray[$key]['contactEmail'] = $hotelVoucherInfo{$key}->emails ?? "";
            $total = 0;

            if (!empty($hotel->adult_single_room_rate_pp)) {
                $hotelCostArray[$key]['cost']['adult_rate_single_room']['adultCostPP'] = $hotel->adult_single_room_rate_pp;
                $hotelCostArray[$key]['cost']['adult_rate_single_room']['adultCost'] = $hotel->adult_single_room_rate_total;
                $hotelCostArray[$key]['cost']['adult_rate_single_room']['roomCount'] = $hotel->adult_single_room_count;
                $single = $hotel->adult_single_room_count ?? 0;
                $total += $hotel->adult_single_room_rate_total * $hotel->adult_single_room_count;

            }
            if (!empty($hotel->adult_double_room_rate_pp)) {
                $hotelCostArray[$key]['cost']['adult_rate_double_room']['adultCostPP'] = $hotel->adult_double_room_rate_pp;
                $hotelCostArray[$key]['cost']['adult_rate_double_room']['adultCost'] = $hotel->adult_double_room_rate_total;
                $hotelCostArray[$key]['cost']['adult_rate_double_room']['roomCount'] = $hotel->adult_double_room_count;
                $double = $hotel->adult_double_room_count ?? 0;
                $total += $hotel->adult_double_room_rate_total * $hotel->adult_double_room_count;
            }
            if (!empty($hotel->adult_triple_room_rate_pp)) {
                $hotelCostArray[$key]['cost']['adult_rate_triple_room']['adultCostPP'] = $hotel->adult_triple_room_rate_pp;
                $hotelCostArray[$key]['cost']['adult_rate_triple_room']['adultCost'] = $hotel->adult_triple_room_rate_total;
                $hotelCostArray[$key]['cost']['adult_rate_triple_room']['roomCount'] = $hotel->adult_triple_room_count;
                $triple = $hotel->adult_triple_room_count ?? 0;
                $total += $hotel->adult_triple_room_rate_total * $hotel->adult_triple_room_count;
            }

            if (!empty($hotel->child_rate)) {
                $hotelCostArray[$key]['cost']['cwb'] = $hotel->child_rate;
                $total += $hotel->child_rate * $FdPackagesQuotationObj->findPax->no_of_cwb;
            }
            if (!empty($hotel->child_w_bed_rate)) {
                $hotelCostArray[$key]['cost']['cnb'] = $hotel->child_w_bed_rate;
                $total += $hotel->child_w_bed_rate * $FdPackagesQuotationObj->findPax->no_of_cnb;
            }
            $hotelCostArray[$key]['cost']['totalCost'] = number_format($total,2);
        }
        $RoomType = ['1' => $single, '2' => $double, '3' => $triple, '4' => 0, '5' => 0];

        $groupPackage = $apple_fd_package->group_package;

        $allotmentTable = "";

        if ($groupPackage == 0) {
            $allotmentTable = apple_fd_packages_hotel_allotments::class;
        } else {
            $allotmentTable = apple_fd_packages_group_hotel_allotments::class;
        }
        $hotelCostArray = $this->getAllotment($hotelCostArray, $allotmentTable);

        // itineraries
        $itinerariesArray = array();
        $day = 1;
        $itenaryLastIndex = 0;
        $totalAttractionsCost = 0;

        foreach ($hotelCostArray as $hotelCostKey => $hotelCostValues) {

            if (isset($hotelCostValues['LasePlaceName'])) {
                $itinerariesArray[$hotelCostKey]['LastPlaceName'] = $hotelCostValues['LasePlaceName'];
                $itinerariesArray[$hotelCostKey]['LastPlaceId'] = $hotelCostValues['LasePlaceId'];
                $itinerariesArray[$hotelCostKey]['Distance'] = $hotelCostValues['Distance'];
                $itinerariesArray[$hotelCostKey]['Duration'] = $hotelCostValues['Duration'];
            } else {
                $itinerariesArray[$hotelCostKey]['LastPlaceName'] = $pickUpLocation;
                $itinerariesArray[$hotelCostKey]['LastPlaceId'] = $pickUpLocationId;
                $itinerariesArray[$hotelCostKey]['Distance'] = $placeModel->getPlaceDistance($pickUpLocationId, $hotelCostValues['placeId'])->distance / 1000 . " KM";
                $itinerariesArray[$hotelCostKey]['Duration'] = formatDuration($placeModel->getPlaceDistance($pickUpLocationId, $hotelCostValues['placeId'])->time);

            }

            $itinerariesArray[$hotelCostKey]['placeID'] = $hotelCostValues['placeId'];
            $itinerariesArray[$hotelCostKey]['placeName'] = $hotelCostValues['placeName'];
            $itinerariesArray[$hotelCostKey]['placeDesc'] = $hotelCostValues['placeDesc'];
            $itinerariesArray[$hotelCostKey]['placeImage'] = $hotelCostValues['placeImage'];
            $itinerariesArray[$hotelCostKey]['roomTypeText'] = $hotelCostValues['roomTypeText'];
            $itinerariesArray[$hotelCostKey]['noOfNights'] = $hotelCostValues['noOfNights'];
            $itinerariesArray[$hotelCostKey]['checkIn'] = $hotelCostValues['checkIn'];
            $itinerariesArray[$hotelCostKey]['checkInFormatted'] = $hotelCostValues['checkInFormatted'];

            $startDate = Carbon::parse($hotelCostValues['checkIn']);
            $endDate = Carbon::parse($hotelCostValues['CheckOut']);
            $dateRange = $this->generateDateRange($startDate, $endDate);

            foreach ($dateRange as $dateKey => $dateValue) {
                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['day'] = $day;
                // attractions
                $attractionsObj = apple_fd_packages_attractions::where('fd_package_id', '=', $fdPackageId)
                    ->where('place_id', '=', $hotelCostValues['placeId'])->get();
                if (count($attractionsObj) > 0) {

                    foreach ($attractionsObj as $attractionKey => $attractionValue) {
                        $totalCost = 0;
                        if (($dateKey + 1) == $attractionValue->day) {

                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_id'] = $attractionValue->attraction_id;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['type'] = $attractionValue->attraction_type;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['adult_rate'] = $attractionValue->adult_rate;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['cwb_rate'] = $attractionValue->child_rate;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['cnb_rate'] = $attractionValue->child_w_out_bed_rate;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];

                            $adultAttractionCost = $attractionValue->adult_rate * $paxArray['adult'];
                            $cwbAttractionCost = $attractionValue->child_rate * $paxArray['cwb'];
                            $cnbAttractionCost = $attractionValue->child_w_out_bed_rate * $paxArray['cnb'];
                            $totalCost += $adultAttractionCost + $cwbAttractionCost + $cnbAttractionCost;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['totalCost'] = number_format($totalCost,2);
                            $totalAttractionsCost += $totalCost;

                            if ($attractionValue->attraction_type == "attraction") {

                                $attraction = AttractionModel::where('ID', '=', $attractionValue->attraction_id)->first();
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $attraction->point;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $attraction->description;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "attraction", 1, $attraction->point)[0];

                            } else if ($attractionValue->attraction_type == "city_tour") {

                                $cityTour = CityTour::where('ID', '=', $attractionValue->attraction_id)->first();
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $cityTour->name;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $cityTour->description;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "city_tour", 1, $cityTour->name)[0];

                            } else {
                                $excursion = Excursion::where('ID', '=', $attractionValue->attraction_id)->first();
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $excursion->name;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $excursion->description;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "place", 1, $excursion->name)[0];
                            }

                        } else {
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['text'] = "Day at leisure at the Hotel!";
                            $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];
                        }
                    }
                } else {
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['text'] = "Day at leisure at the Hotel!";
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];
                }


                $day++;

            }
            $itenaryLastIndex = $hotelCostKey;
        }
        //  drop off array data
        $itenaryLastIndex = $itenaryLastIndex + 1;
        $lastArrObj = end($itinerariesArray);
        $lastDateObj = end($lastArrObj['dates']);
        $itinerariesArray[$itenaryLastIndex]['LastPlaceName'] = $lastArrObj['placeName'];
        $itinerariesArray[$itenaryLastIndex]['LastPlaceId'] = $lastArrObj['placeID'];
        $itinerariesArray[$itenaryLastIndex]['Distance'] = $placeModel->getPlaceDistance($lastArrObj['placeID'], $dropOffLocationID)->distance / 1000 . " KM";;
        $itinerariesArray[$itenaryLastIndex]['Duration'] = formatDuration($placeModel->getPlaceDistance($lastArrObj['placeID'], $dropOffLocationID)->time);
        $itinerariesArray[$itenaryLastIndex]['placeID'] = $dropOffLocationID;
        $itinerariesArray[$itenaryLastIndex]['checkInFormatted'] = Carbon::parse($lastDateObj['date'])->addDay()->toFormattedDateString();
        $itinerariesArray[$itenaryLastIndex]['placeName'] = $dropOffLocation;
        $itinerariesArray[$itenaryLastIndex]['dates'][0]['date'] = Carbon::parse($lastDateObj['date'])->addDay()->toDateString();
        $itinerariesArray[$itenaryLastIndex]['dates'][0]['day'] = $day;

        $roomCostBreakDown = array();
        $quotationCost = $FdPackagesQuotationObj->findFdCost;

        if (!empty($quotationCost->adult_single_room_cost)) {
            $roomCostBreakDown['adultSingleRoomCost']['cost'] = round($quotationCost->adult_single_room_cost, 2);
            $roomCostBreakDown['adultSingleRoomCost']['packageCost'] = $quotationCost->adult_package_cost;
            $roomCostBreakDown['adultSingleRoomCost']['totalCost'] = $roomCostBreakDown['adultSingleRoomCost']['cost'] + $quotationCost->adult_package_cost;
        }
        if (!empty($quotationCost->adult_double_room_cost)) {
            $roomCostBreakDown['adultDoubleRoomCost']['cost'] = round($quotationCost->adult_double_room_cost, 2);
            $roomCostBreakDown['adultDoubleRoomCost']['packageCost'] = $quotationCost->adult_package_cost;
            $roomCostBreakDown['adultDoubleRoomCost']['totalCost'] = $roomCostBreakDown['adultDoubleRoomCost']['cost'] + $quotationCost->adult_package_cost;
        }
        if (!empty($quotationCost->adult_triple_room_cost)) {
            $roomCostBreakDown['adultTripleRoomCost']['cost'] = round($quotationCost->adult_triple_room_cost, 2);
            $roomCostBreakDown['adultTripleRoomCost']['packageCost'] = $quotationCost->adult_package_cost;
            $roomCostBreakDown['adultTripleRoomCost']['totalCost'] = $roomCostBreakDown['adultTripleRoomCost']['cost'] + $quotationCost->adult_package_cost;
        }
        $finalCostArray = array(
            'adultCostBreakDown' => $roomCostBreakDown,
            'cwbHotelPpCost' => $quotationCost->cwb_cost,
            'cwbPackagePpCost' => $quotationCost->cwb_package_cost,
            'cwbPpTotalCost' => $quotationCost->cwb_cost + $quotationCost->cwb_package_cost,
            'cnbHotelPpCost' => $quotationCost->cnb_cost,
            'cnbPackagePpCost' => $quotationCost->cnb_package_cost,
            'cnbPpTotalCost' => $quotationCost->cnb_cost + $quotationCost->cnb_cost,
            'grandCost' => $quotationCost->grand_cost
        );

        $totalHotelCost = $quotationCost->total_hotel_cost;
        $createdDateTime = Carbon::parse($FdPackagesQuotationObj->created_at)->toDayDateTimeString();
        $user = User::where('id', '=', $FdPackagesQuotationObj->user_id)->first()->name;


        $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
        $confirmData = array('statusId' => $FdPackagesQuotationObj->findStatus->ID,
            'status' => $FdPackagesQuotationObj->findStatus->status,
            'statusCode' => "R".$updateCount,
            'dateTime' => $createdDateTime,
            'quotationNo' => $quotationNo,
            'referenceNo' => $referenceNo,
            'user' => $user,
            'userID' => $FdPackagesQuotationObj->user_id,
            'fileHandler' => $FdPackagesQuotationObj->findUser()->first()->email,
            'honorificId' => $FdPackagesQuotationObj->honorific_id,
            'honorific' => $FdPackagesQuotationObj->findHonorific->honorific,
            'clientName' => $FdPackagesQuotationObj->client_name,
            'agentId' => $FdPackagesQuotationObj->agent_id,
            'agentName' => $FdPackagesQuotationObj->findAgent->name,
            'roleId' => $roleId,
        );

        $includesText = "";
        $includes = $apple_fd_package->includes;
        foreach($hotelCostArray as $key => $value) {
            $text = $value['mealType']." at " . $value['hotelName'];
            $includesText.= $this->getIncludesTemplate($text);
        }
        $includesText.= "</ul>";
        $includes = str_replace("</ul>",$includesText,$includes);
        $textArray = array('desc' => $desc, 'inclutions' => $includes, 'exclusions' => $excludes, 'nightsDaysCount' => $DaysNightsText);

        $arrivalDateObj = Carbon::parse($FdPackagesQuotationObj->arrival_date);
        $arrivalDateArray = array('year' => $arrivalDateObj->year, 'month' => $arrivalDateObj->month, 'day' => $arrivalDateObj->day);
        $quotationData = array('textData' => $textArray,
            'packageId' => $fdPackageId,
            'pax' => $paxArray,
            'mealPlanTypes' => $FdPackagesQuotationObj->meal_plan_types,
            'arrivalDate' => str_replace("-", " / ", $FdPackagesQuotationObj->arrival_date),
            'arrival_date_obj' => $arrivalDateArray,
            'arrivalDateFormat' => $FdPackagesQuotationObj->arrival_date,
            'deadLineDate' => Carbon::parse($FdPackagesQuotationObj->arrival_date)->addDays(14)->toDayDateTimeString(),
            'vehicleName' => $FdPackagesQuotationObj->findPax->vehicle_name,
            'packageCost' => $packageCostArray,
            'hotelData' => $hotelCostArray,
            'itineraries' => $itinerariesArray,
            'finalCost' => $finalCostArray,
            'RoomTypes' => $RoomType,
            'totalHotelCost' => $totalHotelCost,
            'totalAttractionCost' => number_format($totalAttractionsCost,2),
            'confirmData' => $confirmData,
            'confirmationData' => $hotelVoucherInfo,
            'packageName' => $packageName,
            'packageType' => $packageType,
            'quotationInfo' => $Quote->getReferenceID($quotationNo),
            'QuotationCurrency' => "USD"
        );
        if ($statusId == "cancel") {
            unset($quotationData['hotelData']);
        }
        if (count($FdPackagesQuotationObj->findCancelledHotelVoucher) > 0) {
            foreach ($FdPackagesQuotationObj->findCancelledHotelVoucher as $key => $value) {
                if ($value->email_status == 0) {
                    $ref = 0;
                    if ($FdPackagesQuotationObj->status == 3) {
                        $ref = $value->reference_id;
                    } else {
                        $ref = $value->reference_id - 1;
                    }
                    $hotelInfo = apple_fd_packages_quotation_hotel_rates::where('reference_id', '=', $ref)->where('hotel_id', '=', $value->hotel_id)->first();
                    if (isset($hotelInfo)) {
                        $quotationData['cancelHotelData'][$key]['hotelId'] = $value->hotel_id;
                        $quotationData['cancelHotelData'][$key]['placeId'] = $value->place_id;
                        $quotationData['cancelHotelData'][$key]['contactEmail'] = $value->cancel_emails;
                        $quotationData['cancelHotelData'][$key]['voucherRemarks'] = $value->cancel_remark;
                        $quotationData['cancelHotelData'][$key]['voucherConfirmationNotes'] = $value->cancel_note;


                        $mealPlan = $hotelInfo->findMealType->first()->plan . " - " . $hotel->findMealType->first()->long_name ?? "";
                        $mealPlanCode = $hotelInfo->findMealType->first()->plan ?? "";

                        $quotationData['cancelHotelData'][$key]['checkIn'] = $hotelInfo->check_in ?? 0;
                        $quotationData['cancelHotelData'][$key]['CheckOut'] = $hotelInfo->check_out ?? 0;
                        $quotationData['cancelHotelData'][$key]['checkInFormatted'] = Carbon::parse($value->check_in)->toFormattedDateString() ?? 0;
                        $quotationData['cancelHotelData'][$key]['CheckOutFormatted'] = Carbon::parse($value->check_out)->toFormattedDateString() ?? 0;

                        $quotationData['cancelHotelData'][$key]['hotelName'] = $hotelInfo->findHotel->first()->name;

                        $quotationData['cancelHotelData'][$key]['provider'] = $hotelInfo->provider;
                        $quotationData['cancelHotelData'][$key]['hotelImage'] = $image->getImage($value->hotel_id, '3x', "hotel", 1, Hotel::find($value->hotel_id)->name)[0];
                        $quotationData['cancelHotelData'][$key]['class'] = $hotelInfo->findHotel->first()->class()->first()->class;
                        $quotationData['cancelHotelData'][$key]['classStar'] = $hotelInfo->findHotel->first()->class()->first()->class;


                        $quotationData['cancelHotelData'][$key]['mealType'] = $mealPlan;
                        $quotationData['cancelHotelData'][$key]['mealTypeId'] = $hotelInfo->meal_type;

                        $quotationData['cancelHotelData'][$key]['roomCategory'] = $hotelInfo->findRoomCategory->name;
                        $quotationData['cancelHotelData'][$key]['roomCategoryId'] = $hotelInfo->room_category;
                        $quotationData['cancelHotelData'][$key]['mealPlanCode'] = $mealPlanCode;
                        $quotationData['cancelHotelData'][$key]['noOfNights'] = $hotel->no_of_nights;
                        $quotationData['cancelHotelData'][$key]['placeId'] = $hotel->place_id;
                        $quotationData['cancelHotelData'][$key]['placeName'] = $hotelInfo->findPlace()->first()->name;
                        $quotationData['cancelHotelData'][$key]['placeDesc'] = $hotelInfo->findPlace()->first()->description;

                        $quotationData['cancelHotelData'][$key]['roomTypeText'] = $hotelInfo->roomTypeText;

                        if (!empty($hotelInfo->adult_single_room_rate_pp)) {
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_single_room']['adultCostPP'] = $hotelInfo->adult_single_room_rate_pp;
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_single_room']['adultCost'] = $hotelInfo->adult_single_room_rate_total;
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_single_room']['roomCount'] = $hotelInfo->adult_single_room_count;
                            $single = $hotelInfo->adult_single_room_count ?? 0;

                        }
                        if (!empty($hotelInfo->adult_double_room_rate_pp)) {
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_double_room']['adultCostPP'] = $hotelInfo->adult_double_room_rate_pp;
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_double_room']['adultCost'] = $hotelInfo->adult_double_room_rate_total;
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_double_room']['roomCount'] = $hotelInfo->adult_double_room_count;
                            $double = $hotelInfo->adult_double_room_count ?? 0;
                        }
                        if (!empty($hotelInfo->adult_triple_room_rate_pp)) {
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_triple_room']['adultCostPP'] = $hotelInfo->adult_triple_room_rate_pp;
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_triple_room']['adultCost'] = $hotelInfo->adult_triple_room_rate_total;
                            $quotationData['cancelHotelData'][$key]['cost']['adult_rate_triple_room']['roomCount'] = $hotelInfo->adult_triple_room_count;
                            $triple = $hotelInfo->adult_triple_room_count ?? 0;
                        }

                        if (!empty($hotelInfo->child_rate)) {
                            $quotationData['cancelHotelData'][$key]['cost']['cwb'] = $hotelInfo->child_rate;
                        }
                        if (!empty($hotelInfo->child_w_bed_rate)) {
                            $quotationData['cancelHotelData'][$key]['cost']['cnb'] = $hotelInfo->child_w_bed_rate;
                        }
                    }

                }
            }

        }
        return $quotationData;
    }

    public function saveUpdatedQuotation(Request $request)
    {
        return DB::transaction(function() use ($request) {

            $quotationData = $request->fd_edit_quotation_front_json;
            $quotationData = json_decode($quotationData, true);
            $agentId = $request->fd_agent;
            $clientHonorific = $request->client_title;
            $clientName = $request->fd_front_edit_quotation_client_name;
            $state = $request->fd_front_edit_type;
            $msg = "";
            $status = 0;
            if ($state == "update") {
                $status = 2;
                $msg = "Your Quotation has been saved!";
            } else {
                $status = 3;
                $msg = "Your Quotation has Cancelled";
            }
            $confirmationData = $request->all();

            $quotationData["confirmationData"] = $confirmationData;

            $quotationNo = $quotationData['confirmData']['quotationNo'];
            $quotationData["quotationNo"] = $quotationNo;

            $apple_fd_packages_quotation = new apple_fd_packages_quotation();
            $apple_fd_packages_quotation->quotation_no = $quotationNo;
            $apple_fd_packages_quotation->status = $status;
            $apple_fd_packages_quotation->currency = 142;
            $apple_fd_packages_quotation->fd_package_id = $quotationData['packageId'];
            $apple_fd_packages_quotation->meal_plan_types = $quotationData['mealPlanTypes'];
            $apple_fd_packages_quotation->arrival_date = $quotationData['arrivalDateFormat'];
            $apple_fd_packages_quotation->nights_days_count = $quotationData['textData']['nightsDaysCount'];
            $apple_fd_packages_quotation->honorific_id = $clientHonorific;
            $apple_fd_packages_quotation->client_name = $clientName;
            $apple_fd_packages_quotation->agent_id = $agentId;
            $apple_fd_packages_quotation->user_id = Auth::user()->id;
            $apple_fd_packages_quotation->save();

            $referenceId = $apple_fd_packages_quotation->id;

            foreach ($quotationData['hotelData'] as $hotelKey => $hotelValue) {
                $apple_fd_packages_quotation_hotel_rates = new apple_fd_packages_quotation_hotel_rates();
                $apple_fd_packages_quotation_hotel_rates->reference_id = $referenceId;
                $apple_fd_packages_quotation_hotel_rates->place_id = $hotelValue['placeId'];
                $apple_fd_packages_quotation_hotel_rates->hotel_id = $hotelValue['hotelId'];
                $apple_fd_packages_quotation_hotel_rates->check_in = $hotelValue['checkIn'];
                $apple_fd_packages_quotation_hotel_rates->check_out = $hotelValue['CheckOut'];
                $apple_fd_packages_quotation_hotel_rates->provider = $hotelValue['provider'];
                $apple_fd_packages_quotation_hotel_rates->roomTypeText = $hotelValue['roomTypeText'];
                $apple_fd_packages_quotation_hotel_rates->no_of_nights = $hotelValue['noOfNights'];
                $apple_fd_packages_quotation_hotel_rates->meal_type = $hotelValue['mealTypeId'];
                $apple_fd_packages_quotation_hotel_rates->room_category = $hotelValue['roomCategoryId'];

                if (isset($hotelValue['cost']['adult_rate_single_room'])) {
                    $apple_fd_packages_quotation_hotel_rates->adult_single_room_rate_pp = $hotelValue['cost']['adult_rate_single_room']['adultCostPP'];
                    $apple_fd_packages_quotation_hotel_rates->adult_single_room_rate_total = $hotelValue['cost']['adult_rate_single_room']['adultCost'];
                    $apple_fd_packages_quotation_hotel_rates->adult_single_room_count = $hotelValue['cost']['adult_rate_single_room']['roomCount'];
                }

                if (isset($hotelValue['cost']['adult_rate_double_room'])) {
                    $apple_fd_packages_quotation_hotel_rates->adult_double_room_rate_pp = $hotelValue['cost']['adult_rate_double_room']['adultCostPP'];
                    $apple_fd_packages_quotation_hotel_rates->adult_double_room_rate_total = $hotelValue['cost']['adult_rate_double_room']['adultCost'];
                    $apple_fd_packages_quotation_hotel_rates->adult_double_room_count = $hotelValue['cost']['adult_rate_double_room']['roomCount'];
                }

                if (isset($hotelValue['cost']['adult_rate_triple_room'])) {
                    $apple_fd_packages_quotation_hotel_rates->adult_triple_room_rate_pp = $hotelValue['cost']['adult_rate_triple_room']['adultCostPP'];
                    $apple_fd_packages_quotation_hotel_rates->adult_triple_room_rate_total = $hotelValue['cost']['adult_rate_triple_room']['adultCost'];
                    $apple_fd_packages_quotation_hotel_rates->adult_triple_room_count = $hotelValue['cost']['adult_rate_triple_room']['roomCount'];
                }

                if (isset($hotelValue['cost']['cwb'])) {
                    $apple_fd_packages_quotation_hotel_rates->child_rate = $hotelValue['cost']['cwb'];
                }
                if (isset($hotelValue['cost']['cnb'])) {
                    $apple_fd_packages_quotation_hotel_rates->child_w_bed_rate = $hotelValue['cost']['cnb'];
                }

                $apple_fd_packages_quotation_hotel_rates->email_status = 0;
                $apple_fd_packages_quotation_hotel_rates->save();
            }

            $apple_fd_packages_quotation_cost = new apple_fd_packages_quotation_cost();
            $apple_fd_packages_quotation_cost->reference_id = $referenceId;
            $apple_fd_packages_quotation_cost->adult_package_cost = $quotationData['packageCost']['adultCost'];
            $apple_fd_packages_quotation_cost->cwb_package_cost = $quotationData['packageCost']['cwb'];
            $apple_fd_packages_quotation_cost->cnb_package_cost = $quotationData['packageCost']['cnb'];

            if (isset($quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost'])) {
                $apple_fd_packages_quotation_cost->adult_single_room_cost = $quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost']['cost'];
            }

            if (isset($quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost'])) {
                $apple_fd_packages_quotation_cost->adult_double_room_cost = $quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost']['cost'];
            }

            if (isset($quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost'])) {
                $apple_fd_packages_quotation_cost->adult_triple_room_cost = $quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost']['cost'];
            }
            $apple_fd_packages_quotation_cost->cwb_cost = $quotationData['finalCost']['cwbHotelPpCost'];
            $apple_fd_packages_quotation_cost->cnb_cost = $quotationData['finalCost']['cnbHotelPpCost'];
            $apple_fd_packages_quotation_cost->total_hotel_cost = $quotationData['totalHotelCost'];
            $apple_fd_packages_quotation_cost->grand_cost = $quotationData['finalCost']['grandCost'];
            $apple_fd_packages_quotation_cost->save();

            if (isset($confirmationData['remark'])) {
                foreach ($confirmationData['remark'] as $remarkKey => $remark) {
                    $apple_fd_packages_quotation_hotel_vouchers_info = new apple_fd_packages_quotation_hotel_vouchers_info();
                    $apple_fd_packages_quotation_hotel_vouchers_info->reference_id = $referenceId;
                    $apple_fd_packages_quotation_hotel_vouchers_info->place_id = $confirmationData['hotel_voucher_place_id'][$remarkKey];
                    $apple_fd_packages_quotation_hotel_vouchers_info->hotel_id = $confirmationData['hotel_voucher_hotel_id'][$remarkKey];
                    $apple_fd_packages_quotation_hotel_vouchers_info->remarks = $remark;
                    $apple_fd_packages_quotation_hotel_vouchers_info->confirmation_notes = $confirmationData['confirmation_note'][$remarkKey];
                    $apple_fd_packages_quotation_hotel_vouchers_info->emails = $confirmationData['emails'][$remarkKey];
                    $apple_fd_packages_quotation_hotel_vouchers_info->email_status = 0;
                    $apple_fd_packages_quotation_hotel_vouchers_info->save();

                }
            }
            if (isset($confirmationData['hotel_voucher_cancell_place_id'])) {
                foreach ($confirmationData['cancell_remark'] as $remarkKey => $remark) {
                    $apple_fd_packages_quotation_cancelled_hotels = new apple_fd_packages_quotation_cancelled_hotels();
                    $apple_fd_packages_quotation_cancelled_hotels->reference_id = $referenceId;
                    $apple_fd_packages_quotation_cancelled_hotels->place_id = $confirmationData['hotel_voucher_cancell_place_id'][$remarkKey];
                    $apple_fd_packages_quotation_cancelled_hotels->hotel_id = $confirmationData['hotel_voucher_cancell_hotel_id'][$remarkKey];
                    $apple_fd_packages_quotation_cancelled_hotels->cancel_remark = $remark;
                    $apple_fd_packages_quotation_cancelled_hotels->cancel_note = $confirmationData['cancell_confirmation_note'][$remarkKey];
                    $apple_fd_packages_quotation_cancelled_hotels->cancel_emails = $confirmationData['cancell_emails'][$remarkKey];
                    $apple_fd_packages_quotation_cancelled_hotels->email_status = 0;
                    $apple_fd_packages_quotation_cancelled_hotels->save();
                }
            }

            $apple_fd_packages_quotation_pax = new apple_fd_packages_quotation_pax();
            $apple_fd_packages_quotation_pax->reference_id = $referenceId;
            $apple_fd_packages_quotation_pax->no_of_adults = $quotationData['pax']['adult'];
            $apple_fd_packages_quotation_pax->no_of_cwb = $quotationData['pax']['cwb'];
            $apple_fd_packages_quotation_pax->no_of_cnb = $quotationData['pax']['cnb'];
            $apple_fd_packages_quotation_pax->vehicle_name = $quotationData['vehicleName'];
            $apple_fd_packages_quotation_pax->save();


            if ($state != "update") {
                unset($quotationData['hotelData']);
                $quotationData = $this->getQuotation($referenceId, $quotationNo);
                $this->sendTourConfirmationVouchers($quotationData);
                $quotationData = $this->getQuotation($referenceId, $quotationNo, "cancel");
                $this->sendNotification($quotationData, 3);
            } else {
                $quotationData = $this->getQuotation($referenceId, $quotationNo);
                $this->sendTourConfirmationVouchers($quotationData);
                $this->sendNotification($quotationData, 2);
            }

            $voutureData = $this->generateHotelVoutures($quotationData);
            $updateCount = apple_fd_packages_quotation::where('quotation_no', '=', $quotationNo)->get()->count();
            $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];

            $pnlRequest = new Request();
            $pnlRequest->replace(['fdQuotationReferenceNo' => $referenceId, 'fdQuotationNo' => $quotationNo]);
            $lostProfitHtml = $this->lostProfit($pnlRequest);

            return view('fd_packages.fd_packages_quotation_save', compact('referenceId', 'quotationNo', "voutureData", 'updateCount', 'msg', 'state', 'roleId', 'lostProfitHtml'));
        });
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\Response|string
     */


    public function changePaxAndDate(Request $request)
    {
        $Data = $request->input();
        $rules = [
            'fd_front_edit_pax' => 'required|array',
            'fd_front_edit_pax.adult' => 'numeric|required|min:1'
        ];

        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }
        $quotationData = $request->fd_edit_quotation_front_json;
        $quotationData = json_decode($quotationData, true);
        $fdPackageId = $quotationData['packageId'];
        $fdPackageType =  $quotationData['packageType'];

        $year = $request->fd_front_edit_arrival_date['year'];
        $month = $request->fd_front_edit_arrival_date['month'];
        $date = $request->fd_front_edit_arrival_date['day'];

        $noOfAdults = $request->fd_front_edit_pax['adult'];
        $noOfCwb = $request->fd_front_edit_pax['cwb'];
        $noOfCnb = $request->fd_front_edit_pax['cnb'];
        $totalPax = $noOfAdults + $noOfCwb + $noOfCnb;

        $quotationNoOfpax = $quotationData['pax']['adult'] + $quotationData['pax']['cwb'] + $quotationData['pax']['cnb'];
        if($fdPackageType == 1) {
            $packageInfoObj = apple_fd_packages::find($fdPackageId);
            $maximumPaxAllowed = $packageInfoObj->max_pax;
                if($totalPax > $maximumPaxAllowed) {
                    $Status['status'] = false;
                    $messages['msgs'][0] = "Maximum Allowed Pax is ".$maximumPaxAllowed ." For This Group Package";
                    $Status['error'] = $messages;
                    return $Status;
                }
        }

        $arrivalDateEdit = Carbon::create($year, $month, $date);
        $quotationArrivalDate = Carbon::parse($quotationData['arrivalDateFormat']);


        $apple_fd_package = apple_fd_packages::where('id', $fdPackageId)->first();
        $countryId = $apple_fd_package->country;

        if (!($arrivalDateEdit->isSameDay($quotationArrivalDate))) {

            $PackageValidFrom = Carbon::parse($apple_fd_package->date_from);
            $PackageValidTo = Carbon::parse($apple_fd_package->date_to);

            $valid = $arrivalDateEdit->between($PackageValidFrom, $PackageValidTo);

            if ($valid == false) {
                $Status['status'] = false;
                $messages['msgs'][0] = "Arrival Date is Invalid For Selected Package";
                $Status['error'] = $messages;
                return $Status;
            }
            $placeModel = new Place();
            $image = new ImgaeGenerator();

            $pickUpLocation = Place::where('ID', '=', $apple_fd_package->pick_up)->first()->name;
            $pickUpLocationId = Place::where('ID', '=', $apple_fd_package->pick_up)->first()->ID;
            $dropOffLocation = Place::where('ID', '=', $apple_fd_package->drop_off)->first()->name;
            $dropOffLocationID = Place::where('ID', '=', $apple_fd_package->drop_off)->first()->ID;

            foreach ($quotationData['hotelData'] as $hotelKey => $hotelValue) {
                if ($hotelKey == 0) {
                    $quotationData['hotelData'][$hotelKey]['checkIn'] = $arrivalDateEdit->toDateString();
                    $quotationData['hotelData'][$hotelKey]['CheckOut'] = $arrivalDateEdit->addDays($hotelValue['noOfNights'])->toDateString();

                    $quotationData['hotelData'][$hotelKey]['checkInFormatted'] = Carbon::parse($quotationData['hotelData'][$hotelKey]['checkIn'])->toFormattedDateString();
                    $quotationData['hotelData'][$hotelKey]['CheckOutFormatted'] = Carbon::parse($quotationData['hotelData'][$hotelKey]['CheckOut'])->toFormattedDateString();
                } else {
                    $quotationData['hotelData'][$hotelKey]['checkIn'] = $quotationData['hotelData'][$hotelKey - 1]['CheckOut'];
                    $quotationData['hotelData'][$hotelKey]['CheckOut'] = $this->generateDates($quotationData['hotelData'][$hotelKey - 1]['CheckOut'], $hotelValue['noOfNights']);

                    $quotationData['hotelData'][$hotelKey]['checkInFormatted'] = Carbon::parse($quotationData['hotelData'][$hotelKey - 1]['CheckOut'])->toFormattedDateString();
                    $quotationData['hotelData'][$hotelKey]['CheckOutFormatted'] = Carbon::parse($this->generateDates($quotationData['hotelData'][$hotelKey - 1]['CheckOut'], $hotelValue['noOfNights']))->toFormattedDateString();
                }
            }

            $itinerariesArray = array();
            $day = 1;
            $itenaryLastIndex = 0;
            foreach ($quotationData['hotelData'] as $hotelCostKey => $hotelCostValues) {

                if (isset($hotelCostValues['LasePlaceName'])) {
                    $itinerariesArray[$hotelCostKey]['LastPlaceName'] = $hotelCostValues['LasePlaceName'];
                    $itinerariesArray[$hotelCostKey]['LastPlaceId'] = $hotelCostValues['LasePlaceId'];
                    $itinerariesArray[$hotelCostKey]['Distance'] = $hotelCostValues['Distance'];
                    $itinerariesArray[$hotelCostKey]['Duration'] = $hotelCostValues['Duration'];
                } else {
                    $itinerariesArray[$hotelCostKey]['LastPlaceName'] = $pickUpLocation;
                    $itinerariesArray[$hotelCostKey]['LastPlaceId'] = $pickUpLocationId;
                    $itinerariesArray[$hotelCostKey]['Distance'] = $placeModel->getPlaceDistance($pickUpLocationId, $hotelCostValues['placeId'])->distance / 1000 . " KM";
                    $itinerariesArray[$hotelCostKey]['Duration'] = formatDuration($placeModel->getPlaceDistance($pickUpLocationId, $hotelCostValues['placeId'])->time);

                }

                $itinerariesArray[$hotelCostKey]['placeID'] = $hotelCostValues['placeId'];
                $itinerariesArray[$hotelCostKey]['placeName'] = $hotelCostValues['placeName'];
                $itinerariesArray[$hotelCostKey]['placeDesc'] = $hotelCostValues['placeDesc'];
                $itinerariesArray[$hotelCostKey]['placeImage'] = $hotelCostValues['placeImage'];
                $itinerariesArray[$hotelCostKey]['roomTypeText'] = $hotelCostValues['roomTypeText'];
                $itinerariesArray[$hotelCostKey]['noOfNights'] = $hotelCostValues['noOfNights'];
                $itinerariesArray[$hotelCostKey]['checkIn'] = $hotelCostValues['checkIn'];
                $itinerariesArray[$hotelCostKey]['checkInFormatted'] = $hotelCostValues['checkInFormatted'];

                $startDate = Carbon::parse($hotelCostValues['checkIn']);
                $endDate = Carbon::parse($hotelCostValues['CheckOut']);
                $dateRange = $this->generateDateRange($startDate, $endDate);

                foreach ($dateRange as $dateKey => $dateValue) {
                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['day'] = $day;
                    // attractions
                    $attractionsObj = apple_fd_packages_attractions::where('fd_package_id', '=', $fdPackageId)
                        ->where('place_id', '=', $hotelCostValues['placeId'])->get();
                    if (count($attractionsObj) > 0) {
                        foreach ($attractionsObj as $attractionKey => $attractionValue) {
                            if (($dateKey + 1) == $attractionValue->day) {
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_id'] = $attractionValue->attraction_id;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['type'] = $attractionValue->attraction_type;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['adult_rate'] = $attractionValue->adult_rate;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['cwb_rate'] = $attractionValue->child_rate;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['cnb_rate'] = $attractionValue->child_w_out_bed_rate;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];

                                if ($attractionValue->attraction_type == "attraction") {
                                    $attraction = AttractionModel::where('ID', '=', $attractionValue->attraction_id)->first();
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $attraction->point;
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $attraction->description;
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "attraction", 1, $attraction->point)[0];

                                } else if ($attractionValue->attraction_type == "city_tour") {

                                    $cityTour = CityTour::where('ID', '=', $attractionValue->attraction_id)->first();
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $cityTour->name;
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $cityTour->description;
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "city_tour", 1, $cityTour->name)[0];

                                } else {
                                    $excursion = Excursion::where('ID', '=', $attractionValue->attraction_id)->first();
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_name'] = $excursion->name;
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['attraction_desc'] = $excursion->description;
                                    $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['data'][$attractionKey]['imagePath'] = $image->getImage($attractionValue->attraction_id, '4x', "place", 1, $excursion->name)[0];
                                }

                            } else {
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['text'] = "Day at leisure at the Hotel!";
                                $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];
                            }
                        }
                    } else {
                        $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['date'] = $dateValue;
                        $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['dateFormatted'] = Carbon::parse($dateValue)->toFormattedDateString();
                        $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['text'] = "Day at leisure at the Hotel!";
                        $itinerariesArray[$hotelCostKey]['dates'][$dateKey]['placeName'] = $hotelCostValues['placeName'];
                    }
                    $day++;

                }
                $itenaryLastIndex = $hotelCostKey;
            }
            //  drop off array data
            $itenaryLastIndex = $itenaryLastIndex + 1;
            $lastArrObj = end($itinerariesArray);
            $lastDateObj = end($lastArrObj['dates']);
            $itinerariesArray[$itenaryLastIndex]['LastPlaceName'] = $lastArrObj['placeName'];
            $itinerariesArray[$itenaryLastIndex]['LastPlaceId'] = $lastArrObj['placeID'];
            $itinerariesArray[$itenaryLastIndex]['Distance'] = $placeModel->getPlaceDistance($lastArrObj['placeID'], $dropOffLocationID)->distance / 1000 . " KM";
            $itinerariesArray[$itenaryLastIndex]['Duration'] = formatDuration($placeModel->getPlaceDistance($lastArrObj['placeID'], $dropOffLocationID)->time);
            $itinerariesArray[$itenaryLastIndex]['placeID'] = $dropOffLocationID;
            $itinerariesArray[$itenaryLastIndex]['checkInFormatted'] = Carbon::parse($lastDateObj['date'])->addDay()->toFormattedDateString();
            $itinerariesArray[$itenaryLastIndex]['placeName'] = $dropOffLocation;
            $itinerariesArray[$itenaryLastIndex]['dates'][0]['date'] = Carbon::parse($lastDateObj['date'])->addDay()->toDateString();
            $itinerariesArray[$itenaryLastIndex]['dates'][0]['day'] = $day;
            $quotationData['itineraries'] = $itinerariesArray;

            $arrivalDateArray = array('year' => $year, 'month' => $month, 'day' => $date);
            $quotationData['arrivalDate'] = $year . " / " . $month . " / " . $date;
            $quotationData['arrivalDateFormat'] = $year . "-" . $month . "-" . $date;
            $quotationData['arrival_date_obj'] = $arrivalDateArray;
            $quotationData['deadLineDate'] = Carbon::create($year, $month, $date)->addDays(14)->toDayDateTimeString();
        }
        if (!($totalPax == $quotationNoOfpax)) {

            $vehicle = $this->getPaxToVehicle($countryId, $totalPax, $quotationArrivalDate->toDateString());
            if (!$vehicle) {
                $Status['status'] = false;
                $messages['msgs'][0] = "Coudn't Identify The Vehicle Type";
                $Status['error'] = $messages;
                return $Status;
            }
            $vehicleName = VehicleType::where('ID', '=', $vehicle->vehicle)->first()->name;

            $totalHotelCost = 0;

            $adultCost = 0;
            $cwdCost = 0;
            $cnbCost = 0;

            $adultPackageChargesColumn = "";
            if($noOfAdults == 1) {
                $adultPackageChargesColumn = "adult_rate";
            } else if ($noOfAdults  == 2) {
                $adultPackageChargesColumn = "adult_rate_1";
            } else if ($noOfAdults == 3) {
                $adultPackageChargesColumn = "adult_rate_2";
            } else if ($noOfAdults == 4) {
                $adultPackageChargesColumn = "adult_rate_3";
            } else if ($noOfAdults == 5) {
                $adultPackageChargesColumn = "adult_rate_4";
            } else if ($noOfAdults == 6) {
                $adultPackageChargesColumn = "adult_rate_5";
            } else if ($noOfAdults == 7) {
                $adultPackageChargesColumn = "adult_rate_6";
            } else if ($noOfAdults == 8) {
                $adultPackageChargesColumn = "adult_rate_7";
            } else if ($noOfAdults == 9) {
                $adultPackageChargesColumn = "adult_rate_8";
            } else if ($noOfAdults == 10) {
                $adultPackageChargesColumn = "adult_rate_9";
            } else if ($noOfAdults == 11) {
                $adultPackageChargesColumn = "adult_rate_10";
            } else if ($noOfAdults == 12) {
                $adultPackageChargesColumn = "adult_rate_11";
            } else if ($noOfAdults == 13) {
                $adultPackageChargesColumn = "adult_rate_12";
            } else if ($noOfAdults == 14) {
                $adultPackageChargesColumn = "adult_rate_13";
            } else if ($noOfAdults == 15) {
                $adultPackageChargesColumn = "adult_rate_14";
            } else if($adultPackageChargesColumn > 15){
                $adultPackageChargesColumn = "adult_rate_14";
            }
            $prices = $apple_fd_package->findPackagePrice{0};

            $adultSupplementCost = 0;
            $cwbSupplementCost = 0;
            $cnbSupplementCost = 0;
            $arrivalDate = Carbon::parse($quotationData['arrivalDateFormat']);
            if(count($apple_fd_package->findSuppliment) > 0) {
                foreach ($apple_fd_package->findSuppliment as $supplementCostKey => $supplement){
                    $fromDate = Carbon::parse($supplement->from);
                    $toDate = Carbon::parse($supplement->to);
                    $valid = $arrivalDate->between($fromDate, $toDate);
                    if($valid){
                        $adultSupplementCost += $supplement->adult_cost;
                        $cwbSupplementCost += $supplement->cwb_cost;
                        $cnbSupplementCost += $supplement->cnb_cost;
                    }
                }
            }

            $adultCost = $prices->$adultPackageChargesColumn * $noOfAdults;
            $cwdCost = $prices->child_rate * $noOfCwb;
            $cnbCost = $prices->child_w_out_bed_rate * $noOfCnb;
            $packageCostArray = array('adultCost' => $prices->$adultPackageChargesColumn + $adultSupplementCost, 'cwb' => $prices->child_rate + $cwdCost, 'cnb' => $prices->child_w_out_bed_rate + $cnbCost);

            $Hotel = new Hotel();
            $RoomType = $Hotel->getHotelRoomCountWithPax($request->fd_front_edit_pax);


            $roomTypeText = "";

            foreach ($RoomType as $RoomTypeID => $RoomCount) {
                if ($RoomCount) {
                    $roomTypeText .= $RoomCount . ' ' . Room::find($RoomTypeID)->short_name . " ";
                }
            }
            foreach ($quotationData['hotelData'] as $hotelKey => $hotelValue) {
                $packageHotelsObj = apple_fd_packages_hotels::where('fd_package_id', '=', $fdPackageId)
                    ->where('hotel_id', '=', $hotelValue['hotelId'])->first();
                unset($quotationData['hotelData'][$hotelKey]['cost']);
                if ($noOfCwb > 0) {
                    $quotationData['hotelData'][$hotelKey]['cost']['cwb'] = $packageHotelsObj->child_rate * $packageHotelsObj->no_of_nights * $noOfCwb;
                }
                if ($noOfCnb > 0) {
                    $quotationData['hotelData'][$hotelKey]['cost']['cnb'] = $packageHotelsObj->child_w_out_bed_rate * $packageHotelsObj->no_of_nights * $noOfCnb;
                }

                $quotationData['hotelData'][$hotelKey]['roomTypeText'] = $roomTypeText;

                foreach ($RoomType as $RoomTypeID => $RoomCount) {
                    $roomColumn = "";
                    if ($RoomCount) {
                        if ($RoomTypeID == 1) {
                            $roomColumn = "adult_rate_single_room";
                        } else if ($RoomTypeID == 2) {
                            $roomColumn = "adult_rate_double_room";
                        } else if ($RoomTypeID == 3) {
                            $roomColumn = "adult_rate_triple_room";
                        }
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCostPP'] = round(($packageHotelsObj->$roomColumn * $packageHotelsObj->no_of_nights) / $RoomTypeID, 2);
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCost'] = $packageHotelsObj->$roomColumn * $packageHotelsObj->no_of_nights;
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['roomCount'] = $RoomCount;
                        $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCost'] * $RoomCount;
                    }
                }
                if ($noOfCwb > 0) {
                    $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost']['cwb'];
                }
                if ($noOfCnb > 0) {
                    $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost']['cnb'];
                }

            }
            $adultPpHotelCost = 0;
            $cwbPpHotelCost = 0;
            $cnbPpHotelCost = 0;

            $roomCostBreakDown = array();
            $adultSingleRoomCost = 0;
            $adultDoubleRoomCost = 0;
            $adultTripleRoomCost = 0;

            foreach ($quotationData['hotelData'] as $hotelCostKey => $hotelCostValues) {

                if (isset($hotelCostValues['cost']['adult_rate_single_room'])) {
                    $adultSingleRoomCost += $hotelCostValues['cost']['adult_rate_single_room']['adultCostPP'];
                }
                if (isset($hotelCostValues['cost']['adult_rate_double_room'])) {
                    $adultDoubleRoomCost += $hotelCostValues['cost']['adult_rate_double_room']['adultCostPP'];
                }
                if (isset($hotelCostValues['cost']['adult_rate_triple_room'])) {
                    $adultTripleRoomCost += $hotelCostValues['cost']['adult_rate_triple_room']['adultCostPP'];
                }
                if (isset($hotelCostValues['cost']['cwb'])) {
                    $cwbPpHotelCost += $hotelCostValues['cost']['cwb'] / $noOfCwb;
                }
                if (isset($hotelCostValues['cost']['cnb'])) {
                    $cnbPpHotelCost += $hotelCostValues['cost']['cnb'] / $noOfCnb;
                }
            }
            $quotationData['RoomTypes'] = $RoomType;

            if ($adultSingleRoomCost > 0) {
                $roomCostBreakDown['adultSingleRoomCost']['cost'] = round($adultSingleRoomCost, 2);
                $roomCostBreakDown['adultSingleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
                $roomCostBreakDown['adultSingleRoomCost']['totalCost'] = $roomCostBreakDown['adultSingleRoomCost']['cost'] + $prices->$adultPackageChargesColumn + $adultSupplementCost;
            }
            if ($adultDoubleRoomCost > 0) {
                $roomCostBreakDown['adultDoubleRoomCost']['cost'] = round($adultDoubleRoomCost, 2);
                $roomCostBreakDown['adultDoubleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
                $roomCostBreakDown['adultDoubleRoomCost']['totalCost'] = $roomCostBreakDown['adultDoubleRoomCost']['cost'] + $roomCostBreakDown['adultDoubleRoomCost']['packageCost'];
            }
            if ($adultTripleRoomCost > 0) {
                $roomCostBreakDown['adultTripleRoomCost']['cost'] = round($adultTripleRoomCost, 2);
                $roomCostBreakDown['adultTripleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
                $roomCostBreakDown['adultTripleRoomCost']['totalCost'] = $roomCostBreakDown['adultTripleRoomCost']['cost'] + $roomCostBreakDown['adultTripleRoomCost']['packageCost'];
            }

            $totCost = 0;
            if(isset($roomCostBreakDown['adultSingleRoomCost'])){
                $totCost += $roomCostBreakDown['adultSingleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][1] * 1);
            }
            if(isset($roomCostBreakDown['adultDoubleRoomCost'])){
                $totCost += $roomCostBreakDown['adultDoubleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][2] * 2);
            }
            if(isset($roomCostBreakDown['adultTripleRoomCost'])){
                $totCost += $roomCostBreakDown['adultTripleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][3] * 3);
            }

            if($quotationData['pax']['cwb'] > 0){
                $totCost += ($prices->child_rate + $cwbSupplementCost + $cwbPpHotelCost) * $quotationData['pax']['cwb'];
            }

            if($quotationData['pax']['cnb'] > 0){
                $totCost += ($prices->child_w_out_bed_rate + $cnbSupplementCost + $cnbPpHotelCost) * $quotationData['pax']['cnb'];
            }


            $finalCostArray = array(
                'adultCostBreakDown' => $roomCostBreakDown,
                'cwbHotelPpCost' => $cwbPpHotelCost,
                'cwbPackagePpCost' => $prices->child_rate + $cwbSupplementCost,
                'cwbPpTotalCost' => $prices->child_rate + $cwbSupplementCost + $cwbPpHotelCost,
                'cnbHotelPpCost' => $cnbPpHotelCost,
                'cnbPackagePpCost' => $prices->child_w_out_bed_rate + $cnbSupplementCost ,
                'cnbPpTotalCost' => $prices->child_w_out_bed_rate + $cnbSupplementCost + $cnbPpHotelCost,
//            'grandCost' => $finalCost + $adultCost + $cwdCost + $cnbCost
                'grandCost' => $totCost
            );

            $quotationData['pax'] = $request->fd_front_edit_pax;
            $quotationData['vehicleName'] = $vehicleName;
            $quotationData['RoomTypes'] = $RoomType;
            $quotationData['packageCost'] = $packageCostArray;
            $quotationData['finalCost'] = $finalCostArray;
            $quotationData['totalHotelCost'] = $totalHotelCost;
        }
//        return json_encode($quotationData);
        return view('fd_packages.edit.fd_packages_edit_quotation', compact('quotationData'));
    }

    function identical_values($arrayA, $arrayB)
    {

        sort($arrayA);
        sort($arrayB);

        return $arrayA == $arrayB;
    }

    public function changeRoomTypes(Request $request)
    {
        $type = $request->fd_quotation_front_type;
        if ($type == "new") {
            $quotationData = $request->fd_quotation_front_json;
        } else {
            $quotationData = $request->fd_edit_quotation_front_json;
        }

        $quotationData = json_decode($quotationData, true);
        $quotationRoomType = $quotationData['RoomTypes'];
        $noOfAdults = $quotationData['pax']['adult'];
        $noOfCwb = $quotationData['pax']['cwb'];
        $noOfCnb = $quotationData['pax']['cnb'];

        $fdPackageId = $quotationData['packageId'];
        $apple_fd_package = apple_fd_packages::where('id', $fdPackageId)->first();

        $RoomType = $request->room_type;
        $RoomType[4] = "0";
        $RoomType[5] = "0";

        $arrivalDate = Carbon::parse($quotationData['arrivalDateFormat']);

        if (!($this->identical_values($RoomType, $quotationRoomType))) {


            $totalHotelCost = 0;

            $adultPackageChargesColumn = "";
            if($noOfAdults == 1) {
                $adultPackageChargesColumn = "adult_rate";
            } else if ($noOfAdults  == 2) {
                $adultPackageChargesColumn = "adult_rate_1";
            } else if ($noOfAdults == 3) {
                $adultPackageChargesColumn = "adult_rate_2";
            } else if ($noOfAdults == 4) {
                $adultPackageChargesColumn = "adult_rate_3";
            } else if ($noOfAdults == 5) {
                $adultPackageChargesColumn = "adult_rate_4";
            } else if ($noOfAdults == 6) {
                $adultPackageChargesColumn = "adult_rate_5";
            } else if ($noOfAdults == 7) {
                $adultPackageChargesColumn = "adult_rate_6";
            } else if ($noOfAdults == 8) {
                $adultPackageChargesColumn = "adult_rate_7";
            } else if ($noOfAdults == 9) {
                $adultPackageChargesColumn = "adult_rate_8";
            } else if ($noOfAdults == 10) {
                $adultPackageChargesColumn = "adult_rate_9";
            } else if ($noOfAdults == 11) {
                $adultPackageChargesColumn = "adult_rate_10";
            } else if ($noOfAdults == 12) {
                $adultPackageChargesColumn = "adult_rate_11";
            } else if ($noOfAdults == 13) {
                $adultPackageChargesColumn = "adult_rate_12";
            } else if ($noOfAdults == 14) {
                $adultPackageChargesColumn = "adult_rate_13";
            } else if ($noOfAdults == 15) {
                $adultPackageChargesColumn = "adult_rate_14";
            } else if($adultPackageChargesColumn > 15){
                $adultPackageChargesColumn = "adult_rate_14";
            }

            $adultSupplementCost = 0;
            $cwbSupplementCost = 0;
            $cnbSupplementCost = 0;
            if(count($apple_fd_package->findSuppliment) > 0) {
                foreach ($apple_fd_package->findSuppliment as $supplementCostKey => $supplement){
                    $fromDate = Carbon::parse($supplement->from);
                    $toDate = Carbon::parse($supplement->to);
                    $valid = $arrivalDate->between($fromDate, $toDate);
                    if($valid){
                        $adultSupplementCost += $supplement->adult_cost;
                        $cwbSupplementCost += $supplement->cwb_cost;
                        $cnbSupplementCost += $supplement->cnb_cost;
                    }
                }
            }

            $prices = $apple_fd_package->findPackagePrice{0};

            $adultCost = ($prices->$adultPackageChargesColumn + $adultSupplementCost) * $noOfAdults;
            $cwdCost = ($prices->child_rate + $cwbSupplementCost) * $noOfCwb;
            $cnbCost = ($prices->child_w_out_bed_rate + $cnbSupplementCost) * $noOfCnb;
            $packageCostArray = array('adultCost' => $prices->$adultPackageChargesColumn + $adultSupplementCost, 'cwb' => $prices->child_rate + $cwbSupplementCost, 'cnb' => $prices->child_w_out_bed_rate + $cnbSupplementCost);

            $roomTypeText = "";
            foreach ($RoomType as $RoomTypeID => $RoomCount) {
                if ($RoomCount) {
                    $roomTypeText .= $RoomCount . ' ' . Room::find($RoomTypeID)->short_name . " ";
                }
            }
            foreach ($quotationData['hotelData'] as $hotelKey => $hotelValue) {
                $packageHotelsObj = apple_fd_packages_hotels::where('fd_package_id', '=', $fdPackageId)
                    ->where('hotel_id', '=', $hotelValue['hotelId'])->first();
                unset($quotationData['hotelData'][$hotelKey]['cost']);
                if ($noOfCwb > 0) {
                    $quotationData['hotelData'][$hotelKey]['cost']['cwb'] = $packageHotelsObj->child_rate * $packageHotelsObj->no_of_nights * $noOfCwb;
                }
                if ($noOfCnb > 0) {
                    $quotationData['hotelData'][$hotelKey]['cost']['cnb'] = $packageHotelsObj->child_w_out_bed_rate * $packageHotelsObj->no_of_nights * $noOfCnb;
                }

                $quotationData['hotelData'][$hotelKey]['roomTypeText'] = $roomTypeText;

                foreach ($RoomType as $RoomTypeID => $RoomCount) {
                    $roomColumn = "";
                    if ($RoomCount) {
                        if ($RoomTypeID == 1) {
                            $roomColumn = "adult_rate_single_room";
                        } else if ($RoomTypeID == 2) {
                            $roomColumn = "adult_rate_double_room";
                        } else if ($RoomTypeID == 3) {
                            $roomColumn = "adult_rate_triple_room";
                        }
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCostPP'] = round(($packageHotelsObj->$roomColumn * $packageHotelsObj->no_of_nights) / $RoomTypeID, 2);
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCost'] = $packageHotelsObj->$roomColumn * $packageHotelsObj->no_of_nights;
                        $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['roomCount'] = $RoomCount;
                        $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost'][$roomColumn]['adultCost'] * $RoomCount;
                    }
                }
                if ($noOfCwb > 0) {
                    $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost']['cwb'];
                }
                if ($noOfCnb > 0) {
                    $totalHotelCost += $quotationData['hotelData'][$hotelKey]['cost']['cnb'];
                }

            }
            $cwbPpHotelCost = 0;
            $cnbPpHotelCost = 0;

            $roomCostBreakDown = array();
            $adultSingleRoomCost = 0;
            $adultDoubleRoomCost = 0;
            $adultTripleRoomCost = 0;

            foreach ($quotationData['hotelData'] as $hotelCostKey => $hotelCostValues) {

                if (isset($hotelCostValues['cost']['adult_rate_single_room'])) {
                    $adultSingleRoomCost += $hotelCostValues['cost']['adult_rate_single_room']['adultCostPP'];
                }
                if (isset($hotelCostValues['cost']['adult_rate_double_room'])) {
                    $adultDoubleRoomCost += $hotelCostValues['cost']['adult_rate_double_room']['adultCostPP'];
                }
                if (isset($hotelCostValues['cost']['adult_rate_triple_room'])) {
                    $adultTripleRoomCost += $hotelCostValues['cost']['adult_rate_triple_room']['adultCostPP'];
                }
                if (isset($hotelCostValues['cost']['cwb'])) {
                    $cwbPpHotelCost += $hotelCostValues['cost']['cwb'] / $noOfCwb;
                }
                if (isset($hotelCostValues['cost']['cnb'])) {
                    $cnbPpHotelCost += $hotelCostValues['cost']['cnb'] / $noOfCnb;
                }
            }

            if ($adultSingleRoomCost > 0) {
                $roomCostBreakDown['adultSingleRoomCost']['cost'] = round($adultSingleRoomCost, 2);
                $roomCostBreakDown['adultSingleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
                $roomCostBreakDown['adultSingleRoomCost']['totalCost'] = $roomCostBreakDown['adultSingleRoomCost']['cost'] + $prices->$adultPackageChargesColumn + $adultSupplementCost;
            }
            if ($adultDoubleRoomCost > 0) {
                $roomCostBreakDown['adultDoubleRoomCost']['cost'] = round($adultDoubleRoomCost, 2);
                $roomCostBreakDown['adultDoubleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
                $roomCostBreakDown['adultDoubleRoomCost']['totalCost'] = $roomCostBreakDown['adultDoubleRoomCost']['cost'] + $roomCostBreakDown['adultDoubleRoomCost']['packageCost'];
            }
            if ($adultTripleRoomCost > 0) {
                $roomCostBreakDown['adultTripleRoomCost']['cost'] = round($adultTripleRoomCost, 2);
                $roomCostBreakDown['adultTripleRoomCost']['packageCost'] = $prices->$adultPackageChargesColumn + $adultSupplementCost;
                $roomCostBreakDown['adultTripleRoomCost']['totalCost'] = $roomCostBreakDown['adultTripleRoomCost']['cost'] + $roomCostBreakDown['adultTripleRoomCost']['packageCost'];
            }

            $quotationData['RoomTypes'] = $RoomType;

            $totCost = 0;
            if(isset($roomCostBreakDown['adultSingleRoomCost'])){
                $totCost += $roomCostBreakDown['adultSingleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][1] * 1);
            }
            if(isset($roomCostBreakDown['adultDoubleRoomCost'])){
                $totCost += $roomCostBreakDown['adultDoubleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][2] * 2);
            }
            if(isset($roomCostBreakDown['adultTripleRoomCost'])){
                $totCost += $roomCostBreakDown['adultTripleRoomCost']['totalCost']  * ($quotationData['RoomTypes'][3] * 3);
            }

            if($quotationData['pax']['cwb'] > 0){
                $totCost += ($prices->child_rate + $cwbSupplementCost + $cwbPpHotelCost) * $quotationData['pax']['cwb'];
            }

            if($quotationData['pax']['cnb'] > 0){
                $totCost += ($prices->child_w_out_bed_rate + $cnbSupplementCost + $cnbPpHotelCost) * $quotationData['pax']['cnb'];
            }

            $finalCostArray = array(
                'adultCostBreakDown' => $roomCostBreakDown,
                'cwbHotelPpCost' => $cwbPpHotelCost,
                'cwbPackagePpCost' => $prices->child_rate + $cwbSupplementCost,
                'cwbPpTotalCost' => $prices->child_rate + $cwbSupplementCost + $cwbPpHotelCost,
                'cnbHotelPpCost' => $cnbPpHotelCost,
                'cnbPackagePpCost' => $prices->child_w_out_bed_rate + $cnbSupplementCost ,
                'cnbPpTotalCost' => $prices->child_w_out_bed_rate + $cnbSupplementCost + $cnbPpHotelCost,
                'grandCost' => $totCost
            );


            $quotationData['packageCost'] = $packageCostArray;
            $quotationData['finalCost'] = $finalCostArray;
            $quotationData['totalHotelCost'] = $totalHotelCost;

        }

        if ($type == "new") {
            return view('fd_packages.fd_packages_quotation', compact('quotationData'));
        } else {
            return view('fd_packages.edit.fd_packages_edit_quotation', compact('quotationData'));
        }
    }

    public function viewQuotation(Request $request)
    {
        $referenceNo = $request->fdQuotationReferenceNo;
        $quotationNo = $request->fdQuotationNo;
        $statusId = $request->statusId;
        $quotationData = $this->getQuotation($referenceNo, $quotationNo, $statusId);
        return view('fd_packages.edit.fd_packages_edit_quotation', compact('quotationData'));
    }

    public function printQuotation(Request $request)
    {
        $referenceNo = $request->fdQuotationReferenceNo;
        $quotationNo = $request->fdQuotationNo;
        $statusId = $request->statusId;
        $quotationData = $this->getQuotation($referenceNo, $quotationNo, $statusId);
        $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];


        $FinalHTML = "<script>window.print();</script>";

        $type = "tour_confirmation";
        $FinalHTML .= View::make('fd_packages.voucher.tour-confirmation-voucher', compact('quotationData', 'type'))->render();
        $FinalHTML .= '<div style="page-break-after: always;"></div>';

        $type = "tour_confirmation_voucher";
        $FinalHTML .= View::make('fd_packages.voucher.tour-confirmation-voucher', compact('quotationData', 'type'))->render();
        $FinalHTML .= '<div style="page-break-after: always;"></div>';

        if (!($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {

            $pnlData = $this->lostProfit($request);
            $FinalHTML .= $pnlData;
            $FinalHTML .= '<div style="page-break-after: always;"></div>';

            $hotelVouchers = $this->generateHotelVoutures($quotationData);
            $FinalHTML .= $hotelVouchers['VoucherHtml'];
            $FinalHTML .= '<div style="page-break-after: always;"></div>';

        }

        if (env("APP_DEBUG"))
            $CSS = file_get_contents(public_path('assets/css/apple.css'));
        else
            $CSS = file_get_contents(public_path('assets/css/apple.min.css'));
        $FinalHTML = View::make('quotation.email.main', ['QuotationHTML' => $FinalHTML, "CSS" => $CSS])->render();

        return $FinalHTML;
    }

    public function cancelQuotation(Request $request)
    {
        $referenceNo = $request->fdQuotationReferenceNo;
        $quotationNo = $request->fdQuotationNo;
        $FdPackagesQuotationObj = apple_fd_packages_quotation::where('id', '=', $referenceNo)
            ->where('quotation_no', '=', $quotationNo)->first();
        if ($FdPackagesQuotationObj->status == 3) {
            $Status['status'] = false;
            $messages['msgs'][0] = "Quotation Is Already Cancelled";
            $Status['error'] = $messages;
            return $Status;
        }


        $quotationData = $this->getQuotation($referenceNo, $quotationNo);
        $quotationData['cancelHotelData'] = $quotationData['hotelData'];
        return view('fd_packages.cancel.fd_packages_cancel_quotation', compact('quotationData'));
    }

    function sendTourConfirmationVouchers($quotationData)
    {

        $FinalHTML = "";
        $emails = array();
        array_push($emails, "<EMAIL>"); // Dilrukshi (Online Travel Agent)
        array_push($emails, "<EMAIL>"); // Nipuni  (Online Travel Agent)
        $fileHandlerEmail = $quotationData['confirmData']['fileHandler'];

        $type = "tour_confirmation";
        $FinalHTML .= View::make('fd_packages.voucher.tour-confirmation-voucher', compact('quotationData', 'type'))->render();
        if ($quotationData['confirmData']['statusId'] == 2) {
            $subject = "TOUR CONFIRMATION : #" . $quotationData['quotationInfo'][0];
        } else {
            $subject = "TOUR CANCELLATION : #" . $quotationData['quotationInfo'][0];
        }
        Mail::to($emails)->cc($fileHandlerEmail)->send(new fdTourConfrimation($emails, $subject, $FinalHTML));

        if ($quotationData['confirmData']['statusId'] == 2) {
            $FinalHTML = "";
            $type = "tour_confirmation_voucher";
            $subject = "TOUR CONFIRMATION VOUCHER : #" . $quotationData['quotationInfo'][0];
            $FinalHTML .= View::make('fd_packages.voucher.tour-confirmation-voucher', compact('quotationData', 'type'))->render();
            Mail::to($emails)->cc($fileHandlerEmail)->send(new fdTourConfrimation($emails, $subject, $FinalHTML));
        }
    }

    function sendNotification($quotationData, $Status)
    {
        $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
        if (($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {
            $userObj = Auth::user();
            $notificationInfo = array(
                'company' => $userObj->Profile->first()->company()->first()->name,
                'agentEmail' => $userObj->email,
                'agentName' => $userObj->name,
                'ArrivalDate' => Carbon::parse($quotationData['arrivalDateFormat'])->toFormattedDateString(),
            );
            $FinalHTML = View::make('fd_packages.notifications.tour-notification', compact('quotationData', 'notificationInfo', 'Status'))->render();
            $subject = "Notification";
            $emails = array();
            array_push($emails, "<EMAIL>"); // Dilrukshi (Online Travel Agent)
            array_push($emails, "<EMAIL>"); // Nipuni  (Online Travel Agent)
            Mail::to($emails)->send(new fdQuotationNotification($emails, $subject, $FinalHTML));
        }
    }

    function resendEmails(Request $request)
    {

        $Validator = Validator::make($request->all(), [
            'quotation_no' => 'required|exists:apple_fd_packages_quotations,quotation_no',
            'type' => 'required',
            'to_emails' => 'required'
        ]);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()], 500);
        }
        $referenceNo = $request->reference_no;
        $quotationNo = $request->quotation_no;
        $state = $request->state;

        $quotationData = $this->getQuotation($referenceNo, $quotationNo, $state);
        $voucherData = $this->generateHotelVoutures($quotationData);
        $hotelIds = $request->hotel_id;
//        $EmailStatus = Event::fire(new QuotationEmail($request->all()));
        if ($hotelIds[0] != 0) {
            $hotelIds = $request->hotel_id;
            $existingHotel = array();
            foreach ($voucherData['VoucherList'] as $hotel) {
                foreach ($hotel as $HotelID => $HTML) {
                    array_push($existingHotel, $HotelID);
                }
            }
            $arrayDiff = array_diff($existingHotel, $hotelIds);

            if (count($arrayDiff) > 0) {
                foreach ($voucherData['VoucherList'] as $key => $hotel) {
                    foreach ($hotel as $HotelID => $HTML) {
                        foreach ($arrayDiff as $diff) {
                            if ($HotelID == $diff) {
                                unset($voucherData['VoucherList'][$key]);
                                break;
                            }
                        }
                    }
                }
            }
        }

        foreach ($voucherData['VoucherList'] as $HTMLArray) {
            foreach ($HTMLArray as $HotelID => $HTML) {
                $emails = array();
                if(!empty($HTML['emails'])) {
                    $emails = explode(",", $HTML['emails']);
                }


                $fileHandlerEmail = $quotationData['confirmData']['fileHandler'];
                array_push($emails, "<EMAIL>");
                array_push($emails, "<EMAIL>");
                array_push($emails, "<EMAIL>");
                array_push($emails, "<EMAIL>"); // Dilrukshi (Online Travel Agent)
                array_push($emails, "<EMAIL>"); // Nipuni  (Online Travel Agent)

                $subject = "Hotel Voucher : " . $HTML['hotelName'] . " / " . $quotationNo . "#" . $quotationData['confirmData']['statusCode'];
                $hotelVoucherMailObj = Mail::to($emails);
                $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
                if (!($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {
                    $hotelVoucherMailObj->cc($fileHandlerEmail);
                }
                $hotelVoucherMailObj->send(new fdHotelVouchers($emails, $subject, $HTML['html'],$quotationData));


                if ($HTML['type'] == "reservation") {
                    $hotel_rates = apple_fd_packages_quotation_hotel_rates::where('hotel_id', '=', $HTML['hotelId'])
                        ->where('reference_id', '=', $referenceNo)->first();
                    $hotelRates = apple_fd_packages_quotation_hotel_rates::find($hotel_rates->id);
                    $hotelRates->email_status = 1;
                    $hotelRates->save();

                    if ($state != "cancel") {
                        $hotelVouchers = apple_fd_packages_quotation_hotel_vouchers_info::where('hotel_id', '=', $HTML['hotelId'])
                            ->where('reference_id', '=', $referenceNo)->first();
                        $hotelVoucherInfo = apple_fd_packages_quotation_hotel_vouchers_info::find($hotelVouchers->id);
                        $hotelVoucherInfo->email_status = 1;
                        $hotelVoucherInfo->save();
                    }
                } else {
                    $cancelledHotels = apple_fd_packages_quotation_cancelled_hotels::where('hotel_id', '=', $HTML['hotelId'])
                        ->where('reference_id', '=', $referenceNo)->first();
                    if ($cancelledHotels) {
                        $cancell = apple_fd_packages_quotation_cancelled_hotels::find($cancelledHotels->id);
                        $cancell->email_status = 1;
                        $cancell->save();
                    }
                }
            }
        }


        $EmailStatus = true;

        if ($EmailStatus)
            return View::make("system.success.success-bootstrap", ["Message" => "Email has been sent!"])->render();
        else
            return View::make("system.error.error-bootstrap", ["Message" => "Something wend wrong!"])->render();
    }

    public function resendEmailBox(Request $request)
    {
        $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
        $referenceNo = $request->referenceNo;
        $quatationNo = $request->quatationNo;
        $selectArray = array();
        $selectArray = [
            'quote' => 'Quotation',
            'tour_confirmation' => 'Tour Confirmation',
            'tour_confirmation_voucher' => 'Tour Confirmation Voucher'
        ];

        if (!($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {
            $selectArray['hotel_vouchers'] = 'Hotel Vouchers';
        }
        $quotationData = $this->getQuotation($referenceNo, $quatationNo);
        return view('fd_packages.email.email_model', compact('referenceNo', 'selectArray', 'quatationNo', 'quotationData'));
    }

    public function emailsResend(Request $request)
    {
        $referenceNo = $request->fd_reference_no;
        $quotationNo = $request->fd_quotation_no;
        $emailType = $request->fd_email_type;
        $toEmails = $request->to_emails;
        $ccEmails = $request->cc_emails;
        $bccEmails = $request->bcc_emails;

        $Validator = Validator::make($request->all(), [
            'to_emails' => 'required'
        ]);


        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()], 500);
        }

        $quotationData = $this->getQuotation($referenceNo, $quotationNo);
        if ($emailType == "quote") {
            $FinalHTML = View::make('fd_packages.email.fd_quotation', compact('quotationData'))->render();
            $toEmails = explode(",", $toEmails);
            $tcEmailObj = Mail::to($toEmails);
            $subject = "Quotation reference: #" . $quotationData['quotationInfo'][0];
            if (!empty($ccEmails)) {
                $ccEmails = explode(",", $ccEmails);
                $tcEmailObj->cc($ccEmails);
            }
            if (!empty($bccEmails)) {
                $bccEmails = explode(",", $bccEmails);
                $tcEmailObj->bcc($bccEmails);
            }
            $tcEmailObj->send(new fdTourConfrimation($toEmails, $subject, $FinalHTML));


        } else if ($emailType == "tour_confirmation") {


            $type = "tour_confirmation";
            $FinalHTML = View::make('fd_packages.voucher.tour-confirmation-voucher', compact('quotationData', 'type'))->render();
            if ($quotationData['confirmData']['statusId'] == 2) {
                $subject = "TOUR CONFIRMATION : #" . $quotationData['quotationInfo'][0];
            } else {
                $subject = "TOUR CANCELLATION : #" . $quotationData['quotationInfo'][0];
            }
            $toEmails = explode(",", $toEmails);
            $tcEmailObj = Mail::to($toEmails);

            if (!empty($ccEmails)) {
                $ccEmails = explode(",", $ccEmails);
                $tcEmailObj->cc($ccEmails);
            }
            if (!empty($bccEmails)) {
                $bccEmails = explode(",", $bccEmails);
                $tcEmailObj->bcc($bccEmails);
            }
            $tcEmailObj->send(new fdTourConfrimation($toEmails, $subject, $FinalHTML));

        } else if ($emailType == "tour_confirmation_voucher") {


            $type = "tour_confirmation_voucher";
            $FinalHTML = View::make('fd_packages.voucher.tour-confirmation-voucher', compact('quotationData', 'type'))->render();
            if ($quotationData['confirmData']['statusId'] == 2) {
                $subject = "TOUR CONFIRMATION Voucher: #" . $quotationData['quotationInfo'][0];
            } else {
                $subject = "TOUR CANCELLATION Voucher: #" . $quotationData['quotationInfo'][0];
            }
            $toEmails = explode(",", $toEmails);
            $tcEmailObj = Mail::to($toEmails);

            if (!empty($ccEmails)) {
                $ccEmails = explode(",", $ccEmails);
                $tcEmailObj->cc($ccEmails);
            }
            if (!empty($bccEmails)) {
                $bccEmails = explode(",", $bccEmails);
                $tcEmailObj->bcc($bccEmails);
            }
            $tcEmailObj->send(new fdTourConfrimation($toEmails, $subject, $FinalHTML));

        } else {
            unset($quotationData['cancelHotelData']);
            $hotelVoucherType = $request->voucher_type;

            if (isset($request->send_individual)) {
                $hotelIds = $request->fd_email_resend_hotel_id;
                $existingHotel = array();
                foreach ($quotationData['hotelData'] as $hotel) {
                    array_push($existingHotel, $hotel['hotelId']);
                }
                $arrayDiff = array_diff($existingHotel, $hotelIds);

                if (count($arrayDiff) > 0) {
                    foreach ($quotationData['hotelData'] as $key => $hotel) {
                        foreach ($arrayDiff as $diff) {
                            if ($hotel['hotelId'] == $diff) {
                                unset($quotationData['hotelData'][$key]);
                                break;
                            }
                        }
                    }
                }


            }
            if (isset($request->no_hotel_voucher)) {
                $this->processVoucherType($quotationData, $hotelVoucherType, "no", $toEmails, $ccEmails, $bccEmails);
            } else {
                $this->processVoucherType($quotationData, $hotelVoucherType, "yes", $toEmails, $ccEmails, $bccEmails);
            }

        }
        return View::make("system.success.success-bootstrap", ["Message" => "Email has been sent!"])->render();
    }

    function processVoucherType($quotationData, $hotelVoucherType, $noHotelEmail, $toEmails, $ccEmails, $bccEmails)
    {
        switch ($hotelVoucherType) {
            case "reservation" :
                $voucher = $this->getHotelVouchers($quotationData, false, 1);
                $this->processVoucherHtml($voucher, $quotationData, $noHotelEmail, $toEmails, $ccEmails, $bccEmails);
                break;
            case  "amendment"  :
                $voucher = $this->getHotelVouchers($quotationData, false, 2);
                $this->processVoucherHtml($voucher, $quotationData, $noHotelEmail, $toEmails, $ccEmails, $bccEmails);
                break;
            case "cancel" :
                $voucher = $this->getHotelVouchers($quotationData, false, 3);
                $this->processVoucherHtml($voucher, $quotationData, $noHotelEmail, $toEmails, $ccEmails, $bccEmails);
                break;
            default :
                $voucher = $this->getHotelVouchers($quotationData, false, 1);
                $this->processVoucherHtml($voucher, $quotationData, $noHotelEmail, $toEmails, $ccEmails, $bccEmails);
                break;
        }
    }


    function processVoucherHtml($data, $quotationData, $noHotelEmail, $toEmails, $ccEmails, $bccEmails)
    {
        $VoucherHtml = "";
        foreach ($data as $HTMLArray) {
            foreach ($HTMLArray as $HotelID => $HTML) {
                $VoucherHtml .= $HTML['html'];
                if ($noHotelEmail == "yes") {

                    if(!empty($HTML['emails'])) {
                        $emails = explode(",", $HTML['emails']);
                    }
                } else {
                    $emails = array();

                }

                $fileHandlerEmail = $quotationData['confirmData']['fileHandler'];

//                if (!empty($toEmails)) {
//                    $toEmails = explode(",", $toEmails);
//                    foreach ($toEmails as $mail) {
//                        array_push($emails, $mail);
//                    }
//                }

                array_push($emails, "<EMAIL>");
                array_push($emails, "<EMAIL>");
                array_push($emails, "<EMAIL>");
                array_push($emails, "<EMAIL>"); // Dilrukshi (Online Travel Agent)
                array_push($emails, "<EMAIL>"); // Nipuni  (Online Travel Agent)


                $subject = "Hotel Voucher : " . $HTML['hotelName'] . " / " . $quotationData['confirmData']['quotationNo'] . "#" . $quotationData['confirmData']['statusCode'];
                $hotelVoucherMailObj = Mail::to($emails);
                $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
                if (!($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {
                    $hotelVoucherMailObj->cc($fileHandlerEmail);
                }

//                if (!empty($ccEmails)) {
//                    $ccEmails = explode(",", $ccEmails);
//                    $hotelVoucherMailObj->cc($ccEmails);
//                }
//                if (!empty($bccEmails)) {
//                    $bccEmails = explode(",", $bccEmails);
//                    $hotelVoucherMailObj->bcc($bccEmails);
//                }
                $hotelVoucherMailObj->send(new fdHotelVouchers($emails, $subject, $HTML['html'],$quotationData));
            }
        }
    }

    public function resendEmailsPreview(Request $request)
    {

        $referenceNo = $request->fd_reference_no;
        $quotationNo = $request->fd_quotation_no;
        $emailType = $request->fd_email_type;
        $toEmails = $request->to_emails;
        $ccEmails = $request->cc_emails;
        $bccEmails = $request->bcc_emails;

        $Validator = Validator::make($request->all(), [
            'to_emails' => 'required'
        ]);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response()->view("system.error.error-bootstrap", ['Message' => $messages->toArray()], 500);
        }
        $FinalHTML = "";
        $quotationData = $this->getQuotation($referenceNo, $quotationNo);
        if ($emailType == "quote") {
            $FinalHTML = View::make('fd_packages.email.fd_quotation', compact('quotationData'))->render();
        }   else if ($emailType == "tour_confirmation") {
            $type = "tour_confirmation";
            $FinalHTML = View::make('fd_packages.voucher.tour-confirmation-voucher', compact('quotationData', 'type'))->render();
        } else if ($emailType == "tour_confirmation_voucher") {
            $type = "tour_confirmation_voucher";
            $FinalHTML = View::make('fd_packages.voucher.tour-confirmation-voucher', compact('quotationData', 'type'))->render();
        }  else {
            unset($quotationData['cancelHotelData']);
            $hotelVoucherType = $request->voucher_type;

                if (isset($request->send_individual)) {
                    $hotelIds = $request->fd_email_resend_hotel_id;
                    $existingHotel = array();
                    foreach ($quotationData['hotelData'] as $hotel) {
                        array_push($existingHotel, $hotel['hotelId']);
                    }
                    $arrayDiff = array_diff($existingHotel, $hotelIds);

                    if (count($arrayDiff) > 0) {
                        foreach ($quotationData['hotelData'] as $key => $hotel) {
                            foreach ($arrayDiff as $diff) {
                                if ($hotel['hotelId'] == $diff) {
                                    unset($quotationData['hotelData'][$key]);
                                    break;
                                }
                            }
                        }
                    }
                }
            if (isset($request->no_hotel_voucher)) {
                $FinalHTML =  $this->processPreviewEmail($quotationData, $hotelVoucherType);
            } else {
                $FinalHTML = $this->processPreviewEmail($quotationData, $hotelVoucherType);
            }
        }
        return $FinalHTML;
    }

    function processPreviewEmail($quotationData,$hotelVoucherType){
        $voucher = "";
        switch ($hotelVoucherType) {
            case "reservation" :
                $voucher = $this->getHotelVouchers($quotationData, false, 1);
                break;
            case  "amendment"  :
                $voucher = $this->getHotelVouchers($quotationData, false, 2);
                break;
            case "cancel" :
                $voucher = $this->getHotelVouchers($quotationData, false, 3);
                break;
            default :
                $voucher = $this->getHotelVouchers($quotationData, false, 1);
                break;
        }
        $VoucherHtml = "";
        foreach ($voucher as $HTMLArray) {
            foreach ($HTMLArray as $HotelID => $HTML) {
                $VoucherHtml .= $HTML['html'];
            }
        }
        return $VoucherHtml;
    }

    public function lostProfit(Request $request){
        $referenceNo = $request->fdQuotationReferenceNo;
        $quotationNo = $request->fdQuotationNo;

        $FdPackagesQuotationObj = apple_fd_packages_quotation::where('id', '=', $referenceNo)->where('quotation_no', '=', $quotationNo)
            ->with('findFdPackage', 'findFdCost', 'findHotelRates', 'findHotelVoucher', 'findPax', 'findStatus')->first();

        $fdPackage = $FdPackagesQuotationObj->findFdPackage;
        $fdPackageTransportCostObj = $fdPackage->findTransportCost;
        $fdPackageMealCostObj = $fdPackage->findMealCost;
        $fdPackagePriceObj = $fdPackage->findPackagePrice{0};


        $quotationData = $this->getQuotation($referenceNo, $quotationNo);
        $quotationNoOfpax = $quotationData['pax']['adult'] + $quotationData['pax']['cwb'] + $quotationData['pax']['cnb'];

        $noOfAdults = $quotationData['pax']['adult'];
        $noOfCwb = $quotationData['pax']['cwb'];
        $noOfCnb = $quotationData['pax']['cnb'];

        $transportCostType = "";
        if ($noOfAdults <= 3) {
            $transportCostType = 1;
        } else if ($noOfAdults > 3 and $noOfAdults <= 6) {
            $transportCostType = 2;
        } else if ($noOfAdults > 6 and $noOfAdults <= 10) {
            $transportCostType = 3;
        } else if ($noOfAdults > 10 and $noOfAdults <= 15) {
            $transportCostType = 4;
        } else {
            $transportCostType = 4;
        }

        $fdTransportType = collect($fdPackageTransportCostObj);
        $fdTransportType = $fdTransportType->firstWhere('type','=',$transportCostType);
        $transportCostArray = array(
            'distance' => $fdTransportType->distance,
            'distance_price' => currency($fdTransportType->distance_price,"LKR","USD",true),
            'distance_price_total' => currency($fdTransportType->distance* $fdTransportType->distance_price,"LKR","USD",true) ,
            'bata' => currency($fdTransportType->bata,"LKR","USD",true),
            'days' => $fdTransportType->no_of_days,
            'bata_total' => currency($fdTransportType->bata * $fdTransportType->no_of_days ,"LKR","USD",true),
            'paging' => currency($fdTransportType->paging,"LKR","USD",true),
            'guide_fee' => currency($fdTransportType->guide_fee ?? 0,"LKR","USD",true),
            'driver_accmomodation_charges' => currency($fdTransportType->driver_accmomodation_charges ?? 0,"LKR","USD",true),
            'total' => currency($fdTransportType->grand_total,"LKR","USD",true),
        );

        $attractionCostArray = array();
        $mealCostArray = array();
        $i = 0;
        foreach ($quotationData['itineraries'] as $itineraryKey => $itineraryValue){

            foreach ($itineraryValue['dates'] as $dateKey => $dateValue){
                $totalMealCostDay = 0;
                if(isset($dateValue['data'])){
                    foreach ($dateValue['data'] as $dataKey => $dataValue){
                        array_push($attractionCostArray,$dataValue);
                    }
                }
                $mealCostArray[$i]['day'] = $dateValue['day'];
                $mealCostArray[$i]['bf_adult_meal_rate'] = $fdPackageMealCostObj->bf_adult_meal_rate;
                $mealCostArray[$i]['bf_child_meal_rate'] = $fdPackageMealCostObj->bf_child_meal_rate;
                $mealCostArray[$i]['lunch_adult_meal_rate'] = $fdPackageMealCostObj->lunch_adult_meal_rate;
                $mealCostArray[$i]['lunch_child_meal_rate'] = $fdPackageMealCostObj->lunch_child_meal_rate;
                $mealCostArray[$i]['dinner_adult_meal_rate'] = $fdPackageMealCostObj->dinner_adult_meal_rate;
                $mealCostArray[$i]['dinner_child_meal_rate'] = $fdPackageMealCostObj->dinner_child_meal_rate;

                $totalMealCostDay += ($fdPackageMealCostObj->bf_adult_meal_rate * $noOfAdults) + ($fdPackageMealCostObj->lunch_adult_meal_rate * $noOfAdults ) + ($fdPackageMealCostObj->dinner_adult_meal_rate * $noOfAdults);
                if($noOfCwb > 0){
                    $totalMealCostDay += ($fdPackageMealCostObj->bf_child_meal_rate * $noOfCwb) + ($fdPackageMealCostObj->lunch_child_meal_rate * $noOfCwb ) + ($fdPackageMealCostObj->dinner_child_meal_rate * $noOfCwb);
                }
                if($noOfCnb > 0){
                    $totalMealCostDay += ($fdPackageMealCostObj->bf_child_meal_rate * $noOfCnb) + ($fdPackageMealCostObj->lunch_child_meal_rate * $noOfCnb ) + ($fdPackageMealCostObj->dinner_child_meal_rate * $noOfCnb);
                }
                $mealCostArray[$i]['total'] = number_format($totalMealCostDay,2);
                $i++;
            }
        }

        $mealCostArray['grandTotal'] = number_format(count($mealCostArray) * str_replace(",","",$mealCostArray[0]['total']),2);

        $quotationData['attractionCost'] = $attractionCostArray;
        $quotationData['mealCost'] = $mealCostArray;
        $quotationData['transportCost'] = $transportCostArray;

        // profit
        $adultProfit = $fdPackagePriceObj->adult_profit * $noOfAdults;
        $cwbProfit = $fdPackagePriceObj->child_profit * $noOfCwb;
        $cnbProfit = $fdPackagePriceObj->child_w_out_profit * $noOfCwb;
        $totalProfit = $adultProfit + $cwbProfit + $cnbProfit;
        $costWithoutMarkup = $quotationData['finalCost']['grandCost'] - $totalProfit;

        $quotationData['quotationNoOfpax'] = $quotationNoOfpax;
        $quotationData['costWithoutMarkup'] = $costWithoutMarkup;
        $quotationData['totalProfit'] = $totalProfit;



        return  View::make('fd_packages.lost-profit.lost-profit',compact('quotationData'))->render();
    }
    function convertCurrency($quotationData,$currency){

        $existingCurrency = $quotationData['QuotationCurrency'];

        if($existingCurrency == $currency){
            return $quotationData;
        } else {
            $quotationData['packageCost']['adultCost'] = currency(str_replace(",","",$quotationData['packageCost']['adultCost']),$existingCurrency,$currency,false);
            $quotationData['packageCost']['cwb'] = currency(str_replace(",","",$quotationData['packageCost']['cwb']),$existingCurrency,$currency,false);
            $quotationData['packageCost']['cnb'] = currency(str_replace(",","",$quotationData['packageCost']['cnb']),$existingCurrency,$currency,false);
            // Hotel Data
            foreach ($quotationData['hotelData'] as $hotelKey => $hotelValue) {
                if(isset($hotelValue['cost']['adult_rate_single_room'])) {
                    $quotationData['hotelData'][$hotelKey]['cost']['adult_rate_single_room']['adultCost'] = currency(str_replace(",","",$hotelValue['cost']['adult_rate_single_room']['adultCost']),$existingCurrency,$currency,false);
                    $quotationData['hotelData'][$hotelKey]['cost']['adult_rate_single_room']['adultCostPP'] = currency(str_replace(",","",$hotelValue['cost']['adult_rate_single_room']['adultCostPP']),$existingCurrency,$currency,false);
                }
                if(isset($hotelValue['cost']['adult_rate_double_room'])) {
                    $quotationData['hotelData'][$hotelKey]['cost']['adult_rate_double_room']['adultCost'] = currency(str_replace(",","",$hotelValue['cost']['adult_rate_double_room']['adultCost']),$existingCurrency,$currency,false);
                    $quotationData['hotelData'][$hotelKey]['cost']['adult_rate_double_room']['adultCostPP'] = currency(str_replace(",","",$hotelValue['cost']['adult_rate_double_room']['adultCostPP']),$existingCurrency,$currency,false);
                }
                if(isset($hotelValue['cost']['adult_rate_triple_room'])) {
                    $quotationData['hotelData'][$hotelKey]['cost']['adult_rate_triple_room']['adultCost'] = currency(str_replace(",","",$hotelValue['cost']['adult_rate_triple_room']['adultCost']),$existingCurrency,$currency,false);
                    $quotationData['hotelData'][$hotelKey]['cost']['adult_rate_triple_room']['adultCostPP'] = currency(str_replace(",","",$hotelValue['cost']['adult_rate_triple_room']['adultCostPP']),$existingCurrency,$currency,false);
                }
                if(isset($hotelValue['cost']['cwb'])) {
                    $quotationData['hotelData'][$hotelKey]['cost']['cwb'] =  currency(str_replace(",","",$hotelValue['cost']['cwb']),$existingCurrency,$currency,false);
                }
                if(isset($hotelValue['cost']['cnb'])) {
                    $quotationData['hotelData'][$hotelKey]['cost']['cnb'] =  currency(str_replace(",","",$hotelValue['cost']['cnb']),$existingCurrency,$currency,false);
                }
                $quotationData['hotelData'][$hotelKey]['cost']['totalCost'] =  currency(str_replace(",","",$hotelValue['cost']['totalCost']),$existingCurrency,$currency,false);
            }
            // final Cost
            if(isset($quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost'])) {
                $quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost']['cost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost']['cost']),$existingCurrency,$currency,false);
                $quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost']['packageCost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost']['packageCost']),$existingCurrency,$currency,false);
                $quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost']['totalCost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultSingleRoomCost']['totalCost']),$existingCurrency,$currency,false);
            }

            if(isset($quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost'])) {
                $quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost']['cost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost']['cost']),$existingCurrency,$currency,false);
                $quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost']['packageCost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost']['packageCost']),$existingCurrency,$currency,false);
                $quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost']['totalCost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultDoubleRoomCost']['totalCost']),$existingCurrency,$currency,false);
            }
            if(isset($quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost'])) {
                $quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost']['cost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost']['cost']),$existingCurrency,$currency,false);
                $quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost']['packageCost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost']['packageCost']),$existingCurrency,$currency,false);
                $quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost']['totalCost'] = currency(str_replace(",","",$quotationData['finalCost']['adultCostBreakDown']['adultTripleRoomCost']['totalCost']),$existingCurrency,$currency,false);
            }

            $quotationData['finalCost']['cnbHotelPpCost'] = currency(str_replace(",","",$quotationData['finalCost']['cnbHotelPpCost']),$existingCurrency,$currency,false);
            $quotationData['finalCost']['cnbPackagePpCost'] = currency(str_replace(",","",$quotationData['finalCost']['cnbPackagePpCost']),$existingCurrency,$currency,false);
            $quotationData['finalCost']['cnbPpTotalCost'] = currency(str_replace(",","",$quotationData['finalCost']['cnbPpTotalCost']),$existingCurrency,$currency,false);
            $quotationData['finalCost']['cwbHotelPpCost'] = currency(str_replace(",","",$quotationData['finalCost']['cwbHotelPpCost']),$existingCurrency,$currency,false);
            $quotationData['finalCost']['cwbPackagePpCost'] = currency(str_replace(",","",$quotationData['finalCost']['cwbPackagePpCost']),$existingCurrency,$currency,false);
            $quotationData['finalCost']['cwbPpTotalCost'] = currency(str_replace(",","",$quotationData['finalCost']['cwbPpTotalCost']),$existingCurrency,$currency,false);
            $quotationData['finalCost']['grandCost'] = currency(str_replace(",","",$quotationData['finalCost']['grandCost']),$existingCurrency,$currency,false);


            $quotationData['totalHotelCost'] = currency(str_replace(",","",$quotationData['totalHotelCost']),$existingCurrency,$currency,false);
            $quotationData['totalAttractionCost'] = currency(str_replace(",","",$quotationData['totalAttractionCost']),$existingCurrency,$currency,false);

            if(isset($quotationData['attractionCost'])){
                foreach($quotationData['attractionCost'] as $attractionKey => $attractionValue){
                    $quotationData['attractionCost'][$attractionKey]['adult_rate'] = currency(str_replace(",","",$attractionValue['adult_rate']),$existingCurrency,$currency,false);
                    $quotationData['attractionCost'][$attractionKey]['cwb_rate'] = currency(str_replace(",","",$attractionValue['cwb_rate']),$existingCurrency,$currency,false);
                    $quotationData['attractionCost'][$attractionKey]['cnb_rate'] = currency(str_replace(",","",$attractionValue['cnb_rate']),$existingCurrency,$currency,false);
                    $quotationData['attractionCost'][$attractionKey]['totalCost'] = currency(str_replace(",","",$attractionValue['totalCost']),$existingCurrency,$currency,false);
                }
            }

            if(isset($quotationData['transportCost'])){
                $quotationData['transportCost']['distance_price'] = currency(str_replace(",","",$quotationData['transportCost']['distance_price']),$existingCurrency,$currency,false);
                $quotationData['transportCost']['distance_price_total'] = currency(str_replace(",","",$quotationData['transportCost']['distance_price_total']),$existingCurrency,$currency,false);
                $quotationData['transportCost']['bata'] = currency(str_replace(",","",$quotationData['transportCost']['bata']),$existingCurrency,$currency,false);
                $quotationData['transportCost']['bata_total'] = currency(str_replace(",","",$quotationData['transportCost']['bata_total']),$existingCurrency,$currency,false);
                $quotationData['transportCost']['paging'] = currency(str_replace(",","",$quotationData['transportCost']['paging']),$existingCurrency,$currency,false);
                $quotationData['transportCost']['total'] = currency(str_replace(",","",$quotationData['transportCost']['total']),$existingCurrency,$currency,false);
            }
            if(isset($quotationData['mealCost'])) {
                foreach ($quotationData['mealCost'] as $key => $value) {
                    if (isset($value['day'])) {
                        $quotationData['mealCost'][$key]['bf_adult_meal_rate'] = currency(str_replace(",","",$value['bf_adult_meal_rate']),$existingCurrency,$currency,false);
                        $quotationData['mealCost'][$key]['bf_child_meal_rate'] = currency(str_replace(",","",$value['bf_child_meal_rate']),$existingCurrency,$currency,false);
                        $quotationData['mealCost'][$key]['dinner_adult_meal_rate'] = currency(str_replace(",","",$value['dinner_adult_meal_rate']),$existingCurrency,$currency,false);
                        $quotationData['mealCost'][$key]['dinner_child_meal_rate'] = currency(str_replace(",","",$value['dinner_child_meal_rate']),$existingCurrency,$currency,false);
                        $quotationData['mealCost'][$key]['lunch_adult_meal_rate'] = currency(str_replace(",","",$value['lunch_adult_meal_rate']),$existingCurrency,$currency,false);
                        $quotationData['mealCost'][$key]['lunch_child_meal_rate'] = currency(str_replace(",","",$value['lunch_child_meal_rate']),$existingCurrency,$currency,false);
                        $quotationData['mealCost'][$key]['total'] = currency(str_replace(",","",$value['total']),$existingCurrency,$currency,false);
                    } else {
                        $quotationData['mealCost']['grandTotal'] = currency(str_replace(",","",$quotationData['mealCost']['grandTotal']),$existingCurrency,$currency,false);
                    }
                }
            }
            if(isset($quotationData['costWithoutMarkup'])){
                $quotationData['costWithoutMarkup'] =  currency(str_replace(",","",$quotationData['costWithoutMarkup']),$existingCurrency,$currency,false);
            }
            if(isset($quotationData['totalProfit'])){
                $quotationData['totalProfit'] =  currency(str_replace(",","",$quotationData['totalProfit']),$existingCurrency,$currency,false);
            }
            $quotationData['QuotationCurrency'] = $currency;
            return $quotationData;
        }
    }
    public function lostProfitCurrency(Request $request){
        $currency =  $request->currency;
        $quotationData = json_decode($request->jsonData,true);
        $quotationData = $this->convertCurrency($quotationData,$currency);
        return  View::make('fd_packages.lost-profit.lost-profit',compact('quotationData'))->render();
    }
    function getIncludesTemplate($string){
        $st = '<li class="MsoNormal" style="color: rgb(102, 102, 102);"><span style="font-size:10.0pt;font-family:&quot;Open Sans&quot;;mso-fareast-font-family:
     &quot;Times New Roman&quot;;mso-bidi-font-family:Arial;letter-spacing:.1pt">'.$string.'
     <o:p></o:p></span></li>';
        return $st;
    }
}

