<?php

namespace App\Http\Controllers\Message;


use App\Events\MessageEvent;
use App\Events\MessageWasSent;
use App\Events\UserEvent;
use App\Http\Controllers\Controller;
use Chat;
use Mu<PERSON>za\Chat\Conversations\ConversationUser;

/**
 * Class Message
 * @package App\Http\Controllers\Message
 */
class Message extends Controller
{

    /**
     * @param $Type
     * @return array|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getTemplate($Type)
    {
        $data = request();

        switch ($Type) {
            case "layout":
                return view('message.layout');
            case "chat-people-item":
                return view('message.chat-list-people');
            case "chat-message":
                return view('message.chat-message');
            case "chat-box":
                return view('message.chat.chat-box');
        }
        return [];
    }

    /**
     * @return array
     */
    function getConversations()
    {

        $ConversationList = Chat::conversations()->for(\Auth::user())->limit(10);
        $ConversationList = $ConversationList->get();
        $data = [];


        if ($ConversationList) {
            foreach ($ConversationList->all() as $ConversationItem) {
                $current_data['id'] = $ConversationItem->id;
                $current_data['title'] = $ConversationItem->data['title'] ?? $ConversationItem->users->implode('name', ', ');
                $current_data['user'] = $ConversationItem->users->where('id', '!=', \Auth::user()->id);

                $data[] = $current_data;
            }
        }

        return $data;

    }

    /**
     * @param $id
     * @param int $page
     * @return array|\Illuminate\Contracts\Routing\ResponseFactory|\Symfony\Component\HttpFoundation\Response
     */
    function getConversation($id, $page = 1)
    {
        $Conversation = Chat::conversation($id);

        if(!$Conversation->users->where('id',\Auth::id())->first())
            return response([],400);

        $ConversationList = $Conversation->getMessages(\Auth::user(), 25, $page, 'desc');

        $data['user'] = \Auth::user()->id;
        $data['data'] = collect($ConversationList->all())->map(function ($el) {
            $user_id = \Auth::user()->id;

            $el['sent_by_me'] = ($user_id == $el['user_id']);
            $el['timestamp'] = $el['created_at']->diffForHumans();
            $el['group_time_minute'] = strtotime($el['created_at']->format('Y-m-d H:i:00'));

            return $el;
        });

        $Details = $Conversation->toArray();
        $Details['data']['title'] = $Details['data']['title'] ?? ($Conversation->users->where('id', '!=', \Auth::user()->id)->implode('name', ', '));


        return [
            'details' => $Details,
            'messages' => $data
        ];


    }

    /**
     * @return array|\Illuminate\Http\JsonResponse
     */
    function send()
    {
        $validator = \Validator::make(request()->all(), [
            'conversation_id' => 'required',
            'body' => 'required',
            'message_random_key' => 'required',
        ]);

        if ($validator->fails()) {
            return \Response::json($validator->messages(), 504);
        }

        $user_id = \Auth::user()->id;

        $conversation = Chat::conversation(request('conversation_id'));
        Chat::message(request('body'))
            ->from(\Auth::user()->id)
            ->to($conversation)
            ->send();


        #get the message ###########################
        $message = Chat::conversation(request('conversation_id'))->getMessages(\Auth::user(), 1, 1, 'desc')->all()[0] ?? false;

        if ($message) {
            $message['sent_by_me'] = ($user_id == $message['user_id']);
            $message['timestamp'] = $message['created_at']->diffForHumans();
            $message['group_time_minute'] = strtotime($message['created_at']->format('Y-m-d H:i:00'));
        }
        ##############################################
        Chat::conversations($conversation)->for(\Auth::user())->readAll();

        //broadcast
        foreach($conversation->users->where('id', '!=', \Auth::user()->id) as  $User){
            broadcast(new UserEvent($User->id,['type' => 'message_received', 'cnv_id' => request('conversation_id'), 'msg_id' => $message->id]))->toOthers();
        }


        return ['status' => 'sent', "message" => $message, 'random_key' => request('message_random_key')];
    }

    /**
     * @return \Illuminate\Http\JsonResponse|\Musonza\Chat\Message
     */
    function get()
    {
        $validator = \Validator::make(request()->all(), [
            'conversation_id' => 'required',
            'message_id' => 'required'
        ]);

        if ($validator->fails()) {
            return \Response::json($validator->messages(), 504);
        }

        $data = Chat::messageById(request('message_id'));
        $user_id = \Auth::user()->id;

        $data['sent_by_me'] = ($user_id == $data['user_id']);
        $data['timestamp'] = $data['created_at']->diffForHumans();
        $data['group_time_minute'] = strtotime($data['created_at']->format('Y-m-d H:i:00'));

        return $data;
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    function typing()
    {
        $validator = \Validator::make(request()->all(), [
            'conversation_id' => 'required',
            'message' => 'required'
        ]);
        if ($validator->fails()) {
            return \Response::json($validator->messages(), 504);
        }
        broadcast(new MessageEvent(['type' => 'typing', 'cnv_id' => request('conversation_id'), 'message' => request('message')]))->toOthers();
    }

    /**
     * @return \Illuminate\Http\JsonResponse|\Musonza\Chat\Conversation
     */
    function create()
    {
        $validator = \Validator::make(request()->all(), [
            'name' => 'required|array',
            'name.*' => 'required|exists:users,id'
        ]);
        if ($validator->fails()) {
            return \Response::json($validator->messages(), 504);
        }

        $participants = request('name');

        if (!in_array(\Auth::id(), $participants))//if auth user is not selected
            array_push($participants, \Auth::id());


        $Available = ConversationUser::whereIn('user_id', $participants)
            ->groupBy('conversation_id')
            ->havingRaw("COUNT(*) = " . count($participants))
            ->first();


        if ($Available) {
            return Chat::Conversation($Available->conversation_id);
        }

        return Chat::createConversation($participants);
    }
}
