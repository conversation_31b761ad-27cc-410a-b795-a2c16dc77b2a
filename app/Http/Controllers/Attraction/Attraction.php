<?php
/**
 * Supun Praneeth 2018.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Http\Controllers\Attraction;

use App\Http\Controllers\Controller;
use App\Model\Image\Image;
use App\Model\Place\Attraction as AttractionModel;
use App\Model\Place\AttractionTime;
use App\Model\Place\CityTour;
use App\Model\Place\CitytourTime;
use App\Model\Place\Excursion;
use App\Model\Place\ExcursionTime;
use App\Model\Place\Place;
use App\Model\Profile\Company;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Model\Vehicle\Vehicle;
use Session;

/**
 * Class Attraction
 * @package App\Http\Controllers\Attraction
 */
class Attraction extends Controller
{


    public function index()
    {

    }


    /**
     * @param $lng
     * @param $lat
     * @return \Illuminate\Http\JsonResponse
     */
    function getMapAreaPlaces($lng, $lat)
    {


        $Attraction = new AttractionModel();
        $Attractions = $Attraction->getMapAreaPlaces($lng, $lat);


        if ($Attractions)
            return response()->json($Attractions);
        else
            return response()->json(['longitude' => $lat, 'latitude' => $lng]);

    }


    /**
     * @param $ID
     * @return \Illuminate\Http\JsonResponse
     */
    function getAttraction($ID)
    {


        $data['details'] = AttractionModel::find($ID);
        $data['rate'] = $data['details']->Rate->toArray();

        return response()->json($data);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getSightseeingLayout()
    {

        return view("element.attraction.attraction-list");
    }

    /**
     * @param $Type
     * @param $ID
     * @param Request $request
     * @return array
     */
    function getSightseeing($Type, $ID)
    {
        $isPaxChange = Session::get('system.isPaxChanged');
        $Image = new Image();
        $Market = \Session::get('quotation.market');
        $Quotation = \Session::get('quotation');

        if ($Market == 1 || $Market == 2 || $Market == 3)
            $Market = 1;
        else
            $Market = 2;

        switch ($Type) {
            case "attraction":
                $data_item = AttractionModel::find($ID);
                $data_array = $data_item->toArray();
                $data_array['image'] = $Image->getImage($ID, '2x', "attraction", 1, $data_array['name'])[0] ?? [];

                if((session()->has('quotation.attraction') && session()->has('quotation.rate.attraction.attraction'))) {
                    $data_array['rate'] = AttractionModel::getAttraction($Quotation)[$ID] ?? ["adult" => 0, "child" => 0];
                } else {
                    $attarction_rate = \App\Model\Place\Attraction::attractionCurrencyConvertSingle($ID, $Quotation);
                    $data_array['rate']['adult'] = (isset($attarction_rate[$ID]['adult'])) ? round($attarction_rate[$ID]['adult'],2) : 0;
                    $data_array['rate']['child'] = (isset($attarction_rate[$ID]['child'])) ? round($attarction_rate[$ID]['child'],2) : 0;
                }

                if(session()->has('quotation.attraction')) {
                    $data_array['attr_time'] = AttractionModel::getAttractionTime($Quotation, $ID) ?? ["start" => 0, "end" => 0];
                } else {
                    $data_array['attr_time']['start'] = 0;
                    $data_array['attr_time']['end'] = 0;
                }
                $data_array['markup'] = Company::find(\Auth::user()->Profile->company)->markup;

                return $data_array;
            case "city_tour":
                $data_item = CityTour::find($ID);
                $data_array = $data_item->toArray();
                $data_array['image'] = $Image->getImage($ID, '2x', "city_tour", 1, $data_array['name'])[0] ?? [];

                if(!$isPaxChange && (session()->has('quotation.city_tour') && session()->has('quotation.rate.attraction.city_tour'))) {
                    $data_array['rate'] = CityTour::getCityTour($Quotation)[$ID] ?? ["adult" => 0, "child" => 0];
                } else {

                    $attarction_rate = \App\Model\Place\CityTour::citytourCurrencyConvertSingle($ID, $Quotation);
                    $data_array['rate']['adult'] = (isset($attarction_rate[$ID]['adult'])) ? round($attarction_rate[$ID]['adult'],2) : 0;
                    $data_array['rate']['child'] = (isset($attarction_rate[$ID]['child'])) ? round($attarction_rate[$ID]['child'],2) : 0;
                }

                if(session()->has('quotation.city_tour')) {
                    $data_array['attr_time'] = CityTour::getCitytourTime($Quotation, $ID) ?? ["start" => 0, "end" => 0];
                } else {
                    $data_array['attr_time']['start'] = 0;
                    $data_array['attr_time']['end'] = 0;
                }

                $data_array['markup'] = Company::find(\Auth::user()->Profile->company)->markup;
                return $data_array;
            case "excursion":
                $data_item = Excursion::find($ID);
                $data_array = $data_item->toArray();
                $data_array['image'] = $Image->getImage($ID, '2x', "excursion", 1, $data_array['name'])[0] ?? [];
                if(!$isPaxChange && (session()->has('quotation.excursion') && session()->has('quotation.rate.attraction.excursion'))) {
                    $data_array['rate'] = Excursion::getExcursion($Quotation)[$ID] ?? ["adult" => 0, "child" => 0];
                } else {
                    $attarction_rate = \App\Model\Place\Excursion::excursionCurrencyConvertSingle($ID, $Quotation);
                    $data_array['rate']['adult'] = (isset($attarction_rate[$ID]['adult'])) ? round($attarction_rate[$ID]['adult'],2) : 0;
                    $data_array['rate']['child'] = (isset($attarction_rate[$ID]['child'])) ? round($attarction_rate[$ID]['child'],2) : 0;
                }

                if(session()->has('quotation.excursion')) {
                    $data_array['attr_time'] = Excursion::getExcursionTime($Quotation, $ID) ?? ["start" => 0, "end" => 0];
                } else {
                    $data_array['attr_time']['start'] = 0;
                    $data_array['attr_time']['end'] = 0;
                }

                $data_array['markup'] = Company::find(\Auth::user()->Profile->company)->markup;
                return $data_array;
            default:
                abort(500);
        }
    }

    /**
     * @param $Type
     * @param Request $request
     * @return array|\Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getSightseeingHTML($Type, Request $request)
    {
        switch ($Type) {
            case "attraction":
                return view("element.attraction.attraction-min");
            case "city_tour":
                return view("element.attraction.city-tour-min");
            case "excursion":
                return view("element.attraction.excursion-min");
            default:
                return [];
        }
    }

    /**
     * @param $PlaceID
     * @param Request $request
     * @return array
     */
    function getSightseeingList($PlaceID, Request $request)
    {
        $place = Place::find($PlaceID);
        if(isset($place)) {
            $country = $place->country;
        }

        $Vehicle = new Vehicle();
        #Vehicle
        $VehicleType = $Vehicle->getPaxToVehicle(\Session::get('quotation'), true, "first", $country);//get vehicle vehicle
        if (!$VehicleType)
            return false;
        $VehicleID = $VehicleType->ID;


        $Market = \Session::get('quotation.market');

        if ($Market == 1 || $Market == 3 || $Market == 4)
            $Market = 2;
        else
            $Market = 1;

        $Image = new Image();
        $time_array = [];
        $AttractionList = AttractionModel::where([['place', $PlaceID], ['disabled', 0]])->orderby('prefered',"DESC")->orderBy('name', 'ASC')->get()->map(function ($item, $key) use ($Image, $Market) {
            $data_array = $item->toArray();
            $data_array['image'] = $Image->getImage($item->ID, '2x', "attraction", 1, $item->name)[0];
            $data_array['durationFormatted'] = $this->convertToHoursMins($item['duration'],'%02d Hr %02d M');
            $data_array['markup'] = Company::find(\Auth::user()->Profile->company)->markup;
            $attarction_rate = \App\Model\Place\Attraction::attractionCurrencyConvertSingle($item->ID, \Session::get('quotation'));
            $data_array['rate']['adult'] = (isset($attarction_rate[$item->ID]['adult'])) ? round($attarction_rate[$item->ID]['adult'], 2) : 0;
            $data_array['rate']['child'] = (isset($attarction_rate[$item->ID]['child'])) ? round($attarction_rate[$item->ID]['child'], 2) : 0;

            $data_array['time'] = AttractionTime::where("attraction", "=", $item->ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->attraction;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });

            return $data_array;
        });

        $time_array = [];
        $CityTourList = CityTour::where([['place', $PlaceID], ['disabled', 0]])->orderby('prefered',"DESC")->orderBy('name', 'ASC')->get()->map(function ($item, $key) use ($Image, $Market, $VehicleID) {
            $data_array = $item->toArray();
            $data_array['image'] = $Image->getImage($item->ID, '2x', "city_tour", 1, $item->name)[0];
            $data_array['durationFormatted'] = $this->convertToHoursMins($item['duration'],'%02d Hr %02d M');
            $data_array['markup'] = Company::find(\Auth::user()->Profile->company)->markup;
            $attarction_rate = \App\Model\Place\CityTour::citytourCurrencyConvertSingle($item->ID, \Session::get('quotation'));
            $data_array['rate']['adult'] = (isset($attarction_rate[$item->ID]['adult'])) ? round($attarction_rate[$item->ID]['adult'], 2) : 0;
            $data_array['rate']['child'] = (isset($attarction_rate[$item->ID]['child'])) ? round($attarction_rate[$item->ID]['child'],2) : 0;

            $data_array['time'] = CitytourTime::where("tour", "=", $item->ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->tour;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });

            return $data_array;
        });

        $time_array = [];
        $ExcursionList = Excursion::where([['from', $PlaceID], ['disabled', 0]])->orderby('prefered',"DESC")->orderBy('name', 'ASC')->get()->map(function ($item, $key) use ($Image, $Market, $VehicleID) {
            $data_array = $item->toArray();
            $data_array['image'] = $Image->getImage($item->ID, '2x', "excursion", 1, $item->name)[0];
            $data_array['durationFormatted'] = $this->convertToHoursMins($item['duration'],'%02d Hr %02d M');
            $data_array['markup'] = Company::find(\Auth::user()->Profile->company)->markup;
            $attarction_rate = \App\Model\Place\Excursion::excursionCurrencyConvertSingle($item->ID, \Session::get('quotation'));
            $data_array['rate']['adult'] = (isset($attarction_rate[$item->ID]['adult'])) ? round($attarction_rate[$item->ID]['adult'], 2) : 0;
            $data_array['rate']['child'] = (isset($attarction_rate[$item->ID]['child'])) ? round($attarction_rate[$item->ID]['child'],2) : 0;

            $data_array['time'] = ExcursionTime::where("excursion", "=", $item->ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->excursion;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });

            return $data_array;
        });

        return [
            'attraction' => $AttractionList,
            'city_tour' => $CityTourList,
            'excursion' => $ExcursionList
        ];
    }

    function convertToHoursMins($time, $format = '%02d:%02d') {
        if ($time < 1) {
            return;
        }
        $hours = floor($time / 60);
        $minutes = ($time % 60);
        return sprintf($format, $hours, $minutes);
    }

    function get_attractions(Request $request)
    {
        $PlaceID = $request->place_id;
        $place = Place::find($PlaceID);
        if(isset($place)) {
            $country = $place->country;
        }

            $Market = 1;

        $Image = new Image();
        $time_array = [];
        $AttractionList = AttractionModel::where([['place', $PlaceID], ['disabled', 0]])->orderBy('name', 'ASC')->get()->map(function ($item, $key) use ($Image) {
            $data_array = $item->toArray();
            $data_array['image'] = $Image->getImage($item->ID, '2x', "attraction", 1, $item->name)[0];
            $data_array['durationFormatted'] = $this->convertToHoursMins($item['duration'],'%02d Hr %02d M');



            $data_array['time'] = AttractionTime::where("attraction", "=", $item->ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->attraction;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });

            return $data_array;
        });

        $time_array = [];
        $CityTourList = CityTour::where([['place', $PlaceID], ['disabled', 0]])->orderBy('name', 'ASC')->get()->map(function ($item, $key) use ($Image) {
            $data_array = $item->toArray();
            $data_array['image'] = $Image->getImage($item->ID, '2x', "city_tour", 1, $item->name)[0];
            $data_array['durationFormatted'] = $this->convertToHoursMins($item['duration'],'%02d Hr %02d M');



            $data_array['time'] = CitytourTime::where("tour", "=", $item->ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->tour;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });

            return $data_array;
        });

        $time_array = [];
        $ExcursionList = Excursion::where([['from', $PlaceID], ['disabled', 0]])->orderBy('name', 'ASC')->get()->map(function ($item, $key) use ($Image, $Market) {
            $data_array = $item->toArray();
            $data_array['image'] = $Image->getImage($item->ID, '2x', "excursion", 1, $item->name)[0];
            $data_array['durationFormatted'] = $this->convertToHoursMins($item['duration'],'%02d Hr %02d M');



            $data_array['time'] = ExcursionTime::where("excursion", "=", $item->ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->excursion;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });

            return $data_array;
        });

        return [
            'attraction' => $AttractionList,
            'city_tour' => $CityTourList,
            'excursion' => $ExcursionList
        ];
    }
}

