<?php

namespace App\Http\Controllers\Attraction;

use App\Http\Controllers\Controller;
use App\Model\Place\Attraction as AttractionModel;
use App\Model\Place\AttractionRate;
use App\Model\Place\CityTour;
use App\Model\Place\CityTourRate;
use App\Model\Place\Excursion;
use App\Model\Place\ExcursionRate;
use Illuminate\Http\Request;
use View;


/**
 * Class ElementHtml
 * @package App\Http\Controllers\Attraction
 */
class ElementHtml extends Controller
{


    /**
     * @param $ID
     * @param $Index
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function getAttraction($ID, $Index, Request $request){
		

		$data['details'] = AttractionModel::find($ID);
		$data['place'] = $data['details']->place;
        $QuoteAttr = intval($request->input('quote_attr'));
        $Rate = AttractionRate::where('attraction',$ID)->first()->toArray();


        $data['html'] = View::make('element.attraction.attraction',['ID'=>$ID,'QuoteAttr'=>$QuoteAttr,'Rate'=>$Rate,'Type'=>'attraction','Index'=>$Index])->render();
 		
		return response()->json($data);
	
	}

    /**
     * @param $ID
     * @param $Index
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function getCityTour($ID, $Index, Request $request){

		$data['details'] = CityTour::find($ID);
		$data['place'] = $data['details']->place;
        $QuoteAttr = intval($request->input('quote_attr'));
        $Rate = CityTourRate::where('tour',$ID)->first()->toArray();


        $data['html'] = View::make('element.attraction.city-tour',['ID'=>$ID,'QuoteAttr'=>$QuoteAttr,'Rate'=>$Rate,'Type'=>'city_tour','Index'=>$Index])->render();
 		
		return response()->json($data);
	
	}

    /**
     * @param $ID
     * @param $Index
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function getExcursion($ID, $Index, Request $request){
		

		$data['details'] = Excursion::find($ID);
        $data['place'] = $data['details']->from;
        $QuoteAttr = intval($request->input('quote_attr'));
        $Rate = ExcursionRate::where('excursion',$ID)->first()->toArray();


        $data['html'] = View::make('element.attraction.excursion',['ID'=>$ID,'QuoteAttr'=>$QuoteAttr,'Rate'=>$Rate,'Type'=>'excursion','Index'=>$Index])->render();


		return response()->json($data);
	
	}

    /**
     * @param $PlaceID
     * @param $Index
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getAttractionList($PlaceID, $Index, Request $request){

		$AttractionList = AttractionModel::where('place',$PlaceID)->get();
		$CityTourList = CityTour::where('place',$PlaceID)->get();	
		$ExcursionList = Excursion::where('from',$PlaceID)->get();

        $quote_attr = intval($request->input('quote_attr'));


		return view('element.attraction.attraction-list',['AttractionList'=>$AttractionList,'CityTourList'=>$CityTourList,'ExcursionList'=>$ExcursionList,"QuoteAttr"=>$quote_attr, "Index"=>$Index]);
	
	}

   
    
}