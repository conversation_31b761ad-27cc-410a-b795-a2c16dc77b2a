<?php
/**
 * Supun Praneeth 2017.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Http\Controllers\Transport;

use App\Http\Controllers\Attraction\Attraction;
use App\Http\Controllers\Controller;
use App\Model\Agent\Agent;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\Market;
use App\Model\Hotel\Meal;
use App\Model\Image\Image;
use App\Model\Operation\Chauffeur;
use App\Model\Operation\ChauffeurLanguage;
use App\Model\Operation\Driver;
use App\Model\Operation\Member;
use App\Model\Operation\MemberBank;
use App\Model\Operation\MemberContact;
use App\Model\Operation\Reconfirm;
use App\Model\Operation\Vehicle;
use App\Model\Operation\VehicleAllocation;
use App\Model\Operation\OutBoundVehicleAllocation;
use App\Model\Operation\VehicleAllocationItem;
use App\Model\Operation\VehicleCheckList;
use App\Model\Operation\VehicleInspection;
use App\Model\Place\AttractionRate;
use App\Model\Place\CityTour;
use App\Model\Place\CityTourRate;
use App\Model\Place\Excursion;
use App\Model\Place\ExcursionRate;
use App\Model\Place\Place;
use App\Model\Quotation\QuotationHotel;
use App\Model\QuotationManage\Quotation;
use App\Model\QuotationManage\Quotation as QuotationManage;
use App\Model\QuotationManage\QuotationFlight;
use App\User;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Validator;
use Auth;
use App\Model\Quotation\Quotation as QuotationQ;
use App\Model\Transport\RestuarantSaveEmail;
use Event;
use App\Events\QuotationEmail;
use App\Model\Itinerary\Itinerary;
use  App\Http\Controllers\Transport\Transport;

use App\Model\Operation\VehicleAllocationChangeHistory;

/**
 * Class Operation
 * @package App\Http\Controllers\Transport
 */
class Operation extends Controller
{
    public function __construct()
    {
        $this->middleware('operation_guard');
    }

    /**
     * @param $Type
     * @param bool $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    function getTemplate($Type, $id = false, $sub_id = false, $quotation_id = false)
    {

        switch ($Type) {
            case "layout":
                return view("operation.layout", ['id' => $id]);
            case "tour_allocation":
                return view("operation.tour-allocation", ['id' => $id]);
            case "movement_chart":
                return view("operation.movement-chart", ['id' => $id]);
            case "out_bound_tour_allocation":
                    return view("operation.out-bound-tour-allocation", ['id' => $id]);
            case "out_bound_movement_chart":
                    return view("operation.out-bound-movement-chart", ['id' => $id]);
            case "movement_chart_table":
                return view("operation.movement-chart-table", ['id' => $id]);
            case "movement_chart_table_outbound":
                return view("operation.movement-chart-table-outbound", ['id' => $id]);
            case "member_list":
                return view("operation.member", ['id' => $id]);
            case "member-item":
                return view("operation.member-item", ['id' => $id]);
            case "vehicle-list":
                return view("operation.vehicle", ['id' => $id]);
            case "vehicle-list-item":
                return view("operation.vehicle-list-item", ['id' => $id]);
            case "vehicle-item":
                return view("operation.vehicle-item", ['id' => $id]);
            case "vehicle_register":
                return view("operation.vehicle-register", ['id' => $id]);
            case "member_register":
                return view("operation.member-register", ['id' => $id]);
            case "quote-min":
                return view("operation.quotation-item", ['id' => $id]);
            case "tour-rows":
                return view("operation.tour-rows", ['id' => $id]);
            case "vehicle-min":
                return view("operation.vehicle-item", ['id' => $id]);
            case "tour_expense_sheet_frame":
                return view("operation.log-sheet", ['id' => $id, 'type' => "tour_expense_sheet"]);
            case "vehicle_running_sheet_frame":
                return view("operation.log-sheet", ['id' => $id, 'type' => "vehicle_running_sheet"]);
            case "tour_expense_sheet":
                return view("operation.tour-expense-sheet", ['id' => $id]);
            case "vehicle_running_sheet":
                return view("operation.vehicle-running-sheet", ['id' => $id]);
            case "driver_calender":
                return view("operation.driver-calender", ['id' => $id]);
            case "flight_details":
                return view("operation.flight-details", ['id' => $id]);
            case "contact-item":
                return view("element.contact-item");
            case "member_availability_layout":
                return view("operation.member-availability-layout");
            case "member_availability":
                return view("operation.member-availability");
            case "movement-settings":
                return view("operation.movement-settings", ['id' => $id]);
            case "movement-settings-outbound":
                return view("operation.movement-settings-outbound", ['id' => $id, 'sub_id' => $sub_id, 'quotation_id' => $quotation_id]);
            case "movement-transport-settings":
                return view("operation.movement-transport-settings", ['id' => $id, 'sub_id' => $sub_id ]);
            case "re-confirm-add":
                return view("operation.re-confirm-add", ['id' => $id]);
            case "re-confirm-view":
                return view("operation.re-confirm-view", ['id' => $id]);
            case "restaurant-vouchers":
                return view("operation.restaurant-vouchers", ['id' => $id]);
            default:
                return "";
        }
    }

    /**
     * @param $type
     * @param $id
     * @return array|bool
     */
    function getSummery($type, $id)
    {
        switch ($type) {
            case "tour":
                $quotationID = self::getTour($id);
                return QuotationManage::getQuotation(false, $quotationID);
            case "quotation":
                return QuotationManage::getQuotation(false, $id);
            case "vehicle":
                return Vehicle::getVehicle($id);
            case "tour_routes":
                $quotationID = self::getTour($id);
                $quotation = QuotationManage::getQuotation(false, $quotationID);
                $TourRoutes['tour_routes'] = self::getTourRoutes($quotation);
                $TourRoutes['quotation_id'] = $quotationID;
                return $TourRoutes;
            case "quotation_routes":
                $quotation = QuotationManage::getQuotation(false, $id);
                $TourRoutes['quotation_id'] = $id;
                $TourRoutes['tour_routes'] = self::getTourRoutes($quotation);
                return $TourRoutes;

        }
        return false;
    }

    static function getTour($id) {
        $Quotes = QuotationManage::has('Confirm');
        //is number
        if (!empty($id)) {
            $Quotes = $Quotes->whereHas('IsNumber', function ($IS) use ($id) {
                $IS->where('is_number', $id);
            });
        }

        return $Quotes->first()->quotation_no;
    }

    static function getTourRoutes($QuotationArray) {
        $Quotation = new QuotationQ();
        $Itinerary = new Itinerary();

        $ItineraryData = $Itinerary->getItinerary($QuotationArray);
        $DaysDetail = QuotationQ::getTourDaysDetail($QuotationArray, true);

        $DayList = $Itinerary->getDayList($DaysDetail);

        // $FormattedItineraryData = $Itinerary->getFormattedItinerary($QuotationArray, 'all');
        $FormattedItineraryData = $Itinerary->getFormattedItineraryByQuotation($QuotationArray, 'attraction');

        $data = [];

        $records = Transport::getOutboundDriverAllocation($QuotationArray['quotation_no']);
        
        foreach ($FormattedItineraryData as $Day => $DayItineraryDetails) {
            $previous = null;
            if(!empty($DayItineraryDetails)) {
                foreach ($DayItineraryDetails as $DayItineraryDetail) {
                    if(!empty($DayItineraryDetail)) {
                        if(!empty($previous)) {
                            $d = [];
                            $d['date']['formatted'] = $Day;
                            $d['date']['year'] = $DayItineraryDetail['date']['year'];
                            $d['date']['month'] = $DayItineraryDetail['date']['month'];
                            $d['date']['day'] = $DayItineraryDetail['date']['day'];
                            $d['start'] = $previous['name'];
                            $d['start_type'] = $previous['type'];
                            $d['start_id'] = $previous['id'];
                            $d['end'] = $DayItineraryDetail['name'];
                            $d['end_type'] = $DayItineraryDetail['type'];
                            $d['end_id'] = $DayItineraryDetail['id'];
                            $data[] = $d;
                            // dd($data);
                        }
                        $previous = $DayItineraryDetail;
                    }
                    
                }
            }
            
        }
        
        if ($records->isNotEmpty()) {
            foreach($data as $key => $value) {
                
                $data[$key]['driver_id'] = $records[$key]??null;
                $data[$key]['driver_name'] = !empty($records[$key]) ? \App\Model\Transport\TransportMembers::find($records[$key])->first_name . " " . \App\Model\Transport\TransportMembers::find($records[$key])->last_name : "";
            }
        }
        
        return $data;
    }

    /**
     * @param $Action
     * @param Request $request
     * @return bool|\Illuminate\Http\Response|void
     */
    function TourAction($Action, Request $request)
    {

        switch ($Action) {
            case "allocate":
                return $this->AllocateTour($request->all());
            case "deallocate":
                return $this->DeallocateTOur($request->all());

        }
        return false;
    }

    /**
     * @param $data
     * @return \Illuminate\Http\Response
     */
    function AllocateTour($data)
    {
        $api = $data['api'] ?? 0;

        $validator = Validator::make($data, [
            'quote' => 'required',
            'vehicle' => 'required|exists:apple_transport_vehicles,id'
        ]);

        if ($validator->fails()){
            if ($api == 1){

                return $validator->errors()->toArray();

            }

            return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);
        }


        $QuoteItem = QuotationManage::where(function ($query) {
            $query->doesntHave('VehicleAllocation');
            $query->has('Confirm');
        })->where('quotation_no', $data['quote'])->first();

        if (!$QuoteItem) {
            $file_handler = User::find(QuotationManage::where('quotation_no', $data['quote'])->first()->Main()->first()->user)->name;
//            $file_handler = QuotationManage::where('quotation_no', $data['quote'])->first()->Main()->first()->user()->first()->name;
            if ($api == 1){

                $data = [
                    'status' => "Tour is already allocated or Tour is not confirm, please contact the file handler:$file_handler!"
                ];

                return $data;

            }

            return response()->view(" system.error.error-ajax", ["Message" => "Tour is already allocated or Tour is not confirm, please contact the file handler:$file_handler!"], 422);
        }

        $VehicleItem = Vehicle::find($data['vehicle']);

        if ($VehicleItem->isAvailable()) {
            $VehicleAllocation = new VehicleAllocation();
            $VehicleAllocation->reference_id = $QuoteItem->ID;
            $VehicleAllocation->vehicle_id = $VehicleItem->id;
            $VehicleAllocation->driver = $VehicleItem->driver;
            $VehicleAllocation->chauffeur = $VehicleItem->driver;
            $VehicleAllocation->remarks = "";
            $VehicleAllocation->save();


            if ($api == 1){

                $data = [
                    'status' => 'Tour is Allocated'
                ];

                return $data;

            }

            return response()->view(" system.error.error-ajax", ["Message" => "Tour is Allocated"]);

        } else {

            if ($api == 1){

                $data = [
                    'status' => 'Vehicle is not available!'
                ];

                return $data;

            }

            return response()->view(" system.error.error-ajax", ["Message" => "Vehicle is not available!"], 422);
        }

    }

    /**
     * @return \Illuminate\Http\Response
     */
    function updateMovement()
    {


        $validator = Validator::make(request()->all(), [
            'id' => 'required',
            'guide' => 'required',
            'vehicle' => 'required',
            'driver' => 'required',
        ]);

        if ($validator->fails())
            return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);


        $id = request('id');
        $guide = request('guide');
        $remarks = request('remarks') ?? '';
        $driver = request('driver');
        $vehicle = request('vehicle');

        $allocate_item = request('allocate_item');
        $allocate_item_remarks = request('allocate_item_remarks');

        try {

            VehicleAllocationItem::where('allocation', $id)->delete();
            foreach ($allocate_item ?? [] as $k => $item) {

                if ($item) {
                    $VehicleAllocationItem = new VehicleAllocationItem();
                    $VehicleAllocationItem->allocation = $id;
                    $VehicleAllocationItem->item = $k;
                    $VehicleAllocationItem->value = $item;
                    $VehicleAllocationItem->remarks = $allocate_item_remarks[$k] ?? '';
                    $VehicleAllocationItem->save();
                }
            }


            $VehicleAllocation = VehicleAllocation::find($id);
            $VehicleAllocationHistoryData = VehicleAllocation::find($id);
            $VehicleAllocation->vehicle_id = $vehicle;
            $VehicleAllocation->driver = $driver;
            $VehicleAllocation->chauffeur = $guide;
            $VehicleAllocation->remarks = $remarks;
            $VehicleAllocation->save();

            $wasChanged = $VehicleAllocation->wasChanged();

            if($wasChanged == true){
                $VehicleAllocationChangeHistory =  new VehicleAllocationChangeHistory();
                $VehicleAllocationChangeHistory->vehicle_allocation_id=  $id;
                $VehicleAllocationChangeHistory->old_driver_id =  $VehicleAllocationHistoryData->driver;
                $VehicleAllocationChangeHistory->new_driver_id =  $driver;
                $VehicleAllocationChangeHistory->old_vehicle_id =   $VehicleAllocationHistoryData->vehicle_id;
                $VehicleAllocationChangeHistory->new_vehicle_id =  $vehicle;
                $VehicleAllocationChangeHistory->old_chauffeur =  $VehicleAllocationHistoryData->chauffeur;
                $VehicleAllocationChangeHistory->new_chauffeur =  $guide;
                $VehicleAllocationChangeHistory->user_id =  Auth::user()->id;
                $VehicleAllocationChangeHistory->save();
            }


            return response()->view(" system.error.error-ajax", ["Message" => "Successfully Updated({$VehicleAllocation->id})"]);

        } catch (\Exception $e) {
            return response()->view(" system.error.error-ajax", ["Message" => $e->getMessage()], 422);
        }


    }

        /**
     * @return \Illuminate\Http\Response
     */
    function updateOutboundMovement()
    {


        $validator = Validator::make(request()->all(), [
            'guide' => 'required',
            'vehicle' => 'required',
            'driver' => 'required',
        ]);

        if ($validator->fails())
            return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);

        $guide = request('guide');
        $remarks = request('remarks') ?? '';
        $driver = request('driver');
        $vehicle = request('vehicle');
        $start_date = request('start_date');
        $end_date = request('end_date');

        $ID = request('ID_Primary');
        $reference_id = request('id');
        $sub_id = request('sub_id');
        $quotation_id = request('quotation_id');

        try {
            if(isset($ID)) {
                $OutBoundVehicleAllocation = OutBoundVehicleAllocation::find($ID);
            } else {
                $OutBoundVehicleAllocation = new OutBoundVehicleAllocation();
            }
            
            $OutBoundVehicleAllocation->reference_id = $reference_id;
            $OutBoundVehicleAllocation->sub_id = $sub_id;
            $OutBoundVehicleAllocation->quotation_id = $quotation_id;
            $OutBoundVehicleAllocation->vehicle_id = $vehicle;
            $OutBoundVehicleAllocation->driver = $driver;
            $OutBoundVehicleAllocation->chauffeur = $guide;
            $OutBoundVehicleAllocation->start_datetime = $start_date;
            $OutBoundVehicleAllocation->end_datetime = $end_date;
            $OutBoundVehicleAllocation->remarks = $remarks ?? '';
            $OutBoundVehicleAllocation->save();

            return response()->view(" system.error.error-ajax", ["Message" => "Successfully Updated({$OutBoundVehicleAllocation->id})"]);

        } catch (\Exception $e) {
            return response()->view(" system.error.error-ajax", ["Message" => $e->getMessage()], 422);
        }
    }


    /**
     * @param $quotation_no
     * @return Vehicle|Vehicle[]|array|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Http\Response|\Illuminate\Support\Collection
     */
    function getTourAvailableVehicles($quotation_no)
    {
        $api = request('api') ?? 0;

        $validator = Validator::make(request()->all(), [
            'quote_id' => 'required|exists:apple_quotation,ID',
            'vehicle_type' => 'required',
            'availability' => 'required',
        ]);

        if ($validator->fails()) {

            if ($api == 1){

                return $validator->errors()->toArray();

            }

            return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);
        }

        $dataArray = [];

        $QuotationManage = QuotationManage::getQuotation(false, $quotation_no);

        if ($QuotationManage) {

            $dataArray = Vehicle::with('driver', 'vehicleType.vehicleType', 'owner');

            if (request('vehicle_type'))
                $dataArray = $dataArray->where('vehicle_type', request('vehicle_type'));

            if (request('vehicle_number')) {

                $dataArray = $dataArray->where(function ($q) {

                    $q->where('vehicle_number', request('vehicle_number'))
                        ->orWhereHas("Driver", function ($q) {
                            $val = request('vehicle_number');
                            $q->where("first_name", "like", "$val%");
                            $q->orWhere("last_name", "like", "$val%");
                        });
                });
            }

            if (request('country')) {
                $dataArray = $dataArray->where('country', request('country'));
            }


            $dataArray = $dataArray->limit(1000)->get()->map(function ($q) use ($QuotationManage) {
                $ArrivalDate = Carbon::create($QuotationManage['arrival_date']['year'], $QuotationManage['arrival_date']['month'], $QuotationManage['arrival_date']['day']);
                $available = $q::isAvailable($q->id, $ArrivalDate);
                $data = $q->toArray();
                $data['available'] = $available;
                $data['available_status'] = ($available === true);
                $data['image'] = Image::getImageDirect($q->id, '2x', 'vehicle', 1);
                return $data;
            });

        }

        return $dataArray;

    }

    /**
     * @param $data
     */
    function DeallocateTour($data)
    {

    }


    /**
     * @param $ID
     * @return Member|mixed
     */
    function getMember($ID)
    {
        return Member::find($ID);
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    function getMembers(Request $request)
    {
        $keyword = "%" . $request->q . "%";

        $Member = new Member();

        if ($keyword) {
            $Member = $Member->where(function ($q) use ($keyword) {
                $q->where("first_name", "like", $keyword);
                $q->orWhere("last_name", "like", $keyword);
            });
        }

        if(isset($request->country) && $request->country != "" ) {
            $Member = $Member->where('country', $request->country);
        }

        return $Member->get()->map(function ($Member) {
            $data = $Member->toArray();
            $data['vehicle'] = $Member->Vehicle;
            return $data;
        });
    }

    /**
     * @return Vehicle[]|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     */
    function getVehicles(Request $request)
    {

        $keyword = "%" . $request->q . "%";

        $Vehicle = Vehicle::with('owner', 'vehicleType.vehicleType:ID,name', 'Brand:id,vehicle_brand');

        if(isset($request->country) && $request->country != "" ) {
            $Vehicle = $Vehicle->where('country', $request->country);
        }

        if ($keyword) {
            $Vehicle = $Vehicle->where(function ($q) use ($keyword) {
                $q->where("vehicle_number", "like", $keyword);
                $q->orWhere("remarks", "like", $keyword);
            });
        }


        return $Vehicle->get()->map(function ($Vehicle) {
            $data = $Vehicle->toArray();
            $data['vehicle'] = $Vehicle->Vehicle;
            $data['vehicle_type'] = $Vehicle->Type;
            return $data;
        });
    }

    function getVehiclesByDriver(Request $request) {


        $Vehicle = Vehicle::with('owner', 'vehicleType.vehicleType:ID,name', 'Brand:id,vehicle_brand');
        $Vehicle->where('driver','=',$request->driver_id);

        return $Vehicle->get()->map(function ($Vehicle) {
            $data = $Vehicle->toArray();
            $data['vehicle'] = $Vehicle->Vehicle;
            $data['vehicle_type'] = $Vehicle->Type;
            return $data;
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Model|\Illuminate\Http\Response
     */
    function setFlightDetails(Request $request)
    {
        $data = $request->all();

        $api = $data['api'] ?? 0;

        $validator = Validator::make($data, [
            'quote' => 'required|exists:apple_quotation,ID',
            'arrival_flight_no' => 'required',
            'departure_flight_no' => 'required',
            'arrival_flight_time' => 'required|date',
            'departure_flight_time' => 'required|date'
        ]);

        if ($validator->fails()) {

            if ($api == 1){

                return $validator->errors()->toArray();

            }

            return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);
        }


        $reference_id = QuotationManage::where('quotation_no', $data['quote'])->orderBy('ID', 'desc')->first()->ID;

        $FlightDetails = QuotationFlight::firstOrNew(['reference_id' => $reference_id]);
        $FlightDetails->reference_id = $reference_id;
        $FlightDetails->arrival_flight_no = $data['arrival_flight_no'];
        $FlightDetails->departure_flight_no = $data['departure_flight_no'];
        $FlightDetails->arrival_flight_time = Carbon::parse($data['arrival_flight_time']);
        $FlightDetails->departure_flight_time = Carbon::parse($data['departure_flight_time']);
        $FlightDetails->save();

        return $FlightDetails;

    }

    /**
     * @return \Illuminate\Http\Response
     */
    function setMembers(Request $request)
    {
        $data = $request->all();

        $api = $data['api'] ?? 0;

        $validator = Validator::make($data, [
            'first_name' => 'required',
            'last_name' => 'required',
            'nic' => 'required',
        ]);


        if (request()->has('contact')) {
            $validator->addRules([
                'contact.type' => 'required|array',
                'contact.value' => 'required|array',
                'contact.type.*' => 'required',
                'contact.value.*' => 'required',
            ]);
        }

        if (request()->has('add_bank')) {
            $validator->addRules([
                'bank' => 'required|exists:apple_banks,id',
                'branch' => 'required',
                'account_number' => 'required|numeric',
                'account_name' => 'required',
            ]);
        }

        if (request()->has('driver_details')) {
            $validator->addRules([
                'license_start_date' => 'required|date',
                'license_category' => 'required',
                'license_no' => 'required',
            ]);
        }

        if (request()->has('chauffeur_details')) {
            $validator->addRules([
                'chauffeur_type' => 'required',
                'chauffeur_license_no' => 'required',
                'chauffeur_uniform' => 'required',
                'chauffeur_languages' => 'required|array',
                'chauffeur_remarks' => 'required',
                'chauffeur_nationality' => 'required',
            ]);
        }

        if ($validator->fails()) {

            if ($api == 1){

                return $validator->errors()->toArray();

            }

            return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);

        }


        try {
            DB::transaction(function () use ($data) {

                $Member = Member::findOrNew(request('member'));
                $Member->first_name = $data['first_name'];
                $Member->last_name = $data['last_name'];
                $Member->nic = $data['nic'];
                $Member->remarks = $data['remarks'] ?? "";
                $Member->country = $data['country'] ?? "";
                $Member->save();

                if (request()->has('contact')) {

                    MemberContact::where('member_id', $Member->id)->delete();

                    foreach ($data['contact']['type'] as $k => $ContactItem) {

                        $MemberContact = new  MemberContact();
                        $MemberContact->member_id = $Member->id;
                        $MemberContact->type = $data['contact']['type'][$k];
                        $MemberContact->contact = $data['contact']['value'][$k];
                        $MemberContact->save();
                    }

                }

                if (request()->has('add_bank')) {

                    $MemberBank = MemberBank::firstOrNew(['member_id' => $Member->id]);
                    $MemberBank->member_id = $Member->id;
                    $MemberBank->bank = $data['bank'];
                    $MemberBank->branch = $data['branch'];
                    $MemberBank->account_number = $data['account_number'];
                    $MemberBank->account_name = $data['account_name'];
                    $MemberBank->save();
                }

                if (request()->has('driver_details')) {

                    $Driver = Driver::firstOrNew(['member_id' => $Member->id]);
                    $Driver->member_id = $Member->id;
                    $Driver->license_start_date = $data['license_start_date'];
                    $Driver->license_category = $data['license_category'];
                    $Driver->license_no = $data['license_no'];
                    $Driver->save();
                }
                if (request()->has('chauffeur_details')) {

                    $Chauffeur = Chauffeur::firstOrNew(['member_id' => $Member->id]);
                    $Chauffeur->member_id = $Member->id;
                    $Chauffeur->type = $data['chauffeur_type'];
                    $Chauffeur->license_no = $data['chauffeur_license_no'];
                    $Chauffeur->uniform = $data['chauffeur_uniform'];
                    $Chauffeur->remarks = $data['chauffeur_remarks'];
                    $Chauffeur->rate_per_day = $data['chauffeur_rate_per_day'];
                    $Chauffeur->tip_per_day = $data['chauffeur_tip_per_day'];
                    $Chauffeur->nationality = $data['chauffeur_nationality'];
                    $Chauffeur->save();


                    if (request()->has('chauffeur_languages')) {

                        ChauffeurLanguage::where('chauffeur_id', $Chauffeur->id)->delete();

                        foreach ($data['chauffeur_languages'] as $lng_id) {

                            $ChauffeurLanguage = new  ChauffeurLanguage();
                            $ChauffeurLanguage->chauffeur_id = $Chauffeur->id;
                            $ChauffeurLanguage->language_id = $lng_id;
                            $ChauffeurLanguage->save();
                        }

                    }

                }

            });
        } catch (\Exception $e) {

            if ($api == 1){

                $data = [
                    'status' => $e->getMessage()
                ];

                return $data;

            }

            return response()->view(" system.error.error-ajax", ["Message" => $e->getMessage()], 422);
        } catch (\Throwable $e) {

            if ($api == 1){

                $data = [
                    'status' => $e->getMessage()
                ];

                return $data;

            }

            return response()->view(" system.error.error-ajax", ["Message" => $e->getMessage()], 422);
        }

        if ($api == 1){

            $data = [
                'status' => 'Member has added to the database!'
            ];

            return $data;

        }

        return response()->view(" system.error.error-ajax", ["Message" => $data['first_name'] . " " . $data['last_name'] . " has added to the database!"]);


    }

    /**
     * @return \Illuminate\Http\Response
     */
    function setVehicles(Request $request)
    {
        $data = $request->all();

        $api = $data['api'] ?? 0;

        $validator = Validator::make($data, [
            'owner' => 'required',
            'driver' => 'required',
            'color' => 'required',
            'vehicle_type' => 'required',
            'insurance' => 'required',
            'vehicle_brand' => 'required',
            'registration_year' => 'required',
            'registration_no' => 'required',
            'agreement_expire_at' => 'required',
            'insurance_date' => 'required',
            'model_year' => 'required',
            'last_inspection_date' => 'required',
            'inspection_by' => 'required',
            'vehicle_number' => 'required',
        ]);

        if ($request->has('vehicle_checklist')) {
            $validator->addRules([
                'vehicle_checklist' => 'required|array',
                'vehicle_checklist.*' => 'required',
            ]);
        }
        if ($request->has('vehicle_inspections')) {
            $validator->addRules([
                'vehicle_inspections' => 'required|array',
                'vehicle_inspections.*' => 'required',
            ]);
        }


        if ($validator->fails()) {

            if ($api == 1){

                return $validator->errors()->toArray();

            }

            return response()->view(" system.error.error-ajax", ["Message" => $validator->errors()->toArray()], 422);

        }


        try {
            DB::transaction(function () use ($data, $request) {

                $Vehicle = Vehicle::findOrNew($request->vehicle);
                $Vehicle->owner = $data['owner'];
                $Vehicle->driver = $data['driver'];
                $Vehicle->color = $data['color'];
                $Vehicle->vehicle_type = $data['vehicle_type'];
                $Vehicle->insurance = $data['insurance'];
                $Vehicle->vehicle_brand = $data['vehicle_brand'];
                $Vehicle->registration_year = $data['registration_year'];
                $Vehicle->registration_no = $data['registration_no'];
                $Vehicle->agreement_expire_at = $data['agreement_expire_at'];
                $Vehicle->insurance_date = $data['insurance_date'];
                $Vehicle->model_year = $data['model_year'];
                $Vehicle->last_inspection_date = $data['last_inspection_date'];
                $Vehicle->inspection_by = $data['inspection_by'];
                $Vehicle->vehicle_number = $data['vehicle_number'];
                $Vehicle->remarks = $data['remarks'] ?? "";
                $Vehicle->country = $data['country'] ?? "";
                $Vehicle->save();


                if ($request->has('vehicle_checklist')) {

                    VehicleCheckList::where('vehicle_id', $Vehicle->id)->delete();

                    foreach ($data['vehicle_checklist'] as $CheckListItem) {
                        $VehicleCheckList = new VehicleCheckList();
                        $VehicleCheckList->vehicle_id = $Vehicle->id;
                        $VehicleCheckList->checklist_id = $CheckListItem;
                        $VehicleCheckList->save();
                    }

                }

                if ($request->has('vehicle_inspections')) {

                    VehicleInspection::where('vehicle_id', $Vehicle->id)->delete();

                    foreach ($data['vehicle_inspections'] as $InspectionItem) {
                        $VehicleInspection = new VehicleInspection();
                        $VehicleInspection->vehicle_id = $Vehicle->id;
                        $VehicleInspection->inspection_id = $InspectionItem;
                        $VehicleInspection->save();
                    }
                }


            });
        } catch (\Exception $e) {
        } catch (\Throwable $e) {
        }


        if ($api == 1){

            $data = [
                'status' => 'Vehicle has added to the database!'
            ];

            return $data;
        }

        return response()->view(" system.error.error-ajax", ["Message" => "Vehicle has added to the database!"]);


    }

    /**
     * @param Request $r
     * @return array
     */
    function getMovementChart(Request $request)
    {
        $Data = $request->input();

        $Date = new Carbon($Data['date']);
        $Type = intval($Data['type']);
        $Country = intval($Data['country']);

        $QuotationHotel = new QuotationHotel();

        $List = [];


        //date filter
        $QuoteSearch = QuotationManage::has('Confirm')
            ->has('main')
            ->whereHas('main', function ($query) use ($Date, $Type) {

                if ($Type == 3) {
                    $query->where('arrival_year', $Date->year);
                } elseif ($Type == 2) {
                    $query->where('arrival_year', $Date->year)
                        ->where('arrival_month', $Date->month);
                } elseif ($Type == 1) {
                    $query->where('arrival_year', $Date->year)
                        ->where('arrival_month', $Date->month)
                        ->where('arrival_day', $Date->day);
                }

            });

        if(isset($request->country) && $request->country != "" ) {
            $QuoteSearch = $QuoteSearch->where('country', $request->country);
        }
        
        $QuoteSearch = $QuoteSearch->get();

        foreach ($QuoteSearch as $K => $QuoteItem) {
            $ListCurrent = [];
            $QuotationSession = [];

            $ListCurrent['id'] = $QuoteItem->VehicleAllocation()->first()->id ?? "-";
            $ListCurrent['quotation_no'] = $QuoteItem->quotation_no;
            $ListCurrent['reference_id'] = $QuoteItem->ID;
            $ListCurrent['client'] = $QuoteItem->confirm()->first()->client_name;
            $ListCurrent['agent'] = Agent::find($QuoteItem->confirm()->first()->agent ?? $QuoteItem->status()->first()->agent);
            $ListCurrent['pax'] = $QuoteItem->pax->adult + $QuoteItem->pax->cwb + $QuoteItem->pax->cnb;
            $ListCurrent['flight'] = $QuoteItem->flight()->first();
            $ListCurrent['user'] = User::find($QuoteItem->main()->select('user')->first()->user)->name;
            $ListCurrent['market'] = Market::find($QuoteItem->pax()->select('market')->first()->market)->market;

            foreach($QuoteItem->place()->get() as $item) {
                $QuotationSession['place_full'][] = $item['place'];
                $QuotationSession['place_type'][] = $item['type'];
            }

            $ListCurrent['meal'] = $QuoteItem->hotel()->groupBy('meal')->get()->map(function ($hotel) {
                return Meal::find($hotel->meal)->plan ?? "";
            });

            $ListCurrent['vehicle_allocation'] = $QuoteItem->VehicleAllocation()->get()->map(function ($Allocate) {

                $data['items'] = $Allocate->Item()->get()->map(function ($q) {

                    $data = $q->toArray();
                    $data['details'] = $q->Item()->select('item')->first();
                    return $data;
                });

                $data['vehicle_number'] = $Allocate->Vehicle()->first()->vehicle_number;
                $data['remarks'] = $Allocate->remarks;

                if ($Allocate->driver) {
                    $data['driver'] = [
                        "tel" => $Allocate->Driver()->first()->contact()->first()->contact,
                        "name" => $Allocate->Driver()->first()->first_name . ' ' . $Allocate->Driver()->first()->last_name
                    ];
                }
                return $data;

            });

            $ListCurrent['flight'] = $QuoteItem->Flight()->first();


            $Main = $QuoteItem->main()->select('arrival_year', 'arrival_month', 'arrival_day')->first()->toArray();
            $Dates = $QuotationHotel->getHotelBookDates(["year" => $Main['arrival_year'], "month" => $Main['arrival_month'], "day" => $Main['arrival_day']], $QuoteItem->place()->select('place')->get()->toArray());

            $ListCurrent['arrival'] = (reset($Dates)['check_in']);
            $ListCurrent['departure'] = (end($Dates)['check_out']);
            $ListCurrent['hotels'] = $QuoteItem->hotel()->get()->map(function ($d) {
                $data = $d;
                $data['details'] = Hotel::find($data->hotel);
                $data['night'] = Carbon::create($d->check_in_year, $d->check_in_month, $d->check_in_day)->diffInDays(Carbon::create($d->check_out_year, $d->check_out_month, $d->check_out_day));
                return $data;
            });

            $ListCurrent['attraction'] = $QuoteItem->Attraction()->get()->map(function ($q) {
                return $q->details()->first();
            });

            $ListCurrent['cityTours'] = $QuoteItem->CityTours()->get()->map(function ($q) {
                return $q->details()->first();
            });

            $ListCurrent['excursion'] = $QuoteItem->Excursion()->get()->map(function ($q) {
                return $q->details()->first();
            });

            $ListCurrent['sightseeing'] = [];
            if($Country == 63 || $Country == 64) {
                foreach($ListCurrent['attraction'] as $item) {
                    $item['attr_type'] = 'attraction';
                    $ListCurrent['sightseeing'][] = $item;
                }
                foreach($ListCurrent['cityTours'] as $item) {
                    $item['attr_type'] = 'citytours';
                    $ListCurrent['sightseeing'][] = $item;
                }
                foreach($ListCurrent['excursion'] as $item) {
                    $item['attr_type'] = 'excursion';
                    $ListCurrent['sightseeing'][] = $item;
                }
            }
            $Quotation = new QuotationQ;
            $TimeDetail = $Quotation->getTimeDetails($QuotationSession);
            $DaysDetail = $Quotation->getMealTransferDetails($QuoteItem->ID, $QuoteItem->quotation_no);
            $ListCurrent['time_detail'] = $TimeDetail;
            $ListCurrent['day_detail'] = $DaysDetail;

            $List[] = $ListCurrent;

        }

        return [
            "data" => $List
        ];
    }

     /**
     * @param Request $r
     * @return array
     */
    function getOutBoundMovementChart(Request $request)
    {
        $Data = $request->input();

        $Date = new Carbon($Data['date']);
        $Type = intval($Data['type']);
        $Country = intval($Data['country']);

        $QuotationHotel = new QuotationHotel();

        $List = [];


        //date filter
        $QuoteSearch = QuotationManage::has('Confirm')
            ->has('main')
            ->whereHas('OutBoundDriverAllocation', function ($query) use ($Date, $Type) {
                    $query->where('year', $Date->year)
                        ->where('month', $Date->month)
                        ->where('day', $Date->day);
            });
        
        if(isset($request->country) && $request->country != "" ) {
            $QuoteSearch = $QuoteSearch->where('country', $request->country);
        }
       
        $QuoteSearch = $QuoteSearch->get();
        foreach ($QuoteSearch as $K => $QuoteItem) {
            $ListCurrent = [];
            $QuotationSession = [];

            $ListCurrent['quotation_no'] = $QuoteItem->quotation_no;
            $ListCurrent['is_number'] = $QuoteItem->IsNumber->is_number ?? $QuoteItem->quotation_no;
            $ListCurrent['reference_id'] = $QuoteItem->ID;
            $ListCurrent['client'] = $QuoteItem->confirm()->first()->client_name;
            $ListCurrent['agent'] = Agent::find($QuoteItem->confirm()->first()->agent ?? $QuoteItem->status()->first()->agent);
            $ListCurrent['adult'] = $QuoteItem->pax->adult;
            $ListCurrent['child'] = $QuoteItem->pax->cwb + $QuoteItem->pax->cnb + $QuoteItem->pax->infant;
            $ListCurrent['flight'] = $QuoteItem->flight()->first();
            $ListCurrent['user'] = User::find($QuoteItem->main()->select('user')->first()->user)->name;
            $ListCurrent['market'] = Market::find($QuoteItem->pax()->select('market')->first()->market)->market;

            //$ListCurrent['meal'] = $QuoteItem->hotel()->groupBy('meal')->get()->map(function ($hotel) {
            //    return Meal::find($hotel->meal)->plan ?? "";
            //});

            $ListCurrent['diver_allocation'] = $QuoteItem->OutBoundDriverAllocation()
                ->where('year', $Date->year)
                ->where('month', $Date->month)
                ->where('day', $Date->day)->get()->map(function ($Allocate) {
                $data = $Allocate->toArray();
                $Date = Carbon::create($data['year'], $data['month'], $data['day'], 14, 15, 16);
                if($data['start_type'] == "place") {
                    $data['start']  = \App\Model\Place\Place::find($data['start_id'])->name;
                } else if($data['start_type'] == "hotel") {
                    $data['start']  = \App\Model\Hotel\Hotel::find($data['start_id'])->name ?? "Own Arrangement";
                } else if($data['start_type'] == "attraction") {
                    $data['start']  = \App\Model\Place\Attraction::find($data['start_id'])->name;
                } else if($data['start_type'] == "city_tour") {
                    $data['start']  = \App\Model\Place\CityTour::find($data['start_id'])->name;
                } else if($data['start_type'] == "excursion") {
                    $data['start']  = \App\Model\Place\Excursion::find($data['start_id'])->name;
                }

                if($data['end_type'] == "place") {
                    $data['end']  = \App\Model\Place\Place::find($data['end_id'])->name;
                } else if($data['end_type'] == "hotel") {
                    $data['end']  = \App\Model\Hotel\Hotel::find($data['end_id'])->name ?? "Own Arrangement";
                } else if($data['end_type'] == "attraction") {
                    $data['end']  = \App\Model\Place\Attraction::find($data['end_id'])->name;
                } else if($data['end_type'] == "city_tour") {
                    $data['end']  = \App\Model\Place\CityTour::find($data['end_id'])->name;
                } else if($data['end_type'] == "excursion") {
                    $data['end']  = \App\Model\Place\Excursion::find($data['end_id'])->name;
                }

                $data['formatted'] = $Date->toFormattedDateString();
                        
                if ($Allocate->Driver) {
                    $data['driver'] = [
                        "tel" => $Allocate->Driver()->first()->contact()->first()->contact,
                        "name" => $Allocate->Driver()->first()->first_name . ' ' . $Allocate->Driver()->first()->last_name
                    ];
                    
                }
                //$data['remarks'] = $Allocate->remarks;
                return $data;

            });
            
            $List[] = $ListCurrent;

        }

        return [
            "data" => $List
        ];
    }


    /**
     * @param $id
     * @param $type
     * @return array
     */
    function getLogSheet($id, $type)
    {
        // var_dump($type);exit();
        $Quotation = QuotationManage::where('quotation_no', $id)->first();
        $QuotationHotel = new QuotationHotel();
        $QuotationMain = new  \App\Model\Quotation\Quotation();
        $QuotationArray = QuotationManage::getQuotation($Quotation['ID'], $id);

        $QuotationAllocation = $Quotation ? $Quotation->VehicleAllocation()->first() : false;
        if (!$QuotationAllocation)
            return [];

        $Dates = $QuotationHotel->getHotelBookDates(["year" => $Quotation->main()->first()->arrival_year, "month" => $Quotation->main()->first()->arrival_month, "day" => $Quotation->main()->first()->arrival_day], $Quotation->place()->pluck('place'));

        $DaysDetail = $QuotationMain->getTourDaysDetail($Quotation::getQuotation(false, $id), true);

        $country_code = Place::where("ID", "=",$QuotationArray['country'])->first()->A2;

        if($type =="vehicle_running_sheet") {
            $increment = $QuotationAllocation->run_print_count + 1;
        } else {
            $increment = $QuotationAllocation->expense_print_count + 1;
        }

        $data['log_sheet_no'] = "AH/" . $country_code . "/" . $QuotationAllocation->id . "-" . $increment;
        $data['printed_date'] = Carbon::now()->toDateTimeString();
        $data['arrival_date'] = (reset($Dates)['check_in']);
        $data['departure_date'] = (end($Dates)['check_in']);
        $data['tour'] = $Quotation::getReferenceID($Quotation->quotation_no);
        $data['driver'] = $QuotationAllocation->Vehicle()->first()->Driver()->with('contact')->first();
        $data['client'] = $Quotation->confirm()->first()->client_name;
        $data['duration'] = count($Dates) . ' Days';
        $data['airport'] = Place::getPlaceBYID($Quotation->Place()->first()->place)->name;
        $data['pax'] = $QuotationArray["pax"];
        $data['market'] = $Quotation->nationality;
        $data['vehicle'] = $QuotationAllocation->Vehicle()->first()->vehicle_number;
        $data['guide'] = $QuotationAllocation->Guide()->first();
        $data['flight'] = $Quotation->Flight()->first() ?? false;
        $data['days'] = collect($DaysDetail)->map(function ($q) {


            $data = ['day' => $q['index'], 'text' => ''];

            if (isset($q[2]))
                $data['text'] .= 'Pick Up from ' . Place::find($q[2]['place'])->name . ' >';


            if (isset($q['route']['from']))
                $data['text'] .= 'From ' . Place::find($q['route']['from'])->name;
            else
                $data['text'] .= Place::find($q['place'])->name;


            if (isset($q[3]))
                $data['text'] .= ' > Drop Off to ' . Place::find($q[3]['place'])->name;


            if (isset($q['route']['to']))
                $data['text'] .= ' to ' . Place::find($q['route']['to'])->name;


            return $data;

        });

        /********************************************/

        $reference_id = QuotationManage::withTrashed()->where('quotation_no', $Quotation->quotation_no)
                                                        ->orderBy('ID', 'desc')->first()->ID ?? false;

        $data['quotation_data'] = Quotation::getQuotation($reference_id);

        $market = $data['quotation_data']['market'];

        $Vehicle = new \App\Model\Vehicle\Vehicle();

        $VehicleType = $Vehicle->getPaxToVehicle($data['quotation_data'], true, "first");//get vehicle vehicle
        if (!$VehicleType)
            return false;
        $VehicleID = $VehicleType->ID;

        $includes = \App\Model\Quotation\QuotationInclude::getQuotationListLimited($QuotationArray);
        $data['includes'] = $includes;

        if(isset($QuotationArray['attraction']) && !empty($QuotationArray['attraction'])) {
            foreach ($QuotationArray['attraction'] as $key =>$Item) {
                $data['attraction'][] = \App\Model\Place\Attraction::find($Item[0])->name??'';


                $data['rate_attraction'] = AttractionRate::where('attraction', $Item[0][$key])
                                                                ->where('market', $market)
                                                                ->orderByDesc('ID')
                                                                ->first();

                $data['rate_attraction']['name'] = \App\Model\Place\Attraction::find($Item[0])->name??''[$key];
            }
        }
        if(isset($QuotationArray['city_tour']) && !empty($QuotationArray['city_tour'])) {
            foreach ($QuotationArray['city_tour'] as $key => $Item) {
                $data['city_tour'][] = \App\Model\Place\CityTour::find($Item[0])->name??'';


                $data['rate_city_tour'] = CityTourRate::where('tour', $Item[0][$key])
                                                                ->where('market', $market)
                                                                ->where('vehicle', $VehicleID)
                                                                ->orderByDesc('ID')
                                                                ->first();

                $data['rate_city_tour']['name'] = \App\Model\Place\CityTour::find($Item[0])->name??''[$key];

            }
        }
        if(isset($QuotationArray['excursion']) && !empty($QuotationArray['excursion'])) {
            foreach ($QuotationArray['excursion'] as $key =>$Item) {
                $data['excursion'][] = \App\Model\Place\Excursion::find($Item[0])->name??'';

                $data['rate_excursion'] = ExcursionRate::where('excursion', $Item[0][$key])
                                                                ->where('market', $market)
                                                                ->where('vehicle', $VehicleID)
                                                                ->orderByDesc('ID')
                                                                ->first();

                $data['rate_excursion']['name'] = \App\Model\Place\CityTour::find($Item[0])->name??''[$key];
            }
        }

            $data['rate_attraction'] = array_values($data['rate_attraction'] ?? []);
            $data['rate_city_tour'] = array_values($data['rate_city_tour'] ?? []);
            $data['rate_excursion'] = array_values($data['rate_excursion'] ?? []);

/*
        foreach(($includes??[]) as $IncludeItem)

                                <li>{{$IncludeItem}}
                                    <input type="hidden" name="include[]" value="1">
                                </li>
    endforeach


                            @if(isset($QuotationArray['attraction']))
        @foreach($QuotationArray['attraction'] as $AttrItem)
        @foreach($AttrItem as $IncludeItemID)
                                        <li>{{\App\Model\Place\Attraction::find($IncludeItemID)->name??''}}</li>
    @endforeach
                                @endforeach
                            @endif
                            @if(isset($QuotationArray['city_tour']))
        @foreach($QuotationArray['city_tour'] as $CityTourItem)
        @foreach($CityTourItem as $IncludeItemID)
                                        <li>{{\App\Model\Place\CityTour::find($IncludeItemID)->name??''}}</li>
    @endforeach
                                @endforeach
                            @endif
                            @if(isset($QuotationArray['excursion']))
        @foreach($QuotationArray['excursion'] as $ExcursionItem)
        @foreach($ExcursionItem as $IncludeItemID)
                                        <li>{{\App\Model\Place\Excursion::find($IncludeItemID)->name??''}}</li>
    @endforeach
                                @endforeach
                            @endif*/

        return $data;

    }

    /**
     *
     *
     */

    function setPrintCount($type, $id) {
        $Quotation = QuotationManage::where('quotation_no', $id)->first();

        $QuotationAllocation = $Quotation ? $Quotation->VehicleAllocation()->first() : false;

        if (!$QuotationAllocation)
            return [];

        $update = DB::table('apple_transport_vehicle_allocation');
        $update = $update->where('id', $QuotationAllocation->id);

        if($type == "vehicle_running_sheet_frame") {
            $update = $update->increment("run_print_count", 1);
        } else {
            $update = $update->increment("expense_print_count", 1);
        }
    }

    /**
     * @return array
     */
    function getDriverAvailabilityList(Request $request)
    {

        $Date = Carbon::parse($request->date)->startOfMonth();
        $NoDays = $Date->daysInMonth;
        $AvailabilityList = [];
        $days = [];
        $Members = new Member;
        if($request->country != 0) {
            $Members = $Members->where('country', $request->country);
        }
        if($request->member != "") {
            $Members = $Members->where('id', $request->member);
        }
        $Members = $Members->paginate(10);

        for ($day_start = 1; $day_start <= $NoDays; $day_start++) {

            $days[] = $day = $Date->format('j');
            
            foreach ($Members as $MemberItem) {
                $AvailabilityList[$MemberItem->id][$day] = [
                    'day' => $Date->format('d'),
                    'status' => $MemberItem->isAvailable($Date),
                    'driver' => $MemberItem->first_name . ' ' . $MemberItem->last_name
                ];

                
            }
            
            $Date->addDay();

        }
        $MembersLinks = Member::paginate(10)->toArray();
        unset($MembersLinks['data']);
        
        return [
            "days" => $days,
            "day_details" => $AvailabilityList,
            "pagination" => $MembersLinks
        ];

    }

    /**
     * @return array
     */
    function getDriverAvailability($country=62)
    {
        $QuotationHotel = new QuotationHotel();

        $start_date = Carbon::parse(request('start'));
        $end_date = Carbon::parse(request('end'));

        $QuoteSearch = QuotationManage::has('Confirm')
            ->where('country', $country)
            ->has('VehicleAllocation')
            ->has('main')
            ->get()
            ->map(function ($q) use ($QuotationHotel, $start_date, $end_date) {


                $Dates = $QuotationHotel->getHotelBookDates(["year" => $q->main()->first()->arrival_year, "month" => $q->main()->first()->arrival_month, "day" => $q->main()->first()->arrival_day], $q->place()->select('place')->get()->toArray());

                $today = Carbon::now();

                $arrival = (reset($Dates)['check_in']);
                $departure = (end($Dates)['check_out']);

                $ArrivalDate = Carbon::create($arrival['year'], $arrival['month'], $arrival['day']);
                $DepartureDate = Carbon::create($departure['year'], $departure['month'], $departure['day']);

                // if (($start_date->month <= $DepartureDate->month) || ($end_date->month >= $DepartureDate->month)) {
                if (($start_date->month == $ArrivalDate->month) && ($start_date->year == $ArrivalDate->year)) {

                    $driver = $q->VehicleAllocation()->first()->Vehicle()->first()->owner()->first();

                    return [
                        'title' => QuotationManage::getReferenceID($q->quotation_no)[2] . " ($driver->first_name $driver->last_name)",
                        'start' => $ArrivalDate->toDateString(),
                        'end' => $DepartureDate->toDateString(),
                        'imageurl' => Image::getImageDirect($driver->id, '2x', 'driver')[0],
                        'color' => $DepartureDate < $today ? "#ccc" : (($DepartureDate >= $today && $ArrivalDate <= $today) ? '#4CAF50' : "#2196F3")

                    ];
                }


            });

        return array_values(array_filter($QuoteSearch->toArray()));

    }

    function loadConfirmation(Request $request) {
        $all = $request->all();

        if(isset($all['tour_no'])) {
            $QuotationArray = Quotation::getQuotation(false, $all['tour_no']);
            // dd($QuotationArray);
            return view('quotation.operation-tour-confirmation', ['QuotationArray' => $QuotationArray, 'Email' => false, 'Client' => true]);
        }
        return false;
    }

    function addReConfirmation(Request $request) {
        try {
            $all = $request->all();

            $reconfirm = new Reconfirm();
            $reconfirm->tour_id = $all['tour_no'];
            $reconfirm->re_confirm_date = $all['date'];
            $reconfirm->re_confirm_user = $all['user'];
            $reconfirm->flight_details = $all['flight_details'];
            $reconfirm->guest_number = $all['guest_number'];
            $reconfirm->payment_collection = $all['payment_collection'] ?? 0;
            $reconfirm->hotel_cruise_confirmation = $all['hotel_cruise_confirmation'] ?? 0;
            $reconfirm->costing_sheet = $all['costing_sheet'] ?? 0;
            $reconfirm->remarks = $all['remarks'];
            $reconfirm->save();

            return response()->view(" system.error.error-ajax", ["Message" => "Successfully reconfirmed ({$reconfirm->id})"]);
        } catch (\Exception $e) {
            return response()->view(" system.error.error-ajax", ["Message" => $e->getMessage()], 422);
        }
    }

    function saveReConfirmation(Request $request) {
        $all = $request->all();
        $QuotationManage = new QuotationManage;

        if(isset($all["tour_no"])) {
            $QuotationManage = $QuotationManage->where('quotation_no', $all["tour_no"]);
        }

        if(isset($all["country"])) {
            $QuotationManage = $QuotationManage->where('country', $all["country"]);
        }
        
        $QuotationManage = $QuotationManage->has('ReConfirm');
        $QuotationManage = $QuotationManage->with('ReConfirm');
        $QuotationManage = $QuotationManage->whereHas('ReConfirm', function ($q) use ($all) {
            if(isset($all["start_date"]))
                $q->where('re_confirm_date', '>=', $all['start_date']);
            if(isset($all["end_date"]))
                $q->where('re_confirm_date', '<=', $all['end_date']);
        });

        $reConfirms = $QuotationManage->get();

        return view("operation.re-confirm-view-item", ['reConfirms' => $reConfirms]);
    }


    function loadRestaurant(Request $request) {
        $all = $request->all();

        if(isset($all['tour_no'])) {
            $oldData = RestuarantSaveEmail::where('quotation_no', $all['tour_no'])->get();

            $QuotationArray = Quotation::getQuotation(false, $all['tour_no']);
            return view('quotation.operation-restaurant-voucher', ['QuotationArray' => $QuotationArray, 'Email' => false, 'Client' => true, 'oldData' => $oldData]);
        }
        return false;
    }

    function sendRestaurantVoucher(Request $request) {
        $all = $request->all();
        $EmailData = [];

        $EmailData['html'] = $all['html'];
            $EmailData['to_emails'] = $all['email'];
            $EmailData['type'] = "restaurant_voucher";
            Event::fire(new QuotationEmail($EmailData));
    }

    function saveRestaurant(Request $request) {
        $all = $request->all();

        foreach ($all['quotation_no'] as $key => $value) {
            if(isset($all['id'][$key])) {
                $RestuarantSaveEmail = RestuarantSaveEmail::find($all['id'][$key]);
            } else {
                $RestuarantSaveEmail = new RestuarantSaveEmail();
            }
            
            $RestuarantSaveEmail->quotation_no = $value;
            $RestuarantSaveEmail->voucher_type = $all['voucher_type'][$key];
            $RestuarantSaveEmail->voucher_date = $all['voucher_date'][$key];
            $RestuarantSaveEmail->guest_name = $all['guest_name'][$key];
            $RestuarantSaveEmail->booking_id = $all['booking_id'][$key];
            $RestuarantSaveEmail->reservation_for = $all['reservation_for'][$key];
            $RestuarantSaveEmail->reservation_date = $all['reservation_date'][$key];
            $RestuarantSaveEmail->restaurant_name = $all['restaurant_name'][$key];
            $RestuarantSaveEmail->meal_type = $all['meal_type'][$key];
            $RestuarantSaveEmail->guest_count = $all['guest_count'][$key];
            $RestuarantSaveEmail->guest_details = $all['guest_details'][$key];
            $RestuarantSaveEmail->meal_preference = $all['meal_preference'][$key];
            $RestuarantSaveEmail->preferred_time_slot = $all['preferred_time_slot'][$key];
            $RestuarantSaveEmail->guests_arriving_from = $all['guests_arriving_from'][$key];
            $RestuarantSaveEmail->chauffer_details = $all['chauffer_details'][$key];
            $RestuarantSaveEmail->remarks = $all['remarks'][$key];
            $RestuarantSaveEmail->email_address = $all['email_address'][$key];

            $RestuarantSaveEmail->save();
        }
    }
    
}
