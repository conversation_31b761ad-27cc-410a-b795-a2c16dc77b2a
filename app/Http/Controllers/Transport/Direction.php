<?php

namespace App\Http\Controllers\Transport;

use App\Http\Controllers\Controller;
use App\Model\Place\Place;
use App\Model\Place\Stop;
use App\Model\Weather\Weather;
use View;
use App\Model\Itinerary\Itinerary;
use Session;
use Carbon\Carbon;
use App\Model\Quotation\Quotation as QuotationQ;

/**
 * Class Direction
 * @package App\Http\Controllers\Transport
 */
class Direction extends Controller
{

    function getRelatedItems()
    {
        $seasons = ([2005, 2006, 2007]);
        $seasonWins = [];
        foreach ($seasons as $season) {
            $seasonWins[$season] = GameData::select('team')
                ->where('win', '1')
                ->where('stage', 'R16')
                ->where('round', 'final')
                ->where('elimination', '1')
                ->where('year', $season)
                ->get();
        }
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getDirectionInput()
    {


        return view("element.transport.direction.place-input");

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getTimeInput()
    {


        return view("element.time.flow", ['flowItem'=>false, 'disabled'=>false]);

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getSubVehicleCity($place)
    {


        return view("element.transport.direction.sub-vehicle-city", ['place' => $place]);

    }


    /**
     * @return mixed
     */
    function getStopDirectionInput()
    {

        $data['html'] = View::make("element.transport.direction.stop-input")->render();
        $data['data'] = [];

        return $data;

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getSuggestionItem()
    {

        return view("element.transport.direction.place-suggestion-item");

    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getSuggestionItemActivity()
    {

        return view("element.transport.direction.activity-suggestion-item");

    }

    /**
     * @param $PlaceID
     * @return array
     */
    function getNearestStop($PlaceID)
    {

        $Weather = new Weather();
        $Place = new Place();
        $data = [];


        $NearestStopList = $Place->getNearestStop($PlaceID);

        foreach ($NearestStopList as $PlaceItem) {

            $DataItem = [
                'id' => $PlaceItem->ID,
                'lat' => $PlaceItem->latitude,
                'lng' => $PlaceItem->longitude,
                'name' => $PlaceItem->name,
                'description' => $PlaceItem->description ?? $PlaceItem->latitude . ", " . $PlaceItem->longitude,
                'icon' => Place::getStopType(Stop::where("place", $PlaceItem->ID)->first()->type),
                'type' => "Place",
	            'country' => $PlaceItem->country,
	            'priority' => $PlaceItem->stop{0}->priority
            ];


            $DataItem['weather'] = $Weather->getPlaceWeather($PlaceItem->ID);
            $data[] = $DataItem;

        }
        
	    if (count($data) > 1) {
		    $priorityArray = "";
		    foreach ($data as $key => $dataItem) {
			    if ($dataItem['priority'] == 1) {
				    $priorityArray = $key;
				    break;
			    }
		    }
		    if($priorityArray !== "") {
			    array_unshift($data, $data[$priorityArray]);
			    unset($data[$priorityArray]);
		    }
	    }
	    $data = array_values($data);
	    
        return $data;

    }

    function getActivities() {
        $Quotation = new QuotationQ();
        $Itinerary = new Itinerary();
        $QuotationSession = Session::get('quotation');
        $FormattedItineraryData = $Itinerary->getFormattedItinerary($QuotationSession, 'all');
        $DaysDetail = QuotationQ::getTourDaysDetail($QuotationSession, true);
        $data = [];
        // dd($FormattedItineraryData);
        foreach ($FormattedItineraryData as $Day => $DayItineraryDetails) {
            $Date = Carbon::create($DaysDetail[$Day]['date']['year'], $DaysDetail[$Day]['date']['month'], $DaysDetail[$Day]['date']['day'], 14, 15, 16);
            
            if(!empty($DayItineraryDetails)) {
                foreach ($DayItineraryDetails as $DayItineraryDetail) {
                    if(!empty($DayItineraryDetail)) {
                        $d = [];

                        if ($DayItineraryDetail['type'] == "transport") {
                            if ($DayItineraryDetail['id'] == "arrival") {
                                $d['name'] = \App\Model\Place\Place::find($QuotationSession['place_full'][ array_key_first($QuotationSession['place_full'])])->name;
                                $d['type'] = 'arrival';
                                $d['id'] = $QuotationSession['place_full'][array_key_first($QuotationSession['place_full'])];
                                $data[] = $d;

                                $d['name'] = \App\Model\Hotel\Hotel::find($DayItineraryDetail['hotel'])->name ?? "Own Arrangement";
                                $d['type'] = 'hotel';
                                $d['id'] = $DayItineraryDetail['hotel'];
                                $data[] = $d;
                            } else if ($DayItineraryDetail['id'] == "departure") {
                                $d['name'] = \App\Model\Hotel\Hotel::find($DayItineraryDetail['hotel'])->name ?? "Own Arrangement";
                                $d['type'] = 'hotel';
                                $d['id'] = $DayItineraryDetail['hotel'];
                                $data[] = $d;

                                $d['name'] = \App\Model\Place\Place::find($QuotationSession['place_full'][ array_key_last($QuotationSession['place_full'])])->name;
                                $d['type'] = 'departure';
                                $d['id'] = $QuotationSession['place_full'][array_key_first($QuotationSession['place_full'])];
                                $data[] = $d;
                            }
                        } else if($DayItineraryDetail['type'] == 'attraction') {
                            $d['name'] = \App\Model\Hotel\Hotel::find($DayItineraryDetail['hotel'])->name ?? "Own Arrangement";
                            $d['type'] = 'hotel';
                            $d['id'] = $DayItineraryDetail['hotel'];
                            $data[] = $d;

                            $d['name'] = \App\Model\Place\Attraction::find($DayItineraryDetail['id'])->name;
                            $d['type'] = 'attraction';
                            $d['id'] = $DayItineraryDetail['id'];
                            $data[] = $d;

                        } else if($DayItineraryDetail['type'] == 'city_tour') {
                            $d['name'] = \App\Model\Hotel\Hotel::find($DayItineraryDetail['hotel'])->name ?? "Own Arrangement";
                            $d['type'] = 'hotel';
                            $d['id'] = $DayItineraryDetail['hotel'];
                            $data[] = $d;

                            $d['name'] = \App\Model\Place\CityTour::find($DayItineraryDetail['id'])->name;
                            $d['type'] = 'city_tour';
                            $d['id'] = $DayItineraryDetail['id'];
                            $data[] = $d;

                        } else if($DayItineraryDetail['type'] == 'excursion') {
                            $d['name'] = \App\Model\Hotel\Hotel::find($DayItineraryDetail['hotel'])->name ?? "Own Arrangement";
                            $d['type'] = 'hotel';
                            $d['id'] = $DayItineraryDetail['hotel'];
                            $data[] = $d;

                            $d['name'] = \App\Model\Place\Excursion::find($DayItineraryDetail['id'])->name;
                            $d['type'] = 'excursion';
                            $d['id'] = $DayItineraryDetail['id'];
                            $data[] = $d;
                        } else {
                            continue;
                        }    
                    }
                }
                
            }
        }

        return array_map("unserialize", array_unique(array_map("serialize", $data)));
    }
}


