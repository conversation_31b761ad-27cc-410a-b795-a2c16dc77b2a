<?php

namespace App\Http\Controllers\Transport;

use App\model\Place\ExtraMileage;
use App\Model\Place\Place;
use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Model\Hotel\Market;
use App\Model\QuotationManage\Quotation;
use App\Model\QuotationManage\QuotationMain;
use App\Model\QuotationManage\QuotationFlight;
use App\Model\QuotationManage\QuotationHotel as ManageHotels;
use App\Model\Quotation\QuotationHotel;


use App\Model\Transport\TransportBankDetail;
use App\Model\Transport\TransportChauffeur;
use App\Model\Transport\TransportChauffeurLanguage;
use App\Model\Transport\TransportChauffeurNationality;
use App\Model\Transport\TransportChauffeurType;
use App\Model\Transport\TransportDriver;
use App\Model\Transport\TransportLanguages;
use App\Model\Transport\TransportMemberContact;
use App\Model\Transport\TransportMembers;
use App\Model\Transport\TransportVehicle;
use App\Model\Transport\TransportVehicleAllocation;
use App\Model\Transport\VehicleBrand;
use App\Model\Transport\TransportVehicleChecklist;
use App\Model\Transport\TransportVehicleColor;
use App\Model\Transport\TransportVehicleDeposite;
use App\Model\Transport\TransportVehicleInspections;
use App\Model\Transport\VehicleType;
use App\Model\Vehicle\Vehicle;
use App\Model\Transport\TransportOutBoundDriverAllocation;

use Carbon\Carbon;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Intervention\Image\ImageManagerStatic as Image;
use Validator;
use Illuminate\Support\Collection;

use App\User;

/**
 * Class Transport
 * @package App\Http\Controllers\Transport
 */
class Transport extends Controller
{


//  Redirect to View with data
    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getTransport()
    {

        $data['city'] = Place::all();
        $data['nationality'] = TransportChauffeurNationality::all();
        $data['languages'] = TransportLanguages::all();
        $data['chaufferType'] = TransportChauffeurType::all();
        $data['vehicleType'] = VehicleType::all();
        $data['vehicleBrand'] = VehicleBrand::all();
        $data['vehicleColor'] = TransportVehicleColor::all();
        $data['vehicle'] = TransportVehicle::all();
        $data['vehicleCategory'] = Vehicle::all();
        $data['driver'] = TransportMembers::where('member_type', 4)->orWhere('member_type', 3)->orWhere('member_type', 2)->get();
        $data['chauffeur'] = TransportMembers::where('member_type', 3)->orWhere('member_type', 2)->get();
        $data['owner'] = TransportMembers::where('member_type', 1)->orWhere('member_type', 2)->get();

        return view("transport_manage.transport")->with('data', $data);
    }

    /**
     * @param Request $r
     * @return array
     */
    function allocateFlight(Request $r)
    {

        $Data = $r->input();
        $QuotationFlight = QuotationFlight::where('reference_id', $Data['quotation']);

        if ($QuotationFlight->first()) {

            $status = $QuotationFlight->update([
                "arrival" => new Carbon($Data['arrival_date'] . $Data['arrival_time']),
                "depature" => new Carbon($Data['depature_date'] . $Data['depature_time']),
                "flight_arrival" => $Data['flight_arrival'],
                "flight_depature" => $Data['flight_depature']
            ]);


            return [$status, "Flight details has updated!"];
        } else {

            $QuotationFlight = new QuotationFlight();
            $QuotationFlight->reference_id = $Data['quotation'];
            $QuotationFlight->arrival = new Carbon($Data['arrival_date'] . $Data['arrival_time']);
            $QuotationFlight->depature = new Carbon($Data['depature_date'] . $Data['depature_time']);
            $QuotationFlight->flight_arrival = $Data['flight_arrival'];
            $QuotationFlight->flight_depature = $Data['flight_depature'];
            return [$QuotationFlight->save(), "Flight details has inserted!"];
        }

    }

    /**
     * @param Request $r
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getMovementChart(Request $r)
    {


        $Data = $r->input();


        $Date = new Carbon($Data['date']);
        $Type = intval($Data['type']);
        $PlaceID = $Data['place'];


        $QuotationMain = new QuotationMain();
        $QuotationHotel = new QuotationHotel();

        $List = [];

        $QuoteSearch = Quotation::has('confirm')
            ->has('main')
            ->has('VehicleAllocation')
            ->whereHas('main', function ($query) use($Date) {
                $query->where('arrival_year',$Date->year)
                    ->where('arrival_month',$Date->month)
                    ->where('arrival_day',$Date->day);
            });
        $QuoteSearch = $QuoteSearch->get();

        foreach ($QuoteSearch as $K => $QuoteItem) {


                $ListCurrent['quotation_no'] = $QuoteItem->quotation_no;
                $ListCurrent['client'] = $QuoteItem->confirm()->first()->client_name;
                $ListCurrent['agent'] = $QuoteItem->confirm()->first()->agent;
                $ListCurrent['user'] = User::find($QuoteItem->main()->select('user')->first()->user)->name;
                $ListCurrent['market'] = Market::find($QuoteItem->pax()->select('market')->first()->market)->market;
                $ListCurrent['meal'] = implode('/ ', $QuotationHotel->getAvailableMealType($QuoteItem->hotel()->get()->toArray()));

                $ListCurrent['vehicle_allocation'] = $QuoteItem->VehicleAllocation()->get();

                $ListCurrent['flight'] = $QuoteItem->Flight()->first();


                $Main = $QuoteItem->main()->select('arrival_year', 'arrival_month', 'arrival_day')->first()->toArray();
                $Dates = $QuotationHotel->getHotelBookDates(["year" => $Main['arrival_year'], "month" => $Main['arrival_month'], "day" => $Main['arrival_day']], $CurrentQuote->place()->select('place')->get()->toArray());

                $ListCurrent['arrival'] = (reset($Dates)['check_in']);
                $ListCurrent['departure'] = (end($Dates)['check_out']);
                $ListCurrent['hotels'] = ManageHotels::where("reference_id", $CurrentQuote->ID)->get();

                $List[] = $ListCurrent;


        }


        return view("transport_manage.movement_chart", ['Data' => $List]);
    }


//  Create Vehicle With Chauffeur when creating a vehicle

    /**
     * @param $vehicle
     * @param $images
     * @return bool|int
     */
    public function createVehicle($vehicle, $images)
    {

        $count = TransportVehicle::where('vehicle_number', '=', $vehicle['vehicle_number'])->count();

        if ($count >= 1) {

            return false;

        } else {

            $newVehicle = TransportVehicle::insertGetId($vehicle);


            if ($newVehicle) {

                $dir = File::makeDirectory(public_path() . '/assets/image/transport/vehicle/' . $newVehicle . '/', $mode = 0777, true);


                if ($dir && $images) {

                    $path = public_path() . '/assets/image/transport/vehicle/' . $newVehicle . '/';

                    foreach ($images as $image) {

                        $this->uploadImages($image, $newVehicle, $path);

                    }

                }

                return $newVehicle;

            } else {

                return false;
            }

        }


    }


//  create human member when creating a vehicle. He may be owner or Chauffeur

    /**
     * @param $member
     * @param $contact
     * @param $image
     * @return int
     */
    public function newMember($member, $contact, $image)
    {

        $newMember = TransportMembers::insertGetId($member);
        $newContact = $this->newContacts($newMember, $contact);

        if ($newContact) {

            $dir = File::makeDirectory(public_path() . '/assets/image/transport/member/' . $newMember, $mode = 0777, true);

            if ($dir && $image) {

                $path = public_path() . '/assets/image/transport/member/' . $newMember;
                $this->uploadImages($image, $newMember, $path);
            }

            return $newMember;
        }

    }


//  Create a Chauffeur when creating vehicle with chauffeur

    /**
     * @param $chauffeur
     * @param $language
     * @param $insertedId
     * @return bool
     */
    public function newChauffeur($chauffeur, $language, $insertedId)
    {


        $newChauffeur = TransportChauffeur::insert($chauffeur);


        if ($newChauffeur && $language) {


            foreach ($language as $lang) {

                $newChaufLanguage = new TransportChauffeurLanguage();
                $newChaufLanguage->language_id = $lang;
                $newChaufLanguage->member_id = $insertedId;
                $newChaufLanguage->save();
            }

            return true;
        }
    }


// Inspection details about vehicle

    /**
     * @param $vehicle
     * @param $inspections
     * @return TransportVehicleInspections
     * @throws \Exception
     */
    public function newInspection($vehicle, $inspections)
    {

        TransportVehicleInspections::where('vehicle_id', $vehicle)->delete();

        if (empty($inspections)) {

            $inspection = 44;

            $newInspection = new TransportVehicleInspections();
            $newInspection->vehicle_id = $vehicle;
            $newInspection->inspection_id = $inspection;

            $newInspection->save();
        } else {

            foreach ($inspections as $inspection) {

                $newInspection = new TransportVehicleInspections();
                $newInspection->vehicle_id = $vehicle;
                $newInspection->inspection_id = $inspection;

                $newInspection->save();


            }
        }


        return $newInspection;
    }


// Checklist about vehicle

    /**
     * @param $vehicle
     * @param $checklist
     * @return TransportVehicleChecklist
     * @throws \Exception
     */
    public function newChecklist($vehicle, $checklist)
    {

        TransportVehicleChecklist::where('vehicle_id', $vehicle)->delete();


        if ($checklist == 14) {

            $newChecklist = new TransportVehicleChecklist();
            $newChecklist->vehicle_id = $vehicle;
            $newChecklist->checklist_id = $checklist;

            $newChecklist->save();

        } else {


            foreach ($checklist as $check) {

                $newChecklist = new TransportVehicleChecklist();
                $newChecklist->vehicle_id = $vehicle;
                $newChecklist->checklist_id = $check;
                $newChecklist->save();

            }
        }


        return $newChecklist;


    }


// Bank account details about vehicle

    /**
     * @param $bank_details
     * @return bool
     */
    public function newBankAccount($bank_details)
    {

        $newBankAccount = TransportBankDetail::insert($bank_details);

        return $newBankAccount;
    }


// contact numbers for owners and chauffeurs

    /**
     * @param $newMember
     * @param $contact
     * @return bool
     */
    public function newContacts($newMember, $contact)
    {

        foreach ($contact as $contacts) {

            $reContact = "+94" . $contacts;

            $newContact = new TransportMemberContact();
            $newContact->member_id = $newMember;
            $newContact->contact = $reContact;
            $newContact->save();
        }

        return true;

    }


// Creating new driver

    /**
     * @param $driver
     * @return bool
     */
    public function newDriver($driver)
    {

        $newDriver = TransportDriver::insert($driver);

        if ($newDriver) {

            return $newDriver;

        } else {

            return false;
        }


    }


// Main function for creating a vehicle

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function newVehicle(Request $request)
    {

        DB::beginTransaction();

        $reg_date = new Carbon($request->get('reg_date'));

        $vehicle = [

            'vehicle_number' => $request->get('vehicle_number'),
            'vehicle_category' => $request->get('vehicle_type'),
            'vehicle_brand' => $request->get('vehicle_brand'),
            'vehicle_model' => $request->get('vehicle_model'),
            'vehicle_pax' => $request->get('vehicle_pax'),
            'color' => $request->get('vehicle_color'),
            'city' => $request->get('vehicle_location'),
            'model_year' => $request->get('model_year'),
            'remarks' => $request->get('vehicle_remarks'),
            'registration_date' => $reg_date,
            'availability' => 1,
        ];

        if ($request->hasFile('vehicle_image')) {

            $image = $request->file('vehicle_image');

        } else {

            $image = "";
        }

        $newVehicle = $this->createVehicle($vehicle, $image);

        if ($newVehicle == false) {

            return [$this, "The vehicle you have entered is already registered with us."];
        } else {

            $lastVehicle = $newVehicle;

            $inspection = $request->get('inspection');
            $newInspection = $this->newInspection($lastVehicle, $inspection);


            $checklist = $request->get('checklist');

            if (!$checklist) {

                $checklist = 14;

            }

            $newChecklist = $this->newChecklist($lastVehicle, $checklist);
        }

        if ($newChecklist && $newInspection && $newVehicle) {

            DB::commit();
            return [$this, "New Vehicle registered successfully"];

        } else {

            DB::rollBack();
            return [$this, "Whoops..! Something went wrong."];
        }


    }


//Create new Owner

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function newOwner(Request $request)
    {

        DB::beginTransaction();

        $this->validate($request, [
            'f_name' => 'required',
            'email' => 'email',
        ]);


        $member = array(

            'f_name' => $request->get('f_name'),
            'l_name' => $request->get('l_name'),
            'nick_name' => $request->get('nick_name'),
            'address' => $request->get('address'),
            'email' => $request->get('email'),
            'nic' => $request->get('nic'),
            'member_type' => $request->get('owner_type'),
            'remarks' => $request->get('owner_remarks'),
            'registration_date' => time(),
        );

        if ($request->hasFile('owner_image')) {

            $image = $request->file('owner_image');

        } else {

            $image = "";
        }

        $contact = $request->get('contact_number');

        $newMember = $this->newMember($member, $contact, $image);

        $bankDetails = [

            'member_id' => $newMember,
            'bank_name' => $request->get('bank_name'),
            'branch' => $request->get('bank_branch'),
            'account_number' => $request->get('account_no'),

        ];

        if ($request->hasFile('passbook')) {

            $passbook = $request->file('passbook');
            $name = "passbook";
            $path = public_path() . '/assets/image/transport/member/' . $newMember . '/';

            $this->uploadImages($passbook, $name, $path);

        }

        $newBankAccount = $this->newBankAccount($bankDetails);

        if ($request->get('owner_type') == 2) {

            $memberChauffeur = TransportMembers::find($newMember);
            $memberChauffeur->member_type = 2;
            $memberChauffeur->save();

            $chauffeur = [

                'chauffer_id' => $newMember,
                'type' => $request->get('chauffer_type'),
                'speciality' => $request->get('chauffer_speciality'),
                'chauffer_license' => $request->get('license_available'),
                'chauffer_license_no' => $request->get('chauffer_license_no'),
                'uniform' => $request->get('chauffer_uniform'),
                'nationality' => $request->get('chauffer_nationality'),
                'remarks' => $request->get('chauffer_remarks'),
                'rate_per_day' => $request->get('rate_per_day'),
                'tip_per_day' => $request->get('tip_per_day'),
            ];

            $language = $request->get('chauffer_languages');

            $newChauffeur = $this->newChauffeur($chauffeur, $language, $newMember);


            $driver = [

                'member_id' => $newMember,
                'license_start_date' => $request->get('license_start_date'),
                'license_end_date' => $request->get('license_end_date'),
                'license_no' => $request->get('driving_license_no'),
            ];

            $newDriver = $this->newDriver($driver);


            if ($newMember && $newChauffeur && $newDriver && $newBankAccount) {

                DB::commit();

                return [$this, "Owner registered successfully..!"];
            } else {

                DB::rollBack();
                return [$this, "Whoops..! Something went wrong"];
            }

        } else {

            DB::commit();
            return [$this, "Owner registered successfully..!"];

        }


    }


// Main function for creating a chauffeur without vehicle

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function newOnlyChauffeur(Request $request)
    {

        DB::beginTransaction();

        $this->validate($request, [
            'f_name' => 'required',
            'email' => 'email',
        ]);
        $member = array(

            'f_name' => $request->get('f_name'),
            'l_name' => $request->get('l_name'),
            'nick_name' => $request->get('nick_name'),
            'address' => $request->get('address'),
            'email' => $request->get('email'),
            'nic' => $request->get('nic'),
            'member_type' => 3,
            'remarks' => $request->get('guide_remarks'),
            'city' => $request->get('chauffer_city'),
            'registration_date' => time(),
        );

        $contact = $request->get('contact_number');

        $language = $request->get('chauffer_languages');

        if ($request->hasFile('member_image')) {

            $image = $request->file('owner_image');

        } else {

            $image = "";
        }

        $newMember = $this->newMember($member, $contact, $image);


        $chauffeur = [

            'chauffer_id' => $newMember,
            'type' => $request->get('chauffer_type'),
            'speciality' => $request->get('chauffer_speciality'),
            'chauffer_license' => $request->get('license_available'),
            'chauffer_license_no' => $request->get('chauffer_license_no'),
            'uniform' => $request->get('chauffer_uniform'),
            'nationality' => $request->get('chauffer_nationality'),
            'remarks' => $request->get('chauffer_remarks'),
            'rate_per_day' => $request->get('rate_per_day'),
            'tip_per_day' => $request->get('tip_per_day'),
        ];


        $newChauffeur = $this->newChauffeur($chauffeur, $language, $newMember);


        $bankDetails = [

            'member_id' => $newMember,
            'bank_name' => $request->get('bank_name'),
            'branch' => $request->get('bank_branch'),
            'account_number' => $request->get('account_no'),

        ];

        if ($request->hasFile('passbook')) {

            $passbook = $request->file('passbook');
            $name = "passbook";
            $path = public_path() . '/assets/image/transport/member/' . $newMember . '/';

            $this->uploadImages($passbook, $name, $path);

        }

        $this->newBankAccount($bankDetails);

        $driver = [

            'member_id' => $newMember,
            'license_start_date' => $request->get('license_start_date'),
            'license_end_date' => $request->get('license_end_date'),
            'license_no' => $request->get('driving_license_no'),
        ];

        $newDriver = $this->newDriver($driver);

        if ($newMember && $newChauffeur && $newDriver) {

            DB::commit();
            return [$this, "Chauffeur registered successfully..!"];

        } else {

            DB::rollBack();
            return [$this, "Whoops..! Something went wrong."];
        }


    }


// Main Function for create a driver without vehicle or chauffeur

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function newOnlyDriver(Request $request)
    {

        DB::beginTransaction();

        $this->validate($request, [
            'f_name' => 'required',
            'email' => 'email',
        ]);

        $member = array(

            'f_name' => $request->get('f_name'),
            'l_name' => $request->get('l_name'),
            'nick_name' => $request->get('nick_name'),
            'address' => $request->get('address'),
            'email' => $request->get('email'),
            'nic' => $request->get('nic'),
            'member_type' => 4,
            'remarks' => $request->get('driver_remarks'),
            'city' => $request->get('driver_city'),
            'registration_date' => time(),
        );

        $contact = $request->get('contact_number');
        $image = $request->file('member_image');

        $newMember = $this->newMember($member, $contact, $image);

        $driver = [

            'member_id' => $newMember,
            'license_start_date' => $request->get('license_start_date'),
            'license_end_date' => $request->get('license_end_date'),
            'license_no' => $request->get('driving_license_no'),
        ];

        $newDriver = $this->newDriver($driver);

        if ($newMember && $newDriver) {

            DB::commit();
            return [$this, "Driver registered successfully..!"];

        } else {

            DB::rollBack();
            return [$this, "Whoops..! Something went wrong."];
        }

    }


// Make Refundable Deposit. Deposit make under vehicle

    /**
     * @param Request $request
     * @return array
     */
    public function makeDeposit(Request $request)
    {

        $deposit = [

            'vehicle_id' => $request->get('vehicle_id'),
            'installment' => $request->get('installment'),
            'tour_id' => $request->get('tour_id'),
            'amount' => $request->get('amount'),
            'deposit_type' => $request->get('deposit_type'),
        ];

        $newDeposit = TransportVehicleDeposite::insert($deposit);

        if ($newDeposit) {

            return [$this, "Refundable deposit made successfully"];

        } else {

            return [$this, "Whoops..! Something went wrong."];
        }
    }


// Allocate Driver to vehicle

    /**
     * @param Request $request
     * @return bool|int
     */
    public function allocateDriver(Request $request)
    {

        $driver = $request->get('driver_id');
        $vehicle = $request->get('vehicle_id');

        $newVehicle = TransportVehicle::find($vehicle);

        $newVehicle->driver = $driver;

        if ($newVehicle->save()) {

            return 1;

        } else {

            return false;
        }

    }


// Allocate Chauffeur to vehicle

    /**
     * @param Request $request
     * @return bool|int
     */
    public function allocateChauffeur(Request $request)
    {

        $chauffeur = $request->get('chauffeur_id');
        $vehicle = $request->get('vehicle_id');

        $newVehicle = TransportVehicle::find($vehicle);

        $newVehicle->guide = $chauffeur;

        if ($newVehicle->save()) {

            return 1;

        } else {

            return false;
        }

    }


// Get Available vehicle

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getAvailableVehicles()
    {

        $vehicle = new TransportVehicle();

        $result = $vehicle->getAvailableVehicles();

        $members['owners'] = TransportMembers::where('member_type', 1)->orWhere('member_type', 2)->get();
        $members['chauffeurs'] = TransportMembers::where('member_type', 2)->orWhere('member_type', 3)->get();
        $members['drivers'] = TransportMembers::where('member_type', 2)->orWhere('member_type', 3)->orWhere('member_type', 4)->get();

        return view('transport_manage.available_drivers')->with('vehicles', $result)->with('members', $members);

    }


// Get All vehicle

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getAllVehicles()
    {

        $result = TransportVehicle::all();

        $members['owners'] = TransportMembers::where('member_type', 1)->orWhere('member_type', 2)->get();
        $members['chauffeurs'] = TransportMembers::where('member_type', 2)->orWhere('member_type', 3)->get();
        $members['drivers'] = TransportMembers::where('member_type', 2)->orWhere('member_type', 3)->orWhere('member_type', 4)->get();

        return view('transport_manage.all_vehicles')->with('vehicles', $result)->with('members', $members);

    }


// Upload Images of vehicle

    /**
     * @param $image
     * @param $insertedId
     * @param $path
     * @return bool
     */
    public function uploadImages($image, $insertedId, $path)
    {


        $new_image = Image::make($image);

        $new_image->resize(300, 300);

        if ($new_image->save($path . '/' . $insertedId . '.jpg')) {

            return true;

        } else {

            return false;
        }


    }


// Get Details of a vehicle

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getVehicleInfo($id)
    {


        $vehicle['vehicle'] = DB::table('apple_transport_vehicles')
            ->join('apple_vehicle_rate', 'apple_vehicle_rate.ID', '=', 'apple_transport_vehicles.vehicle_category')
            ->join('apple_transport_vehicle_brand', 'apple_transport_vehicle_brand.id', '=', 'apple_transport_vehicles.vehicle_brand')
            ->join('apple_transport_vehicle_color', 'apple_transport_vehicle_color.id', '=', 'apple_transport_vehicles.color')
            ->where('apple_transport_vehicles.id', $id)
            ->select('apple_transport_vehicles.*', 'apple_transport_vehicles.id as vehicle_id', 'apple_vehicle_rate.name as vehicle_type', 'apple_transport_vehicle_brand.*', 'apple_transport_vehicle_color.*')
            ->get();


        foreach ($vehicle['vehicle'] as $vehicles) {

            $vehicle_id = $vehicles->vehicle_id;
            $owner_id = $vehicles->owner;
            $chauffeur_id = $vehicles->guide;
            $driver_id = $vehicles->driver;

        }

        $vehicle['owner'] = $this->getMemberDetails($owner_id);

        $vehicle['chauffeur'] = $this->getMemberDetails($chauffeur_id);

        $vehicle['chauffeur_details'] = $this->getChauffeur($chauffeur_id);

        $vehicle['chauffeur_languages'] = $this->getChauffeurLanguages($chauffeur_id);

        $vehicle['driver'] = $this->getDriver($driver_id);

        $vehicle['inspections'] = $this->getVehicleInspection($vehicle_id);

        $vehicle['checklist'] = $this->getVehicleChecklist($vehicle_id);

        return view('transport_manage.vehicle_info')->with('vehicle', $vehicle);

    }


// Get Chauffeur related info

    /**
     * @param $chauffeur_id
     * @return Collection
     */
    public function getChauffeur($chauffeur_id)
    {

        $chauffeur_details = DB::table('apple_transport_chauffer')
            ->join('apple_transport_chauffer_type', 'apple_transport_chauffer_type.id', '=', 'apple_transport_chauffer.type')
            ->join('apple_transport_chauffer_nationality', 'apple_transport_chauffer_nationality.id', '=', 'apple_transport_chauffer.nationality')
            ->where('apple_transport_chauffer.chauffer_id', $chauffeur_id)
            ->select('apple_transport_chauffer.*', 'apple_transport_chauffer_type.type as chauffeur_type', 'apple_transport_chauffer_nationality.nationality as chauffeur_nationality')
            ->get();

        return $chauffeur_details;

    }


// Get comon details of members

    /**
     * @param $id
     * @return Collection
     */
    public function getMemberDetails($id)
    {

        $member = TransportMembers::where('id', $id)->get();

        return $member;

    }


// Get chauffeur Languages

    /**
     * @param $chauffeur_id
     * @return Collection
     */
    public function getChauffeurLanguages($chauffeur_id)
    {

        $chauffeur_languages = DB::table('apple_transport_chauffer_languages')
            ->join('apple_transport_languages', 'apple_transport_languages.id', '=', 'apple_transport_chauffer_languages.language_id')
            ->where('apple_transport_chauffer_languages.member_id', $chauffeur_id)
            ->pluck('apple_transport_languages.language');

        return $chauffeur_languages;
    }


// Get Drivers Details

    /**
     * @param $driver_id
     * @return Collection
     */
    public function getDriver($driver_id)
    {

        $driver = DB::table('apple_transport_members_details')
            ->join('apple_transport_drivers', 'apple_transport_drivers.member_id', '=', 'apple_transport_members_details.id')
            ->where('apple_transport_members_details.id', $driver_id)
            ->select('apple_transport_members_details.*', 'apple_transport_drivers.*', 'apple_transport_members_details.id as driver_id')
            ->get();

//        $driver = TransportMembers::find($driver_id);

        return $driver;
    }


// Get vehicle inspections details

    /**
     * @param $vehicle_id
     * @return Collection
     */
    public function getVehicleInspection($vehicle_id)
    {

        $inspections = DB::table('apple_transport_vehicle_inspections')
            ->join('apple_transport_inspections', 'apple_transport_inspections.id', '=', 'apple_transport_vehicle_inspections.inspection_id')
            ->where('apple_transport_vehicle_inspections.vehicle_id', $vehicle_id)
            ->select('apple_transport_vehicle_inspections.*', 'apple_transport_inspections.inspections')
            ->get();

        return $inspections;

    }


    // Get vehicle checklist details

    /**
     * @param $vehicle_id
     * @return Collection
     */
    public function getVehicleChecklist($vehicle_id)
    {

        $checklist = TransportVehicleChecklist::where('vehicle_id', $vehicle_id)->get();

        return $checklist;

    }


// Get vehicle not have Inspections

    /**
     * @param $vehicle
     * @return Collection
     */
    public function getNotInspect($vehicle)
    {

        $not_inspected = DB::table('apple_transport_inspections')
            ->whereNotExists(function ($query) {
                $query->select('apple_transport_vehicle_inspections.*')
                    ->from('apple_transport_vehicle_inspections')
                    ->whereRaw('apple_transport_inspections.id = apple_transport_vehicle_inspections.inspection_id AND apple_transport_vehicle_inspections.vehicle_id =70');
            })
            ->get();

        return $not_inspected;
    }


// get filtered vehicles

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getFilteredVehicles(Request $request)
    {

        $vehicle_number = $request->vehicle_number;
        $vehicle_category = $request->vehicle_category;
        $vehicle_owner = $request->vehicle_owner;
        $vehicle_chauffeur = $request->vehicle_chauffeur;
        $vehicle_driver = $request->vehicle_driver;
        $model_year = $request->model_year;

        $result = TransportVehicle::where('vehicle_number', $vehicle_number)
            ->orWhere('vehicle_category', $vehicle_category)
            ->orWhere('owner', $vehicle_owner)
            ->orWhere('guide', $vehicle_chauffeur)
            ->orWhere('driver', $vehicle_driver)
            ->orWhere('model_year', '>=', $model_year)
            ->get();

        return view('transport_manage.filtered')->with('vehicles', $result);

    }

    // get filtered vehicles

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getAvailableFilteredVehicles(Request $request)
    {

        $vehicle_number = $request->vehicle_number;
        $vehicle_category = $request->vehicle_category;
        $vehicle_owner = $request->vehicle_owner;
        $vehicle_chauffeur = $request->vehicle_chauffeur;
        $vehicle_driver = $request->vehicle_driver;
        $model_year = $request->model_year;

        $result = TransportVehicle::where('availability', 1)
            ->orWhere('vehicle_number', $vehicle_number)
            ->orWhere('vehicle_category', $vehicle_category)
            ->orWhere('owner', $vehicle_owner)
            ->orWhere('guide', $vehicle_chauffeur)
            ->orWhere('driver', $vehicle_driver)
            ->orWhere('model_year', '>=', $model_year)
            ->get();

        return view('transport_manage.filtered')->with('vehicles', $result);

    }


// Get all available vehicles

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getAllocatableVehicles()
    {

        $vehicles = DB::table('apple_transport_vehicles')
            ->join('apple_transport_members_details', 'apple_transport_members_details.id', '=', 'apple_transport_vehicles.owner')
            ->join('apple_vehicle_rate', 'apple_vehicle_rate.ID', '=', 'apple_transport_vehicles.vehicle_category')
            ->join('apple_transport_vehicle_brand', 'apple_transport_vehicle_brand.id', '=', 'apple_transport_vehicles.vehicle_brand')
            ->where('apple_transport_vehicles.availability', 1)
            ->where('apple_transport_vehicles.vehicle_category', 2)
            ->select('apple_transport_vehicles.id as vehicle_id', 'apple_transport_vehicles.*', 'apple_transport_members_details.id as member_id', 'apple_transport_members_details.*', 'apple_vehicle_rate.name as vehicle_type', 'apple_transport_vehicle_brand.*')
            ->get();


        $driver = TransportDriver::all();

        return view('transport_manage.allocatable_vehicle')->with('vehicles', $vehicles)->with('drivers', $driver);


    }


// Get confirmed quotation List and available vehicles for tour allocation

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getQuotationListAllocation()
    {

        $date = Carbon::parse('now');
        $day = $date->day;
        $month = $date->month;
        $year = $date->year;

        $quotation = Quotation::where('status', 2)->get();

        $vehicles['car'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 1)->get();
        $vehicles['van1'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 2)->get();
        $vehicles['van2'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 3)->get();
        $vehicles['miniCoach'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 4)->get();
        $vehicles['mediumCoach'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 5)->get();
        $vehicles['largCoach'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 6)->get();
        $vehicles['shuttle'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 7)->get();

        return view('transport_manage.quotation')->with('quotationLists', $quotation)->with('vehicleList', $vehicles);
    }


// Get selected quotation info for view before allocation

    /**
     * @param int $quotation_id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getQuotationInfo($quotation_id = 0)
    {

        $quotationLists = Quotation::find($quotation_id);

        $pax['adult'] = $quotationLists->Pax->adult;
        $pax['cwb'] = $quotationLists->Pax->cwb;

        $getVehiclePax = new Vehicle();

        $vehicleCategory = $getVehiclePax->getPaxToVehicle($quotationLists);

        $paxCategory = $vehicleCategory->ID;

        $vehicles = TransportVehicle::where('availability', 1)->where('vehicle_category', $paxCategory)->get();

        return view('transport_manage.quotation_info')->with('vehicles', $vehicles)->with('quotations', $quotationLists);
    }


// Get vehicle details when select a vehicle in tour allocation page

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getQuotationVehicleInfo($id)
    {

        $vehicle = TransportVehicle::find($id);

        $chauffeur_language = $this->getChauffeurLanguages($vehicle->guide);


        return view('transport_manage.quotation_vehicle')->with('vehicle', $vehicle)->with('chuaffeur_language', $chauffeur_language);

    }


// Allocate vehicles to confirmed quotations

    /**
     * @param Request $request
     * @return array
     */
    public function allocateTour(Request $request)
    {


        $validator = Validator::make($request->all(), [
            'quotation_list' => 'required',
            'quotation_vehicle_list' => 'required',
        ]);

        if ($validator->fails()) {

            return [$validator, "Select a Quotation and A Vehicle"];

        } else {


            $tour = $request->get('quotation_list');
            $vehicles = $request->get('quotation_vehicle_list');

            $data = array();

            if (is_array($vehicles)) {

                foreach ($vehicles as $vehicle) {

                    $vehicle_info = TransportVehicle::find($vehicle);

                    $owner = $vehicle_info->owner;
                    $guide = $vehicle_info->guide;
                    $driver = $vehicle_info->driver;

                    $data[] = array(
                        'reference_id' => $tour,
                        'vehicle_id' => $vehicle,
                        'owner_id' => $owner,
                        'chuaffeur_id' => $guide,
                        'driver_id' => $driver
                    );
                }

            } else {

                $vehicle_info = TransportVehicle::find($vehicles);

                $owner = $vehicle_info->owner;
                $guide = $vehicle_info->guide;
                $driver = $vehicle_info->driver;

                $data[] = array(

                    'reference_id' => $tour,
                    'vehicle_id' => $vehicles,
                    'owner_id' => $owner,
                    'chuaffeur_id' => $guide,
                    'driver_id' => $driver
                );
            }


            $allocated = TransportVehicleAllocation::insert($data);

            if ($allocated) {

                $availability = 0;

                if ($this->changeVehicleAvailability($data, $availability)) {

                    return [$this, "Vehicle allocated successfully..!"];

                } else {

                    return [$this, "Whoops..! Something went wrong."];

                }

            } else {

                return [$this, "Whoops..! Something went wrong."];

            }

        }

    }

// Allocate vehicles to confirmed quotations

    /**
     * @param Request $request
     * @return array
     */
    public function addAllocationMoreVehicle(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'add_quotation_quotation_id' => 'required',
            'add_quotation_vehicle_id' => 'required',
            'add_quotation_start_date' => 'required',
        ]);

        if ($validator->fails()) {

            return [$validator, "Vehicle and Start Date are required"];

        } else {


            $FormData = $request->input();

            $vehicle_info = TransportVehicle::find($FormData['add_quotation_vehicle_id']);

            $owner = $vehicle_info->owner;
            $guide = $vehicle_info->guide;
            $driver = $vehicle_info->driver;

            $data[] = array(

                'reference_id' => $FormData['add_quotation_quotation_id'],
                'vehicle_id' => $FormData['add_quotation_vehicle_id'],
                'owner_id' => $owner,
                'chuaffeur_id' => $guide,
                'driver_id' => $driver,
                'start_date' => $FormData['add_quotation_start_date']
            );


            $allocated = TransportVehicleAllocation::insert($data);

            if ($allocated) {

                $availability = 0;

                if ($this->changeVehicleAvailability($data, $availability)) {

                    return [$this, "Vehicle allocated successfully..!"];

                } else {

                    return [$this, "Whoops..! Something went wrong"];
                }

            } else {

                return [$this, "Whoops..! Something went wrong"];

            }
        }

    }

// Change vehicles from allocated quotations

    /**
     * @param Request $request
     * @return array
     */
    public function changeAllocatedTour(Request $request)
    {


        $validator = Validator::make($request->all(), [
            'reason' => 'required',
            'leave_date' => 'required',
        ]);

        if ($validator->fails()) {

            return [$validator, "Reason And Date is required..!"];

        } else {

            $Data = $request->input();

            $vehicle_info = TransportVehicleAllocation::whereReferenceIdAndVehicleId($Data['quote_id'], $Data['vehicle_id']);

            $update = $vehicle_info->update([
                "end_date" => Carbon::parse($Data['leave_date']),
                "reason" => $Data['reason']
            ]);

            return [$update, "Vehicle Changed Successfully..!"];

        }

    }


// Change vehicle availability status

    /**
     * @param $vehicles
     * @param $availability
     * @return bool|int
     */
    public function changeVehicleAvailability($vehicles, $availability)
    {

        if (is_array($vehicles)) {

            foreach ($vehicles as $vehicle) {

                $vehicle_status_change = TransportVehicle::whereIn('id', $vehicle)->update(['availability' => $availability]);
            }

            if ($vehicle_status_change) {

                return true;

            } else {

                return false;
            }

        } else {

            $vehicle_status_change = TransportVehicle::where('id', $vehicles)->update(['availability' => $availability]);

            if ($vehicle_status_change) {

                return 1;

            } else {

                return 0;
            }
        }


    }

// Get Allocated Tour ID and Vehicle ID List

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public static function getAllocationHistory()
    {

        $tourList = TransportVehicleAllocation::groupBy('reference_id')->get();
        $vehicleList = TransportVehicleAllocation::groupBy('vehicle_id')->get();

        return view('transport_manage.allocation_history')->with('tourList', $tourList)->with('vehicleList', $vehicleList);
    }

    /**
     * @param $reference_id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getAllocatedQuotationHistory($reference_id)
    {

        $quotationInfo['quotation'] = Quotation::where('ID', $reference_id)->get();

        $quotationInfo['allocated_vehicles'] = TransportVehicleAllocation::where('reference_id', $reference_id)->get();

        $vehicles['car'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 1)->get();
        $vehicles['van1'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 2)->get();
        $vehicles['van2'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 3)->get();
        $vehicles['miniCoach'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 4)->get();
        $vehicles['mediumCoach'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 5)->get();
        $vehicles['largCoach'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 6)->get();
        $vehicles['shuttle'] = TransportVehicle::where('availability', 1)->where('vehicle_category', 7)->get();

        return view('transport_manage.allocation_history_quotation')->with('quotation', $quotationInfo)->with('vehicleList', $vehicles);

    }

    /**
     * @param $vehicle_id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getAllocatedVehicleHistory($vehicle_id)
    {


        $data['vehicle'] = TransportVehicleAllocation::where('vehicle_id', $vehicle_id)->groupBy('vehicle_id')->get();

        $data['vehicleList'] = TransportVehicleAllocation::where('vehicle_id', $vehicle_id)->get();

        foreach ($data['vehicleList'] as $vehicle) {

            $data['quotation'][$vehicle->reference_id] = Quotation::where('ID', $vehicle->reference_id)->get();

        }

        return view('transport_manage.allocation_history_vehicle')->with('data', $data);

    }

//    Get All Vehicles List

    /**
     * @param int $id
     * @return TransportVehicle|TransportVehicle[]|\Illuminate\Database\Eloquent\Collection|mixed
     */
    private function getVehicleList($id = 0)
    {

        if ($id == 0) {

            $vehicles = TransportVehicle::all();

        } else {

            $vehicles = TransportVehicle::find($id);
        }

        return $vehicles;

    }

//    Get All Owners List

    /**
     * @param int $id
     * @return TransportMembers|Collection|mixed
     */
    private function getOwnerList($id = 0)
    {


        if ($id == 0) {

            $owners = TransportMembers::where('member_type', 1)->orWhere('member_type', 2)->get();

        } else {

            $owners = TransportMembers::find($id);
        }

        return $owners;

    }

//    Get All Chauffeur List

    /**
     * @param int $id
     * @return Collection
     */
    private function getChauffeurList($id = 0)
    {


        if ($id == 0) {

            $chauffeur = TransportMembers::where('member_type', 3)->get();

        } else {

            $chauffeur = TransportMembers::where('member_id', $id)->where('member_type', 3)->get();
        }

        return $chauffeur;

    }

//    Get All Chauffeur List

    /**
     * @param int $id
     * @return Collection
     */
    private function getDriverList($id = 0)
    {


        if ($id == 0) {

            $driver = TransportMembers::where('member_type', 4)->get();

        } else {

            $driver = TransportMembers::where('member_id', $id)->where('member_type', 4)->get();
        }

        return $driver;

    }

//    Allocate owner / chauffeur / driver to vehicle

    /**
     * @param int $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getVehicleInfoToAllocate($id = 0)
    {

        $vehicleList = $this->getVehicleList($id);

        return view('transport_manage.vehicle_to_allocate')->with('vehicle', $vehicleList);


    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getOwnerInfoToAllocation($id)
    {

        $owner = $this->getOwnerList($id);

        return view('transport_manage.owner_to_allocate')->with('owner', $owner);

    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getChauffeurInfoToAllocation($id)
    {

        $chauffeur = TransportChauffeur::where('chauffer_id', $id)->get();

        return view('transport_manage.chauffeur_to_allocate')->with('chauffeurs', $chauffeur);

    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getDriverInfoToAllocation($id)
    {

        $drivers = $driver = TransportMembers::find($id);

        return view('transport_manage.driver_to_allocate')->with('driver', $drivers);

    }

    /**
     * @param Request $request
     * @return array
     */
    public function vehicleComplete(Request $request)
    {

        $vehicle = TransportVehicle::find($request->vehicle_id_allocate);

        $vehicle->owner = $request->owner_id_allocate;
        $vehicle->guide = $request->chauffeur_id_allocate;
        $vehicle->driver = (!empty($request->driver_id_allocate) ? $request->driver_id_allocate : "");

        if ($vehicle->save()) {

            return [$this, "Vehicle coupled successfully"];

        } else {

            return [$this, "Whoops..! Something went wrong"];
        }


    }

//  end Allocate owner / chauffeur / driver to vehicle


    /**
     * @param $vehicle_id
     * @param $reference_id
     * @return bool|null
     * @throws \Exception
     */
    public function removeQuotationVehicle($vehicle_id, $reference_id)
    {

        $result = TransportVehicleAllocation::where('reference_id', $reference_id)->where('vehicle_id', $vehicle_id)->delete();

        return $result;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getAllQuotationList()
    {

        $quotations = Quotation::where('status', 2)->get();

        return view('transport_manage.quotation_list')->with('quotations', $quotations);


    }

    /**
     * @param Request $request
     * @return array
     */
    function getQuotationList(Request $request)
    {

        $QuotationMain = new QuotationMain();
        $QuotationHotel = new QuotationHotel();

        $Data = $request->input();

        $List = [];
        $QuoteSearch = $QuotationMain->where('arrival_year', $Data['year'])->where('arrival_month', $Data['month'])->where('arrival_day', $Data['day'])->get();
        foreach ($QuoteSearch as $K => $QuoteItem) {

            $CurrentQuote = $QuoteItem->quotation()->first();

            if ($CurrentQuote->confirm()->first()) {

                $ListCurrent['quotation_no'] = $CurrentQuote->quotation_no;
                $ListCurrent['client'] = $CurrentQuote->confirm()->first()->client_name;
                $ListCurrent['agent'] = $CurrentQuote->confirm()->first()->agent;
                $ListCurrent['user'] = User::find($CurrentQuote->main()->select('user')->first()->user)->name;
                $ListCurrent['market'] = Market::find($CurrentQuote->pax()->select('market')->first()->market)->market;


                $Main = $CurrentQuote->main()->select('arrival_year', 'arrival_month', 'arrival_day')->first()->toArray();
                $Dates = $QuotationHotel->getHotelBookDates(["year" => $Main['arrival_year'], "month" => $Main['arrival_month'], "day" => $Main['arrival_day']], $CurrentQuote->place()->select('place')->get()->toArray());

                $ListCurrent['arrival'] = (reset($Dates)['check_in']);
                $ListCurrent['departure'] = (end($Dates)['check_out']);

                $List[] = $ListCurrent;
            }


        }
        return $List;

    }


//    Editing Section

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getEditProfileList()
    {

        $data['vehicles'] = $this->getVehicleList();
        $data['owners'] = $this->getOwnerList();
        $data['chauffeurs'] = $this->getChauffeurList();
        $data['drivers'] = $this->getDriverList();

        return view('transport_manage.profile_manage_tab')->with('data', $data);
    }

    /**
     * @param $vehicle_id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getEditVehicle($vehicle_id)
    {

        $data['vehicle'] = TransportVehicle::find($vehicle_id);
        $data['vehicleCategory'] = Vehicle::all();
        $data['inspections'] = TransportVehicle::find($vehicle_id)->Inspections()->where('vehicle_id', '=', $vehicle_id)->get();
        $data['checklist'] = TransportVehicle::find($vehicle_id)->Checklist()->where('vehicle_id', '=', $vehicle_id)->get();
        $data['vehicleBrand'] = VehicleBrand::all();
        $data['vehicleColor'] = TransportVehicleColor::all();
        $data['city'] = Place::all();

        return view('transport_manage.edit_vehicle')->with('data', $data);
    }

    /**
     * @param $owner_id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getEditOwner($owner_id)
    {

        $data['owner'] = TransportMembers::find($owner_id);
        $data['chauffeur'] = TransportChauffeur::where('chauffer_id', $owner_id)->first();
        $data['driver'] = TransportDriver::where('member_id', $owner_id)->first();
        $data['bank'] = TransportBankDetail::where('member_id', $owner_id)->first();
        $data['languages'] = TransportLanguages::all();
        $data['nationality'] = TransportChauffeurNationality::all();
        $data['chaufferType'] = TransportChauffeurType::all();
        $data['chaufferLanuages'] = TransportChauffeurLanguage::where('member_id', $owner_id)->get();

        return view('transport_manage.edit_owner')->with('data', $data);
    }

    /**
     * @param $owner_id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getEditChauffeur($owner_id)
    {

        $data['owner'] = TransportMembers::find($owner_id);
        $data['chauffeur'] = TransportChauffeur::where('chauffer_id', $owner_id)->first();
        $data['driver'] = TransportDriver::where('member_id', $owner_id)->first();
        $data['bank'] = TransportBankDetail::where('member_id', $owner_id)->first();
        $data['languages'] = TransportLanguages::all();
        $data['nationality'] = TransportChauffeurNationality::all();
        $data['chaufferType'] = TransportChauffeurType::all();
        $data['chaufferLanuages'] = TransportChauffeurLanguage::where('member_id', $owner_id)->get();

        return view('transport_manage.edit_chauffeur')->with('data', $data);
    }

    /**
     * @param $driver_id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getEditDriver($driver_id)
    {

        $data['owner'] = TransportMembers::find($driver_id);
        $data['driver'] = TransportDriver::where('member_id', $driver_id)->first();
        $data['bank'] = TransportBankDetail::where('member_id', $driver_id)->first();

        return view('transport_manage.edit_driver')->with('data', $data);
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getVehicleAllocationTab()
    {

        $data['vehicle'] = TransportVehicle::all();
        $data['driver'] = TransportMembers::where('member_type', 4)->orWhere('member_type', 3)->orWhere('member_type', 2)->get();
        $data['chauffeur'] = TransportMembers::where('member_type', 3)->orWhere('member_type', 2)->get();
        $data['owner'] = TransportMembers::where('member_type', 1)->orWhere('member_type', 2)->get();

        return view('transport_manage.combine_vehicle')->with('data', $data);

    }

    /**
     * @param Request $request
     * @return int
     * @throws \Exception
     */
    public function editOwner(Request $request)
    {

        $member = $request->get('member_id');

        $memberInfo = array(

            'f_name' => $request->get('f_name'),
            'l_name' => $request->get('l_name'),
            'nick_name' => $request->get('nick_name'),
            'address' => $request->get('address'),
            'email' => $request->get('email'),
            'nic' => $request->get('nic'),
            'member_type' => $request->get('owner_type'),
            'remarks' => $request->get('owner_remarks'),
        );


        if ($request->hasFile('owner_image')) {

            $image = $request->file('owner_image');
            $path = public_path() . '/assets/image/transport/member/' . $member . '/';
            $filename = public_path() . '/assets/image/transport/member/' . $member . '/' . $member;

            if (File::exists($filename)) {

                File::delete($filename);
                $this->uploadImages($image, $member, $path);

            } else {

                $this->uploadImages($image, $member, $path);

            }

        }

        $contact = $request->get('contact_number');

        $update_contact = $this->updateContact($contact, $member);

        $update_member_details = $this->updateMember($memberInfo, $member);

        if ($request->get('owner_type') == 2) {

            $chauffeurInfo = [

                'type' => $request->get('chauffer_type'),
                'speciality' => $request->get('chauffer_speciality'),
                'chauffer_license' => $request->get('license_available'),
                'chauffer_license_no' => $request->get('chauffer_license_no'),
                'uniform' => $request->get('chauffer_uniform'),
                'nationality' => $request->get('chauffer_nationality'),
                'remarks' => $request->get('chauffer_remarks'),
                'rate_per_day' => $request->get('rate_per_day'),
                'tip_per_day' => $request->get('tip_per_day'),
            ];

//            $language = $request->get('chauffer_languages');


            $array = array_filter($chauffeurInfo);

            if (!empty($array)) {

                $update_chauffeur_details = $this->updateChauffeur($chauffeurInfo, $member);

            }


            if ($update_chauffeur_details) {

                $driver = [

                    'member_id' => $member,
                    'license_start_date' => $request->get('license_start_date'),
                    'license_end_date' => $request->get('license_end_date'),
                    'license_no' => $request->get('driving_license_no'),
                ];


                if (!empty($driver)) {

                    $driverUpdate = $this->updateDriver($driver, $member);

                }

                if ($driverUpdate) {

                    return true;

                } else {

                    return false;
                }

            }

        }

        $bankDetails = [

            'bank_name' => $request->get('bank_name'),
            'branch' => $request->get('bank_branch'),
            'account_number' => $request->get('account_no'),

        ];


        if ($request->hasFile('passbook')) {

            $passbook = $request->file('passbook');
            $name = "passbook";
            $path = public_path() . '/assets/image/transport/member/' . $member . '/';
            $filename = public_path() . '/assets/image/transport/member/' . $member . '/passbook.jpg';
            if (File::exists($filename)) {

                File::delete($filename);

                $this->uploadImages($passbook, $name, $path);

            } else {

                $this->uploadImages($passbook, $name, $path);

            }

        }

        $update_bank_details = $this->updateBankAccount($bankDetails, $member);


        return 1;


    }

    /**
     * @param Request $request
     * @return int
     * @throws \Exception
     */
    public function editDriver(Request $request)
    {

        $member = $request->get('member_id');


        $memberInfo = array(

            'f_name' => $request->get('f_name'),
            'l_name' => $request->get('l_name'),
            'nick_name' => $request->get('nick_name'),
            'address' => $request->get('address'),
            'email' => $request->get('email'),
            'nic' => $request->get('nic'),
            'remarks' => $request->get('owner_remarks'),
        );


        if ($request->hasFile('owner_image')) {

            $image = $request->file('owner_image');
            $path = public_path() . '/assets/image/transport/member/' . $member . '/';
            $filename = public_path() . '/assets/image/transport/member/' . $member . '/' . $member;

            if (File::exists($filename)) {

                File::delete($filename);

                $this->uploadImages($image, $member, $path);

            } else {

                $this->uploadImages($image, $member, $path);

            }

        }

        $contact = $request->get('contact_number');

        $update_contact = $this->updateContact($contact, $member);


        $update_member_details = $this->updateMember($memberInfo, $member);


        $bankDetails = [

            'bank_name' => $request->get('bank_name'),
            'branch' => $request->get('bank_branch'),
            'account_number' => $request->get('account_no'),

        ];


        if ($request->hasFile('passbook')) {

            $passbook = $request->file('passbook');
            $name = "passbook";
            $path = public_path() . '/assets/image/transport/member/' . $member . '/';
            $filename = public_path() . '/assets/image/transport/member/' . $member . '/passbook.jpg';
            if (File::exists($filename)) {

                File::delete($filename);

                $this->uploadImages($passbook, $name, $path);

            } else {

                $this->uploadImages($passbook, $name, $path);

            }

        }

        $update_bank_details = $this->updateBankAccount($bankDetails, $member);


        if ($update_member_details) {

            return 1;

        } else {

            return 0;
        }


    }

    /**
     * @param Request $request
     * @return int
     * @throws \Exception
     */
    public function editVehicle(Request $request)
    {

        DB::beginTransaction();

        $vehicle = $request->get('vehicle_id');

        $data = [

            'vehicle_number' => $request->get('vehicle_number'),
            'vehicle_category' => $request->get('vehicle_type'),
            'vehicle_brand' => $request->get('vehicle_brand'),
            'vehicle_model' => $request->get('vehicle_model'),
            'vehicle_pax' => $request->get('vehicle_pax'),
            'color' => $request->get('vehicle_color'),
            'city' => $request->get('vehicle_location'),
            'model_year' => $request->get('model_year'),
            'remarks' => $request->get('vehicle_remarks'),
        ];

        $newVehicle = $this->updateVehicle($data, $vehicle);

        $inspection = $request->get('inspection');
        $newInspection = $this->newInspection($vehicle, $inspection);

        $checklist = $request->get('checklist');

        if (!$checklist) {

            $checklist = 14;

        }

        $newChecklist = $this->newChecklist($vehicle, $checklist);

        if ($request->hasFile('vehicle_image')) {

            $image = $request->file('vehicle_image');
            $path = public_path() . '/assets/image/transport/vehicle/' . $vehicle;
            $filename = public_path() . '/assets/image/transport/vehicle/' . $vehicle . '/' . $vehicle;

            if (File::exists($filename)) {

                File::delete($filename);

                $this->uploadImages($image, $vehicle, $path);

            } else {

                $this->uploadImages($image, $vehicle, $path);

            }

        }

        if ($newChecklist && $newInspection && $newVehicle) {

            DB::commit();
            return 1;

        } else {

            DB::rollBack();
            return 0;
        }


    }

    /**
     * @param $data
     * @param $member
     * @return bool
     */
    private function updateMember($data, $member)
    {

        TransportMembers::where('id', $member)->update($data);

        return true;

    }

    /**
     * @param $data
     * @param $vehicle
     * @return bool
     */
    private function updateVehicle($data, $vehicle)
    {

        TransportVehicle::where('id', $vehicle)->update($data);

        return true;

    }

    /**
     * @param $chauffeurInfo
     * @param $member
     * @return bool
     */
    private function updateChauffeur($chauffeurInfo, $member)
    {

        $count = TransportChauffeur::where('chauffer_id', $member)->count();

        if ($count >= 1) {

            $chauffeur = TransportChauffeur::where('chauffer_id', $member)->update($chauffeurInfo);

        } else {

            $newChauffeur = new TransportChauffeur();
            $newChauffeur->chauffer_id = $member;

            if ($newChauffeur->save()) {

                $chauffeur = TransportChauffeur::where('chauffer_id', $member)->update($chauffeurInfo);

            }
        }

        return true;

    }

    /**
     * @param $data
     * @param $member
     * @return bool
     */
    private function updateDriver($data, $member)
    {

        $count = TransportDriver::where('member_id', $member)->count();

        if ($count >= 1) {

            $driver = TransportDriver::where('member_id', $member)->update($data);

        } else {

            $newDriver = new TransportDriver();
            $newDriver->member_id = $member;

            if ($newDriver->save()) {

                $driver = TransportDriver::where('member_id', $member)->update($data);

            }


        }


        return true;

    }

    /**
     * @param $bankDetails
     * @param $member
     * @return bool
     */
    private function updateBankAccount($bankDetails, $member)
    {

        $count = TransportBankDetail::where('member_id', $member)->count();

        if ($count >= 1) {

            $bank = TransportBankDetail::where('member_id', $member)->update($bankDetails);

        } else {

            $acc = new TransportBankDetail();
            $acc->member_id = $member;

            if ($acc->save()) {

                $bank = TransportBankDetail::where('member_id', $member)->update($bankDetails);
            }
        }

        if ($bank) {

            return true;

        } else {

            return false;
        }

    }

    /**
     * @param $contacts
     * @param $member
     * @return bool
     * @throws \Exception
     */
    private function updateContact($contacts, $member)
    {

        $count = TransportMemberContact::where('member_id', $member)->count();

        if ($count >= 1) {

            TransportMemberContact::where('member_id', $member)->delete();

            foreach ($contacts as $contact) {

                $tel = new TransportMemberContact();
                $tel->member_id = $member;
                $tel->contact = $contact;

                $tel->save();
            }

            return true;

        } else {

            foreach ($contacts as $contact) {

                $tel = new TransportMemberContact();
                $tel->member_id = $member;
                $tel->contact = $contact;
                $tel->save();
            }

            return true;
        }


    }

//    Get Owners List to view or print

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getOwnersList()
    {

        $owners = TransportMembers::where('member_type', 1)->orWhere('member_type', 2)->get();

        return view('transport_manage.owners_list')->with('owners', $owners);
    }

//    Get Chauffeurs to view or print

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getChauffeursList()
    {

        $chauffeurs = TransportMembers::where('member_type', 2)->orWhere('member_type', 3)->get();

        return view('transport_manage.chauffeurs_list')->with('chauffeurs', $chauffeurs);
    }

    //    Get Chauffeurs to view or print

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getDriversList()
    {

        $drivers = TransportMembers::where('member_type', 4)->get();

        return view('transport_manage.drivers_list')->with('drivers', $drivers);
    }

    //    Get Vehicles to view or print

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getVehiclesList()
    {

        $vehicles = $this->getVehicleList();
        return view('transport_manage.vehicles_list')->with('vehicles', $vehicles);
    }

    /**
     * @param $ID
     * @return Vehicle|mixed
     */
    function getVehicle($ID)
    {

        return Vehicle::find($ID);

    }

    /**
     * @param Request $request
     * @return int
     * @throws \Exception
     */
    public function addExtraMileage(Request $request)
    {

        $city = ExtraMileage::where("city", $request->get('city'))->first();

        if ($city === null) {
            $extraMileage = new ExtraMileage();
            $extraMileage->city = $request->get('city');
            $extraMileage->mileage = $request->get('mileage');
            $extraMileage->extra = $request->get('extra');

            $extraMileage->save();

            return "Succussfully Added ...!!";
        } else {

//            $extraMileage = ExtraMileage::find($city["ID"]);
//            $extraMileage->city = $request->get('city');
//            $extraMileage->mileage = $request->get('mileage');
//            $extraMileage->extra = $request->get('extra');

//            $extraMileage->save();

            return "Succussfully updated ...!!";
        }


    }

    /**
     * @param Request $request
     * @return int
     * @throws \Exception
     */
    public function saveOutboundDriverAllocation(Request $request)
    {
        $TransportOutBoundDriverAllocationOld = TransportOutBoundDriverAllocation::where('quotation_id', $request->get('quotation_id'))->get();
        if(isset($TransportOutBoundDriverAllocationOld) && !empty($TransportOutBoundDriverAllocationOld)) {
            TransportOutBoundDriverAllocation::where('quotation_id', $request->get('quotation_id'))->delete();
        }

        foreach($request->get('start_id')??[] as $key => $start_id) {
            $TransportOutBoundDriverAllocation = new TransportOutBoundDriverAllocation();
            $TransportOutBoundDriverAllocation->quotation_id = $request->get('quotation_id');
            $TransportOutBoundDriverAllocation->start_id = $request->get('start_id')[$key];
            $TransportOutBoundDriverAllocation->end_id = $request->get('end_id')[$key];
            $TransportOutBoundDriverAllocation->start_type = $request->get('start_type')[$key];
            $TransportOutBoundDriverAllocation->end_type = $request->get('end_type')[$key];
            $TransportOutBoundDriverAllocation->driver_id = $request->get('driver_id')[$key];
            $TransportOutBoundDriverAllocation->year = $request->get('year')[$key];
            $TransportOutBoundDriverAllocation->month = $request->get('month')[$key];
            $TransportOutBoundDriverAllocation->day = $request->get('day')[$key];

            $TransportOutBoundDriverAllocation->save();
        }
        

        

        return "Succussfully Added ...!!";
    }

    /**
     * @param Request $request
     * @return int
     * @throws \Exception
     */
    public static function getOutboundDriverAllocation($id)
    {

        $TransportOutBoundDriverAllocation = TransportOutBoundDriverAllocation::where('quotation_id', $id)->orderBy('id', 'ASC')->pluck('driver_id');
        
        return $TransportOutBoundDriverAllocation;
    }

}


