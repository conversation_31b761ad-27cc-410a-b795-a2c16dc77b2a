<?php

namespace App\Http\Controllers\dashboard;

use App\Model\Place\Place;
use App\Model\QuotationManage\Quotation as QuotationManage;
use App\Model\QuotationManage\QuotationConfirm;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Validator;
use DB;
use App\Model\UserTargets\apple_userTargets;
use App\Model\Admin\SalesTrack;

use App\User;

use App\Model\QuotationManage\Quotation;

use App\Model\dashboard\apple_dashboard_teamleads;
use App\Model\dashboard\apple_dashboard_user_hierarchy;
use App\Model\dashboard\apple_dashboard_user_transfer_history;
use View;

class dashboardController extends Controller
{
    public function __construct()
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 0);

    }

    public function getReport(Request $request)
    {
        $Data = $request->input();
        $rules = [];
        if (empty($request->dashboard_sales_tracking_id)) {
            $rules['dashboard_user'] = "required|array";
            $rules['dashboard_date'] = "required|date";
        }


        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }

        $resultType = $request->dashboard_result_type;
        if ($resultType == "I") {
            $data = $this->getIndividualReport($request);
            return $data;
        } else {
            $data = $this->getGroupedReport($request);
            return $data;
        }
    }

    function getGroupedReport($request)
    {
        $date = $request->dashboard_date;
        $UserIDs = $request->dashboard_user;
        $status = $request->dashboard_status;
        $destination = $request->dashboard_destination;
        $agent = $request->dashboard_agent;
        $dateObj = Carbon::parse($date)->addDay();
        $sameDayObj = Carbon::parse($date);
        $teamLeaderID = $request->dashboard_team_lead_user;
        $teamLead = apple_dashboard_teamleads::find($teamLeaderID);
        $teamLeadName = $teamLead->findUser->name;
        $teamMembers = $teamLead->findTeamMembers;



        $oldMembers = apple_dashboard_user_transfer_history::where('was_under_id', $teamLeaderID)->get();
        if ($oldMembers->count() > 0) {
            foreach ($oldMembers as $key => $oldMember) {
                $oldMemArray = array(
                    'user_id' => $oldMember->user_id,
                    'team_lead_id' => $teamLeaderID,
                    'oldMember' => true,
                    'member_from' => $oldMember->from_date,
                    'member_to' => $oldMember->to_date
                );
                $teamMembers->push($oldMemArray);
            }
        }

        $teamLeadObj = array('user_id' => $teamLead->user_id, 'team_lead_id' => $teamLeaderID);
        $teamMembers->push($teamLeadObj);



        $teamMembers = $teamMembers->toArray();
        $reportArray = array();

        foreach ($teamMembers as $key => $teamMember) {
            $dateObj = Carbon::parse($date)->addDay();
            $sameDayObj = Carbon::parse($date);
            $startOfCurrentMonth = $dateObj->copy()->startOfMonth();
            $startOfCurrentYear = $dateObj->copy()->startOfYear();

            $userObj = User::find($teamMember['user_id']);
            $userID = $teamMember['user_id'];
            $reportArray[$key]['userId'] = $userID;
            $reportArray[$key]['email'] = $userObj->email;
            $reportArray[$key]['name'] = $userObj->name;
            $reportArray[$key]['type'] = "userWise";

            if (apple_userTargets::where('user_id', '=', $userID)->exists()) {
                $targetObj = apple_userTargets::where('user_id', '=', $userID)->first();
                $quotationTarget = $targetObj->user_quotation_targets;
                $profitTarget = $targetObj->user_profit_targets;
            } else {
                $quotationTarget = 0;
                $profitTarget = 0;
            }


            $reportArray[$key]['quotationTarget'] = $quotationTarget;
            $reportArray[$key]['profitTarget'] = $profitTarget;

            if (isset($teamMember['oldMember'])) {

                $startDate = Carbon::Parse($teamMember['member_from']);
                $toDate = Carbon::parse($teamMember['member_to']);
                $reportArray[$key]['oldMember'] = true;

                $reportArray[$key]['data']['dateRange']['query'] = $this->getReportObj($request, $userID)->whereBetween('created_at', [$teamMember['member_from'], $teamMember['member_to']])->where('status', 1)  ->whereColumn('ID','quotation_no')->withTrashed()->get()->count();
                $reportArray[$key]['data']['dateRange']['reQuote'] = $this->getReportObj($request, $userID)->whereBetween('created_at', [$teamMember['member_from'], $teamMember['member_to']])->onlyTrashed()->count();
                $reportArray[$key]['data']['dateRange']['confirm'] = $this->getReportObj($request, $userID)->whereBetween('created_at', [$teamMember['member_from'], $teamMember['member_to']])->where('status', 2)->count();
                $reportArray[$key]['data']['dateRange']['dateRange'] = $startDate->toFormattedDateString() . " to " . $toDate->toFormattedDateString();


            } else {
                if (apple_dashboard_user_transfer_history::where('user_id', $userID)->exists()) {
                    $transferObj = apple_dashboard_user_transfer_history::where('user_id', $userID)->orderBy('to_date', 'DESC')->first();
                    $transferDateObj = Carbon::Parse($transferObj->to_date);

                    $startOfCurrentMonth = $transferDateObj;
                    $startOfCurrentYear = $transferDateObj;
                    $reportArray[$key]['transfer'] = true;

                } else {
                    $reportArray[$key]['transfer'] = false;
                }

                $todayFreshQuotes = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())->whereColumn('ID','quotation_no')->withTrashed()->get()->count();


                $todayRequotesSaved = 0;
                $todayRequotesSavedObj = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())->onlyTrashed()->where('status', 1)->get();
                $todayRequotesSavedObjArray = $this->dashboardArraySort($todayRequotesSavedObj, 'quotation_no');

                foreach ($todayRequotesSavedObjArray as $keyOb => $array) {
                    $todayRequotesSaved += count($array) ;
                }

                $todayRequotesConfirmedObj = $this->getReportObj($request, $userID)->onlyTrashed()->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                    $confirm->whereDate('created_at', $sameDayObj->toDateString());
                })->get()->toArray();
                $todayRequotesConfirmed = 0;
                $todayRequotesConfirmedArray = $this->dashboardArraySort($todayRequotesConfirmedObj, 'quotation_no');
                foreach ($todayRequotesConfirmedArray as $Key => $array){
                    $todayRequotesConfirmed +=  count($array) -1 ;
                }

                $todaConfirmed = $this->getReportObj($request, $userID)->with('Confirm')->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                    $confirm->orderBy('created_at',"ASC");
                })->withTrashed()->groupBy('quotation_no')->get();

                $todayConfirmed = 0;
                $DesArray = array();
                foreach ($todaConfirmed as $KeyObj => $valueObj){
                    if($valueObj->confirm->created_at) {

                        $date = Carbon::parse($valueObj->confirm->created_at);
                        if ($date->isSameDay($sameDayObj)) {
                            $todayConfirmed ++;
                        }
                    }
                }




                //Current Day
                $reportArray[$key]['data']['dayWise']['query'] = $todayFreshQuotes;
                $reportArray[$key]['data']['dayWise']['reQuote'] = $todayRequotesSaved + $todayRequotesConfirmed ;
                $reportArray[$key]['data']['dayWise']['confirm'] = $todayConfirmed;
                $reportArray[$key]['data']['dayWise']['dateRange'] = $sameDayObj->toFormattedDateString();

                // Current Week
                $currentWeekhSaved = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfWeek(), $dateObj->toDateString()])
                    ->whereColumn('ID','quotation_no')->withTrashed()->get()->count();

                $currentWeekReQuoteSaved = 0;
                $currentMonthReQuoteSavedObj = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfWeek(), $dateObj->toDateString()])
                    ->onlyTrashed()->where('status', 1)->get();
                $currentMonthRequotesSavedObjArray = $this->dashboardArraySort($currentMonthReQuoteSavedObj, 'quotation_no');
                foreach ($currentMonthRequotesSavedObjArray as $keyOb => $array) {
                    $currentWeekReQuoteSaved += count($array)  ;
                }

                $currentWeekReQuoteConfirmed = 0;
                $currentMonthReQuoteConfirmedObj = $this->getReportObj($request, $userID)->onlyTrashed()
                    ->whereHas('Confirm', function ($confirm) use ($dateObj) {
                        $confirm->whereBetween('created_at', [$dateObj->copy()->startOfWeek(), $dateObj->toDateString()]);
                    })->get();
                $currentMonthRequoteSavedObjArray = $this->dashboardArraySort($currentMonthReQuoteConfirmedObj, 'quotation_no');
                foreach ($currentMonthRequoteSavedObjArray as $Key => $array){
                    $currentWeekReQuoteConfirmed +=  count($array) -1  ;
                }

                $reportArray[$key]['data']['weekWise']['query'] = $currentWeekhSaved;
                $reportArray[$key]['data']['weekWise']['reQuote'] = $currentWeekReQuoteSaved + $currentWeekReQuoteConfirmed;
                $reportArray[$key]['data']['weekWise']['confirm'] = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfWeek(), $dateObj->toDateString()])->where('status', 2)->groupBy('quotation_no')->get()->count();
                $reportArray[$key]['data']['weekWise']['dateRange'] = $dateObj->copy()->startOfWeek()->toFormattedDateString() . " to " . $sameDayObj->toFormattedDateString();


                // Current Month
                $currentMonthSaved = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfMonth(), $dateObj->toDateString()])
                    ->whereColumn('ID','quotation_no')->withTrashed()->get()->count();

                $currentMonthReQuoteSaved = 0;
                $currentMonthReQuoteSavedObj = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfMonth(), $dateObj->toDateString()])
                    ->onlyTrashed()->where('status', 1)->get();
                $currentMonthRequotesSavedObjArray = $this->dashboardArraySort($currentMonthReQuoteSavedObj, 'quotation_no');
                foreach ($currentMonthRequotesSavedObjArray as $keyOb => $array) {
                    $currentMonthReQuoteSaved += count($array);
                }

                $currentMonthReQuoteConfirmed = 0;
                $currentMonthReQuoteConfirmedObj = $this->getReportObj($request, $userID)->onlyTrashed()
                    ->whereHas('Confirm', function ($confirm) use ($dateObj) {
                        $confirm->whereBetween('created_at', [$dateObj->copy()->startOfMonth(), $dateObj->toDateString()]);
                    })->get();
                $currentMonthRequoteSavedObjArray = $this->dashboardArraySort($currentMonthReQuoteConfirmedObj, 'quotation_no');
                foreach ($currentMonthRequoteSavedObjArray as $Key => $array){
                    $currentMonthReQuoteConfirmed +=  count($array) -1 ;
                }

                $monthConfimations = 0;
                $monthConfimationsObj = $this->getReportObj($request, $userID)->with('Confirm')->whereHas('Confirm', function ($confirm) use ($dateObj,$sameDayObj) {
                    $confirm->orderBy('created_at',"ASC")->whereBetween('created_at', [$sameDayObj->copy()->startOfMonth(), $dateObj->toDateString()]);
                })->where('status',2)->groupBy('quotation_no')->get();


                foreach ($monthConfimationsObj as $KeyObj => $valueObj){
                    $quotationsNo = $valueObj->quotation_no;
                    $quotationObj = QuotationManage::where('quotation_no',$quotationsNo)->withTrashed()->where('status',2)->first();
                    $creationDate = Carbon::parse($quotationObj->created_at);
                    $valid = $creationDate->between($sameDayObj->copy()->startOfMonth(), $dateObj);
                    if($valid == true) {
                        $monthConfimations++;
                    }
                }

                $reportArray[$key]['data']['monthWise']['query'] = $currentMonthSaved;
                $reportArray[$key]['data']['monthWise']['reQuote'] = $currentMonthReQuoteSaved + $currentMonthReQuoteConfirmed;
                $reportArray[$key]['data']['monthWise']['confirm'] = $monthConfimations;
                $reportArray[$key]['data']['monthWise']['dateRange'] = $dateObj->copy()->startOfMonth()->toFormattedDateString() . " to " . $sameDayObj->toFormattedDateString();


                // Current Year

                $currentYearSaved = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])
                    ->whereColumn('ID','quotation_no')->withTrashed()->get()->count();

                $currentYearReQuoteSaved = 0;
                $currentYearReQuoteSavedObj = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])
                    ->onlyTrashed()->where('status', 1)->get()->toArray();
                $currentYearReQuoteArray = $this->dashboardArraySort($currentYearReQuoteSavedObj, 'quotation_no');
                foreach ($currentYearReQuoteArray as $Key => $array){
                    $currentYearReQuoteSaved +=  count($array)  ;
                }
                $currentYearReQuoteConfirmed = 0;
                $currentYearReQuoteConfirmedObj = $this->getReportObj($request, $userID)->onlyTrashed()
                    ->whereHas('Confirm', function ($confirm) use ($dateObj) {
                        $confirm->whereBetween('created_at', [$dateObj->copy()->startOfMonth(), $dateObj->toDateString()]);
                    })->get()->toArray();
                $currentYearReQuoteConfirmedArray = $this->dashboardArraySort($currentYearReQuoteConfirmedObj, 'quotation_no');
                foreach ($currentYearReQuoteConfirmedArray as $Key => $array){
                    $currentYearReQuoteConfirmed +=  count($array)  -1 ;
                }


                $reportArray[$key]['data']['yearWise']['query'] = $currentYearSaved;
                $reportArray[$key]['data']['yearWise']['reQuote'] = $currentYearReQuoteSaved + $currentYearReQuoteConfirmed;
                $reportArray[$key]['data']['yearWise']['confirm'] = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])->where('status', 2)->groupBy('quotation_no')->get()->count();
                $reportArray[$key]['data']['yearWise']['dateRange'] = $dateObj->copy()->startOfYear()->toFormattedDateString() . " to " . $sameDayObj->toFormattedDateString();
                $reportArray[$key]['data']['yearWise']['targetYear'] = $quotationTarget * 12;
            }


            // Sales Track & agent Data
            $graphSalesTrackObj = $this->getReportObj($request, $userID)->whereHas('findSalesTrack')->with('findSalesTrack')->get();
//            dump($graphSalesTrackObj);
            if (count($graphSalesTrackObj) > 0) {
                $salesIds = array();
                foreach ($graphSalesTrackObj as $salesKey => $value) {
                    array_push($salesIds, $value->findSalesTrack->ID);
                }
                $salesObjSaved = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) use($sameDayObj) {
                    $quotation->whereDate('created_at', '=', $sameDayObj->toDateString())->whereRaw('ID','quotation_no');
                })->get()->toArray();


                $salesObjConfirmed = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) use ($sameDayObj) {
                    $quotation->where('status', 2)->whereDate('created_at', '=', $sameDayObj->toDateString());
                })->get()->toArray();

                $salesObjRequotes = array();
                $salesObjRequotesSaved = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) use ($sameDayObj)  {
                    $quotation->whereDate('created_at', '=', $sameDayObj->toDateString())->where('status',1)->onlyTrashed();
                })->get()->toArray();

                $salesObjRequotesConfirmed = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) use ($sameDayObj)  {
                    $quotation->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                        $confirm->whereDate('created_at', '=', $sameDayObj->toDateString());
                    });
                })->get()->toArray();
                $salesObjRequotes = array_merge($salesObjRequotesSaved,$salesObjRequotesConfirmed);

                $countsSaved = array();
                foreach ($salesObjSaved as $value) {
                    foreach ($value as $k => $v) {
                        if (!isset($countsSaved[$k])) $countsSaved[$k] = array();
                        if (!isset($countsSaved[$k][$v])) $countsSaved[$k][$v] = 0;
                        $countsSaved[$k][$v] += 1;
                    }
                }

                $countsConfirmed = array();
                foreach ($salesObjConfirmed as $value) {
                    foreach ($value as $k => $v) {
                        if (!isset($countsConfirmed[$k])) $countsConfirmed[$k] = array();
                        if (!isset($countsConfirmed[$k][$v])) $countsConfirmed[$k][$v] = 0;
                        $countsConfirmed[$k][$v] += 1;
                    }
                }

                $countsReQuotes = array();
                foreach ($salesObjRequotes as $value) {
                    foreach ($value as $k => $v) {
                        if (!isset($countsReQuotes[$k])) $countsReQuotes[$k] = array();
                        if (!isset($countsReQuotes[$k][$v])) $countsReQuotes[$k][$v] = 0;
                        $countsReQuotes[$k][$v] += 1;
                    }
                }
                $datArray = array();


                $i = 0;
                foreach ($countsSaved['company_agent'] ?? [] as $savedKey => $SavedValue) {
                    $datArray[$i]['name'] = $savedKey;
                    $datArray[$i]['count'] = $SavedValue;
                    $datArray[$i]['type'] = "Queries";
                    $i++;
                }
                foreach ($countsConfirmed['company_agent'] ?? [] as $savedKey => $SavedValue) {
                    $datArray[$i]['name'] = $savedKey;
                    $datArray[$i]['count'] = $SavedValue;
                    $datArray[$i]['type'] = "Confirmations";
                    $i++;
                }
                foreach ($countsReQuotes['company_agent'] ?? [] as $savedKey => $SavedValue) {
                    $datArray[$i]['name'] = $savedKey;
                    $datArray[$i]['count'] = $SavedValue;
                    $datArray[$i]['type'] = "ReQuotes";
                    $i++;
                }
                $groupedAgentData = array_values($this->dashboardArraySort($datArray, 'name'));

                $NameArray = ["Queries", "Confirmations", "ReQuotes"];

                foreach ($groupedAgentData as $groupKey => $data) {

                    $TempAssigner = [];
                    $name = "";
                    foreach ($data as $keyInner => $valueInner) {
                        if (in_array($valueInner["type"], $TempAssigner)) {
                            // continue;
                        } else {
                            $TempAssigner[] = $valueInner["type"];
                            $name = $valueInner["name"];
                        }
                    }

                    foreach ($NameArray as $Check) {
                        if (!in_array($Check, $TempAssigner)) {
                            $tempArray = array(
                                "name" => $name,
                                "count" => 0,
                                "type" => $Check,
                            );
                            array_push($groupedAgentData[$groupKey], $tempArray);
                        }
                    }
                }

                $datArray2 = array();


                $i = 0;
                foreach ($countsSaved['user_id'] ?? [] as $savedKey => $SavedValue) {
                    $datArray2[$i]['name'] = User::find($savedKey)->name;
                    $datArray2[$i]['userID'] = $savedKey;
                    $datArray2[$i]['count'] = $SavedValue;
                    $datArray2[$i]['type'] = "Queries";
                    $i++;
                }
                foreach ($countsConfirmed['user_id'] ?? [] as $savedKey => $SavedValue) {
                    $datArray2[$i]['name'] = User::find($savedKey)->name;
                    $datArray2[$i]['userID'] = $savedKey;
                    $datArray2[$i]['count'] = $SavedValue;
                    $datArray2[$i]['type'] = "Confirmations";
                    $i++;
                }
                foreach ($countsReQuotes['user_id'] ?? [] as $savedKey => $SavedValue) {
                    $datArray2[$i]['name'] = User::find($savedKey)->name;
                    $datArray2[$i]['userID'] = $savedKey;
                    $datArray2[$i]['count'] = $SavedValue;
                    $datArray2[$i]['type'] = "ReQuotes";
                    $i++;
                }
                $groupedSalesData = array_values($this->dashboardArraySort($datArray2, 'userID'));


                foreach ($groupedSalesData as $groupKey => $data) {
                    $TempAssigner = [];
                    $name = "";
                    $userID = "";
                    foreach ($data as $keyInner => $valueInner) {
                        if (in_array($valueInner["type"], $TempAssigner)) {
                            // continue;
                        } else {
                            $TempAssigner[] = $valueInner["type"];
                            $name = $valueInner["name"];
                            $userID = $valueInner["userID"];
                        }
                    }

                    foreach ($NameArray as $Check) {
                        if (!in_array($Check, $TempAssigner)) {
                            $tempArray = array(
                                "userID" => $userID,
                                "name" => $name,
                                "count" => 0,
                                "type" => $Check,
                            );
                            array_push($groupedSalesData[$groupKey], $tempArray);
                        }
                    }
                }
                $agentNames = array();

                $agentSaved = array();
                $agentReQuote = array();
                $agentConfirm = array();


                foreach ($groupedAgentData as $agentKey => $agentValue) {
                    array_push($agentNames, $agentValue[0]['name']);
                    foreach ($agentValue as $agKey => $agents) {
                        if ($agents['type'] == "Queries") {
                            array_push($agentSaved, $agents['count']);
                        } else if ($agents['type'] == "ReQuotes") {
                            array_push($agentReQuote, $agents['count']);
                        } else {
                            array_push($agentConfirm, $agents['count']);
                        }
                    }
                }

                $salesNames = array();

                $salesSaved = array();
                $salesReQuote = array();
                $salesConfirm = array();


                foreach ($groupedSalesData as $salesKey => $salesValue) {
                    array_push($salesNames, $salesValue[0]['name']);

                    foreach ($salesValue as $slKey => $sales) {
                        if ($sales['type'] == "Queries") {
                            array_push($salesSaved, $sales['count']);
                        } else if ($sales['type'] == "ReQuotes") {
                            array_push($salesReQuote, $sales['count']);
                        } else {
                            array_push($salesConfirm, $sales['count']);
                        }
                    }
                }
                $reportArray[$key]['data']['agentData'] = $groupedAgentData;
                $reportArray[$key]['data']['salesData'] = $groupedSalesData;
            }
            $userID = $teamMember['user_id'];
//            $destinationConfirmDataObj = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())
//                ->where('status', 2)->get()->toArray();

            $todaConfirmed = $this->getReportObj($request, $userID)->with('Confirm')->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                $confirm->orderBy('created_at',"ASC");
            })->withTrashed()->groupBy('quotation_no')->get();


            $DesArray = array();
            foreach ($todaConfirmed as $KeyObj => $valueObj){
                if($valueObj->confirm->created_at) {
                    $date = Carbon::parse($valueObj->confirm->created_at);
                    if ($date->isSameDay($sameDayObj)) {
                        array_push($DesArray, $valueObj->toArray());
                    }
                }
            }
            foreach ($DesArray as $keysofDesc => $keysofDescVal){
                unset($DesArray[$keysofDesc]['confirm']);
            }

                $confirmCounts = array();
                foreach ($DesArray as $value) {
                    foreach ($value as $k => $v) {
                        if (!isset($confirmCounts[$k])) $confirmCounts[$k] = array();
                        if (!isset($confirmCounts[$k][$v])) $confirmCounts[$k][$v] = 0;
                        $confirmCounts[$k][$v] += 1;
                    }
                }
                /*Saved*/
                $destinationSavedDataObj = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())
                    ->whereRaw('ID','quotation_no')->get()->toArray();
                $savedCounts = array();
                foreach ($destinationSavedDataObj as $value) {
                    foreach ($value as $k => $v) {
                        if (!isset($savedCounts[$k])) $savedCounts[$k] = array();
                        if (!isset($savedCounts[$k][$v])) $savedCounts[$k][$v] = 0;
                        $savedCounts[$k][$v] += 1;
                    }
                }
            $todayRequotesSaved = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())->onlyTrashed()->where('status', 1)->get()->toArray();

            $todayRequotesConfirmed = $this->getReportObj($request, $userID)->onlyTrashed()->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                $confirm->whereDate('created_at', $sameDayObj->toDateString());
            })->get()->toArray();

            if(count($todayRequotesSaved) > 1) {
                unset($todayRequotesSaved[ count($todayRequotesSaved)-1]);
            }
            if(count($todayRequotesConfirmed) > 1) {
                unset($todayRequotesConfirmed[count($todayRequotesConfirmed)-1]);
            }
            $destinationReQuoteDataObj = array_merge($todayRequotesSaved,$todayRequotesConfirmed);
                $requoteCounts = array();
                foreach ($destinationReQuoteDataObj as $value) {
                    foreach ($value as $k => $v) {
                        if (!isset($requoteCounts[$k])) $requoteCounts[$k] = array();
                        if (!isset($requoteCounts[$k][$v])) $requoteCounts[$k][$v] = 0;
                        $requoteCounts[$k][$v] += 1;
                    }
                }


                $destinationArray = array();
                $i = 0;
                foreach ($confirmCounts['country'] ?? [] as $countryID => $count) {
                    $destinationArray[$i]['count'] = $count;
                    $destinationArray[$i]['placeId'] = $countryID;
                    $destinationArray[$i]['name'] = "Confirmations";
                    $destinationArray[$i]['status'] = 2;
                    $i++;
                }

                foreach ($savedCounts['country'] ?? [] as $countryID => $count) {
                    $destinationArray[$i]['count'] = $count;
                    $destinationArray[$i]['placeId'] = $countryID;
                    $destinationArray[$i]['name'] = "Queries";
                    $destinationArray[$i]['status'] = 1;
                    $i++;
                }
                foreach ($requoteCounts['country'] ?? [] as $countryID => $count) {
                    $destinationArray[$i]['count'] = $count;
                    $destinationArray[$i]['placeId'] = $countryID;
                    $destinationArray[$i]['name'] = "ReQuotes";
                    $destinationArray[$i]['status'] = 3;
                    $i++;
                }

                $destinationValues = $this->dashboardArraySort($destinationArray, 'placeId');
                $destinationsVal = array();

                $destinationSaved = array();
                $destinationReQuote = array();
                $destinationConfirm = array();

                foreach ($destinationValues as $desKey => $destinations) {
                    array_push($destinationsVal, Place::where('ID', '=', $desKey)->first()->name);
                    foreach ($destinations as $destinationKey => $destination) {

                        if ($destination['name'] == "Queries") {
                            array_push($destinationSaved, $destination['count']);
                        } else if ($destination['name'] == "ReQuotes") {
                            array_push($destinationReQuote, $destination['count']);
                        } else {
                            array_push($destinationConfirm, $destination['count']);
                        }
                    }
                }

                $reportArray[$key]['data']['destinationData'] = $destinationValues;

        }
        $data = $this->mergeGroupedArray($reportArray);
        $teamLeads = apple_dashboard_teamleads::where('id',$teamLeaderID)->with('findTeamMembers')->get();
        $oldMemberHierarchy = array();
        if(count($oldMembers) > 0){
            foreach ($oldMembers as $key => $memberData) {
                $oldMemberHierarchy[$key]['name'] = $memberData->findUser()->first()->name;
                $oldMemberHierarchy[$key]['email'] = $memberData->findUser()->first()->email;
                $oldMemberHierarchy[$key]['member_from'] = Carbon::parse($memberData->from_date)->toFormattedDateString();
                $oldMemberHierarchy[$key]['member_to'] =  Carbon::parse($memberData->to_date)->toFormattedDateString();
            }
        }
        $hierarchyView = View::make('dashboard.dashboard_tooltip_data', compact('teamLeads','teamMembers','oldMemberHierarchy'))->render();
//        dd($hierarchyView);
        return view('dashboard.dashboard_group_data', compact('data','teamLeadName','hierarchyView'));
    }

    function mergeGroupedArray($teamMembers)
    {

        $combinedArray = array();


        $dayWiseQuery = 0;
        $dayWiseReQuote = 0;
        $dayWiseConfirm = 0;

        $weekWiseQuery = 0;
        $weekWiseReQuote = 0;
        $weekWiseConfirm = 0;

        $monthWiseQuery = 0;
        $monthWiseReQuote = 0;
        $monthWiseConfirm = 0;

        $yearWiseQuery = 0;
        $yearWiseReQuote = 0;
        $yearWiseConfirm = 0;

        $destinationsArr = array();
        $salesArr = array();
        $agentArr = array();

        $destinationChart = "";
        $salesChart = "";
        $agentChart = "";

        foreach ($teamMembers as $key => $teamMember) {
            if (isset($teamMember['transfer'])) {

                if (isset($teamMember['data']['dayWise'])) {
                    $dayWiseQuery += $teamMember['data']['dayWise']['query'];
                    $dayWiseReQuote += $teamMember['data']['dayWise']['reQuote'];
                    $dayWiseConfirm += $teamMember['data']['dayWise']['confirm'];
                }
                if (isset($teamMember['data']['weekWise'])) {
                    $weekWiseQuery += $teamMember['data']['weekWise']['query'];
                    $weekWiseReQuote += $teamMember['data']['weekWise']['reQuote'];
                    $weekWiseConfirm += $teamMember['data']['weekWise']['confirm'];
                }
                if (isset($teamMember['data']['monthWise'])) {
                    $monthWiseQuery += $teamMember['data']['monthWise']['query'];
                    $monthWiseReQuote += $teamMember['data']['monthWise']['reQuote'];
                    $monthWiseConfirm += $teamMember['data']['monthWise']['confirm'];
                }
                if (isset($teamMember['data']['yearWise'])) {
                    $yearWiseQuery += $teamMember['data']['yearWise']['query'];
                    $yearWiseReQuote += $teamMember['data']['yearWise']['reQuote'];
                    $yearWiseConfirm += $teamMember['data']['yearWise']['confirm'];
                }
            }
            if (isset($teamMember['oldMember'])) {
                $yearWiseQuery += $teamMember['data']['dateRange']['query'];
                $yearWiseReQuote += $teamMember['data']['dateRange']['reQuote'];
                $yearWiseConfirm += $teamMember['data']['dateRange']['confirm'];
            }
            if (isset($teamMember['data']['destinationData'])) {
                foreach ($teamMember['data']['destinationData'] as $desKey => $destinations) {
                    foreach ($destinations as $destinationKey => $destination) {
                        array_push($destinationsArr, $destination);
                    }
                }
            }
            if (isset($teamMember['data']['salesData'])) {
                foreach ($teamMember['data']['salesData'] as $salesKey => $salesData) {
                    foreach ($salesData as $salesDataKey => $sales) {
                        array_push($salesArr, $sales);
                    }
                }
            }

            if (isset($teamMember['data']['agentData'])) {
                foreach ($teamMember['data']['agentData'] as $agent => $agentData) {
                    foreach ($agentData as $agentDataKey => $agent) {
                        array_push($agentArr, $agent);
                    }
                }
            }
        }
        // destination group data ---------------------------------------------------------------------------------------------
        if (!empty($destinationsArr)) {
            $destinationGroupPlace = $this->dashboardArraySort($destinationsArr, 'placeId');
            $destinationWise = array();

            foreach ($destinationGroupPlace as $key => $des) {
                $destinationName = Place::where('ID', '=', $key)->first()->name;
                $sortByCondition = $this->dashboardArraySort($des, 'name');
                foreach ($sortByCondition as $sortKey => $sort) {
                    if ($sortKey == "Confirmations") {
                        $destinationWise[$destinationName]['Confirmations'] = $this->getSumOfArray($sort);
                    } else if ($sortKey == "Queries") {
                        $destinationWise[$destinationName]['Queries'] = $this->getSumOfArray($sort);
                    } else {
                        $destinationWise[$destinationName]['ReQuotes'] = $this->getSumOfArray($sort);
                    }
                }
            }

            foreach ($destinationWise as $key => $value) {
                if (!isset($value['Confirmations'])) {
                    $destinationWise[$key]['Confirmations'] = 0;
                }
                if (!isset($value['Queries'])) {
                    $destinationWise[$key]['Queries'] = 0;
                }
                if (!isset($value['ReQuotes'])) {
                    $destinationWise[$key]['ReQuotes'] = 0;
                }
            }

            $destinationsVal = array();

            $destinationSaved = array();
            $destinationReQuote = array();
            $destinationConfirm = array();

            foreach ($destinationWise as $desKey => $destinations) {
                array_push($destinationsVal, $desKey);
                foreach ($destinations as $destinationKey => $destination) {
                    if ($destinationKey == "Queries") {
                        array_push($destinationSaved, $destination);
                    } else if ($destinationKey == "ReQuotes") {
                        array_push($destinationReQuote, $destination);
                    } else {
                        array_push($destinationConfirm, $destination);
                    }
                }
            }
            $finalDestinationArray = array(
                array(
                    'name' => "Queries",
                    'data' => $destinationSaved,
                    'color' => "#2196F3"

                ),
                array(
                    'name' => "ReQuotes",
                    'data' => $destinationReQuote,
                    'color' => "#1758cf"

                ),
                array(
                    'name' => "Confirmations",
                    'data' => $destinationConfirm,
                    'color' => "#4caf50"

                ),

            );
            $destinationChart = $this->generateDestinationChart("1",$finalDestinationArray,$destinationsVal);
        }
        // destination group data ---------------------------------------------------------------------------------------------
        $salesWise = array();
        $agentWise = array();

        if (!empty($salesArr)) {
            $salesGroupByName = $this->dashboardArraySort($salesArr, "userID");

            foreach ($salesGroupByName as $key => $sales) {
                $sortByCondition = $this->dashboardArraySort($sales, 'type');
                $user = User::find($key)->name;
                foreach ($sortByCondition as $sortKey => $sort) {
                    if ($sortKey == "Confirmations") {
                        $salesWise[$user]['Confirmations'] = $this->getSumOfArray($sort);
                    } else if ($sortKey == "Queries") {
                        $salesWise[$user]['Queries'] = $this->getSumOfArray($sort);
                    } else {
                        $salesWise[$user]['ReQuotes'] = $this->getSumOfArray($sort);
                    }
                }
            }
            $salesNames = array();

            $salesSaved = array();
            $salesReQuote = array();
            $salesConfirm = array();

            foreach ($salesWise as $salesKey => $salesValue) {
                array_push($salesNames, $salesKey);
                foreach ($salesValue as $slKey => $sales) {
                    if ($slKey == "Queries") {
                        array_push($salesSaved, $sales);
                    } else if ($slKey == "ReQuotes") {
                        array_push($salesReQuote, $sales);
                    } else {
                        array_push($salesConfirm, $sales);
                    }
                }
            }

            $finalSalesArray = array(
                array(
                    'name' => "Queries",
                    'data' => $salesSaved,
                    'color' => "#2196F3"

                ),
                array(
                    'name' => "ReQuotes",
                    'data' => $salesReQuote,
                    'color' => "#1758cf"

                ),
                array(
                    'name' => "Confirmations",
                    'data' => $salesConfirm,
                    'color' => "#4caf50"

                ),

            );
            $salesChart = $this->generateSalesChart("1",$finalSalesArray,$salesNames);
        }
        if (!empty($agentArr)) {
            $agentGroupByName = $this->dashboardArraySort($agentArr, "name");
            foreach ($agentGroupByName as $key => $agent) {
                $sortByCondition = $this->dashboardArraySort($agent, 'type');
                foreach ($sortByCondition as $sortKey => $sort) {
                    if ($sortKey == "Confirmations") {
                        $agentWise[$key]['Confirmations'] = $this->getSumOfArray($sort);
                    } else if ($sortKey == "Queries") {
                        $agentWise[$key]['Queries'] = $this->getSumOfArray($sort);
                    } else {
                        $agentWise[$key]['ReQuotes'] = $this->getSumOfArray($sort);
                    }
                }
            }

            $agentNames = array();

            $agentSaved = array();
            $agentReQuote = array();
            $agentConfirm = array();


            foreach ($agentWise as $agentKey => $agentValue) {
                array_push($agentNames, $agentKey);
                foreach ($agentValue as $agKey => $agents) {
                    if ($agKey == "Queries") {
                        array_push($agentSaved, $agents);
                    } else if ($agKey == "ReQuotes") {
                        array_push($agentReQuote, $agents);
                    } else {
                        array_push($agentConfirm, $agents);
                    }
                }
            }
            $finalAgentArray = array(
                array(
                    'name' => "Queries",
                    'data' => $agentSaved,
                    'color' => "#2196F3"

                ),
                array(
                    'name' => "ReQuotes",
                    'data' => $agentReQuote,
                    'color' => "#1758cf"

                ),
                array(
                    'name' => "Confirmations",
                    'data' => $agentConfirm,
                    'color' => "#4caf50"

                ),

            );
            $agentChart = $this->generateAgentChartData("1",$finalAgentArray,$agentNames);
        }


        $dayWiseArr = array('dateRange' => $teamMembers[0]['data']['dayWise']['dateRange'],"wise" => "Day Wise" ,'Query' => $dayWiseQuery, 'ReQuote' => $dayWiseReQuote, 'Confirm' => $dayWiseConfirm);
        $weekWiseArr = array('dateRange' => $teamMembers[0]['data']['weekWise']['dateRange'],'wise' => "Week Wise",'Query' => $weekWiseQuery, 'ReQuote' => $weekWiseReQuote, 'Confirm' => $weekWiseConfirm);
        $monthWiseArr = array('dateRange' => $teamMembers[0]['data']['monthWise']['dateRange'],'wise' => "Month Wise",'Query' => $monthWiseQuery, 'ReQuote' => $monthWiseReQuote, 'Confirm' => $monthWiseConfirm);
        $yearWiseArr = array('dateRange' => $teamMembers[0]['data']['yearWise']['dateRange'],'wise' => "Year Wise",'Query' => $yearWiseQuery, 'ReQuote' => $yearWiseReQuote, 'Confirm' => $yearWiseConfirm);

        $finalArray = array();
        array_push($finalArray, $dayWiseArr);
        array_push($finalArray, $weekWiseArr);
        array_push($finalArray, $monthWiseArr);
        array_push($finalArray, $yearWiseArr);


        $data = array(
            'teamMember' => $teamMembers,
            'finalData' => $finalArray,
            'destinationData' => $destinationChart,
            'agentData' => $agentChart ,
            'salesData' => $salesChart);

        return $data;
    }

    function getSumOfArray($data)
    {
        $count = 0;
        foreach ($data as $value) {
            $count += $value['count'];
        }
        return $count;
    }


    function getIndividualReport($request)
    {

        $date = $request->dashboard_date;
        $UserIDs = $request->dashboard_user;
        $status = $request->dashboard_status;
        $destination = $request->dashboard_destination;
        $agent = $request->dashboard_agent;
        $dateObj = Carbon::parse($date)->addDay();
        $sameDayObj = Carbon::parse($date);

        $reportArray = array();
        if (empty($request->dashboard_sales_tracking_id)) {
            foreach ($UserIDs as $key => $userID) {
                $userObj = User::find($userID);
                $reportArray[$key]['userId'] = $userID;
                $reportArray[$key]['email'] = $userObj->email;
                $reportArray[$key]['name'] = $userObj->name;
                $reportArray[$key]['type'] = "userWise";

                if (apple_userTargets::where('user_id', '=', $userID)->exists()) {
                    $targetObj = apple_userTargets::where('user_id', '=', $userID)->first();
                    $quotationTarget = $targetObj->user_quotation_targets;
                    $profitTarget = $targetObj->user_profit_targets;
                } else {
                    $quotationTarget = 0;
                    $profitTarget = 0;
                }


                $reportArray[$key]['quotationTarget'] = $quotationTarget;
                $reportArray[$key]['profitTarget'] = $profitTarget;

                $todayFreshQuotes = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())->whereColumn('ID','quotation_no')->withTrashed()->get()->count();


                $todayRequotesSaved = 0;
                $todayRequotesSavedObj = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())->onlyTrashed()->where('status', 1)->get();
                $todayRequotesSavedObjArray = $this->dashboardArraySort($todayRequotesSavedObj, 'quotation_no');

                foreach ($todayRequotesSavedObjArray as $keyOb => $array) {
                    $todayRequotesSaved += count($array) ;
                }

                $todayRequotesConfirmedObj = $this->getReportObj($request, $userID)->onlyTrashed()->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                    $confirm->whereDate('created_at', $sameDayObj->toDateString());
                })->get()->toArray();
                $todayRequotesConfirmed = 0;
                $todayRequotesConfirmedArray = $this->dashboardArraySort($todayRequotesConfirmedObj, 'quotation_no');
                foreach ($todayRequotesConfirmedArray as $Key => $array){
                    $todayRequotesConfirmed +=  count($array) -1 ;
                }

                $todayConfimations = 0;
                $todaConfirmedObj = $this->getReportObj($request, $userID)->with('Confirm')->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                    $confirm->orderBy('created_at',"ASC")->whereDate('created_at',$sameDayObj);
                })->where('status',2)->groupBy('quotation_no')->get();




                foreach ($todaConfirmedObj as $KeyObj => $valueObj){
                    if($valueObj->confirm->created_at) {
                        $date = Carbon::parse($valueObj->confirm->created_at);
                        if ($date->isSameDay($sameDayObj)) {
                            $todayConfimations++;
                        }
                    }
                }



                //Current Day
                $reportArray[$key]['data']['dayWise']['query'] = $todayFreshQuotes;
                $reportArray[$key]['data']['dayWise']['reQuote'] = $todayRequotesSaved + $todayRequotesConfirmed ;
                $reportArray[$key]['data']['dayWise']['confirm'] = $todayConfimations;
                $reportArray[$key]['data']['dayWise']['dateRange'] = $sameDayObj->toFormattedDateString();

                // Current Week
                $currentWeekhSaved = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfWeek(), $dateObj->toDateString()])
                    ->whereColumn('ID','quotation_no')->withTrashed()->get()->count();

                $currentWeekReQuoteSaved = 0;
                $currentMonthReQuoteSavedObj = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfWeek(), $dateObj->toDateString()])
                    ->onlyTrashed()->where('status', 1)->get();
                $currentMonthRequotesSavedObjArray = $this->dashboardArraySort($currentMonthReQuoteSavedObj, 'quotation_no');
                foreach ($currentMonthRequotesSavedObjArray as $keyOb => $array) {
                    $currentWeekReQuoteSaved += count($array)  ;
                }

                $currentWeekReQuoteConfirmed = 0;
                $currentMonthReQuoteConfirmedObj = $this->getReportObj($request, $userID)->onlyTrashed()
                    ->whereHas('Confirm', function ($confirm) use ($dateObj) {
                        $confirm->whereBetween('created_at', [$dateObj->copy()->startOfWeek(), $dateObj->toDateString()]);
                    })->get();
                $currentMonthRequoteSavedObjArray = $this->dashboardArraySort($currentMonthReQuoteConfirmedObj, 'quotation_no');
                foreach ($currentMonthRequoteSavedObjArray as $Key => $array){
                    $currentWeekReQuoteConfirmed +=  count($array)  -1;
                }

                $weekConfimations = 0;
                $weekConfimationsObj = $this->getReportObj($request, $userID)->with('Confirm')->whereHas('Confirm', function ($confirm) use ($dateObj) {
                    $confirm->orderBy('created_at',"ASC")->whereBetween('created_at', [$dateObj->copy()->startOfWeek(), $dateObj->toDateString()]);
                })->where('status',2)->groupBy('quotation_no')->get();

                foreach ($weekConfimationsObj as $KeyObj => $valueObj){
                    if($valueObj->confirm->created_at) {
                        $date = Carbon::parse($valueObj->confirm->created_at);
                            $weekConfimations++;
                    }
                }

                $reportArray[$key]['data']['weekWise']['query'] = $currentWeekhSaved;
                $reportArray[$key]['data']['weekWise']['reQuote'] = $currentWeekReQuoteSaved + $currentWeekReQuoteConfirmed;
                $reportArray[$key]['data']['weekWise']['confirm'] = $weekConfimations;
                $reportArray[$key]['data']['weekWise']['dateRange'] = $dateObj->copy()->startOfWeek()->toFormattedDateString() . " to " . $sameDayObj->toFormattedDateString();


                // Current Month
                $currentMonthSaved = 0;
                $currentMonthSavedObj = $this->getReportObj($request, $userID)->whereBetween('created_at', [$sameDayObj->copy()->startOfMonth()->toDateString(), $dateObj->toDateString()])
                    ->whereColumn('ID','quotation_no')->withTrashed()->get();
//                return $currentMonthSaved;
                $currentMonthSaved = $currentMonthSavedObj->count();

                $currentMonthReQuoteSaved = 0;
                $currentMonthReQuoteSavedObj = $this->getReportObj($request, $userID)->whereBetween('created_at', [$sameDayObj->copy()->startOfMonth(), $dateObj->toDateString()])
                    ->onlyTrashed()->where('status', 1)->get();
                $currentMonthRequotesSavedObjArray = $this->dashboardArraySort($currentMonthReQuoteSavedObj, 'quotation_no');
                foreach ($currentMonthRequotesSavedObjArray as $keyOb => $array) {
                    $currentMonthReQuoteSaved += count($array);
                }

                $currentMonthReQuoteConfirmed = 0;
                $currentMonthReQuoteConfirmedObj = $this->getReportObj($request, $userID)->onlyTrashed()
                    ->whereHas('Confirm', function ($confirm) use ($dateObj,$sameDayObj) {
                        $confirm->whereBetween('created_at', [$sameDayObj->copy()->startOfMonth(), $dateObj->toDateString()]);
                    })->get();
                $currentMonthRequoteSavedObjArray = $this->dashboardArraySort($currentMonthReQuoteConfirmedObj, 'quotation_no');
                foreach ($currentMonthRequoteSavedObjArray as $Key => $array){
                    $currentMonthReQuoteConfirmed +=  count($array) -1;
                }

                $monthConfimations = 0;
                $monthConfimationsObj = $this->getReportObj($request, $userID)->with('Confirm')->whereHas('Confirm', function ($confirm) use ($dateObj,$sameDayObj) {
                    $confirm->orderBy('created_at',"ASC")->whereBetween('created_at', [$sameDayObj->copy()->startOfMonth(), $dateObj->toDateString()]);
                })->where('status',2)->groupBy('quotation_no')->get();


                foreach ($monthConfimationsObj as $KeyObj => $valueObj){
                    $quotationsNo = $valueObj->quotation_no;
                    $quotationObj = QuotationManage::where('quotation_no',$quotationsNo)->withTrashed()->where('status',2)->first();
                    $creationDate = Carbon::parse($quotationObj->created_at);
                    $valid = $creationDate->between($sameDayObj->copy()->startOfMonth(), $dateObj);
                    if($valid == true) {
                        $monthConfimations++;
                    }
                }

                $reportArray[$key]['data']['monthWise']['query'] = $currentMonthSaved;
                $reportArray[$key]['data']['monthWise']['reQuote'] = $currentMonthReQuoteSaved + $currentMonthReQuoteConfirmed;
                $reportArray[$key]['data']['monthWise']['confirm'] = $monthConfimations;
                $reportArray[$key]['data']['monthWise']['dateRange'] = $sameDayObj->copy()->startOfMonth()->toFormattedDateString() . " to " . $sameDayObj->toFormattedDateString();


                // Current Year

                $currentYearSaved = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])
                    ->whereColumn('ID','quotation_no')->withTrashed()->get()->count();

                $currentYearReQuoteSaved = 0;
                $currentYearReQuoteSavedObj = $this->getReportObj($request, $userID)->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])
                    ->onlyTrashed()->where('status', 1)->get()->toArray();
                $currentYearReQuoteArray = $this->dashboardArraySort($currentYearReQuoteSavedObj, 'quotation_no');
                foreach ($currentYearReQuoteArray as $Key => $array){
                    $currentYearReQuoteSaved +=  count($array)  ;
                }
                $currentYearReQuoteConfirmed = 0;
                $currentYearReQuoteConfirmedObj = $this->getReportObj($request, $userID)->onlyTrashed()
                    ->whereHas('Confirm', function ($confirm) use ($dateObj) {
                        $confirm->whereBetween('created_at', [$dateObj->copy()->startOfMonth(), $dateObj->toDateString()]);
                    })->get()->toArray();
                $currentYearReQuoteConfirmedArray = $this->dashboardArraySort($currentYearReQuoteConfirmedObj, 'quotation_no');
                foreach ($currentYearReQuoteConfirmedArray as $Key => $array){
                    $currentYearReQuoteConfirmed +=  count($array) -1  ;
                }

                $yearConfimations = 0;
                $yearConfimationsObj = $this->getReportObj($request, $userID)->with('Confirm')->whereHas('Confirm', function ($confirm) use ($dateObj) {
                    $confirm->orderBy('created_at',"ASC")->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()]);
                })->where('status',2)->groupBy('quotation_no')->get();

                foreach ($yearConfimationsObj as $KeyObj => $valueObj){
                    if($valueObj->confirm->created_at) {
                        $date = Carbon::parse($valueObj->confirm->created_at);
                        $yearConfimations++;
                    }
                }





                $reportArray[$key]['data']['yearWise']['query'] = $currentYearSaved;
                $reportArray[$key]['data']['yearWise']['reQuote'] = $currentYearReQuoteSaved + $currentYearReQuoteConfirmed;
                $reportArray[$key]['data']['yearWise']['confirm'] = $yearConfimations;
                $reportArray[$key]['data']['yearWise']['dateRange'] = $dateObj->copy()->startOfYear()->toFormattedDateString() . " to " . $sameDayObj->toFormattedDateString();
                $reportArray[$key]['data']['yearWise']['targetYear'] = $quotationTarget * 12;

                // Sales Track & agent Data
                $graphSalesTrackObj = $this->getReportObj($request, $userID)->whereHas('findSalesTrack')->with('findSalesTrack')->get();

                if (count($graphSalesTrackObj) > 0) {
                    $salesIds = array();
                    foreach ($graphSalesTrackObj as $salesKey => $value) {
                        array_push($salesIds, $value->findSalesTrack->ID);
                    }
                    $salesObjSaved = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) use ($sameDayObj) {
                        $quotation->whereDate('created_at', '=', $sameDayObj->toDateString())->whereRaw('ID','quotation_no');
                    })->get()->toArray();


                    $salesObjConfirmed = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) use ($sameDayObj) {
                        $quotation->where('status', 2)->whereDate('created_at', '=', $sameDayObj->toDateString())->groupBy('quotation_no');
                    })->get()->toArray();


                    $salesObjRequotes = array();
                    $salesObjRequotesSaved = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) use ($sameDayObj)  {
                        $quotation->whereDate('created_at', '=', $sameDayObj->toDateString())->where('status',1)->onlyTrashed();
                    })->get()->toArray();

                    $salesObjRequotesConfirmed = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) use ($sameDayObj)  {
                        $quotation->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                            $confirm->whereDate('created_at', '=', $sameDayObj->toDateString());
                        });
                    })->get()->toArray();
                    $salesObjRequotes = array_merge($salesObjRequotesSaved,$salesObjRequotesConfirmed);



                    $countsSaved = array();
                    foreach ($salesObjSaved as $value) {
                        foreach ($value as $k => $v) {
                            if (!isset($countsSaved[$k])) $countsSaved[$k] = array();
                            if (!isset($countsSaved[$k][$v])) $countsSaved[$k][$v] = 0;
                            $countsSaved[$k][$v] += 1;
                        }
                    }

                    $countsConfirmed = array();
                    foreach ($salesObjConfirmed as $value) {
                        foreach ($value as $k => $v) {
                            if (!isset($countsConfirmed[$k])) $countsConfirmed[$k] = array();
                            if (!isset($countsConfirmed[$k][$v])) $countsConfirmed[$k][$v] = 0;
                            $countsConfirmed[$k][$v] += 1;
                        }
                    }

                    $countsReQuotes = array();
                    foreach ($salesObjRequotes as $value) {
                        foreach ($value as $k => $v) {
                            if (!isset($countsReQuotes[$k])) $countsReQuotes[$k] = array();
                            if (!isset($countsReQuotes[$k][$v])) $countsReQuotes[$k][$v] = 0;
                            $countsReQuotes[$k][$v] += 1;
                        }
                    }
                    $datArray = array();


                    $i = 0;
                    foreach ($countsSaved['company_agent'] ?? [] as $savedKey => $SavedValue) {
                        $datArray[$i]['name'] = $savedKey;
                        $datArray[$i]['count'] = $SavedValue;
                        $datArray[$i]['type'] = "Queries";
                        $i++;
                    }
                    foreach ($countsConfirmed['company_agent'] ?? [] as $savedKey => $SavedValue) {
                        $datArray[$i]['name'] = $savedKey;
                        $datArray[$i]['count'] = $SavedValue;
                        $datArray[$i]['type'] = "Confirmations";
                        $i++;
                    }
                    foreach ($countsReQuotes['company_agent'] ?? [] as $savedKey => $SavedValue) {
                        $datArray[$i]['name'] = $savedKey;
                        $datArray[$i]['count'] = $SavedValue;
                        $datArray[$i]['type'] = "ReQuotes";
                        $i++;
                    }

                    $groupedAgentData = array_values($this->dashboardArraySort($datArray, 'name'));
                    $NameArray = ["Queries", "Confirmations", "ReQuotes"];

                    foreach ($groupedAgentData as $groupKey => $data) {

                        $TempAssigner = [];
                        $name = "";

                        foreach ($data as $keyInner => $valueInner) {
                            if (in_array($valueInner["type"], $TempAssigner)) {
                                // continue;
                            } else {
                                $TempAssigner[] = $valueInner["type"];
                                $name = $valueInner["name"];

                            }
                        }

                        foreach ($NameArray as $Check) {
                            if (!in_array($Check, $TempAssigner)) {
                                $tempArray = array(
                                    "name" => $name,

                                    "count" => 0,
                                    "type" => $Check,
                                );
                                array_push($groupedAgentData[$groupKey], $tempArray);
                            }
                        }
                    }

                    $datArray2 = array();


                    $i = 0;
                    foreach ($countsSaved['user_id'] ?? [] as $savedKey => $SavedValue) {
                        $datArray2[$i]['name'] = User::find($savedKey)->name;
                        $datArray2[$i]['userID'] = $savedKey;
                        $datArray2[$i]['count'] = $SavedValue;
                        $datArray2[$i]['type'] = "Queries";
                        $i++;
                    }
                    foreach ($countsConfirmed['user_id'] ?? [] as $savedKey => $SavedValue) {
                        $datArray2[$i]['name'] = User::find($savedKey)->name;
                        $datArray2[$i]['userID'] = $savedKey;
                        $datArray2[$i]['count'] = $SavedValue;
                        $datArray2[$i]['type'] = "Confirmations";
                        $i++;
                    }
                    foreach ($countsReQuotes['user_id'] ?? [] as $savedKey => $SavedValue) {
                        $datArray2[$i]['name'] = User::find($savedKey)->name;
                        $datArray2[$i]['userID'] = $savedKey;
                        $datArray2[$i]['count'] = $SavedValue;
                        $datArray2[$i]['type'] = "ReQuotes";
                        $i++;
                    }
                    $groupedSalesData = array_values($this->dashboardArraySort($datArray2, 'userID'));

                    foreach ($groupedSalesData as $groupKey => $data) {
                        $TempAssigner = [];
                        $name = "";
                        foreach ($data as $keyInner => $valueInner) {
                            if (in_array($valueInner["type"], $TempAssigner)) {
                                // continue;
                            } else {
                                $TempAssigner[] = $valueInner["type"];
                                $name = $valueInner["name"];
                            }
                        }

                        foreach ($NameArray as $Check) {
                            if (!in_array($Check, $TempAssigner)) {
                                $tempArray = array(
                                    "name" => $name,
                                    "count" => 0,
                                    "type" => $Check,
                                );
                                array_push($groupedSalesData[$groupKey], $tempArray);
                            }
                        }
                    }
                    $agentNames = array();

                    $agentSaved = array();
                    $agentReQuote = array();
                    $agentConfirm = array();


                    foreach ($groupedAgentData as $agentKey => $agentValue) {
                        array_push($agentNames, $agentValue[0]['name']);
                        foreach ($agentValue as $agKey => $agents) {
                            if ($agents['type'] == "Queries") {
                                array_push($agentSaved, $agents['count']);
                            } else if ($agents['type'] == "ReQuotes") {
                                array_push($agentReQuote, $agents['count']);
                            } else {
                                array_push($agentConfirm, $agents['count']);
                            }
                        }
                    }

                    $salesNames = array();

                    $salesSaved = array();
                    $salesReQuote = array();
                    $salesConfirm = array();


                    foreach ($groupedSalesData as $salesKey => $salesValue) {
                        array_push($salesNames, $salesValue[0]['name']);

                        foreach ($salesValue as $slKey => $sales) {
                            if ($sales['type'] == "Queries") {
                                array_push($salesSaved, $sales['count']);
                            } else if ($sales['type'] == "ReQuotes") {
                                array_push($salesReQuote, $sales['count']);
                            } else {
                                array_push($salesConfirm, $sales['count']);
                            }
                        }
                    }

                    $finalAgentArray = array(
                        array(
                            'name' => "Queries",
                            'data' => $agentSaved,
                            'color' => "#2196F3"

                        ),
                        array(
                            'name' => "ReQuotes",
                            'data' => $agentReQuote,
                            'color' => "#1758cf"

                        ),
                        array(
                            'name' => "Confirmations",
                            'data' => $agentConfirm,
                            'color' => "#4caf50"

                        ),

                    );


                    $finalSalesArray = array(
                        array(
                            'name' => "Queries",
                            'data' => $salesSaved,
                            'color' => "#2196F3"

                        ),
                        array(
                            'name' => "ReQuotes",
                            'data' => $salesReQuote,
                            'color' => "#1758cf"

                        ),
                        array(
                            'name' => "Confirmations",
                            'data' => $salesConfirm,
                            'color' => "#4caf50"

                        ),

                    );
//                    dd($finalAgentArray,$finalSalesArray);

                    if(count($groupedAgentData) > 0) {
                        $reportArray[$key]['data']['agentData'] = $groupedAgentData;
                        $reportArray[$key]['data']['agentChartData'] = $this->generateAgentChartData($userID, $finalAgentArray, $agentNames);
                    }
                    if(count($groupedSalesData) > 0) {
                        $reportArray[$key]['data']['salesData'] = $groupedSalesData;
                        $reportArray[$key]['data']['salesChartData'] = $this->generateSalesChart($userID, $finalSalesArray, $salesNames);
                    }
                }

                // Destination Data
                /*Confrim*/


                $todaConfirmed = $this->getReportObj($request, $userID)->with('Confirm')->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                    $confirm->orderBy('created_at',"ASC")->whereDate('created_at',$sameDayObj);
                })->where('status',2)->groupBy('quotation_no')->get();




                $DesArray = array();
                foreach ($todaConfirmed as $KeyObj => $valueObj){
                    if($valueObj->confirm->created_at) {
                        $date = Carbon::parse($valueObj->confirm->created_at);
                        if ($date->isSameDay($sameDayObj)) {
                            array_push($DesArray, $valueObj->toArray());
                        }
                    }
                }
                foreach ($DesArray as $keysofDesc => $keysofDescVal){
                    unset($DesArray[$keysofDesc]['confirm']);
                }


                $destinationConfirmDataObj = $this->getReportObj($request, $userID)
                    ->whereDate('created_at', '=', $sameDayObj->toDateString())->where('status', 2)->groupBy('quotation_no')->get()->toArray();


                    $confirmCounts = array();
                    foreach ($DesArray as $value) {
                        foreach ($value as $k => $v) {
                            if (!isset($confirmCounts[$k])) $confirmCounts[$k] = array();
                            if (!isset($confirmCounts[$k][$v])) $confirmCounts[$k][$v] = 0;
                            $confirmCounts[$k][$v] += 1;
                        }
                    }

                    /*Saved*/

                    $destinationSavedDataObj = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())
                        ->whereColumn('ID','quotation_no')->withTrashed()->get()->toArray();
                    $savedCounts = array();
                    foreach ($destinationSavedDataObj as $value) {
                        foreach ($value as $k => $v) {
                            if (!isset($savedCounts[$k])) $savedCounts[$k] = array();
                            if (!isset($savedCounts[$k][$v])) $savedCounts[$k][$v] = 0;
                            $savedCounts[$k][$v] += 1;
                        }
                    }


                $todayRequotesSaved = $this->getReportObj($request, $userID)->whereDate('created_at', '=', $sameDayObj->toDateString())->onlyTrashed()->where('status', 1)->get()->toArray();
                $todayRequotesConfirmed = $this->getReportObj($request, $userID)->onlyTrashed()->whereHas('Confirm', function ($confirm) use ($sameDayObj) {
                    $confirm->whereDate('created_at', $sameDayObj->toDateString());
                })->get()->toArray();

                    if(count($todayRequotesSaved) > 1) {
                        unset($todayRequotesSaved[ count($todayRequotesSaved)-1]);
                    }
                    if(count($todayRequotesConfirmed) > 1) {
                        unset($todayRequotesConfirmed[count($todayRequotesConfirmed)-1]);
                    }
                $destinationReQuoteDataObj = array_merge($todayRequotesSaved,$todayRequotesConfirmed);

                    $requoteCounts = array();
                    foreach ($destinationReQuoteDataObj ?? [] as $value) {
                        foreach ($value as $k => $v) {
                            if (!isset($requoteCounts[$k])) $requoteCounts[$k] = array();
                            if (!isset($requoteCounts[$k][$v])) $requoteCounts[$k][$v] = 0;
                            $requoteCounts[$k][$v] += 1;
                        }
                    }

                    $destinationArray = array();
                    $i = 0;
                    foreach ($confirmCounts['country'] ?? [] as $countryID => $count) {
                        $destinationArray[$i]['count'] = $count;
                        $destinationArray[$i]['placeId'] = $countryID;
                        $destinationArray[$i]['name'] = "Confirmations";
                        $destinationArray[$i]['status'] = 2;
                        $i++;
                    }

                    foreach ($savedCounts['country'] ?? [] as $countryID => $count) {
                        $destinationArray[$i]['count'] = $count;
                        $destinationArray[$i]['placeId'] = $countryID;
                        $destinationArray[$i]['name'] = "Queries";
                        $destinationArray[$i]['status'] = 1;
                        $i++;
                    }
                    foreach ($requoteCounts['country'] ?? [] as $countryID => $count) {
                        $destinationArray[$i]['count'] = $count;
                        $destinationArray[$i]['placeId'] = $countryID;
                        $destinationArray[$i]['name'] = "ReQuotes";
                        $destinationArray[$i]['status'] = 3;
                        $i++;
                    }

                    $destinationValues = $this->dashboardArraySort($destinationArray, 'placeId');

                    $destinationsVal = array();

                    $destinationSaved = array();
                    $destinationReQuote = array();
                    $destinationConfirm = array();

                    foreach ($destinationValues as $desKey => $destinations) {
                        array_push($destinationsVal, Place::where('ID', '=', $desKey)->first()->name);
                        foreach ($destinations as $destinationKey => $destination) {

                            if ($destination['name'] == "Queries") {
                                array_push($destinationSaved, $destination['count']);
                            } else if ($destination['name'] == "ReQuotes") {
                                array_push($destinationReQuote, $destination['count']);
                            } else {
                                array_push($destinationConfirm, $destination['count']);
                            }
                        }
                    }
                    $finalDestinationArray = array(
                        array(
                            'name' => "Queries",
                            'data' => $destinationSaved,
                            'color' => "#2196F3"

                        ),
                        array(
                            'name' => "ReQuotes",
                            'data' => $destinationReQuote,
                            'color' => "#1758cf"

                        ),
                        array(
                            'name' => "Confirmations",
                            'data' => $destinationConfirm,
                            'color' => "#4caf50"

                        ),

                    );
                if(count($destinationsVal) > 0) {
                    $reportArray[$key]['data']['destinationChartData'] = $this->generateDestinationChart($userID, $finalDestinationArray, $destinationsVal);
                    $reportArray[$key]['data']['destinationData'] = $destinationValues;
                }



            }
        } else {
            if (QuotationManage::where('sales_tracking_id', '=', $request->dashboard_sales_tracking_id)->exists()) {
                $reportArray[0]['type'] = "salesTrack";
                $reportArray[0]['userId'] = $this->getReportObj($request)->first()->Main->user;
                $reportArray[0]['email'] = User::find($this->getReportObj($request)->first()->Main->user)->email;
                $reportArray[0]['name'] = User::find($this->getReportObj($request)->first()->Main->user)->name;
                $reportArray[0]['salesTrackId'] = $request->dashboard_sales_tracking_id;
                $reportArray[0]['data']['dayWise']['query'] = $this->getReportObj($request)->whereRaw('ID','quotation_no')->groupBy('sales_tracking_id')->get()->count();
                $reportArray[0]['data']['dayWise']['reQuote'] = $this->getReportObj($request)->onlyTrashed()->count();
                $reportArray[0]['data']['dayWise']['confirm'] = $this->getReportObj($request)->whereRaw('ID','quotation_no')->groupBy('sales_tracking_id')->get()->count();
                $reportArray[0]['data']['dayWise']['dateRange'] = Carbon::parse($this->getReportObj($request)->first()->Main->created_at)->toFormattedDateString();

                if (apple_userTargets::where('user_id', '=', $this->getReportObj($request)->first()->Main->user)->exists()) {
                    $targetObj = apple_userTargets::where('user_id', '=', $this->getReportObj($request)->first()->Main->user)->first();
                    $quotationTarget = $targetObj->user_quotation_targets;
                    $profitTarget = $targetObj->user_profit_targets;
                } else {
                    $quotationTarget = 0;
                    $profitTarget = 0;
                }
                $reportArray[0]['quotationTarget'] = $quotationTarget;
                $reportArray[0]['profitTarget'] = $profitTarget;

                $graphSalesTrackObj = $this->getReportObj($request)->whereHas('findSalesTrack')->with('findSalesTrack')->first();
                if (!empty($graphSalesTrackObj)) {
                    $salesTrackId = $graphSalesTrackObj->sales_tracking_id;
                    $quotationNo = $graphSalesTrackObj->quotation_no;
                    $agentName = array($graphSalesTrackObj->findSalesTrack()->first()->company_agent);
                    $salesPerson = array(User::find($graphSalesTrackObj->findSalesTrack->first()->user_id)->name);

                    $agentSaved = 1;
                    $agentConfirm = 0;
                    if ($this->getReportObj($request)->whereHas('findSalesTrack')->with('findSalesTrack')->where('status', '2')->exists()) {
                        $agentConfirm = 1;
                    }
                    $agentReQuote = QuotationManage::where('quotation_no', $quotationNo)->onlyTrashed()->count();


                    $finalAgentArray = array(
                        array(
                            'name' => "Queries",
                            'data' => array($agentSaved),
                            'color' => "#2196F3"

                        ),
                        array(
                            'name' => "ReQuotes",
                            'data' => array($agentReQuote),
                            'color' => "#1758cf"

                        ),
                        array(
                            'name' => "Confirmations",
                            'data' => array($agentConfirm),
                            'color' => "#4caf50"

                        ),
                    );
//                    dd($agentName);
                    $reportArray[0]['data']['agentChartData'] = $this->generateAgentChartData($this->getReportObj($request)->first()->Main->user, $finalAgentArray, $agentName);
                    $reportArray[0]['data']['salesChartData'] = $this->generateSalesChart($this->getReportObj($request)->first()->Main->user, $finalAgentArray, $salesPerson);

                }
                $Destination = Place::where('ID', '=', $this->getReportObj($request)->first()->country)->first()->name;
                $Destination = array($Destination);
                $reportArray[0]['data']['destinationChartData'] = $this->generateDestinationChart($this->getReportObj($request)->first()->Main->user, $finalAgentArray, $Destination);
//                    $destinationValues = $this->dashboardArraySort($destinationArray, 'placeId');


            } else {
                $Status['error']['msgs'][0] = "Sales Track ID is Doesn't Exist or Sales Track ID is Not Used";
                $Status['status'] = false;
                return $Status;
            }
        }

        foreach ($reportArray as $arrKey => $value){
            $reportArray[$arrKey]['transfer'] = true;
        }
        $mergeData = $this->mergeGroupedArray($reportArray);

        $reportArray['teamMember'] = $reportArray;
        $reportArray['finalData'] = $mergeData['finalData'];

//        return json_encode($reportArray,true);
        return view('dashboard.dashboard_data', compact('reportArray'));
    }

    function getReportObj($request, $UserID = null)
    {
        $status = $request->dashboard_status;
        $destination = $request->dashboard_destination;
        $agent = $request->dashboard_agent;
        $salesTrack = $request->dashboard_sales_tracking_id;

        //WY5Q0001
        if (!empty($salesTrack)) {
            $Quotation = QuotationManage::where('sales_tracking_id', '=', $salesTrack)->with('Main', 'Confirm');
        } else {
            $Quotation = QuotationManage::whereHas('Main', function ($Main) use ($UserID) {
                $Main->where('user', $UserID);
            });
            if ($agent != 0) {
                $Quotation->whereHas('Confirm', function ($confirm) use ($agent) {
                    $confirm->where('agent', $agent);
                });
            }
            if ($status != 0) {
                $Quotation->where('status', $status);
            }
            if ($destination != 0) {
                $Quotation->where('country', $destination);
            }
        }
        return $Quotation;
    }

    function dashboardArraySort($input, $sortkey)
    {
        $output = array();
        foreach ($input as $key => $val) {
            $output[$val[$sortkey]][] = $val;
        }
        return $output;
    }

    function generateAgentChartData($userID, $AgentData, $agents)
    {

        $chart = \Chart::title([
            'text' => 'Agents',
        ])
            ->chart([
                'type' => 'column', // pie , columnt ect
                'renderTo' => $userID . "_AgentChart", // render the chart into your div with id
                'borderColor' => '#23547b',
                'borderWidth' => 1,
            ])
            ->subtitle([
                'text' => 'Quotations',
            ])
            ->colors([
                '#0c2959'
            ])->plotOptions(['column' => ['stacking' => 'normal', 'dataLabels' => ['enabled' => 'true']]])
            ->xaxis([
                'categories' => $agents,
                'title' => ['text' => 'Agents'],
                'labels' => [
                    'rotation' => 15,
                    'align' => 'top',
//                    'formatter' => 'startJs:function(){return this.value + " (Footbal Player)"}:endJs',
                    // use 'startJs:yourjavasscripthere:endJs'
                ],
            ])
            ->yaxis([
                'title' => ['text' => 'No Of Qutations'],
            ])
            ->legend([
                'layout' => 'vertical',
                'align' => 'right',
                'verticalAlign' => 'middle',
            ])
            ->credits(['enabled' => false])
            ->series($AgentData)
            ->display();

        return $chart;
    }

    function generateSalesChart($userID, $salesData, $sales)
    {
        $chart = \Chart::title([
            'text' => 'Sales',
        ])
            ->chart([
                'type' => 'column', // pie , columnt ect
                'renderTo' => $userID . "_salesChart", // render the chart into your div with id
                'borderColor' => '#23547b',
                'borderWidth' => 1,
            ])
            ->subtitle([
                'text' => 'Quotations',
            ])
            ->colors([
                '#0c2959'
            ])
            ->xaxis([
                'categories' => $sales,
                'title' => ['text' => 'Sales Persons'],
                'labels' => [
                    'rotation' => 15,
                    'align' => 'top',
//                    'formatter' => 'startJs:function(){return this.value + " (Footbal Player)"}:endJs',
                    // use 'startJs:yourjavasscripthere:endJs'
                ],
            ])
            ->yaxis([
                'title' => ['text' => 'No Of Qutations'],
            ])
            ->plotOptions(['column' => ['stacking' => 'normal', 'dataLabels' => ['enabled' => 'true']]])
            ->legend([
                'layout' => 'vertical',
                'align' => 'right',
                'verticalAlign' => 'middle',
            ])
            ->credits(['enabled' => false])
            ->series($salesData)
            ->display();

        return $chart;
    }

    function generateDestinationChart($userID, $destinationData, $destinations)
    {

        $chart = \Chart::title([
            'text' => 'Destinations',
        ])
            ->chart([
                'type' => 'column', // pie , columnt ect
                'renderTo' => $userID . "_destinationChart", // render the chart into your div with id
                'borderColor' => '#23547b',
                'borderWidth' => 1,
            ])
            ->subtitle([
                'text' => 'Quotations',
            ])
            ->colors([
                '#0c2959'
            ])
            ->xaxis([
                'categories' => $destinations,
                'title' => ['text' => 'Destinations'],
                'labels' => [
                    'rotation' => 15,
                    'align' => 'top',
//                    'formatter' => 'startJs:function(){return this.value + " (Footbal Player)"}:endJs',
                    // use 'startJs:yourjavasscripthere:endJs'
                ],
            ])
            ->yaxis([
                'title' => ['text' => 'No Of Qutations'],
            ])
            ->plotOptions(['column' => ['stacking' => 'normal', 'dataLabels' => ['enabled' => 'true']]])
            ->legend([
                'layout' => 'vertical',
                'align' => 'right',
                'verticalAlign' => 'middle',
            ])
            ->credits(['enabled' => false])
            ->series($destinationData)
            ->display();

        return $chart;
    }

    public function AddTeamLead(Request $request)
    {
        $Data = $request->input();

        $rules['teamLeads'] = "required|array";
        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }
        $teamLeads = $request->teamLeads;
        foreach ($teamLeads as $teamLead) {
            $apple_dashboard_teamleads = new apple_dashboard_teamleads();
            $apple_dashboard_teamleads->user_id = $teamLead;
            $apple_dashboard_teamleads->save();
        }
    }

    public function getTeamMembers(Request $request)
    {
        $teamleaderID = $request->teamleaderID;
        $teamLead = apple_dashboard_teamleads::find($teamleaderID);
        $userHierarchy = apple_dashboard_user_hierarchy::where('team_lead_id', $teamleaderID)->with('findUser')->get();
        $existingAllMembers = apple_dashboard_user_hierarchy::all();
        $teamLeadsObj = apple_dashboard_teamleads::all();
        $otherUsers = User::all();

        $existingMembers = array();
        $teamLeadAll = array();

        if ($existingAllMembers->count() > 0) {
            foreach ($existingAllMembers as $existing) {
                array_push($existingMembers, $existing->user_id);
            }
        }

        if ($teamLeadsObj->count() > 0) {
            foreach ($teamLeadsObj as $teamLeads) {
                array_push($teamLeadAll, $teamLeads->user_id);
            }
        }
        $otherMembers = User::whereNotIn('id', $existingMembers)->whereNotIn('id', $teamLeadAll)->get();
        return view('admin.dashboard.dashboard_team_member', compact('otherMembers', 'userHierarchy', 'teamLead', 'otherUsers'));
    }

    public function saveTeamMembers(Request $request)
    {
        $teamLeadID = $request->team_lead_id;

        $changeTeamLeadID = $request->teamLeaderChange;
        if (isset($request->existing_members)) {
            $deletedRows = apple_dashboard_user_hierarchy::where('team_lead_id', $teamLeadID)->delete();
            foreach ($request->existing_members as $key => $userId) {
                $apple_dashboard_user_hierarchy = new apple_dashboard_user_hierarchy();
                $apple_dashboard_user_hierarchy->user_id = $userId;
                $apple_dashboard_user_hierarchy->team_lead_id = $teamLeadID;
                $apple_dashboard_user_hierarchy->save();
            }
        }

        if (isset($request->new_members)) {
            foreach ($request->new_members as $key => $userId) {
                $apple_dashboard_user_hierarchy = new apple_dashboard_user_hierarchy();
                $apple_dashboard_user_hierarchy->user_id = $userId;
                $apple_dashboard_user_hierarchy->team_lead_id = $teamLeadID;
                $apple_dashboard_user_hierarchy->save();
            }
        }
        $apple_dashboard_teamleads = apple_dashboard_teamleads::find($teamLeadID);
        if ($apple_dashboard_teamleads->user_id != $changeTeamLeadID) {
            $apple_dashboard_teamleads->user_id = $changeTeamLeadID[0];
            $apple_dashboard_teamleads->save();
        }
    }

    public function getTeamMemberTransfer(Request $request)
    {
        $teamleaderID = $request->teamleaderID;
        $teamLead = apple_dashboard_teamleads::find($teamleaderID);
        $teamMembers = $teamLead->findTeamMembers;
        $allTeamLeads = apple_dashboard_teamleads::whereNotIn('id', [$teamleaderID])->get();
        return view('admin.dashboard.dashboard_team_member_transfer', compact('teamLead', 'teamMembers', 'allTeamLeads'));
    }

    public function saveTeamMemberTransfer(Request $request)
    {
        $Data = $request->input();
        $rules = array(
            'transfer_members' => "required|array",
            "transfer_team_lead" => "required"
        );


        $Validator = Validator::make($Data, $rules);

        if ($Validator->fails()) {
            $messages = $Validator->messages();
            $Status['error'] = $messages;
            $Status['status'] = false;
            return $Status;
        }

        $transferMembers = $request->transfer_members;
        $transferTeamLead = $request->transfer_team_lead;

        $teamMemberObj = apple_dashboard_user_hierarchy::whereIn('user_id', $transferMembers)->get();
        foreach ($teamMemberObj as $teamMember) {

            $apple_dashboard_user_transfer_history = new apple_dashboard_user_transfer_history();
            $apple_dashboard_user_transfer_history->user_id = $teamMember->user_id;
            $apple_dashboard_user_transfer_history->was_under_id = $teamMember->team_lead_id;
            $apple_dashboard_user_transfer_history->new_under_id = $transferTeamLead;
            $apple_dashboard_user_transfer_history->from_date = $teamMember->updated_at;
            $apple_dashboard_user_transfer_history->to_date = Carbon::now();
            $apple_dashboard_user_transfer_history->save();
        }

        $teamMemberObjUpdate = apple_dashboard_user_hierarchy::whereIn('user_id', $transferMembers)->update(['team_lead_id' => $transferTeamLead]);
        if ($teamMemberObjUpdate) {
            return array('status' => true, 'msg' => "Error.");
        } else {
            return array('status' => false, 'msg' => "Members Transfered Successfully.");
        }

    }

    public function viewHierarchy()
    {
        $teamLeads = apple_dashboard_teamleads::all();
        return view('admin.dashboard.dashboard_view_user_hierarchy', compact('teamLeads'));
    }

    public function downloadExcel(Request $request)
    {

        $styleArray = array(
            'borders' => array(
                'allborders' => array(
                    'style' => \PHPExcel_Style_Border::BORDER_THIN
                )
            )
        );
        $jsonData = $request->data_dashboard_export;
        $Data = json_decode($jsonData, true);
        $reportType = $request->dashboard_data_type;

        if ($reportType == "I") {
            $reportTypeName = "Individual Performance Report";

        } else {
            $reportTypeName = "Grouped Performance Report";
        }

        $fileName = Carbon::now()->toFormattedDateString() . "_" . Carbon::now()->toTimeString() ."_" .$reportTypeName;
        $excel = \Excel::create($fileName, function ($excel) use ($Data, $styleArray,$reportTypeName,$reportType) {

            $excel->sheet('Sheet', function ($sheet) use ($Data, $styleArray,$reportTypeName,$reportType) {
                $sheet->getProtection()->setPassword('p9QDAy@p_zu6K&GW');
                $sheet->getProtection()->setSheet(true);



                $sheet->cell('G2', function($cell) use($reportTypeName) {
                    $cell->setValue($reportTypeName);
                    $cell->setFontWeight('bold');
                    $cell->setAlignment('center');
                    $cell->setValignment('center');
                });


                $rangeC = count($Data['teamMember']) * 8;
                $rangeC = $rangeC + 8;
                $sheet->loadView("dashboard.excel.dashboard_data", ["data" => $Data['teamMember'],"fullData" => $Data,'reportType' => $reportType]);

                $sheet->getStyle('A3:P' . $rangeC)->applyFromArray($styleArray);
                $sheet->setWidth(array(
                    'A' => 10,
                ));

                foreach (range(3, $rangeC) as $number) {
                    $sheet->setHeight($number, 17);
                }

            });
        });
        return $excel->download('xls');
    }

}
