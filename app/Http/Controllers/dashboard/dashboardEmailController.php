<?php

namespace App\Http\Controllers\dashboard;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Mail\dashboardEmail;
use App\Model\Place\Place;
use App\Model\QuotationManage\Quotation as QuotationManage;
use Carbon\Carbon;
use Validator;
use DB;
use App\Model\UserTargets\apple_userTargets;
use App\Model\Admin\SalesTrack;

use App\User;
use View;
use Mail;

use App\Model\QuotationManage\Quotation;

class dashboardEmailController extends Controller
{
    public function __construct()
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 0);
        $this->middleware('auth')->except([
            'getReport',
        ]);
    }

    public function getReport()
    {

        $dateObj = Carbon::now()->addDay();
        $sameDayObj = Carbon::now();

        $reportArray = array();


        $reportArray['data']['dayWise']['query'] = $this->getReportObj()->whereDate('created_at', '=', $sameDayObj->toDateString())->where('status', 1)->count();
        $reportArray['data']['dayWise']['reQuote'] = $this->getReportObj()->whereDate('created_at', '=', $sameDayObj->toDateString())->onlyTrashed()->count();
        $reportArray['data']['dayWise']['confirm'] = $this->getReportObj()->whereDate('created_at', '=', $sameDayObj->toDateString())->where('status', 2)->count();
        $reportArray['data']['dayWise']['dateRange'] = $sameDayObj->toFormattedDateString();


        $reportArray['data']['monthWise']['query'] = $this->getReportObj()->whereBetween('created_at', [$dateObj->copy()->startOfMonth(), $dateObj->toDateString()])->where('status', 1)->count();
        $reportArray['data']['monthWise']['reQuote'] = $this->getReportObj()->whereBetween('created_at', [$dateObj->copy()->startOfMonth(), $dateObj->toDateString()])->onlyTrashed()->count();
        $reportArray['data']['monthWise']['confirm'] = $this->getReportObj()->whereBetween('created_at', [$dateObj->copy()->startOfMonth(), $dateObj->toDateString()])->where('status', 2)->count();
        $reportArray['data']['monthWise']['dateRange'] = $dateObj->copy()->startOfMonth()->toFormattedDateString() . " to " . $sameDayObj->toFormattedDateString();


        $reportArray['data']['yearWise']['query'] = $this->getReportObj()->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])->where('status', 1)->count();
        $reportArray['data']['yearWise']['reQuote'] = $this->getReportObj()->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])->onlyTrashed()->count();
        $reportArray['data']['yearWise']['confirm'] = $this->getReportObj()->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])->where('status', 2)->count();
        $reportArray['data']['yearWise']['dateRange'] = $dateObj->copy()->startOfYear()->toFormattedDateString() . " to " . $sameDayObj->toFormattedDateString();

        // Sales Track & agent Data
        $graphSalesTrackObj = $this->getReportObj()->whereHas('findSalesTrack')->with('findSalesTrack')->get();

        if (count($graphSalesTrackObj) > 0) {
            $salesIds = array();
            foreach ($graphSalesTrackObj as $salesKey => $value) {
                array_push($salesIds, $value->findSalesTrack->ID);
            }
            $salesObjSaved = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) {
                $quotation->where('status', 1);
            })->get()->toArray();


            $salesObjConfirmed = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) {
                $quotation->where('status', 2);
            })->get()->toArray();

            $salesObjRequotes = SalesTrack::whereIn('ID', $salesIds)->whereHas('findQuotation', function ($quotation) {
                $quotation->onlyTrashed();
            })->get()->toArray();

//                    dd($salesObjSaved,$salesObjConfirmed,$salesObjRequotes);

            $countsSaved = array();
            foreach ($salesObjSaved as $value) {
                foreach ($value as $k => $v) {
                    if (!isset($countsSaved[$k])) $countsSaved[$k] = array();
                    if (!isset($countsSaved[$k][$v])) $countsSaved[$k][$v] = 0;
                    $countsSaved[$k][$v] += 1;
                }
            }

            $countsConfirmed = array();
            foreach ($salesObjConfirmed as $value) {
                foreach ($value as $k => $v) {
                    if (!isset($countsConfirmed[$k])) $countsConfirmed[$k] = array();
                    if (!isset($countsConfirmed[$k][$v])) $countsConfirmed[$k][$v] = 0;
                    $countsConfirmed[$k][$v] += 1;
                }
            }

            $countsReQuotes = array();
            foreach ($salesObjRequotes as $value) {
                foreach ($value as $k => $v) {
                    if (!isset($countsReQuotes[$k])) $countsReQuotes[$k] = array();
                    if (!isset($countsReQuotes[$k][$v])) $countsReQuotes[$k][$v] = 0;
                    $countsReQuotes[$k][$v] += 1;
                }
            }
            $datArray = array();


            $i = 0;
            foreach ($countsSaved['company_agent'] ?? [] as $savedKey => $SavedValue) {
                $datArray[$i]['name'] = $savedKey;
                $datArray[$i]['count'] = $SavedValue;
                $datArray[$i]['type'] = "Queries";
                $i++;
            }
            foreach ($countsConfirmed['company_agent'] ?? [] as $savedKey => $SavedValue) {
                $datArray[$i]['name'] = $savedKey;
                $datArray[$i]['count'] = $SavedValue;
                $datArray[$i]['type'] = "Confirmations";
                $i++;
            }
            foreach ($countsReQuotes['company_agent'] ?? [] as $savedKey => $SavedValue) {
                $datArray[$i]['name'] = $savedKey;
                $datArray[$i]['count'] = $SavedValue;
                $datArray[$i]['type'] = "ReQuotes";
                $i++;
            }
            $groupedAgentData = array_values($this->dashboardArraySort($datArray, 'name'));

            foreach ($groupedAgentData as $groupKey => $data) {
                $groupedAgentData[$groupKey]['name'] = $data[0]['name'];

            }

            $datArray2 = array();


            $i = 0;
            foreach ($countsSaved['user_id'] ?? [] as $savedKey => $SavedValue) {
                $datArray2[$i]['name'] = User::find($savedKey)->name;
                $datArray2[$i]['userID'] = $savedKey;
                $datArray2[$i]['count'] = $SavedValue;
                $datArray2[$i]['type'] = "Queries";
                $i++;
            }
            foreach ($countsConfirmed['user_id'] ?? [] as $savedKey => $SavedValue) {
                $datArray2[$i]['name'] = User::find($savedKey)->name;
                $datArray2[$i]['userID'] = $savedKey;
                $datArray2[$i]['count'] = $SavedValue;
                $datArray2[$i]['type'] = "Confirmations";
                $i++;
            }
            foreach ($countsReQuotes['user_id'] ?? [] as $savedKey => $SavedValue) {
                $datArray2[$i]['name'] = User::find($savedKey)->name;
                $datArray2[$i]['userID'] = $savedKey;
                $datArray2[$i]['count'] = $SavedValue;
                $datArray2[$i]['type'] = "ReQuotes";
                $i++;
            }
            $groupedSalesData = array_values($this->dashboardArraySort($datArray2, 'userID'));

            foreach ($groupedSalesData as $groupKey => $data) {
                $groupedSalesData[$groupKey]['name'] = $data[0]['name'];
            }
            $reportArray['data']['agentData'] = $groupedAgentData;
            $reportArray['data']['salesData'] = $groupedSalesData;
        }

        // Destination Data
        /*Confrim*/
        $destinationConfirmDataObj = $this->getReportObj()->where('status', 2)
            ->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])
            ->get()->toArray();
        if(count($destinationConfirmDataObj) > 0) {
            $confirmCounts = array();
            foreach ($destinationConfirmDataObj as $value) {
                foreach ($value as $k => $v) {
                    if (!isset($confirmCounts[$k])) $confirmCounts[$k] = array();
                    if (!isset($confirmCounts[$k][$v])) $confirmCounts[$k][$v] = 0;
                    $confirmCounts[$k][$v] += 1;
                }
            }
            /*Saved*/
            $destinationSavedDataObj = $this->getReportObj()->where('status', 1)
                ->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])
                ->get()->toArray();
            $savedCounts = array();
            foreach ($destinationSavedDataObj as $value) {
                foreach ($value as $k => $v) {
                    if (!isset($savedCounts[$k])) $savedCounts[$k] = array();
                    if (!isset($savedCounts[$k][$v])) $savedCounts[$k][$v] = 0;
                    $savedCounts[$k][$v] += 1;
                }
            }
            $destinationReQuoteDataObj = $this->getReportObj()->onlyTrashed()
                ->whereBetween('created_at', [$dateObj->copy()->startOfYear(), $dateObj->toDateString()])
                ->get()->toArray();
            $requoteCounts = array();
            foreach ($destinationReQuoteDataObj as $value) {
                foreach ($value as $k => $v) {
                    if (!isset($requoteCounts[$k])) $requoteCounts[$k] = array();
                    if (!isset($requoteCounts[$k][$v])) $requoteCounts[$k][$v] = 0;
                    $requoteCounts[$k][$v] += 1;
                }
            }
            $destinationArray = array();
            $i = 0;
            foreach ($confirmCounts['country'] ?? [] as $countryID => $count) {
                $destinationArray[$i]['count'] = $count;
                $destinationArray[$i]['placeId'] = $countryID;
                $destinationArray[$i]['name'] = "Confirmations";
                $destinationArray[$i]['status'] = 2;
                $i++;
            }

            foreach ($savedCounts['country'] ?? [] as $countryID => $count) {
                $destinationArray[$i]['count'] = $count;
                $destinationArray[$i]['placeId'] = $countryID;
                $destinationArray[$i]['name'] = "Queries";
                $destinationArray[$i]['status'] = 1;
                $i++;
            }
            foreach ($requoteCounts['country'] ?? [] as $countryID => $count) {
                $destinationArray[$i]['count'] = $count;
                $destinationArray[$i]['placeId'] = $countryID;
                $destinationArray[$i]['name'] = "ReQuotes";
                $destinationArray[$i]['status'] = 3;
                $i++;
            }
            $destinationValues = $this->dashboardArraySort($destinationArray, 'placeId');
            $finalDestinationArray = array();

            foreach ($destinationValues as $desKey => $destinations) {
                 $finalDestinationArray[$desKey]['destination'] =  Place::where('ID', '=', $desKey)->first()->name;
                 $finalDestinationArray[$desKey]['data'] =  $destinations;
            }
            $reportArray['data']['destinationChartData'] = array_values($finalDestinationArray);
        }
//        return json_encode($reportArray);
        $subject = "Quotation Summary For ".$sameDayObj->toFormattedDateString();

        $users =  User::whereHas('Profile', function ($profile) {
            $profile->where('company', 1);
        })->get();
        $emails = array();
        foreach ($users as $user) {
            if(!($user->id == 455) || ($user->id == 454)) {
                array_push($emails,$user->email);
            }
        }
        $FinalHtml = View::make('dashboard.dashboard_email_data', compact('reportArray'))->render();
        Mail::to($emails)->send(new dashboardEmail($emails, $subject, $FinalHtml));
        return $FinalHtml;

    }

    function getReportObj()
    {
        $Quotation = QuotationManage::whereHas('Main');
        return $Quotation;
    }

    function dashboardArraySort($input, $sortkey)
    {

        foreach ($input as $key => $val) {
            $output[$val[$sortkey]][] = $val;
        }
        return $output;
    }
}
