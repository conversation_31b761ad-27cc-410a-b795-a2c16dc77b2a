<?php

namespace App\Http\Controllers\Meal;

use App\Http\Controllers\Controller;
use App\Model\Meal\Meal as MealModel;
use App\Model\Meal\MealType;
use App\Model\Place\Place;
use App\Model\Restaurant\RestaurantMealRate;
use Session;
use DB;
/**
 * Class Meal
 * @package App\Http\Controllers\Meal
 */
class Meal extends Controller
{


    /**
     * @param bool $preference
     * @param bool $restaurant
     * @param bool $mealTime
     * @return \Illuminate\Http\JsonResponse
     */
    function getOutsideMeal($preference = false, $restaurant = false, $mealTime = false)
    {

        if (!$restaurant) {
            $MealType = new MealType();
            return jsona($MealType->find($preference));
        } elseif ($restaurant && $mealTime) {

            $RestaurantMealRate = new RestaurantMealRate();
            $rate = $RestaurantMealRate->where("restaurant", $restaurant)->where("meal_time", $mealTime)->first();

            if ($rate)
                return jsona($rate);
            else {
                $MealModel = new MealModel();
                $rate = $MealModel->getDefaultOutsideResturentRate($mealTime);
                return jsona($rate);
            }
        } else {
            return jsona(['false'],418);
        }
    }

    function getOutsideMealResturent($type = false, $from, $mealTime) {
        $returents = DB::table('apple_meal_type_restaurant')
            ->join('apple_restaurant', 'apple_restaurant.ID', '=', 'apple_meal_type_restaurant.restaurant_id')
            ->join('apple_meal_type', 'apple_meal_type.ID', '=', 'apple_meal_type_restaurant.meal_type_id')
            ->join('apple_restaurant_meal_rate', 'apple_restaurant.ID', '=', 'apple_restaurant_meal_rate.restaurant')
            ->select('apple_restaurant.ID','apple_restaurant.name')
            ->where('apple_meal_type.ID','=',$type)
            ->where('apple_restaurant.from','=',$from)
            ->where('apple_restaurant_meal_rate.meal_time','=',$mealTime)
            ->groupBy("ID")
            ->get();

        return view("element.meal.meal-item-option-item", compact("returents"));

    }


    function getRestaurantMealTypes($restaurant = false) {

        $meal_types = DB::table('apple_meal_type_restaurant')
                      ->join('apple_restaurant', 'apple_restaurant.ID', '=', 'apple_meal_type_restaurant.restaurant_id')
                      ->join('apple_meal_type', 'apple_meal_type.ID', '=', 'apple_meal_type_restaurant.meal_type_id')
                      ->select('apple_meal_type.meal','apple_meal_type.ID')
                      ->where('apple_meal_type_restaurant.restaurant_id','=',$restaurant)
                      ->get();

        return response()->json(['meal_types' => $meal_types]);

    }
}
