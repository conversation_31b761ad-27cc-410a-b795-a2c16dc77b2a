<?php


namespace App\Http\Controllers\Follow;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Quotation\Quotation;
use App\Model\FollowUP\ClientFollowUP;
use App\Model\QuotationManage\Quotation as QuotationManage;
use App\User;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Model\FollowUP\FollowUP;
use Illuminate\Support\Facades\DB;

use Maatwebsite\Excel\Facades\Excel;

/**
 * Class FollowUPController
 * @package App\Http\Controllers\Flight
 */
class FollowUPController extends Controller {

    function followUP(Request $request) {
        return view("quotation.follow-up-form");
    }

    function followUPSave(Request $request) {
        $followUP = new FollowUP();
        $arr = explode("-",  $_POST['tour_number']);

        $followUP->tour_no = trim($arr[0]);
        $followUP->tour_requote = trim($arr[1]);
        $followUP->sales_track_id = $_POST['sales_track_id'];
        $followUP->agent_id = $_POST['agent_name'];
        $followUP->called_at = $_POST['date_time'];
        $followUP->contact_person = $_POST['contact_person'];
        $followUP->remark = $_POST['remark'];
        $followUP->followup_type = $_POST['followup_type'];
        $followUP->user_id = Auth::user()->id;

        $followUP->save();

    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function followSearch(Request $request) {

        $followUPs = DB::table('apple_follow_up')
            ->join('users', 'users.id', '=', 'apple_follow_up.user_id')
            ->join('apple_agent', 'apple_agent.id', '=', 'apple_follow_up.agent_id')
            ->join('apple_quotation_main', 'apple_follow_up.tour_no', '=', 'apple_quotation_main.reference_id')
            ->leftJoin('apple_quotation_confirm', 'apple_quotation_confirm.reference_id', '=', 'apple_follow_up.tour_no')
            ->select('apple_follow_up.*','users.name AS user_name', 'apple_agent.name AS agent_name', 'apple_quotation_confirm.ID as confirm_id',
                'apple_quotation_main.arrival_year','apple_quotation_main.arrival_month','apple_quotation_main.arrival_day');

        if(!empty($_POST["tour_no"])) {
            $followUPs->where("tour_no", "=", $_POST["tour_no"]);
        }
        if(!empty($_POST["user"])) {
            $followUPs->where("user_id", "=", $_POST["user"]);
        }

        if (!empty($request->from_date) && !empty($request->to_date)) {
            $followUPs->whereBetween('called_at', [$request->from_date, $request->to_date]);
        }

	    $followUPs->orderBy('ID', 'desc');
	    $followUPs->orderBy('tour_no', 'desc');
	    $followUPs->orderBy('tour_requote', 'desc');

	    $followUPs = $followUPs->get();


        foreach ($followUPs as $key => $value) {
            $arrivalDate = Carbon::create($value->arrival_year,$value->arrival_month,$value->arrival_day);
            $followUPs[$key]->arriavalDate = $arrivalDate->toDateString();
            $followUPs[$key]->arriavalDateFormat = $arrivalDate->toFormattedDateString();
        }
        $monthData = array();
        foreach ($followUPs as $key => $value) {
            $arrDate = $value->arriavalDate;

            $arrDate = \Carbon\Carbon::parse($arrDate);
            $now = \Carbon\Carbon::now();
            $month = $arrDate->copy()->startOfMonth();
            $valid = $arrDate->between($month,$now);
            if(!$valid) {
                array_push($monthData,$value);
                unset($followUPs[$key]);
            }
        }

        $sort = array();
        foreach ($monthData as $monthKey => $monthValue){
            $arrDate = $monthValue->arriavalDate;
            $arrDate = \Carbon\Carbon::parse($arrDate);
            $sort[$monthKey]['key'] = $monthKey ;
            $sort[$monthKey]['arrivalDate'] = $arrDate->toDateString();
        }
        foreach ($sort as $key => $row) {
            $count[$key] = $row['arrivalDate'];
        }
        if(!empty($sort)) {
            array_multisort($count, SORT_ASC, $sort);
            $sortedArray = array();
            foreach ($sort as $sortKey => $sortValue) {
                $key = $sortValue['key'];
                $sortedArray[$sortKey] = $monthData[$key];
            }

            $followUPs = $followUPs->toArray();
            $followUPs = array_merge($sortedArray, $followUPs);
        }



        $followUPsNew = array();
        foreach ($followUPs as $key => $value) {
            if(isset($value->tour_no)) {
                $AllQuote = QuotationManage::withTrashed()->where("quotation_no", $value->tour_no)->get();
                $count=0;
                $currStatus='1';
                $PrevStatus='1';
                foreach ($AllQuote as $Quote) {

                    $currStatus = $Quote->status;

                    if($currStatus==1 && $PrevStatus==2) {
                        break;
                    }
                    $count++;
                    $PrevStatus=$currStatus;

                }

                for ($i=1; $i<=$count;$i++) {

                    if($value->tour_requote == "R".$i) {
                        $followUPsNew[$value->tour_no][$i] = $value;
                    } else if(isset($followUPsNew[$value->tour_no][$i]->ID) && $followUPsNew[$value->tour_no][$i]->ID != "-") {
                        continue;
                    } else {
                        $vallue = (object)[];
                        $vallue->ID = "-";
                        $vallue->tour_no = $value->tour_no;
                        $vallue->tour_requote = "R".$i;
                        $vallue->sales_track_id = "-";
                        $vallue->agent_id = "-";
                        $vallue->contact_person = "-";
                        $vallue->remark = "-";
                        $vallue->called_at = "-";
                        $vallue->created_at = "-";
                        $vallue->updated_at = "-";
                        $vallue->user_name = $value->user_name;
                        $vallue->agent_name = "-";
                        $vallue->confirm_id = null;
                        $vallue->arriavalDate = $value->arriavalDate;
                        $vallue->arriavalDateFormat = $value->arriavalDateFormat;
                        $vallue->colour  = $value->arriavalDateFormat;

                        $followUPsNew[$value->tour_no][$i] = $vallue;
                    }
                }
            }

        }

        $followUPs = $followUPsNew;
//        dd($followUPs);

        return view("follow-up.report", compact('followUPs'));
    }
    function date_compare($a, $b)
    {
        $t1 = strtotime($a['arrivalDate']);
        $t2 = strtotime($b['arrivalDate']);
        return $t1 - $t2;
    }

    public function downloadfollowUpToExcel(Request $request) {

        $followUPs = DB::table('apple_follow_up')
            ->join('users', 'users.id', '=', 'apple_follow_up.user_id')
            ->join('apple_agent', 'apple_agent.id', '=', 'apple_follow_up.agent_id')
            ->join('apple_quotation_main', 'apple_follow_up.tour_no', '=', 'apple_quotation_main.reference_id')
            ->leftJoin('apple_quotation_confirm', 'apple_quotation_confirm.reference_id', '=', 'apple_follow_up.tour_no')
            ->select('apple_follow_up.*','users.name AS user_name', 'apple_agent.name AS agent_name', 'apple_quotation_confirm.ID as confirm_id',
                'apple_quotation_main.arrival_year','apple_quotation_main.arrival_month','apple_quotation_main.arrival_day');

        if(!empty($request->tour_no)) {
            $followUPs->where("tour_no", "=", $request->tour_no);
        }
        if(!empty($request->user)) {
            $followUPs->where("user_id", "=", $request->user);
        }

        if (!empty($request->from_date) && !empty($request->to_date)) {
            $followUPs->whereBetween('called_at', [$request->from_date, $request->to_date]);
        }

        $followUPs->orderBy('tour_no', 'desc');
        $followUPs->orderBy('tour_requote', 'desc');
        $followUPs = $followUPs->get();

        foreach ($followUPs as $key => $value) {
            $arrivalDate = Carbon::create($value->arrival_year,$value->arrival_month,$value->arrival_day);
            $followUPs[$key]->arriavalDate = $arrivalDate->toDateString();
            $followUPs[$key]->arriavalDateFormat = $arrivalDate->toFormattedDateString();
        }
        $monthData = array();
        foreach ($followUPs as $key => $value) {
            $arrDate = $value->arriavalDate;

            $arrDate = \Carbon\Carbon::parse($arrDate);
            $now = \Carbon\Carbon::now();
            $month = $arrDate->copy()->startOfMonth();
            $valid = $arrDate->between($month,$now);
            if(!$valid) {
                array_push($monthData,$value);
                unset($followUPs[$key]);
            }
        }

        $sort = array();
        foreach ($monthData as $monthKey => $monthValue){
            $arrDate = $monthValue->arriavalDate;
            $arrDate = \Carbon\Carbon::parse($arrDate);
            $sort[$monthKey]['key'] = $monthKey ;
            $sort[$monthKey]['arrivalDate'] = $arrDate->toDateString();
        }
        foreach ($sort as $key => $row) {
            $count[$key] = $row['arrivalDate'];
        }
        if(!empty($sort)) {
            array_multisort($count, SORT_ASC, $sort);
            $sortedArray = array();
            foreach ($sort as $sortKey => $sortValue) {
                $key = $sortValue['key'];
                $sortedArray[$sortKey] = $monthData[$key];
            }

            $followUPs = $followUPs->toArray();
            $followUPs = array_merge($sortedArray, $followUPs);
        }

        $followUPsNew = array();
        foreach ($followUPs as $key => $value) {
            if(isset($value->tour_no)) {
                $AllQuote = QuotationManage::withTrashed()->where("quotation_no", $value->tour_no)->get();
                $count=0;
                $currStatus='1';
                $PrevStatus='1';
                foreach ($AllQuote as $Quote) {

                    $currStatus = $Quote->status;

                    if($currStatus==1 && $PrevStatus==2) {
                        break;
                    }
                    $count++;
                    $PrevStatus=$currStatus;

                }

                for ($i=1; $i<=$count;$i++) {

                    if($value->tour_requote == "R".$i) {
                        $followUPsNew[$value->tour_no][$i] = $value;
                    } else if(isset($followUPsNew[$value->tour_no][$i]->ID) && $followUPsNew[$value->tour_no][$i]->ID != "-") {
                        continue;
                    } else {
                        $vallue = (object)[];
                        $vallue->ID = "-";
                        $vallue->tour_no = $value->tour_no;
                        $vallue->tour_requote = "R".$i;
                        $vallue->sales_track_id = "-";
                        $vallue->agent_id = "-";
                        $vallue->contact_person = "-";
                        $vallue->remark = "-";
                        $vallue->called_at = "-";
                        $vallue->created_at = "-";
                        $vallue->updated_at = "-";
                        $vallue->user_name = $value->user_name;
                        $vallue->agent_name = "-";
                        $vallue->confirm_id = null;
                        $vallue->arriavalDate = $value->arriavalDate;
                        $vallue->arriavalDateFormat = $value->arriavalDateFormat;
                        $followUPsNew[$value->tour_no][$i] = $vallue;
                    }
                }
            }
        }

        $followUPs = $followUPsNew;

        $data = [];
        $i = 0;

        foreach ($followUPs as $key => $follow){

            foreach ($follow as $k => $f){
                $data[$i]['user_name'] = $f->user_name;
                $data[$i]['sales_track_id'] = $f->sales_track_id;
                $data[$i]['tour_no'] = $f->tour_no;
                $data[$i]['tour_requote'] = $f->tour_requote;
                $data[$i]['agent_name'] = $f->agent_name;
//            $data[$key]['agent_id'] = $f-agent_id;
                $data[$i]['contact_person'] = $f->contact_person;
                $data[$i]['arrival_date'] = $f->arriavalDateFormat;
//            $data[$key]['user_id'] = $f-user_id;
                $data[$i]['called_date'] = $f->called_at;
                $data[$i]['remark'] = $f->remark;
                 $i++;
            }

        }

        Excel::create('Follow Up Data', function($excel) use ($data) {

            $excel->sheet('Sheet', function($sheet) use ($data) {

                $sheet->fromArray($data);
            });
        })->download('xls');

        return redirect('#follow_up_report_container');

    }

    public function clientFollowSearch(Request $request) {

        $clientFollowUps = DB::table('client_follow_up_details');

        if(!empty($_POST["tour_id"])) {
            $clientFollowUps->where("tour_id", "=", $request->tour_id);
        }
        /*if(!empty($_POST["user_id"])) {
            $clientFollowUps->where("user_id", "=", $request->user_id);
        }*/

        if (!empty($request->from_date) && !empty($request->to_date)) {
            $clientFollowUps->whereBetween('date', [$request->from_date, $request->to_date]);
        }
//        $clientFollowUps->orderBy('id', 'desc');
        $clientFollowUps = $clientFollowUps->get()->toArray();

        return view("follow-up.client-follow-up-report", compact('clientFollowUps'));
    }

    public function clientFollowUpSubmit(Request $request)
    {
        $clientFollowUp = new ClientFollowUP();

        $clientFollowUp->tour_id = $request->tour_id;
        $clientFollowUp->client_name = $request->client_name;
        $clientFollowUp->hotel_name = $request->hotel_name;
        $clientFollowUp->vehicle_review = $request->vehicle_review;
        $clientFollowUp->hotel_review = $request->hotel_review;
        $clientFollowUp->guide_review = $request->guide_review;
        $clientFollowUp->food_review = $request->food_review;
        $clientFollowUp->overall_experience_review = $request->overall_experience_review;

        if ($request->date)
            $clientFollowUp->date = $request->date;
        else
            $clientFollowUp->date = Carbon::now()->format('Y-m-d');

        $clientFollowUp->comments = $request->comments;
        $clientFollowUp->user_id = Auth::user()->id;

        $clientFollowUp->saveOrFail();

        $img = $request->file('comment_img');

        if($img && $img->isValid()) {
            \Storage::disk('image')->put('/client_comment/'.$clientFollowUp->id.'.jpg', file_get_contents($img));
        }

        return response()->json(['status' => 'Client follow up added successfully!']);
    }

    public function downloadClientFollowUpToExcel(Request $request)
    {
        $clientFollowUps = DB::table('client_follow_up_details');

        if(!empty( $request->tour_id)) {
            $clientFollowUps->where("tour_id", "=", $request->tour_id);
        }

        if (!empty($request->from_date) && !empty($request->to_date)) {
            $clientFollowUps->whereBetween('date', [$request->from_date, $request->to_date]);
        }

        $clientFollowUps = $clientFollowUps->get();

        $data = [];

        foreach ($clientFollowUps as $key => $follow){
            $data[$key]['id'] = $follow->id;
            $data[$key]['tour_id'] = $follow->tour_id;
            $data[$key]['client_name'] = $follow->client_name;
            $data[$key]['hotel_name'] = $follow->hotel_name;
            $data[$key]['vehicle_review'] = $follow->vehicle_review;
            $data[$key]['guide_review'] = $follow->guide_review;
            $data[$key]['food_review'] = $follow->food_review;
            $data[$key]['overall_experience_review'] = $follow->overall_experience_review;
            $data[$key]['date'] = $follow->date;
            $data[$key]['comments'] = $follow->comments;
        }

        Excel::create('Export data', function($excel) use ($data) {

            $excel->sheet('Sheet', function($sheet) use ($data) {

                $sheet->fromArray($data);
            });
        })->download('xls');

        return redirect('#client_follow_up_report_container');

    }

    public function getSalesTrackIdByQuotation(Request $request)
    {
        $tourNo = explode('-', $request->tour_no);

        $quotationNo = $tourNo[0];

        $salesTrack = QuotationManage::where('quotation_no', $quotationNo)->first();

        $salesTrackId = $salesTrack->sales_tracking_id ?? null;

        return $salesTrackId;
    }
    function findClosestDate($dates, $finDate){
        $newDates = array();

        foreach($dates as $date)
        {
            $newDates[] = strtotime($date);
        }
        sort($newDates);
        foreach ($newDates as $a)
        {
            if ($a >= strtotime($finDate));
        }
        return $newDates;
    }

}