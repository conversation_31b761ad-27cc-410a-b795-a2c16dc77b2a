<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Model\Agent\Agent;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\Market;
use App\Model\Place\Place;
use App\Model\QuotationManage\Quotation;
use App\Model\QuotationManage\QuotationHotel;
use App\Model\QuotationManage\QuotationConfirm;
use App\Model\Quotation\QuotationHotel as QuotationHot;
use App\User;
use DB;
use Carbon\Carbon;
/**
 * Class Reports
 * @package App\Http\Controllers
 */
class Reports extends Controller
{
    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getReport()
    {
        $type = request('report_type');
        $data = request()->toArray();

        $Report = new \App\Model\Reports();
        $Data = $Report->getReport($type, $data, "");

        return view("quotation.report_table", ["Data" => $Data]);
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function filters()
    {

        return view('quotation.report');

    }

    function downloadExcel()
    {

        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '256M');

        $type = request('report_type');
        $data = request()->toArray();
        $data['limit'] = 20000;
        $cat = "excel";

        $Report = new \App\Model\Reports();
        $Data = $Report->getReport($type, $data, $cat);

        \Excel::create($type, function ($excel) use ($Data) {
            $excel->sheet('Sheet', function ($sheet) use ($Data) {
                $sheet->loadView("quotation.report_table", ["Data" => $Data, "table_only" => true]);
            });
        })->download('xls');
    }

    function agentProfileQuestionFilters() {
        return view('quotation.report');
    }

    function salesTrackReport() {
        return view('quotation.report');
    }

    function getHotelReport()
    {

        return view('quotation.reports.hotel-report');

    }

    function getUserReport()
    {

        return view('quotation.reports.user-report');

    }

    function getHotelReportSearch(Request $request)
    {
        $data = $request->all();

        ini_set('memory_limit', '-1');
		ini_set('max_execution_time', 0);
        $page = $data['page'] ?? null;

		$Data = [];
		$QuotationHotel = new QuotationHotel();
        $QuotationHot = new QuotationHot();
		$QuotationHotel = $QuotationHotel->whereHas("quotation.confirm");
		
		#Filters
		if (isset($data['check_in_start']) && isset($data['check_in_end'])) {
            $startDate = Carbon::parse($data['check_in_start']);
            $endDate = Carbon::parse($data['check_in_end']);

            $QuotationHotel = $QuotationHotel->where(function ($query) use ($startDate, $endDate) {
                $query->whereRaw("STR_TO_DATE(CONCAT(check_in_year, '-', LPAD(check_in_month, 2, '0'), '-', LPAD(check_in_day, 2, '0')), '%Y-%m-%d') >= ?", [$startDate->format('Y-m-d')])
                    ->whereRaw("STR_TO_DATE(CONCAT(check_in_year, '-', LPAD(check_in_month, 2, '0'), '-', LPAD(check_in_day, 2, '0')), '%Y-%m-%d') <= ?", [$endDate->format('Y-m-d')]);
            });
		}
		
		//if (isset($data['check_out'])) {
		//	$date = Carbon::parse($data['check_out']);
		//	$QuotationHotel = $QuotationHotel->where("check_out_year", $date->year)->where("check_out_month", $date->month)->where("check_out_day", $date->day);
		//}
		
		if (isset($data['hotels'])) {
			$QuotationHotel = $QuotationHotel->whereIn("hotel", $data['hotels']);
		}
		
		if (isset($data['user'])) {
			$QuotationHotel = $QuotationHotel->whereHas("quotation.main", function ($q) use ($data) {
				$q->whereIn('user', $data['user']);
			});
		}

		$QuotationHotel = $QuotationHotel->groupBy('reference_id');
		$QuotationHotel = $QuotationHotel->orderBy('created_at', 'ASC');
        $PaginateResult = $QuotationHotel->paginate(20, ['*'], 'page', $page);

        $i = 0;
		foreach ($PaginateResult as $HotelSettings) {
			$QuoteItem = $HotelSettings->quotation()->first();
			$checkin = array('year'=>(int)$HotelSettings->check_in_year, 'month'=>(int)$HotelSettings->check_in_month, 'day'=>(int)$HotelSettings->check_in_day);
			$checkout = array('year'=>(int)$HotelSettings->check_out_year, 'month'=>(int)$HotelSettings->check_out_month, 'day'=>(int)$HotelSettings->check_out_day);
            // dd($checkin, $checkout);
            $nights = $QuotationHot->getNightHotelBook($checkin, $checkout);

			$DataArray['tour_no'] = $QuoteItem->quotation_no;
			$DataArray['hotel'] = Hotel::find($HotelSettings->hotel)->name ?? "[Own Arrangement]";
			$DataArray['file_handler'] = User::find($QuoteItem->Main()->first()->user)->name;
			$DataArray['nights'] = $nights;

			$Data['rows'][] = $DataArray;
			$i++;
		}
		
        $Data['links'] = $PaginateResult->links();

        return view("quotation.report_table", ["Data" => $Data]);
    }

    function getUserReportSearch(Request $request)
    {
        $data = $request->all();
    }

    function createReportArray($QuoteItem) {
		$DataArray['ID'] = $QuoteItem->ID;
		$DataArray['ReferenceID'] = $QuoteItem->quotation_no;
		$DataArray['CreatedDate'] = $QuoteItem->created_at;
		$DataArray['ClientName'] = isset($QuoteItem->Confirm()->first()->client_name) ? $QuoteItem->Confirm()->first()
			->client_name : "";
		$DataArray['market'] = Market::find($QuoteItem->Pax()->first()->market)->market;
		$DataArray['Destination'] = implode(", ", array_map(function ($ID) {
			if (Place::find($ID))
				return Place::find($ID)->name;
			else
				return $ID;
		}, array_column($QuoteItem->Place()->get()->toArray(), "place")));
		
		if (isset($data['hotels'])) {
			$hotel = $QuoteItem->Hotel()->where("hotel", "=", $data['hotels'][0])->first();
			
			$DataArray['HotelName'] = Hotel::find($hotel->hotel)->name;
			$DataArray['MealPlan'] = \App\Model\Hotel\Meal::find($hotel->meal)->plan;
			$DataArray['HotelNightCount'] = Carbon::create($hotel->check_in_year, $hotel->check_in_month, $hotel->check_in_day)->diffInDays(Carbon::create($hotel->check_out_year, $hotel->check_out_month, $hotel->check_out_day));
		}
		
		$DataArray['TravelDate'] = Carbon::create($QuoteItem->main->arrival_year, $QuoteItem->main->arrival_month, $QuoteItem->main->arrival_day)->toFormattedDateString();
		$DataArray['NoPax'] = $QuoteItem->Pax()->first()->adult + $QuoteItem->Pax()->first()->cnb + $QuoteItem->Pax()->first()->cwb;
		$DataArray['StaffName'] = User::find($QuoteItem->main()->first()->user)->name;
		$DataArray['Status'] = $QuoteItem->status == 1 ? "Pending" : ($QuoteItem->status == 2 ? "Confirm" : "Cancel");
		
		return $DataArray;
	}
}
