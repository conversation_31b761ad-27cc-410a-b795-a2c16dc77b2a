<?php

namespace App\Http\Controllers\Api;

use App\Api\Booking;
use App\Api\BookingResponse;
use App\Api\BookingTrait;
use App\Api\HotelAvailabilityResponse;
use App\Exceptions\ApiHandler;
use App\Model\Cruise\Cruise;
use App\Model\Cruise\CruiseCabinRate;
use App\Model\Hotel\Availability;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelClass;
use App\Model\Hotel\Rates;
use App\Model\Image\Image;
use App\Model\Place\Place;
use App\Model\QuotationManage\Quotation;
use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CruiseController extends Controller
{

    public function filterList(Request $request) {
        $data = $request->all();
        $hotel = new \App\Http\Controllers\Hotel\Hotel();
        return $hotel->ApiGetCruiseFilterList($data);
    }

    public function filterShowList(Request $request) {
        $data = $request->all();

        $hotel = new \App\Http\Controllers\Hotel\Hotel();
        if(isset($data["cruise"]) && !empty($data["cruise"])) {
            return $hotel->ApiGetCruiseFilterList($data);
        } else {
            return $hotel->ApiGetCruiseList($data);
        }
    }

    public function showNearPlace(Request $request) {
        if(isset($request->place_id)) {
            $PlaceID = $request->place_id;
            $NearPlace = new Place();
            $NearPlace = $NearPlace->getPlacesByNear($PlaceID)->get()->toArray();

            return $NearPlace;
        }
    }

}
