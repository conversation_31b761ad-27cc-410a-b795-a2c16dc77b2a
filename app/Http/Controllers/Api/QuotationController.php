<?php

namespace App\Http\Controllers\Api;

use App\Api\Booking;
use App\Api\BookingTrait;
use App\Currency;
use App\Exceptions\ApiHandler;
use App\Model\Hotel\Hotel;
use App\Model\Quotation\Quotation;
use App\Model\QuotationManage\Quotation as QuotationManage;
use App\Model\FdPackages\apple_fd_packages_quotation;
use App\Model\Image\Image;
use App\Model\Quotation\QuotationHotel;
use App\Model\Quotation\QuotationTransport;
use App\Model\Meal\Meal;
use App\Model\Costcut\Costcut;
use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Carbon\Carbon;

class QuotationController extends Controller
{
    use BookingTrait;

    /**
     * @param Request $request
     */
    function getCost(Request $request) {
        $Quotation = new Quotation();
        $QuotationSession = $this->apiArrayToQuotationArray($request->all());

        $baseCurrency = Currency::find($QuotationSession["base_currency"]??142)->code;
        $chCurrency = Currency::find($QuotationSession["ch_currency"]??142)->code;

        $Cost = $Quotation->getCost($QuotationSession);
        $Cost = $this->costCurrencyChange($Cost, $baseCurrency, $chCurrency);

        $Cost = $this->ApiFormatArray($Cost);

        return $Cost;
    }

    function costCurrencyChange($Cost, $from, $to) {
        $EleArray = array(
            '["hotel"]["cost"]',
            '["hotel"]["cost_pp"]',
            '["hotel"]["room_type_cost"]["1"]', '["hotel"]["room_type_cost"]["2"]', '["hotel"]["room_type_cost"]["3"]', '["hotel"]["room_type_cost"]["4"]', '["hotel"]["room_type_cost"]["5"]',
            '["hotel"]["child_cost"]["cwb"]', '["hotel"]["child_cost"]["cnb"]',
            '["cruise"]["cost"]',
            '["cruise"]["cost_pp"]',
            '["cruise"]["room_type_cost"]["1"]', '["cruise"]["room_type_cost"]["2"]', '["cruise"]["room_type_cost"]["3"]', '["cruise"]["room_type_cost"]["4"]', '["cruise"]["room_type_cost"]["5"]',
            '["cruise"]["child_cost"]["cwb"]', '["cruise"]["child_cost"]["cnb"]',
            '["attraction"]["cost"]',
            '["attraction"]["pax_cost"]["adult"]', '["attraction"]["pax_cost"]["child"]',
            '["transport"]["transport_data"]["rates"]',
            '["transport"]["transport_data"]["meal_transfer"]["cost"]', '["transport"]["transport_data"]["meal_transfer"]["pp"]',
            '["transport"]["cost"]["total"]', '["transport"]["cost"]["per_person"]',
            '["hotel_transport"]["total"]',
            '["hotel_transport"]["total_adult"]', '["hotel_transport"]["total_child"]', '["hotel_transport"]["pp_adult"]','["hotel_transport"]["pp_child"]',
            '["meal"]["pax_cost"]["adult"]', '["meal"]["cost"]["total"]',
            '["supplement"]["adult"]', '["supplement"]["child"]', '["supplement"]["total"]',
            '["other"]["cost"]',
            '["pp"]["adult"]["1"]', '["pp"]["adult"]["2"]', '["pp"]["adult"]["3"]', '["pp"]["adult"]["4"]', '["pp"]["adult"]["5"]',
            '["pp"]["cwb"]', '["pp"]["cnb"]',
            '["cost_cut"]["break_down"]["PP"]["total"]', '["cost_cut"]["break_down"]["PP"]["pp"]',
            '["cost_cut"]["break_down"]["MT"]["total"]', '["cost_cut"]["break_down"]["MT"]["pp"]',
            '["cost_cut"]["total"]',
            '["cost_cut_pkg"]'
        );

        foreach ($Cost as $index1 => $value1) {
            if(in_array('["'.$index1.'"]', $EleArray)) {
                $Cost[$index1] = currency($Cost[$index1], $from, $to, false);
            }

            if(isset($value1) && is_array($value1)) {
                foreach ($value1 as $index2 => $value2) {
                    if(in_array('["'.$index1.'"]["'.$index2.'"]', $EleArray)) {
                        $Cost[$index1][$index2] = currency($Cost[$index1][$index2], $from, $to, false);
                    }

                    if(isset($value2) && is_array($value2)) {

                        foreach ($value2 as $index3 => $value3) {
                            if (in_array('["' . $index1 . '"]["' . $index2 . '"]["' . $index3 . '"]', $EleArray)) {
                                $Cost[$index1][$index2][$index3] = currency($Cost[$index1][$index2][$index3], $from, $to, false);
                            }

                            if(isset($value3) && is_array($value3)) {

                                foreach ($value3 as $index4 => $value4) {
                                    if (in_array('["' . $index1 . '"]["' . $index2 . '"]["' . $index3 . '"]["' . $index4 . '"]', $EleArray)) {
                                        $Cost[$index1][$index2][$index3][$index4] = currency($Cost[$index1][$index2][$index3][$index4], $from, $to, false);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $Cost;
    }

    /**
     * @param $Cost
     * @return mixed
     */
    function ApiFormatArray($Cost) {
        if(isset($Cost["attraction"]["pax_cost"]["currency"]["from"]["code"]))
            $Cost["attraction"]["pax_cost"]["currency"]["from"] = $Cost["attraction"]["pax_cost"]["currency"]["from"]["code"];

        if(isset($Cost["attraction"]["pax_cost"]["currency"]["to"]["code"]))
            $Cost["attraction"]["pax_cost"]["currency"]["to"] = $Cost["attraction"]["pax_cost"]["currency"]["to"]["code"];

        return $Cost;
    }

    function getRoomType(Request $request) {
        $data = $request->all();
        $Hotel = new Hotel();

        $HotelRoomCount = $Hotel->getHotelRoomCountWithPax($data);

        return $HotelRoomCount;
    }

    function retrieve(Request $request) {
        $data = $request->all();
        $Quotation = new Quotation();

        $QuotationArray = \App\Model\QuotationManage\Quotation::getQuotation($data["reference_id"], $data["quotation_no"]);
        unset($QuotationArray["user_details"]);
        unset($QuotationArray["slide_array"]);
        unset($QuotationArray["slide"]);
        unset($QuotationArray["slide"]);

        $CostBreak = $Quotation->getCost($QuotationArray);

        return ["Quotation"=>$QuotationArray, "CostBreakDown"=>$CostBreak];
    }

    /**
     * Get quotation list with filters
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQuotationList(Request $request)
    {
        try {
            $data = $request->all();
            $exactQuot = false;

            // Get user IDs from request, if not provided, don't filter by user
            $userID = null;
            if (!empty($data['user_ids'])) {
                $userID = is_array($data['user_ids']) ? $data['user_ids'] : [$data['user_ids']];
            }

            // Check if exact quotation search
            if (!empty($data['id'])) {
                $pos = strpos($data['id'], '$');
                if ($pos === 0) {
                    $exactQuot = true;
                }
            }

            if (!$exactQuot) {
                // Build quotation query
                $quotes = QuotationManage::whereHas('Main', function ($main) use ($userID, $data) {
                    // User or users filter - only if user_ids are provided
                    if (!empty($userID)) {
                        if (is_array($userID)) {
                            $main->whereIn('user', $userID);
                        } else {
                            $main->where('user', $userID);
                        }
                    }

                    // Arrival date range filter
                    if (!empty($data['from_arrival_date']) && !empty($data['to_arrival_date'])) {
                        $fromDate = new Carbon($data['from_arrival_date']);
                        $toDate = new Carbon($data['to_arrival_date']);

                        $main->where(function($query) use ($fromDate, $toDate) {
                            // Create a raw SQL condition to compare the constructed date
                            $query->whereRaw("STR_TO_DATE(CONCAT(arrival_year, '-', LPAD(arrival_month, 2, '0'), '-', LPAD(arrival_day, 2, '0')), '%Y-%m-%d') BETWEEN ? AND ?", [
                                $fromDate->format('Y-m-d'),
                                $toDate->format('Y-m-d')
                            ]);
                        });
                    } elseif (!empty($data['arrival_date'])) {
                        // Single arrival date filter (backward compatibility)
                        $arrivalDate = new Carbon($data['arrival_date']);
                        $main->where('arrival_year', $arrivalDate->year);
                        $main->where('arrival_month', $arrivalDate->month);
                        $main->where('arrival_day', $arrivalDate->day);
                    }
                });

                // Quotation status filter
                if (!empty($data['status'])) {
                    $quotes->whereIn('status', $data['status']);
                }

                // Quotation number filter
                if (!empty($data['id'])) {
                    $quotes->where('quotation_no', $data['id']);
                }

                // IS number filter
                if (!empty($data['is_number'])) {
                    $quotes = $quotes->whereHas('IsNumber', function ($is) use ($data) {
                        $is->where('is_number', $data['is_number']);
                    });
                }

                $quotes->limit(16);

            } else {
                // Exact quotation search
                
                $quotes = QuotationManage::where('quotation_no', str_replace("$", "", $data['id']));

                // Add user filter only if user_ids are provided
                if (!empty($userID)) {
                    $quotes->whereHas('Main', function ($main) use ($userID) {
                        if (is_array($userID)) {
                            $main->whereIn('user', $userID);
                        } else {
                            $main->where('user', $userID);
                        }
                    });
                }
            }

            $quotes->orderByDesc('created_at');

            $quotationReturnList = [];
            $image = new Image();

            // Process regular quotations
            foreach ($quotes->with(['Main', 'Pax', 'Place', 'Hotel'])->get() as $quotationItem) {
                if ($quotationItem) {
                    // Get status class
                    $statusClass = 'pending';
                    if ($quotationItem->status == 1) {
                        $statusClass = 'pending';
                    } elseif ($quotationItem->status == 2) {
                        $statusClass = 'confirm';
                    } elseif ($quotationItem->status == 3) {
                        $statusClass = 'cancel';
                    }

                    // Get reference ID
                    $referenceId = QuotationManage::getReferenceID($quotationItem->quotation_no);

                    // Get all quotation updates for this quotation number
                    $quotationUpdates = QuotationManage::withTrashed()
                        ->where('quotation_no', $quotationItem->quotation_no)
                        ->get();

                    // Get hotel list
                    $hotelList = $quotationItem->hotel ? $quotationItem->hotel->pluck('hotel')->toArray() : [];

                    // Get places for map image
                    $places = $quotationItem->place ? $quotationItem->place->toArray() : [];
                    $mapImageUrl = $image->getGoogleGenDirectionPathStatic($places);

                    // Build structured data matching the HTML template
                    $quotationData = [
                        'type' => 'regular',
                        'id' => $quotationItem->ID,
                        'quotation_no' => $quotationItem->quotation_no,
                        'status' => $quotationItem->status,
                        'status_class' => $statusClass,
                        'country' => $quotationItem->country,
                        'currency' => $quotationItem->currency,
                        'tour_type' => $quotationItem->tour_type,
                        'created_at' => $quotationItem->created_at,
                        'updated_at' => $quotationItem->updated_at,

                        // Reference ID data
                        'reference_id' => $referenceId[1], // Formatted reference ID
                        'reference_id_full' => $referenceId,

                        // Main data
                        'main' => $quotationItem->main ? [
                            'user' => $quotationItem->main->user,
                            'arrival_year' => $quotationItem->main->arrival_year,
                            'arrival_month' => $quotationItem->main->arrival_month,
                            'arrival_day' => $quotationItem->main->arrival_day,
                        ] : null,

                        // Pax data
                        'pax' => $quotationItem->pax ? [
                            'adult' => $quotationItem->pax->adult ?? 0,
                            'cwb' => $quotationItem->pax->cwb ?? 0,
                            'cnb' => $quotationItem->pax->cnb ?? 0,
                            'total_children' => ($quotationItem->pax->cwb ?? 0) + ($quotationItem->pax->cnb ?? 0),
                        ] : null,

                        // Hotel data
                        'hotels' => $hotelList,

                        // Places and map
                        'places' => $places,
                        'map_image_url' => $mapImageUrl,

                        // Quotation update counts
                        'quotation_update_count_R' => $quotationUpdates->where('status', 1)->count(),
                        'quotation_update_count_C' => $quotationUpdates->where('status', 2)->count(),
                        'quotation_update_count_X' => $quotationUpdates->where('status', 3)->count(),

                        // Quotation updates for dropdown menus
                        'quotation_updates' => $quotationUpdates->map(function($update, $index) {
                            return [
                                'id' => $update->ID,
                                'quotation_no' => $update->quotation_no,
                                'status' => $update->status,
                                'status_letter' => $update->status == 1 ? 'R' : ($update->status == 3 ? 'X' : 'C'),
                                'revision_number' => $index + 1,
                                'created_at' => $update->created_at,
                            ];
                        })->toArray(),
                    ];

                    $quotationReturnList[] = $quotationData;
                }
            }

            return response()->json([
                'success' => true,
                'data' => $quotationReturnList,
                'total' => count($quotationReturnList)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve quotation list',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get PNL (Profit and Loss) data for a quotation
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPnlData(Request $request)
    {
        try {
            $data = $request->all();

            // Validate required parameters
            if (empty($data['reference_id']) || empty($data['quotation_no']) || empty($data['currency'])) {
                return response()->json([
                    'success' => false,
                    'error' => 'Missing required parameters',
                    'message' => 'reference_id, quotation_no, and currency are required'
                ], 400);
            }

            // Get quotation data
            $quotationArray = QuotationManage::getQuotation($data['reference_id'], $data['quotation_no']);
            $requestedCurrency = $data['currency'];

            if (!$quotationArray) {
                return response()->json([
                    'success' => false,
                    'error' => 'Quotation not found',
                    'message' => 'No quotation found with the provided reference_id and quotation_no'
                ], 404);
            }

            // Initialize models
            $quotation = new Quotation();
            $quotationHotel = new QuotationHotel();
            $costcut = new Costcut();
            $quotationTransport = new QuotationTransport();

            // Get budget and cost data
            $budget = Quotation::getBudget($quotationArray, $requestedCurrency);
            $attractionBreakDown = Quotation::getAttractionBreakDown($quotationArray);
            $dayCity = Quotation::getDayCity($quotationArray);
            $obVehicleRate = Quotation::getOBVehicleRate($quotationArray);
            $cost = $quotation->getCost($quotationArray);

            // Cost cut calculations
            $cost['cost_cut'] = $costcut->getCostCutRate($quotationArray);
            $cost['cost_cut_pkg'] = $costcut->getCostCutPkgRate($quotationArray);

            // Calculate accommodation data
            $accommodationTotal = 0;
            $accommodationDays = 0;
            $isLocal = $quotationArray['country'] == 62;

            // Tour type specific calculations
            if ($quotationArray['tour_type'] == 6) {
                $counts = array_count_values($quotationArray['accommodation']);
                if (isset($counts[2])) {
                    foreach ($quotationArray['accommodation'] as $key => $accommodation) {
                        if ($accommodation == 2) {
                            $ownData = $quotationArray['hotel'][$key];
                            if (isset($ownData['driver_accommodation']) && $ownData['driver_accommodation'] == 1) {
                                $count = $quotationHotel->getNightHotelBook($ownData['check_in'], $ownData['check_out']);
                                $accommodationTotal += $count;
                                $accommodationDays += $count;
                            }
                        }
                    }
                }
            }

            if ($quotationArray['tour_type'] == 3) {
                foreach ($quotationArray['accommodation'] as $key => $accommodation) {
                    if ($accommodation == 4) continue;
                    $ownData = $quotationArray['hotel'][$key];
                    $rates = $quotationArray['rate']['hotel'][$key] ?? null;

                    if (isset($rates) && isset($rates['driver_accommodation'])) {
                        foreach ($rates['driver_accommodation'] as $day => $details) {
                            $accommodationTotal += $details['modified_rate'];
                            $accommodationDays = $accommodationDays + 1;
                        }
                    } else if (isset($ownData)) {
                        if (isset($ownData['driver_accommodation']) && $ownData['driver_accommodation'] == 1) {
                            $count = $quotationHotel->getNightHotelBook($ownData['check_in'], $ownData['check_out']);
                            $accommodationTotal += 8 * $count; // 8 USD driver accommodation
                            $accommodationDays += $count;
                        }
                    }
                }
            }

            // Calculate costs without markup
            $quotationArrayWM = $quotationArray;
            $quotationArrayWM['markup_amount'] = 0;
            $quotationArrayWM['markup_amount_child'] = 0;
            $costWM = $quotation->getCost($quotationArrayWM);

            // Get meal rates
            $mealRates = Meal::getMeal($quotationArray);

            // Day use hotel calculations
            $dayUse = $quotationArray['other_rate'] ?? [];
            $beds = [];
            $totalRoomsValue = 0;

            if (!empty($dayUse)) {
                foreach ($dayUse as $key => $room) {
                    if (isset($room['details']['hotel_id']) && $room['details']['hotel_id'] != 0) {
                        $hotel = Hotel::find($room['details']['hotel_id']);

                        $beds[$key] = [
                            'hotel_id' => $room['details']['hotel_id'],
                            'hotel_name' => $hotel->name ?? '',
                            'singleBed' => $room['details']['single_bed_room_count'] ?? 0,
                            'doubleBed' => $room['details']['double_bed_room_count'] ?? 0,
                            'tripleBed' => $room['details']['triple_bed_room_count'] ?? 0,
                        ];

                        $singleBedRate = ($room['details']['single_bed_rate'] ?? 0) * ($room['details']['single_bed_room_count'] ?? 0);
                        $doubleBedRate = ($room['details']['double_bed_rate'] ?? 0) * ($room['details']['double_bed_room_count'] ?? 0);
                        $tripleBedRate = ($room['details']['triple_bed_rate'] ?? 0) * ($room['details']['triple_bed_room_count'] ?? 0);

                        $totalRoomsValue += ($singleBedRate + $doubleBedRate + $tripleBedRate);

                        $beds[$key]['singleBedRate'] = $singleBedRate;
                        $beds[$key]['doubleBedRate'] = $doubleBedRate;
                        $beds[$key]['tripleBedRate'] = $tripleBedRate;
                        $beds[$key]['singleBedRatePP'] = ($room['details']['single_bed_rate'] ?? 0) / 1;
                        $beds[$key]['doubleBedRatePP'] = ($room['details']['double_bed_rate'] ?? 0) / 2;
                        $beds[$key]['tripleBedRatePP'] = ($room['details']['triple_bed_rate'] ?? 0) / 3;
                    }
                }
            }

            $cost['total'] += $totalRoomsValue;
            $costWM['total'] += $totalRoomsValue;

            $totalPax = $quotationArray['pax']['adult'];
            $perPerson = $totalPax > 0 ? $totalRoomsValue / $totalPax : 0;

            $nights = Quotation::getNights($quotationArray);
            $days = Quotation::getDays($quotationArray);

            // Guide cost calculation
            $guideData = [];
            if (isset($quotationArray['guide'])) {
                $guideData = QuotationTransport::getGuideCostList($quotationArray['pax'], $quotationArray['guide'], $nights, $days);
            } else {
                $guideData['total'] = 0;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'quotation_info' => [
                        'quotation_no' => $quotationArray['quotation_no'],
                        'reference_id' => $quotationArray['ID'],
                        'is_number' => $quotationArray['is_number'] ?? 'NA',
                        'agent_name' => isset($quotationArray['confirm']['agent']) ?
                            \App\Model\Agent\Agent::find($quotationArray['confirm']['agent'] ?? $quotationArray['agent'])->name :
                            (isset($quotationArray['agent']) ? \App\Model\Agent\Agent::find($quotationArray['agent'])->name : 'NA'),
                        'total_pax' => array_sum($quotationArray['pax']),
                        'nights' => $nights,
                        'days' => $days,
                        'currency' => $requestedCurrency,
                        'exchange_rate' => currency(1, 'USD', $requestedCurrency, true),
                        'is_local' => $isLocal,
                    ],
                    'accommodation' => [
                        'total' => $accommodationTotal,
                        'days' => $accommodationDays,
                    ],
                    'budget' => $budget,
                    'cost' => $cost,
                    'cost_without_markup' => $costWM,
                    'meal_rates' => $mealRates,
                    'day_use_beds' => $beds,
                    'total_rooms_value' => $totalRoomsValue,
                    'per_person_room_cost' => $perPerson,
                    'guide_data' => $guideData,
                    'attraction_breakdown' => $attractionBreakDown,
                    'day_city' => $dayCity,
                    'ob_vehicle_rate' => $obVehicleRate,
                    'profit_loss' => $cost['total'] - $costWM['total'],
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve PNL data',
                'message' => $e->getMessage()
            ], 500);
        }
    }


}
