<?php

namespace App\Http\Controllers\Api;

use App\Api\Booking;
use App\Api\BookingResponse;
use App\Api\BookingTrait;
use App\Events\QuotationEmail;
use App\Exceptions\ApiHandler;
use App\Http\Resources\BookingRetrieveResource;
use App\Model\QuotationManage\Quotation;
use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Event;

class BookingController extends Controller
{

    use BookingTrait;


    protected $booking;


    /**
     * ContentController constructor.
     * @param Booking $booking
     */
    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return mixed
     * @throws \Throwable
     */
    public function store($type, Request $request)
    {

        if($type == "retrieve") {
            if ($request->user || $request->arrival_date || $request->id) {
                $quotation = new \App\Http\Controllers\Quotation\Quotation();

                return $quotation->getQuotationsForApi($request);
            }
        } else if($type == "save") {
            $QuotationSession = $this->apiArrayToQuotationArray($request->all());

            $Quotation = new Quotation();

            $QuotationSave = $Quotation->saveQuotation($QuotationSession);


            if (!$QuotationSave) {
                throw new ApiHandler($request, new Exception($Quotation->error));
            }

            //$Quotation->sendEmails($QuotationSession, $QuotationSave, $Quotation);

            return new BookingResponse($QuotationSave);
        }
    }

    public function retrieve(Request $request)
    {
        if ($request->user || $request->arrival_date || $request->quotation_no) {
            $quotation = new \App\Http\Controllers\Quotation\Quotation();

            return $quotation->getQuotationsForApi($request);
        }
    }

    public function save(Request $request)
    {
        $QuotationSession = $this->apiArrayToQuotationArray($request->all());

        $Quotation = new Quotation();

        $QuotationSave = $Quotation->saveQuotation($QuotationSession);


        if (!$QuotationSave) {
            throw new ApiHandler($request, new Exception($Quotation->error));
        }

        #Confirmation

        if ($QuotationSession['save_type'] == Quotation::CONFIRM && $QuotationSession['status'] != 3) {
            $Quotation->confirmQuotation($QuotationSave['reference_id'], $QuotationSave['quotation_no']);
        }

        if ($QuotationSession['save_type'] == Quotation::CANCEL) {
            $Quotation->cancelQuotation($QuotationSave['reference_id'], $QuotationSave['quotation_no']);
        }

        // Send tour confirmation voucher
        if ($QuotationSession['save_type'] == Quotation::CONFIRM) {

            $EmailData = $QuotationSave;
            $EmailData['to_emails'] = \Auth::user()->email;
            $EmailData['type'] = "tour_confirmation";
            Event::fire(new QuotationEmail($EmailData));
//            $this->emailHotelVoucher($EmailData);
        }

        if ($QuotationSession['save_type'] == Quotation::SAVE) {
            //Sending quotation
            $EmailData = $QuotationSave;
            $EmailData['to_emails'] = \Auth::user()->email;
            $EmailData['type'] = "quote";
            Event::fire(new QuotationEmail($EmailData));
        }

        //Send tour cancellation voucher
        if ($QuotationSession['save_type'] == Quotation::CANCEL) {

            $EmailData = $QuotationSave;
            $EmailData['to_emails'] = \Auth::user()->email;
            $EmailData['type'] = "tour_confirmation";
            $EmailData['ID'] = $QuotationSession['ID'];
            Event::fire(new QuotationEmail($EmailData));
            // $this->emailHotelVoucher($EmailData);
        }
        //$Quotation->sendEmails($QuotationSession, $QuotationSave, $Quotation);

        return new BookingResponse($QuotationSave);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
