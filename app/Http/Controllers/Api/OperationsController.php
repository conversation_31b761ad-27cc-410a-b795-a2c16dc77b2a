<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Transport\Operation;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class OperationsController extends Controller
{
    public function getTourAvailableVehicles($quotation_no)
    {
        $operation = new Operation();

        $operation = $operation->getTourAvailableVehicles($quotation_no);

        return replaceNullWithEmptyString($operation->toArray());
    }

    public function TourAction($action, Request $request)
    {
        $operation = new Operation();

        return $operation->TourAction($action, $request);
    }

    public function getMovementChart(Request $request)
    {
        $operation = new Operation();

        return $operation->getMovementChart($request);
    }

    public function getSummery($type, $id)
    {
        $operation = new Operation();

        return $operation->getSummery($type, $id);
    }

    public function setFlightDetails(Request $request)
    {
        $operation = new Operation();

        return $operation->setFlightDetails($request);
    }

    public function getMembers(Request $request)
    {
        $operation = new Operation();

        $operation = $operation->getMembers($request);

        return replaceNullWithEmptyString($operation->toArray());
    }

    public function setMembers(Request $request)
    {
        $operation = new Operation();

        return $operation->setMembers($request);
    }

    public function getDriverAvailabilityList(Request $request)
    {
        $operation = new Operation();

        return $operation->getDriverAvailabilityList($request);
    }

    public function getVehicles(Request $request)
    {
        $operation = new Operation();

        $operation = $operation->getVehicles($request);

        return replaceNullWithEmptyString($operation->toArray());
    }

    public function setVehicles(Request $request)
    {
        $operation = new Operation();

        return $operation->setVehicles($request);
    }

    public function getLogSheet($id, $type)
    {
        $operation = new Operation();

        return $operation->getLogSheet($id, $type);
    }


}
