<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    public function __construct()
    {
        // Middleware is handled at route level
    }

    /**
     * Login user and create token
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $credentials = $request->only('email', 'password');

        // Find user
        $user = User::where('email', $credentials['email'])->first();

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Create token using auth guard with error handling
        try {
            // Suppress deprecation warnings temporarily
            $oldErrorReporting = error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);

            $token = auth('api')->login($user);

            // Restore error reporting
            error_reporting($oldErrorReporting);

            if (!$token) {
                return response()->json(['error' => 'Could not create token'], 500);
            }

            return $this->respondWithToken($token);
        } catch (\Exception $e) {
            // Restore error reporting in case of exception
            error_reporting($oldErrorReporting ?? E_ALL);

            return response()->json([
                'error' => 'Could not create token',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Register a new user
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        // Create token for new user with error handling
        try {
            // Suppress deprecation warnings temporarily
            $oldErrorReporting = error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);

            $token = auth('api')->login($user);

            // Restore error reporting
            error_reporting($oldErrorReporting);

            if (!$token) {
                return response()->json(['error' => 'User created but could not create token'], 500);
            }

            return $this->respondWithToken($token);
        } catch (\Exception $e) {
            // Restore error reporting in case of exception
            error_reporting($oldErrorReporting ?? E_ALL);

            return response()->json([
                'error' => 'User created but could not create token',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the authenticated User
     */
    public function me()
    {
        // User is already authenticated by middleware
        return response()->json(auth()->user());
    }

    /**
     * Log the user out (Invalidate the token)
     */
    public function logout()
    {
        try {
            // Suppress deprecation warnings temporarily
            $oldErrorReporting = error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);

            JWTAuth::invalidate(JWTAuth::getToken());

            // Restore error reporting
            error_reporting($oldErrorReporting);

            return response()->json(['message' => 'Successfully logged out']);
        } catch (\Exception $e) {
            error_reporting($oldErrorReporting ?? E_ALL);
            return response()->json([
                'error' => 'Could not logout',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh a token
     */
    public function refresh()
    {
        try {
            // Get the token from the request
            $token = JWTAuth::getToken();

            if (!$token) {
                return response()->json(['error' => 'Token not provided'], 401);
            }

            // Suppress deprecation warnings temporarily
            $oldErrorReporting = error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);

            // Refresh the token
            $newToken = JWTAuth::refresh($token);

            // Restore error reporting
            error_reporting($oldErrorReporting);

            return $this->respondWithToken($newToken);

        } catch (\Tymon\JWTAuth\Exceptions\TokenExpiredException $e) {
            return response()->json(['error' => 'Token has expired and cannot be refreshed'], 401);
        } catch (\Tymon\JWTAuth\Exceptions\TokenInvalidException $e) {
            return response()->json(['error' => 'Token is invalid'], 401);
        } catch (\Tymon\JWTAuth\Exceptions\JWTException $e) {
            return response()->json(['error' => 'Token absent or malformed'], 401);
        } catch (\Exception $e) {
            // Restore error reporting in case of exception
            error_reporting($oldErrorReporting ?? E_ALL);

            return response()->json([
                'error' => 'Could not refresh token',
                'message' => $e->getMessage()
            ], 401);
        }
    }

    /**
     * Get the token array structure
     */
    protected function respondWithToken($token)
    {
        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => config('jwt.ttl') * 60
        ]);
    }
}
