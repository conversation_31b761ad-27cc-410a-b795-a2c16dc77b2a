<?php

namespace App\Http\Controllers\Api;

use App\Api\Booking;
use App\Api\BookingResponse;
use App\Api\BookingTrait;
use App\Api\HotelAvailabilityResponse;
use App\Exceptions\ApiHandler;
use App\Model\Cruise\Cruise;
use App\Model\Cruise\CruiseCabinRate;
use App\Model\Hotel\Availability;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelClass;
use App\Model\Hotel\Rates;
use App\Model\Image\Image;
use App\Model\Place\Place;
use App\Model\Quotation\QuotationHotel;
use App\Model\QuotationManage\Quotation;
use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class HotelController extends Controller
{


    protected $hotel;


    /**
     * ContentController constructor.
     * @param Hotel $hotel
     */
    public function __construct(Hotel $hotel)
    {
        $this->hotel = $hotel;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param $HotelSettings
     * @return bool|\Illuminate\Support\Collection
     * @throws \ReflectionException
     */
    public function show($HotelSettings)
    {


    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return mixed
     * @throws \Throwable
     */
    public function store(Request $request)
    {
        $hotelColor = [0=>"Green", 1=>"Yellow", 2=>"Red"];
        $Rate = new Rates();
        $Image = new Image();
        $Hotel = new Hotel();
        $CruiseRate = new CruiseCabinRate();
        $Cruise = new Cruise();

        $hotelList = [];
        $cruiseList = [];

        $HotelRoomCount = $Hotel->getHotelRoomCountWithPax($request["pax"]??[]);

        foreach ($request['days'] as $day){

            // $day['room_type'] = $Hotel->getHotelRoomCountWithPax($day['pax']);

            // $availability = $Rate->ApiGetLowestHotelPlace($day['place'], $day['check_in'], $day['room_type'], $day['meal_type'], $day['room_category'], $day['market'], 1, false, true);


            $availability = $Rate->getLowestHotelPlace($day['place'], $day['check_in'], $day['room_type'], $day['meal_type'], $day['room_category'], $day['market'], 1,false, true);
            if(!$availability) {
                $availability = $Rate->getLowestHotelPlace($day['place'], $day['check_in'], $day['room_type'], $day['meal_type'], $day['room_category'], $day['market'], 1,false, false);
            }

            $CruiseAvailability = $CruiseRate->getLowestCruisePlace($day['place'], $day['check_in'], $day['check_out'], $day['room_type'], $day['meal_type'], $day['room_category'], $day['market'], 10); // last false is for $dateRange

            if (!$availability)
                $availability[] = ['status' => 'No available hotels found.'];

            if ($day['top_preferred'] == 1) { // only return top preferred hotel

                foreach ($availability as $key => $one) {
                    if ($key == 0 && is_object($one)) {
                        $one = toArray($one);
                        $one['hotel_availability_status'] = Availability::find((Hotel::getStatus($one['hotel'], $day['check_in'], $one['room_type'], $one['meal'], $one['room_category'], $day['market'])[0]))->status;

                        $one['hotel_cwb_rate'] = $Rate->ApiGetRateChild($day['place'], $day['check_in'], $one['meal'], $day['room_category'], $day['market'], 2, 12, $one['room_type']);
                        $one['hotel_cnb_rate'] = $Rate->ApiGetRateChild($day['place'], $day['check_in'], $one['meal'], $day['room_category'], $day['market'], 0, 2, $one['room_type']);

                        $availabilityCount = Hotel::getStatus($one['hotel'], $day['check_in']);
                        $one['hotel_availability_count'] = $availabilityCount[1] ?? 0;
                        $one['hotel_image'] = $Image->getImage($one['hotel'],'3x',"hotel",1,Hotel::find($one['hotel'])->name)[0] or "";
                        $one['hotel_image_large'] = $Image->getImage($one['hotel'],'300',"hotel",1,Hotel::find($one['hotel'])->name)[0] or "";
                        $one['city_name'] = Place::find($one['CityH'])->name;
                        $one['class_name'] = HotelClass::find($one['ClassH'])->class;

                        if(isset($availabilityCount[0]) && $availabilityCount[0] == 1 && isset($availabilityCount[1]) &&  $availabilityCount[1] > 0 && isset($one['rate'])) {
                            $one['hotel_color'] = $hotelColor[0];
                        } else if(isset($availabilityCount[0]) && $availabilityCount[0] == 1 && isset($availabilityCount[1]) && $availabilityCount[1] <= 0 && isset($one['rate'])) {
                            $one['hotel_color'] = $hotelColor[1];
                        } else if(isset($availabilityCount[0]) && $availabilityCount[0] == 2) {
                            $one['hotel_color'] = $hotelColor[1];
                        } else {
                            $one['hotel_color'] = $hotelColor[2];
                        }

                        $availability = [];

                        array_push($availability, $one);
                    }
                }

                if($CruiseAvailability) {
                    foreach ($CruiseAvailability as $key => $one) {
                        if ($key == 0 && is_object($one)) {
                            $one = toArray($one);

                            $one['cruise_availability_status'] = Availability::find(Cruise::getStatus($one['cruise'], $day['check_in'], $one['cabin_type'], $one['meal'], $one['cabin_occupancy_type'], $day['market'], $one['package'])[0])->status;

                            if(isset($request['child_age'])) {
                                if(isset($request['child_age']['cwb'])) {
                                    foreach ($request['child_age']['cwb'] as $key => $value) {
                                        $value = (isset($value) && $value!=0) ? $value : 8;
                                        $one['cruise_cwb_rate'][] = $CruiseRate->getRateChild($one->ID, $value)->toArray();
                                    }
                                }

                                if(isset($request['child_age']['cnb'])) {
                                    foreach ($request['child_age']['cnb'] as $key => $value) {
                                        $value = (isset($value) && $value!=0) ? $value : 3;
                                        $one['cruise_cwb_rate'][] = $CruiseRate->getRateChild($one->ID, $value)->toArray();
                                    }
                                }
                            }


                            $availabilityCount = Cruise::getStatus($one['cruise'], $day['check_in']);

                            $one['cruise_image'] = $Image->getImage($one['cruise'],'3x',"cruise",1,Cruise::find($one['cruise'])->name)[0] or "";
                            $one['city_name'] = Place::find($one['CityH'])->name;
                            $one['class_name'] = HotelClass::find($one['ClassH'])->class;

                            if(isset($availabilityCount[0]) && $availabilityCount[0] == 1 && isset($one['rate'])) {
                                $one['cruise_color'] = $hotelColor[0];
                            } else {
                                $one['cruise_color'] = $hotelColor[2];
                            }

                            $CruiseAvailability = [];

                            array_push($CruiseAvailability, $one);
                        }
                    }
                }
            }

            // $availability = array_column($availability, ['city', 'class']);
            $availability = [
                $day['place'] => $availability
            ];

            $CruiseAvailability = [
                $day['place'] => $CruiseAvailability??[]
            ];

            array_push($hotelList, $availability);
            array_push($cruiseList, $CruiseAvailability);
        }

        $Data = array("hotels" => toArray($hotelList),"cruise" => toArray($cruiseList) ,"room_type" => $HotelRoomCount);
        return replaceNullWithEmptyString($Data);
//        return new HotelAvailabilityResponse([replaceNullWithEmptyString(toArray($hotelList)), $request->get('check_in')]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function filterList(Request $request) {
        $data = $request->all();
        $hotel = new \App\Http\Controllers\Hotel\Hotel();
        return $hotel->ApiGetHotelFilterList($data);
    }

    public function filterShowList(Request $request) {
        $data = $request->all();
        $hotel = new \App\Http\Controllers\Hotel\Hotel();
        if(isset($data["hotel"]) && !empty($data["hotel"])) {
            return $hotel->ApiGetHotelFilterList($data);
        } else {
            return $hotel->ApiGetHotelList($data);
        }
    }

    public function showNearPlace(Request $request) {
        if(isset($request->place_id)) {
            $PlaceID = $request->place_id;
            $NearPlace = new Place();
            $NearPlace = $NearPlace->getPlacesByNear($PlaceID)->get()->toArray();

            return $NearPlace;
        }
    }

    public function getHotelRate(Request $request) {
        $Data = $request->all();
        $Rates = QuotationHotel::getAPIHotelCost($Data);

        return $Rates;
    }

}
