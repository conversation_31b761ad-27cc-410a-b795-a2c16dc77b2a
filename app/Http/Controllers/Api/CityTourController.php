<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\CityTourResource;
use App\Model\Place\CityTour;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CityTourController extends Controller
{
    public function getCityTourByTypeOrPlace(Request $request)
    {
        $cityTour = CityTour::select('*');

        if ($request->type)
            $cityTour->where('type', $request->type);

        if ($request->place)
            $cityTour->where('place', $request->place);

        $cityTour = $cityTour->get();

        if ($cityTour->isEmpty())
            return ['status' => 'No city tour found.'];

        return CityTourResource::collection($cityTour);

    }
}
