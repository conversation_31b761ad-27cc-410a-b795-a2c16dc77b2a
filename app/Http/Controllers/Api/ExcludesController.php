<?php

namespace App\Http\Controllers\Api;

use App\Model\Place\Place;
use App\Model\Quotation\QuotationExclude;
use App\Model\QuotationManage\Quotation;
use App\Model\Hotel\Hotel;
use App\Model\Meal\MealPlan;
use App\Model\Quotation\QuotationInclude;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ExcludesController extends Controller
{
    public function getExcludes(Request $request)
    {
        if ($request->quotation_no){
            $QuotationArray = Quotation::getQuotation($request->reference_id, $request->quotation_no);
        } else {
            $QuotationArray = $request->all();
        }

        $excludes = QuotationExclude::getQuotationList($QuotationArray);

        $excludesArray = [];

        foreach ($excludes as $exclude){
            array_push($excludesArray, $exclude->exclude);
        }

        $data = [
            'excludes' => $excludesArray
        ];

        return $data;
    }

}
