<?php

namespace App\Http\Controllers\Api;

use App\ClientEmail;
use App\Model\MobileApp\EmailVerification;
use App\Model\QuotationManage\Quotation;
use App\Model\QuotationManage\QuotationConfirm;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ApiRelatedFunctionsController extends Controller
{
    public function updateVerifyStatus($email, $token)
    {
        $tokenData = EmailVerification::where('email', $email)->get();

        foreach ($tokenData as $data):

            if ($data->verify_token == $token):

                $verifyData = EmailVerification::find($data->id);

                $verifyData->verify_status = 1;
                $verifyData->saveOrFail();

            endif;

        endforeach;

        return redirect()->route('email.verification.successful');

    }

    public function emailVerifySuccess()
    {
        return view('email-verification-success');
    }

    public function currentTour(Request $request)
    {
        $client = ClientEmail::where('email', $request->email)->first();

       if ($client){
           $currentTour = QuotationConfirm::where('client_id', $client->id)->orderByDesc('ID')->first();

           if ($currentTour){
               $quotation = Quotation::find($currentTour->reference_id)->first();
           } else {
               return [
                   'error' => 'No quotation found for this client'
               ];
           }
       } else {
           return [
               'error' => 'No client by this email found'
           ];
       }

        $tour = Quotation::getQuotation($quotation->ID, $quotation->quotation_no);

        return replaceNullWithEmptyString($tour);
    }

    public function tourHistory(Request $request)
    {
        $client = ClientEmail::where('email', $request->email)->first();

        if ($client){
            $previousTours = QuotationConfirm::where('client_id', $client->id)->orderBy('ID', 'asc')->get();

            if ($previousTours){
                $previousTours->pop();
            } else {
                return [
                    'error' => 'No quotation found for this client'
                ];
            }
        } else {
            return [
                'error' => 'No client by this email found'
            ];
        }

        $tourHistory = [];

        foreach ($previousTours as $ptour) {

            $quotation = Quotation::withTrashed()->where('id', $ptour->reference_id)->first();

            $tour = Quotation::getQuotation($quotation->ID, $quotation->quotation_no);

            array_push($tourHistory, $tour);
        }

        return replaceNullWithEmptyString($tourHistory);
    }
}
