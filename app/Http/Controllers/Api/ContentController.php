<?php

namespace App\Http\Controllers\Api;

use App\Api\Content;
use App\Exceptions\ApiHandler;
use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;

/**
 * Class ContentController
 * @package App\Http\Controllers\Api
 */
class ContentController extends Controller
{

    protected $content;


    /**
     * ContentController constructor.
     * @param Content $content
     */
    public function __construct(Content $content)
    {
        $this->content = $content;
    }


    /**
     * @param Request $request
     * @throws ApiHandler
     */
    public function index(Request $request)
    {
        throw new ApiHandler($request, new Exception("Invalid api endpoint!"));
    }

    /**
     * @param $type
     * @return array|mixed
     * @throws ApiHandler
     */
    public function show($type)
    {
        return $this->content->get($type,request()->all());

    }

    /**
     * @param $type
     * @return array|mixed
     * @throws ApiHandler
     */

}
