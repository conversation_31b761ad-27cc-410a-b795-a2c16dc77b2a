<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Meal\Meal;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class MealController extends Controller
{
    public function getOutsideMealRestaurants(Request $request) {

        $restaurants = DB::table('apple_meal_type_restaurant')
            ->join('apple_restaurant', 'apple_restaurant.ID', '=', 'apple_meal_type_restaurant.restaurant_id')
            ->join('apple_meal_type', 'apple_meal_type.ID', '=', 'apple_meal_type_restaurant.meal_type_id')
            ->select('apple_restaurant.ID','apple_restaurant.name')
            ->where('apple_meal_type.ID','=', $request->type)
            ->groupBy("ID")
            ->get();

        return $restaurants->isNotEmpty() ? $restaurants : ['status' => 'No restaurants found.'];
    }

    public function getRestaurantMealTypes(Request $request)
    {
        $mealTypes = new Meal();

        return $mealTypes->getRestaurantMealTypes($request->restaurant);
    }
}
