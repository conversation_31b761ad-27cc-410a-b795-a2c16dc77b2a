<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\ProfileResource;
use App\Model\Image\Image;
use App\Model\Profile\Company;
use App\Model\Profile\Designation;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Model\Profile\Profile as UserProfile;
use Illuminate\Support\Facades\Auth;

class Profile extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $user = User::find($request->user_id);

        $profile = UserProfile::where('user', $user->id)->first();

        $profileData = UserProfile::find($profile->ID);

        if ($request->has('first_name')) {
            $profileData->first_name =  $request->first_name;
        }
        if ($request->has('last_name')) {
            $profileData->last_name =  $request->last_name;
        }
        /*if ($request->has('designation')) {
             $profileData->designation = $request->designation;
        }
        if ($request->has('company')) {
             $profileData->company = $request->company;
        }*/
        if ($request->has('nic')) {
             $profileData->nic = $request->nic;
        }
        if ($request->has('dob')) {
             $profileData->dob = $request->dob;
        }
        if ($request->has('tel')) {
             $profileData->tel = $request->tel;
        }

        $profileData->saveOrFail();

        $UserUpdate = User::find($request->user_id);
        $UserUpdate->name = trim($profileData->first_name . ' ' . $profileData->last_name);

        /*if ($request->has('email')) {
            $UserUpdate->email = $request->email;
        }*/

        $UserUpdate->save();

        if ($request->file('pro_pic')) {
            //image upload
            \File::deleteDirectory(public_path('assets/image/user/' .$user->id));
            \Storage::disk('image')->put('/user/' . $user->id . '/0.jpg', file_get_contents($request->file('pro_pic')));

            #profile
            User::where('id', $user->id)->update(['updated_at' => Carbon::now()]);
        }

        $img['1x'] = Image::getImageDirect($user->id, '1x', 'user', 1, $user->name)[0] . '?' . time();
        $img['2x'] = Image::getImageDirect($user->id, '2x', 'user', 1, $user->name)[0] . '?' . time();
        $img['3x'] = Image::getImageDirect($user->id, '3x', 'user', 1, $user->name)[0] . '?' . time();

        $data = [
            'status' => 'Profile Updated!',
            'profile' => $profileData->toArray(),
            'profile_img' => $img,
        ];

        return replaceNullWithEmptyString($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($email)
    {
        $user = User::where('email', $email)->first();

        $profile = $user->Profile;

        $company = Company::find($profile->company);

        $designation = Designation::find($profile->designation);

        $profile_pic = Image::getImageDirect($user->id, '3x', 'user', 1, $user->name)[0];

        $profile = [
//            'ID' => $profile->ID,
            'user_id' => (int)$profile->user,
            'first_name' => $profile->first_name,
            'last_name' => $profile->last_name,
            'company' => $company->name,
            'designation' => $designation->designation,
            'nic' => $profile->nic,
            'dob' => $profile->dob,
            'tel' => $profile->tel,
            'profile_pic' => $profile_pic,
        ];

        return replaceNullWithEmptyString($profile);

//        return new ProfileResource($profile);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
