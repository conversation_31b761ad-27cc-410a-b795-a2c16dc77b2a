<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\AttractionResource;
use App\Model\Place\Attraction;
use App\Model\Place\AttractionTime;
use App\Model\Place\AttractionType;
use App\Model\Place\CitytourTime;
use App\Model\Place\ExcursionTime;
use App\Model\Place\Place;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class AttractionController extends Controller
{
    public function getAttractionTypes()
    {
        return AttractionType::select('ID','type')->orderBy('ID', 'asc')->get();
    }

    public function getAttractionByTypeOrPlace(Request $request)
    {
        $attraction = Attraction::select('*');

        if ($request->type)
            $attraction->where('type', $request->type);

        if ($request->place)
            $attraction->where('place', $request->place);

        return AttractionResource::collection($attraction->get());
    }

    public function getTimes($type, $ID) {
        $data_array = [];

        if($type == "attraction") {
            $data_array['time'] = AttractionTime::where("attraction", "=", $ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->attraction;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });
        }

        if($type == "citytour") {
            $data_array['time'] = CitytourTime::where("tour", "=", $ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->tour;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });
        }

        if($type == "excursion") {
            $data_array['time'] = ExcursionTime::where("excursion", "=",  $ID)->get()->map(function ($itemTime, $keyTime) {
                $time_array["time_id"] = $itemTime->ID;
                $time_array["attration_id"] = $itemTime->excursion;
                $time_array["starting"] = isset($itemTime->starting) ? Carbon::parse($itemTime->starting)->format('g:i A') : "N/S";
                $time_array["ending"] = isset($itemTime->ending) ? Carbon::parse($itemTime->ending)->format('g:i A') : "N/S";

                return $time_array;
            });
        }

        return $data_array;
    }
}
