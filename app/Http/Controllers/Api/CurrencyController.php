<?php

namespace App\Http\Controllers\Api;

use App\Api\BookingTrait;
use App\Currency;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CurrencyController extends Controller
{
    use BookingTrait;

    public function getCurrency(Request $request) {
        $Quotation = $request->all();

        $returnCurrency = [];

        $Currency = 142;
        if(isset($Quotation['country'])) {
            $Currency = \App\Model\Place\Place::find($Quotation['country'])->currency;
            if(isset($Quotation['place'])) {
                $CurrencyList = \App\Model\Quotation\Quotation::getCurrency($Quotation);
                if (count($CurrencyList) > 1)
                    $Currency = 142;
            }

        }

        $baseCurrency = Currency::find($Currency)->code;

        $returnCurrency["currency"] = $Currency;
        $returnCurrency["currency_code"] = $baseCurrency;

        return $returnCurrency;
    }

}
