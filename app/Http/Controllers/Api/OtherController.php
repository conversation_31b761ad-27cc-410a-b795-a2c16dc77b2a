<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Home\Ajax;
use App\Http\Controllers\Transport\Direction;
use App\Http\Resources\RoomTypeResource;
use App\Http\Resources\ShortestPathResource;
use App\Http\Resources\ShortestPathResource2;
use App\Model\Hotel\RoomType;
use App\Model\Place\Place;
use App\Model\Tool\Honorific;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class OtherController extends Controller
{
    function getShortestPath(Request $request)
    {
        $pickUp = $request['pickup'];
        $dropOff = $request['dropoff'];
        $cities = $request['places'];

        if(!(Place::find($pickUp)->country == 62))
            return array("status"=>false, "error"=>"No shortest path", 'data'=>[]);

        if(!(Place::find($dropOff)->country == 62))
            return array("status"=>false, "error"=>"No shortest path", 'data'=>[]);

        $Places = [$pickUp];

        foreach ($cities as $city) {
            if(Place::find($city['cityId'])->country == 62)
                array_push($Places, $city['cityId']);
            else
                return array("status"=>false, "error"=>"No shortest path", 'data'=>[]);
        }

        array_push($Places, $dropOff);

        $Markers = array_keys($Places);

        $Place = new Place();
        $Airport = $Place->getDefaultAirport();

        if ($request->input('airport_pick_up')) {
            array_unshift($Markers, -1);
            array_unshift($Places, $Airport->ID);
        }
        if ($request->input('airport_drop_off')) {
            array_push($Markers, -2);
            array_push($Places, $Airport->ID);
        }

        $ShortestPath = $Place->getShortestPath($Places, $Markers);

        $ShortestPath = toArray($ShortestPath);
        $ShortestPath['original_places_list'] = $request->all();

        return array('status'=>true,'data'=>new ShortestPathResource($ShortestPath));

    }

    function getShortestPath2(Request $request)
    {
        $Places = [];

        foreach ($request['places'] as $cityId) {
            array_push($Places, $cityId['cityId']);
        }

        $Markers = array_keys($Places);

        $Place = new Place();
        $Airport = $Place->getDefaultAirport();

        if ($request->input('airport_pick_up')) {
            array_unshift($Markers, -1);
            array_unshift($Places, $Airport->ID);
        }
        if ($request->input('airport_drop_off')) {
            array_push($Markers, -2);
            array_push($Places, $Airport->ID);
        }

        $ShortestPath = $Place->getShortestPath($Places, $Markers);

        $ShortestPath = toArray($ShortestPath);

        $ShortestPath['original_places_list'] = $request['places'];

        return new ShortestPathResource2(replaceNullWithEmptyString($ShortestPath));
    }

    public function getPlacesByCountryId(Request $request)
    {
        $places = Place::where('country', $request->country_id)->orderBy('name', 'asc')->get();

        return replaceNullWithEmptyString($places->toArray());
    }

    public function getNearestStop($placeId)
    {
        $location = new Direction();

        $location = toArray($location->getNearestStop($placeId));

        return replaceNullWithEmptyString($location);
    }

    public function getHonorifics()
    {
        return Honorific::select('ID', 'honorific')->get();
    }

    public function getRoomTypes()
    {
        return RoomTypeResource::collection(RoomType::all());
    }
}
