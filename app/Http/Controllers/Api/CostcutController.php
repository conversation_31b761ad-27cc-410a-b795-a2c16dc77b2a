<?php

namespace App\Http\Controllers\Api;

use App\Api\BookingTrait;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CostcutController extends Controller
{
    use BookingTrait;

    public function getCostcutPoints(Request $request) {
        $Costcut = new \App\Model\Costcut\Costcut();

        $QuotationSession = $this->apiArrayToQuotationArray($request->all());

        $CostCutText = $Costcut->getCostCutPoints($QuotationSession);

        return $CostCutText;
    }

}
