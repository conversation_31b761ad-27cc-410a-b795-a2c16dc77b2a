<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\ExcursionResource;
use App\Model\Place\Excursion;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ExcursionController extends Controller
{
    public function getExcursionByType(Request $request)
    {
        $excursion = Excursion::select('*')->where('duration', '<', 1440);

        if ($request->type)
            $excursion->where('type', $request->type);

        if ($request->place)
            $excursion->where('place', $request->place);

        if ($request->from)
            $excursion->where('from', $request->from);

        $excursion = $excursion->get();

        if ($excursion->isEmpty())
            return ['status' => 'No excursion tour found.'];

        return ExcursionResource::collection($excursion);

    }
}
