<?php

namespace App\Http\Controllers\Api;

use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class PasswordReset extends Controller
{
    public function resetPassword(Request $request)
    {
        if ($request->email && $request->password)
        {
            $userData = User::select('id')->where('email', $request->email)->first();

            $userId = $userData->id;

            $user = User::find($userId);

            $user->password = bcrypt($request->password);

            $user->save();

            $data = [
                'status' => 'Password Reset Successful!'
            ];

            return $data;
        }
        else
        {
            $data = [
                'status' => 'Email and Password Required'
            ];

            return $data;
        }

    }
}
