<?php

namespace App\Http\Controllers\Api;

use App\Model\Place\Place;
use App\Model\Quotation\QuotationExclude;
use App\Model\QuotationManage\Quotation;
use App\Model\Hotel\Hotel;
use App\Model\Meal\MealPlan;
use App\Model\Quotation\QuotationInclude;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class IncludesAndExcludesController extends Controller
{
    public function getIncludesAndExcludes(Request $request)
    {
        if ($request->quotation_no){
            $QuotationArray = Quotation::getQuotation($request->reference_id, $request->quotation_no);
        } else {
            $QuotationArray = $request->all();
        }

        $includes = QuotationInclude::getQuotationList($QuotationArray);

        $pickUpDropOff = [];

        foreach($QuotationArray['place_full'] as $i => $PlaceID){

            if($QuotationArray['place_type'][$i] != 1){

                $placeName = Place::where('ID', $PlaceID)->first()->name;

                $status = $QuotationArray['place_type'][$i] == 2 ? "Pick up from " : "Drop off to ";

                $fullStatus = $status . $placeName;

                array_push($pickUpDropOff, $fullStatus);
            }
        }

        $hotelWithMealPlan = [];

        foreach($QuotationArray['hotel'] as $HotelIndex => $HotelItem){

            $hotel = Hotel::getHotelFromAll($HotelItem['hotel'])->name;
            $mealPlan = MealPlan::find($HotelItem['meal_type'])->plan;

            switch ($mealPlan):

                case 'BB':
                    $meals = 'Breakfast';
                    break;

                case 'HB':
                    $meals = 'Breakfast & Dinner';
                    break;

                case 'FB':
                    $meals = 'Breakfast, Lunch & Dinner';
                    break;

                case 'AL':
                    $meals = 'All Inclusive';
                    break;

            endswitch;

            $plan =  $meals. ' at ' . $hotel;

            array_push($hotelWithMealPlan, $plan);

        }

        foreach ($hotelWithMealPlan as $plan){
            array_push($includes, $plan);
        }

        $reversePickUpDropOff = array_reverse($pickUpDropOff);

        foreach ($reversePickUpDropOff as $pickDrop){
            array_unshift($includes, $pickDrop);
        }

        /* ------- Excludes ------- */

        $excludes = QuotationExclude::getQuotationList($QuotationArray);

        $excludesArray = [];

        foreach ($excludes as $exclude){
            array_push($excludesArray, $exclude->exclude);
        }

        $data = [
            'includes' => array_values(array_filter($includes)),
            'excludes' => $excludesArray
        ];

        return $data;
    }

}
