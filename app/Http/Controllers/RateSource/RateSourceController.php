<?php

namespace App\Http\Controllers\RateSource;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Model\Rate\apple_hotel_rate_source;
use App\Model\Rate\apple_hotel_rate_vendor;
use \Carbon\Carbon;
use Auth;

class RateSourceController extends Controller
{
    public function addRateSourceVender(Request $request)
    {
        $vendorName = $request->vendorName;
        $apple_hotel_rate_vendor = new apple_hotel_rate_vendor();
        $apple_hotel_rate_vendor->vendor_name = $vendorName;
        $apple_hotel_rate_vendor->save();
    }

    public function viewRateSourceVender()
    {
        $vendors = apple_hotel_rate_vendor::all();
        return view('rate-source.view-rate-source-vendor', compact('vendors'));
    }

    public function addRateSource(Request $request)
    {
        $vendorId = $request->vendorId;
        $hotelId = $request->hotelId;
        $date = $request->date;
        $rate = $request->rate;
        $validFrom = $request->validFrom;
        $validTo = $request->validTo;
     //return $request->all();
        if (!(apple_hotel_rate_source::where('vendor_id', $vendorId)->where('hotel_id', $hotelId)->where('date', $date)->exists())) {
            $apple_hotel_rate_source = new  apple_hotel_rate_source();
            $apple_hotel_rate_source->vendor_id = $vendorId;
            $apple_hotel_rate_source->hotel_id = $hotelId;
            $apple_hotel_rate_source->date = $date;
            $apple_hotel_rate_source->rate = $rate;
            $apple_hotel_rate_source->valid_from = $validFrom;
            $apple_hotel_rate_source->valid_to = $validTo;
            $apple_hotel_rate_source->user_id = Auth::id();
            $apple_hotel_rate_source->save();
            return array('status' => true, 'data' => 'Rate Has been Added');
        } else {
            return array('status' => false, 'data' => 'Rate Already Exists');
        }
    }

    public function rateSourceReport(Request $request){
        $vendorId = $request->vendorId;
        $hotelId = $request->hotelId;
        $date = $request->date;
//     return $request->all();
        $rateSource = apple_hotel_rate_source::where('created_at','!=','');
        if (!empty($vendorId)) {
            $rateSource->where("vendor_id", "=", $vendorId);
        }
        if (!empty($hotelId)) {
            $rateSource->where("hotel_id", "=", $hotelId);
        }
        if (!empty($date)) {
            $rateSource->where("date", "=", $date);
        }
        $rateSource->orderBy('created_at', 'desc');
        $rateSources = $rateSource->get();
        return view('rate-source.view-rate-source-report', compact('rateSources'));
    }

    public function rateSourceReportExport(Request $request){
        $vendorId = $request->vendorId;
        $hotelId = $request->hotelId;
        $date = $request->date;
//     return $request->all();
        $rateSource = apple_hotel_rate_source::where('created_at','!=','');
        if (!empty($vendorId)) {
            $rateSource->where("vendor_id", "=", $vendorId);
        }
        if (!empty($hotelId)) {
            $rateSource->where("hotel_id", "=", $hotelId);
        }
        if (!empty($date)) {
            $rateSource->where("date", "=", $date);
        }
        $rateSource->orderBy('created_at', 'desc');
        $rateSources = $rateSource->get();
        $dtTime = Carbon::now();
        \Excel::create("Rate_Source_Report_".$dtTime, function ($excel) use ($rateSources) {
            $excel->sheet('Sheet', function ($sheet) use ($rateSources) {
                $sheet->loadView("rate-source.view-rate-source-report", compact('rateSources'));
            });
        })->download('xlsx');
//        return view('rate-source.view-rate-source-report', compact('rateSources'));
    }
}
