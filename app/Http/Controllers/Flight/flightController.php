<?php


namespace App\Http\Controllers\Flight;

use App\Http\Controllers\Controller;
use App\Model\Flight\Airport;
use App\Model\Flight\Flight;
use App\Model\Flight\Sabre\Recommendation;
use App\Model\Flight\Sabre\SACSClient;

use App\Mail\sendPnrDetails;

//use GuzzleHttp\Client;
use App\User;
use Illuminate\Http\Request;
// use Illuminate\Support\Facades\Mail;
use Session;

use App\Model\Flight\Sabre\Workflow;
use App\Model\Flight\Sabre\BargainFinderMaxSoapActivity;
use App\Model\Flight\Sabre\EnhancedAirBookActivity;
use App\Model\Flight\Sabre\PassengerDetailsActivity;
use App\Model\Flight\Sabre\GetReservation;
use App\Model\Flight\Sabre\TravelItineraryReadActivity;
use App\Model\Flight\Sabre\OTA_Cancel;
use App\Model\Flight\Sabre\EndTransactionLLSRQ;
use App\Model\Flight\Sabre\AirTicketRQ;
use App\Model\Flight\Sabre\ContextChangeRQ;
use App\Model\Flight\Sabre\EnhancedAirBookActivityPrice;
use App\Model\Flight\Airline;
use Mail;

//pnr
use App\Model\Flight\apple_pnr;
use App\Model\Flight\apple_pnr_passangers;
use App\Model\Flight\apple_pnr_ssr;

use Auth;


/**
 * Class flightController
 * @package App\Http\Controllers\Flight
 */
class flightController extends Controller
{

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function getFlightResult() {
        return view("flight.flight-result");
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function viewEticket() {
        return view("flight.e-ticket-itinerary");
    }

    /**
     * @param $ref
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function info($ref) {
        $fareBoardSearch = Session::get('quotation.flight.fareBoardSearch');

        $selectedFare = $fareBoardSearch[$ref];

        return view("flight.flight-info", compact('selectedFare'));
    }


    /**
     * @param $ref
     * @return mixed
     */
    function infoTest($ref) {
        $fares = Session::get('quotation.flight.Recommendations');

        $recomds = new Recommendation();

        $res = $recomds->getRecommendations(Session::get('quotation.flight.fareBoardSearch'));


        $selectedFare = $res[$ref];
        dd($selectedFare);

        return $selectedFare;
    }

    /**
     * @param Request $request
     * @return mixed
     */
    function calenderSearch(Request $request) {
        dd($request->all());
        parse_str($request, $output);
        $from = $output['from'];
        $to = $output['to'];

        $departureDate = new \DateTime($output['departure']['year'] . '-' . $output['departure']['month'] . '-' . $output['departure']['date'] . 'T00:00:00+0000', new \DateTimeZone('UTC'));

        $recomds = new Recommendation();
        $recomds->setup();
        $deplist = $recomds->calenderSearch($from, $to, $departureDate);

        $recomds->signOut();

        return $deplist;
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    function fareSearch(Request $request) {
        parse_str($request->getContent(), $output);

        Session::put('quotation.test', $output);


        $from = $output['from'];
        $to = $output['to'];

        $departureDate = new \DateTime($output['departure']['year'] . '-' . $output['departure']['month'] . '-' . $output['departure']['date'] . 'T00:00:00+0000', new \DateTimeZone('UTC'));

        $recomds = new Recommendation();

        $recomds->setup();

        $deplist = $recomds->fareSearch($from, $to, $departureDate);

        $res = $recomds->getRecommendations($deplist);
        Session::put('quotation.recommendations', $res);


        $Optimize = $recomds->recommendationList($deplist);
        Session::put('quotation.fareSearch', $Optimize);

        $recomds->signOut();

        return $Optimize;
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function fareView() {
        return view('flight.flight-result-item');
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function summeryView() {
        return view('flight.flight-summery');
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function pnrDetails(Request $request) {
        return view('flight.flight-pnr-details');
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function pnrReviewTPL(Request $request) {
        return view('flight.flight-pnr-review');
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    function ticketViewTPL(Request $request) {
        return view('flight.ticket-view-tpl');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function fullSearch(Request $request) {
        parse_str($request->getContent(), $output);

        Session::forget('sharedContext');
        Session::forget('search_data');
        Session::put('search_data', $output);
        Session::save();

        // parse_str("_token=NVLxrP7EczruHF8BZdeo8rBGAECawfQ9kQ6T9Lsd&from[]=CMB&to[]=SIN&flight_departure_date[year]=2018&flight_departure_date[month]=12&flight_departure_date[day]=05&pax[adult]=2&pax[child]=2&pax[cnb]=0&Currency=0", $output);
        $output["callType"] = "BargainFinderMaxRQ";

        $workflow = new Workflow();
        $bargainFinderMaxSoapActivity = new BargainFinderMaxSoapActivity();
        $bargainFinderMaxSoapActivity->filters($output);
        $workflow->create($bargainFinderMaxSoapActivity);
        $result = $workflow->runWorkflow();
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function bookingItinerary(Request $request) {
        // $output = json_decode('{"ways":[{"segment":[{"DepartureLocationCode":"CMB","DepartureTerminalID":"","TecDepartureDateTime":"2018-11-01T01:10:00","ResBookDesigCode":"K","DepartureDateTime":"Thu, 1 Nov, 01:10","DepartureDate":"2018-11-01","DepartureTime":"01:10","ArrivalLocationCode":"SIN","ArrivalTerminalID":"0","TecArrivalDateTime":"2018-11-01T07:40:00","ArrivalDateTime":"Thu, 1 Nov, 07:40","ArrivalDate":"2018-11-01","ArrivalTime":"07:40","StopQuantity":"0","ElapsedTime":"04h 00m","AirlineCode":"MI","OperatingAirline":"SQ","FlightNumber":"469","AirEquipType":"333","AirlineImgURL":"http:\/\/apple.d\/assets\/image\/flight\/airline_logos\/silk-air.jpg","Mileage":"","SeatsRemaining":"9","Cabin":"Economy"}],"summary":{"DepartureLocationCode":"CMB","DepartureDateTime":"Thu, 1 Nov, 01:10","DepartureDate":"2018-11-01","DepartureTime":"01:10","ArrivalLocationCode":"SIN","ArrivalDateTime":"Thu, 1 Nov, 07:40","ArrivalDate":"2018-11-01","ArrivalTime":"07:40","AirlineCode":"MI","SeatsRemaining":"9","AirlineImgURL":"http:\/\/apple.d\/assets\/image\/flight\/airline_logos\/silk-air.jpg","Cabin":"Economy","StopQuantity":0,"ElapsedTime":"04h 00m"}},{"segment":[{"DepartureLocationCode":"SIN","DepartureTerminalID":"2","TecDepartureDateTime":"2018-11-03T08:30:00","ResBookDesigCode":"K","DepartureDateTime":"Sat, 3 Nov, 08:30","DepartureDate":"2018-11-03","DepartureTime":"08:30","ArrivalLocationCode":"CMB","ArrivalTerminalID":"","TecArrivalDateTime":"2018-11-03T09:50:00","ArrivalDateTime":"Sat, 3 Nov, 09:50","ArrivalDate":"2018-11-03","ArrivalTime":"09:50","StopQuantity":"0","ElapsedTime":"03h 50m","AirlineCode":"MI","OperatingAirline":"MI","FlightNumber":"428","AirEquipType":"738","AirlineImgURL":"http:\/\/apple.d\/assets\/image\/flight\/airline_logos\/silk-air.jpg","Mileage":"","SeatsRemaining":"9","Cabin":"Economy"}],"summary":{"DepartureLocationCode":"SIN","DepartureDateTime":"Sat, 3 Nov, 08:30","DepartureDate":"2018-11-03","DepartureTime":"08:30","ArrivalLocationCode":"CMB","ArrivalDateTime":"Sat, 3 Nov, 09:50","ArrivalDate":"2018-11-03","ArrivalTime":"09:50","AirlineCode":"MI","SeatsRemaining":"9","AirlineImgURL":"http:\/\/apple.d\/assets\/image\/flight\/airline_logos\/silk-air.jpg","Cabin":"Economy","StopQuantity":0,"ElapsedTime":"03h 50m"}}],"TotalPrice":{"Amount":760.6,"CurrencyCode":"USD"},"Type":"return","RecommendationNO":1,"PTC_FareBreakdown":[{"Quantity":"2","TypeID":"ADT","Type":"Adult","TotalFare":"262.20","TotalTax":"84.20","EquivFare":"178.00","BaggageInformation":[{"Allowance":"30","Segment":[{"From":"CMB","To":"SIN"}]},{"Allowance":"30","Segment":[{"From":"SIN","To":"CMB"}]}],"Penalty":[{"Type":"Exchange","Applicability":"B","Amount":"50.00"},{"Type":"Exchange","Applicability":"A","Amount":"50.00"},{"Type":"Refund","Applicability":"B","Amount":"--"},{"Type":"Refund","Applicability":"A","Amount":"--"},{"Type":"Refundable","Applicability":"B","Amount":"No"},{"Type":"Refundable","Applicability":"A","Amount":"No"}]},{"Quantity":"1","TypeID":"CNN","Type":"Child","TotalFare":"218.20","TotalTax":"84.20","EquivFare":"134.00","BaggageInformation":[{"Allowance":"30","Segment":[{"From":"CMB","To":"SIN"}]},{"Allowance":"30","Segment":[{"From":"SIN","To":"CMB"}]}],"Penalty":[{"Type":"Exchange","Applicability":"B","Amount":"50.00"},{"Type":"Exchange","Applicability":"A","Amount":"50.00"},{"Type":"Refund","Applicability":"B","Amount":"--"},{"Type":"Refund","Applicability":"A","Amount":"--"},{"Type":"Refundable","Applicability":"B","Amount":"No"},{"Type":"Refundable","Applicability":"A","Amount":"No"}]},{"Quantity":"1","TypeID":"INF","Type":"Infant","TotalFare":"18.00","TotalTax":"","EquivFare":"18.00","BaggageInformation":[{"Allowance":"10","Segment":[{"From":"CMB","To":"SIN"}]},{"Allowance":"10","Segment":[{"From":"SIN","To":"CMB"}]}],"Penalty":[{"Type":"Exchange","Applicability":"B","Amount":"50.00"},{"Type":"Exchange","Applicability":"A","Amount":"50.00"},{"Type":"Refund","Applicability":"B","Amount":"--"},{"Type":"Refund","Applicability":"A","Amount":"--"},{"Type":"Refundable","Applicability":"B","Amount":"No"},{"Type":"Refundable","Applicability":"A","Amount":"No"}]}],"SeatCount": 3}');
        // $output = json_decode("{\"ways\":[{\"segment\":[{\"DepartureLocationCode\":\"CMB\",\"DepartureTerminalID\":\"\",\"TecDepartureDateTime\":\"2018-12-06T01:10:00\",\"ResBookDesigCode\":\"N\",\"DepartureDateTime\":\"Thu, 6 Dec, 01:10\",\"DepartureDate\":\"2018-12-06\",\"DepartureTime\":\"01:10\",\"ArrivalLocationCode\":\"SIN\",\"ArrivalTerminalID\":\"0\",\"TecArrivalDateTime\":\"2018-12-06T07:40:00\",\"ArrivalDateTime\":\"Thu, 6 Dec, 07:40\",\"ArrivalDate\":\"2018-12-06\",\"ArrivalTime\":\"07:40\",\"StopQuantity\":\"0\",\"ElapsedTime\":\"04h 00m\",\"AirlineCode\":\"MI\",\"OperatingAirline\":\"SQ\",\"FlightNumber\":\"469\",\"AirEquipType\":\"333\",\"AirlineImgURL\":\"http:\/\/apple.d\/assets\/image\/flight\/airline_logos\/silk-air.jpg\",\"Mileage\":\"\",\"SeatsRemaining\":\"9\",\"Cabin\":\"Economy\"}],\"summary\":{\"DepartureLocationCode\":\"CMB\",\"DepartureDateTime\":\"Thu, 6 Dec, 01:10\",\"DepartureDate\":\"2018-12-06\",\"DepartureTime\":\"01:10\",\"ArrivalLocationCode\":\"SIN\",\"ArrivalDateTime\":\"Thu, 6 Dec, 07:40\",\"ArrivalDate\":\"2018-12-06\",\"ArrivalTime\":\"07:40\",\"AirlineCode\":\"MI\",\"SeatsRemaining\":\"9\",\"AirlineImgURL\":\"http:\/\/apple.d\/assets\/image\/flight\/airline_logos\/silk-air.jpg\",\"Cabin\":\"Economy\",\"StopQuantity\":0,\"ElapsedTime\":\"04h 00m\"}}],\"TotalPrice\":{\"Amount\":332,\"CurrencyCode\":\"USD\"},\"Type\":\"one-way\",\"RecommendationNO\":1,\"PTC_FareBreakdown\":[{\"Quantity\":\"1\",\"TypeID\":\"ADT\",\"Type\":\"Adult\",\"TotalFare\":\"175.00\",\"TotalTax\":\"50.00\",\"EquivFare\":\"125.00\",\"BaggageInformation\":[{\"Allowance\":\"30\",\"Segment\":[{\"From\":\"CMB\",\"To\":\"SIN\"}]}],\"Penalty\":[{\"Type\":\"Exchange\",\"Applicability\":\"B\",\"Amount\":\"50.00\"},{\"Type\":\"Exchange\",\"Applicability\":\"A\",\"Amount\":\"--\"},{\"Type\":\"Refund\",\"Applicability\":\"B\",\"Amount\":\"--\"},{\"Type\":\"Refund\",\"Applicability\":\"A\",\"Amount\":\"--\"},{\"Type\":\"Refundable\",\"Applicability\":\"B\",\"Amount\":\"No\"},{\"Type\":\"Refundable\",\"Applicability\":\"A\",\"Amount\":\"No\"}]},{\"Quantity\":\"1\",\"TypeID\":\"CNN\",\"Type\":\"Child\",\"TotalFare\":\"144.00\",\"TotalTax\":\"50.00\",\"EquivFare\":\"94.00\",\"BaggageInformation\":[{\"Allowance\":\"30\",\"Segment\":[{\"From\":\"CMB\",\"To\":\"SIN\"}]}],\"Penalty\":[{\"Type\":\"Exchange\",\"Applicability\":\"B\",\"Amount\":\"50.00\"},{\"Type\":\"Exchange\",\"Applicability\":\"A\",\"Amount\":\"--\"},{\"Type\":\"Refund\",\"Applicability\":\"B\",\"Amount\":\"--\"},{\"Type\":\"Refund\",\"Applicability\":\"A\",\"Amount\":\"--\"},{\"Type\":\"Refundable\",\"Applicability\":\"B\",\"Amount\":\"No\"},{\"Type\":\"Refundable\",\"Applicability\":\"A\",\"Amount\":\"No\"}]},{\"Quantity\":\"1\",\"TypeID\":\"INF\",\"Type\":\"Infant\",\"TotalFare\":\"13.00\",\"TotalTax\":\"\",\"EquivFare\":\"13.00\",\"BaggageInformation\":[{\"Allowance\":\"10\",\"Segment\":[{\"From\":\"CMB\",\"To\":\"SIN\"}]}],\"Penalty\":[{\"Type\":\"Exchange\",\"Applicability\":\"B\",\"Amount\":\"50.00\"},{\"Type\":\"Exchange\",\"Applicability\":\"A\",\"Amount\":\"--\"},{\"Type\":\"Refund\",\"Applicability\":\"B\",\"Amount\":\"--\"},{\"Type\":\"Refund\",\"Applicability\":\"A\",\"Amount\":\"--\"},{\"Type\":\"Refundable\",\"Applicability\":\"B\",\"Amount\":\"No\"},{\"Type\":\"Refundable\",\"Applicability\":\"A\",\"Amount\":\"No\"}]}],\"SeatCount\":2}");
        $output = $request->all();

        Session::forget('flight_data');
        Session::put('flight_data', $output);
        Session::save();

        $output["callType"] = "EnhancedAirBookRQ";

        $workflow = new Workflow();
        $enhancedAirBookActivity = new EnhancedAirBookActivity();
        $enhancedAirBookActivity->filters($output);
        $workflow->create($enhancedAirBookActivity);
        $result = $workflow->runWorkflow();
    }

    function savePNR(Request $request) {
        if (isset($_POST)) {
            Session::forget('pnr_data');
            Session::put('pnr_data', $_POST);
            Session::save();

            $output = $_POST;
            $output['callType'] = "PassengerDetailsRQ";

            $workflow = new Workflow();
            $PassengerDetailsActivity = new PassengerDetailsActivity();
            $PassengerDetailsActivity->filters($output);
            $workflow->create($PassengerDetailsActivity);
            $result = $workflow->runWorkflow();
        }
    }

    function reviewPNR(Request $request) {
        Session::forget('sharedContext');
        Session::save();

        $data = $request->all();

        $output['pnrID'] = $data["pnr"]; // "CICGIU"; // "NPKJEU";
        Session::put('pnr_ref_id', $output['pnrID']);
        Session::save();

        if(isset($data["booking_id"])) {
            $output['bookingID'] = $data["booking_id"];
        }
        $output['callType'] = "GetReservationRQ";

        $workflow = new Workflow();
        $GetReservation = new GetReservation();
        $GetReservation->filters($output);
        $workflow->create($GetReservation);
        $result = $workflow->runWorkflow();
    }

    function sendEmailPNR() {
        $pnr_all_data = Session::get('pnr_all_data');

        $ref_id = Session::get('pnr_ref_id');
        $pnr_all_data=(array)$pnr_all_data;

        // $recipients = User::hasRole('flight_executive')->email;
        $emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
        $user_name = Auth::user()->name;
        $user_mail = Auth::user()->email;
        array_push($emails, $user_mail);
        $apple_ref_id = Session::get("apple_ref_id");

        $message = '';
        $subject = "PNR Details (Reference No : " . $ref_id . ")";

        Mail::to($emails)->bcc('<EMAIL>')->send(new sendPnrDetails($emails, $user_name, $message, $subject, $apple_ref_id, $pnr_all_data));

        echo json_encode(array("status"=>true, "data"=> true));
    }

    function loadReviewPNR(Request $request) {
        Session::forget('sharedContext');
        Session::save();

        $data = $request->all();
        // $data = array('pnr' => "UESCVI", "booking_id" => "WWWWWW");

        $output['pnrID'] = $data["pnr"]; // "CICGIU"; // "NPKJEU";
        if(isset($data["booking_id"])) {
            $output['bookingID'] = $data["booking_id"];
        }
        $output['callType'] = "TravelItineraryReadRQ";

        $workflow = new Workflow();
        $TravelItineraryReadActivity = new TravelItineraryReadActivity();
        $TravelItineraryReadActivity->filters($output);
        $workflow->create($TravelItineraryReadActivity);
        $result = $workflow->runWorkflow();

        $obj = Session::get("sharedContext");
        Session::put('sharedContext', $obj);
        Session::save();
    }

    function loadPNR(Request $request) {
        $data = $request->all();

        $output['pnrID'] = $data["pnr"]; // "CICGIU"; // "NPKJEU";
        if(isset($data["booking_id"])) {
            $output['bookingID'] = $data["booking_id"];
        }
        $output['callType'] = "GetReservationRQ";

        $workflow = new Workflow();
        $GetReservation = new GetReservation();
        $GetReservation->filters($output);
        $workflow->create($GetReservation);
        $result = $workflow->runWorkflow();
    }

    function cancelPNR(Request $request) {

        $output['callType'] = "OTA_CancelRQ";

        $workflow = new Workflow();
        $OTA_Cancel = new OTA_Cancel();
        $OTA_Cancel->filters($output);
        $workflow->create($OTA_Cancel);
        $result = $workflow->runWorkflow();
         $ref_id = Session::get('pnr_ref_id');
         $pnr = apple_pnr::where('pnr_ref','=',$ref_id)->first();
         $id=$pnr->id;
         $pnrUpdate=apple_pnr::find($id);
         $pnrUpdate->status="1";
         $pnrUpdate->save();
    }

    function endTransaction(Request $request) {

        $output['callType'] = "EndTransactionLLSRQ";

        $workflow = new Workflow();
        $EndTransactionLLSRQ = new EndTransactionLLSRQ();
        $EndTransactionLLSRQ->filters($output);
        $workflow->create($EndTransactionLLSRQ);
        $result = $workflow->runWorkflow();
    }

    function issueTicket(Request $request) {
        $arr =  $request->all();

        if(isset($arr['data-set'])) {
            $output['data-set'] = json_decode($arr['data-set']);
            Session::put('ticketing_air_code', $output['data-set']->ways[0]->segment[0]->MarketingAirline);
        } else if($arr['data']) {
            $output['data-set'] = json_decode($arr['data']);
            // var_dump($output['data-set']);
            Session::put('ticketing_air_code', $output['data-set']->data->Segment[0]->MarketingAirlineCode);
        }

        $output['callType'] = "AirTicketRQ";


        var_dump(Session::get('pnr_ref_id'));

        if(isset($arr['pnr-id'])) {
            Session::put('pnr_ref_id', $arr['pnr-id']);
        }
        var_dump(Session::get('pnr_ref_id'));

        Session::save();

        $workflow = new Workflow();
        $AirTicketRQ = new AirTicketRQ();
        $AirTicketRQ->filters($output);
        $workflow->create($AirTicketRQ);
        $result = $workflow->runWorkflow();
    }

    function changeAAA(Request $request) {
        $output['callType'] = "ContextChangeRQ";

        $workflow = new Workflow();
        $ContextChangeRQ = new ContextChangeRQ();
        $ContextChangeRQ->filters($output);
        $workflow->create($ContextChangeRQ);
        $result = $workflow->runWorkflow();
    }

    function ticketView(Request $request) {
        echo json_encode(array('status'=>true, 'data'=>''));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function changePrice(Request $request) {
        $output = $request->all();

        $output["data"] = json_decode($output['data']);
        $output["callType"] = "EnhancedAirBookRQ";

        $workflow = new Workflow();
        $EnhancedAirBookActivityPrice = new EnhancedAirBookActivityPrice();
        $EnhancedAirBookActivityPrice->filters($output);
        $workflow->create($EnhancedAirBookActivityPrice);
        $result = $workflow->runWorkflow();
    }

    function Recomendations(Request $request) {
        $client = new Client([
            'headers' => ['content-type' => 'application/json', 'Accept' => 'application/json'],
        ]);

        parse_str($request->getContent(), $output);

        $from = $output['from'][0];
        $to = $output['to'][0];


        $depDate = $output['flight_arrival_date']['day'];
        $depMonth = $output['flight_arrival_date']['month'];
        $depYear = $output['flight_arrival_date']['year'];

        $dateString = $depYear . '-' . $depMonth . '-' . $depDate;
        $departureDate = new \DateTime($dateString, new \DateTimeZone('UTC'));

        $response = $client->request('POST', "https://flights-api.appletechlabs.com/airSellFromRecommendation", [
            'Itinerary' => [
                array(
                    'from' => $from,
                    'to' => $to,
                    'segments' => [
                        array(
                            'departureDate' => $departureDate,
                            'from' => 'CMB',
                            'to' => 'DOH',
                            'companyCode' => 'UL',
                            'flightNumber' => '183',
                            'bookingClass' => 'Y',
                            'nrOfPassengers' => '4',
                        ),
                    ]
                ),
            ],
        ]);

        dd($response);
    }

    function CreatePNR(Request $request) {

    }


    /**
     * @return \Illuminate\Http\JsonResponse
     */
    function getResultFromSession() {
        $result = [];
        $result['calendarSearch'] = Session::get('quotation.flight.calendarSearch');
        $fareBoardSearch = Session::get('quotation.flight.fareBoardSearch');
        foreach ($fareBoardSearch as $fare) {
            foreach ($fare->airports as $airports) {
                $stops = new \stdClass();
                $stops->name = $airports->IATA;
                $stops->lat = $airports->coordinates['latitude'];
                $stops->lng = $airports->coordinates['longitude'];

                $fare->stops[] = $stops;
            }
            $fare->originTime = $fare->origin["dateTime"]->format('H:i');
            $fare->destinationTime = $fare->destination["dateTime"]->format('H:i');
            $fareBoardSearchNew[] = $fare;

        }

        $result['fareBoardSearch'] = $fareBoardSearchNew;

        $result['status'] = "OK";

        $response['result'] = $result;


        return response()->json($response);

    }

    function sessions() {
        dd(Session::get('quotation.flight'));
    }


    /**
     * @return array|\Illuminate\Http\JsonResponse|mixed
     * @throws \Exception
     */
    function test() {
        return $this->getResultFromSession();

        $recomds = new Recommendation();

        if (null == Session::get('quotation.flight.fareBoardSearch')) {
            $from = 'CMB';
            $to = 'SIN';

            $departureDate = new \DateTime('2018-02-07T00:00:00+0000', new \DateTimeZone('UTC'));

            $recomds->setup();
//            $result = $recomds->fareSearchMain($from,$to,$departureDate);
            $result = $recomds->fareSearch($from, $to, $departureDate);
            $recomds->signOut();
            dd($result);
            session(['quotation.flight.fareBoardSearch' => $recomds->fillInfo($result['fareBoardSearch'])]);
            session(['quotation.flight.calendarSearch' => $result['calendarSearch']]);

        }

        $fareboardSeach = Session::get('quotation.flight.fareBoardSearch');

        return $recomds->recommendationList($fareboardSeach);

        $result['Recommendations'] = $recomds->getRecommendations($result['fareBoardSearch']);

        $result['recommendationList'] = $recomds->recommendationList($result['fareBoardSearch']);

        // $Optimize = $recomds->recommendationList($deplist);

        return $result;
    }

    /**
     * @param $from
     * @param $to
     * @return mixed
     */
    function getCoordinates($from, $to) {
        $coordinates['from'] = Airport::getCoordinates($from);
        $coordinates['from']['name'] = $from;
        $coordinates['to'] = Airport::getCoordinates($to);
        $coordinates['to']['name'] = $to;
        return $coordinates;
    }

    /**
     * @param $countries
     * @return array
     */
    function checkCountries($countries) {
        return Flight::checkCountries($countries);
    }

    /**
     * @return null
     */
    function getAirports() {
        return Flight::getAirports();
    }

    function save_pnr() {
        //return Auth::user();
        $ref_id=Session::get('pnr_ref_id');
        $data=Session::get('pnr_data');
        $all_data=Session::get('flight_data');
        $all_data=(array)$all_data;
        $pnr_all_data = Session::get('pnr_all_data');
        $pnr_all_data=(array)$pnr_all_data;
        $date_time=date('Y-m-d H:i:s');

        if (!empty($ref_id)) {
            //save data to db

            $pnr = new apple_pnr();
            $pnr->pnr_ref = $ref_id;
            $pnr->apple_ref = $this->get_pnr_ref();
            $pnr->date_time = $date_time;
            $pnr->user_id = Auth::user()->id;
            $pnr->status = 0;
            $pnr->gds_type = "sabre";
            $pnr->save();
            Session::put('apple_ref_id', $pnr->apple_ref);

            if (!empty($data['traveler_title'])) {
                foreach ($data['traveler_title'] as $key => $value) {
                    $passanger = new apple_pnr_passangers();
                    $passanger->pnr_id = $pnr->id;
                    $passanger->type = getPaxTypeCode($data['traveler_type'][$key]);
                    $passanger->title = $data['traveler_title'][$key];
                    $passanger->first_name = $data['traveler_fname'][$key];
                    $passanger->last_name = $data['traveler_lname'][$key];
                    $passanger->dob = $data['traveler_dob'][$key];
                    $passanger->gender = getGenderCode($data['traveler_gender'][$key]);
                    $passanger->ff_id = $data['traveler_ff'][$key];
                    $passanger->passport_no = $data['traveler_passport_no'][$key];
                    $passanger->passport_country_code = $data['traveler_passport_name'][$key];
                    $passanger->passport_expiary_date = $data['traveler_passport_expire'][$key];
                    $passanger->traveller_no = $data['traveler_name_number'][$key];
                    $passanger->save();
                }
            }

            if (!empty($data['ssr_code'])) {
                foreach ($data['ssr_code'] as $ssrKey => $val) {
                    if ($ssrKey != 0) {

                        $ssr = new apple_pnr_ssr();
                        $ssr->pnr_id = $pnr->id;
                        $ssr->traveller_no = $data['ssr_person_name'][$ssrKey];
                        /*$ssr->ssr = $data['ssr_text'][$ssrKey];*/
                        $ssr->save();
                    }
                }
            }
        }
    }

    function get_pnr_ref() {
        $pnr_ref = $this->genrate_apple_pnr_ref();
        $count = apple_pnr::where('apple_ref', '=', $pnr_ref)->get()->count();
        if ($count == 0) {
            return $pnr_ref;
        } else {
            return get_pnr_ref();
        }
    }

    function genrate_apple_pnr_ref() {
        $random_string = strtoupper(str_random(6));
        return $random_string;
    }
}


?>