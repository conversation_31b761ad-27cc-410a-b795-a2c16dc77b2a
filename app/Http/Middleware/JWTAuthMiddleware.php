<?php

namespace App\Http\Middleware;

use Closure;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenExpiredException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenInvalidException;

class JWTAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        try {
            // Suppress deprecation warnings temporarily
            $oldErrorReporting = error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);
            
            // Get the token from the request
            $token = JWTAuth::getToken();
            
            if (!$token) {
                error_reporting($oldErrorReporting);
                return response()->json(['error' => 'Token not provided'], 401);
            }
            
            // Authenticate the user
            $user = JWTAuth::authenticate($token);
            
            if (!$user) {
                error_reporting($oldErrorReporting);
                return response()->json(['error' => 'User not found'], 401);
            }
            
            // Restore error reporting
            error_reporting($oldErrorReporting);
            
            // Set the authenticated user
            auth()->setUser($user);
            
        } catch (TokenExpiredException $e) {
            error_reporting($oldErrorReporting ?? E_ALL);
            return response()->json(['error' => 'Token expired'], 401);
        } catch (TokenInvalidException $e) {
            error_reporting($oldErrorReporting ?? E_ALL);
            return response()->json(['error' => 'Token invalid'], 401);
        } catch (JWTException $e) {
            error_reporting($oldErrorReporting ?? E_ALL);
            return response()->json(['error' => 'Token absent'], 401);
        } catch (\Exception $e) {
            error_reporting($oldErrorReporting ?? E_ALL);
            return response()->json([
                'error' => 'Authentication failed',
                'message' => $e->getMessage()
            ], 500);
        }

        return $next($request);
    }
}
