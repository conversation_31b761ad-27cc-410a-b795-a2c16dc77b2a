<?php

namespace App\Http\Middleware;

use App\Model\MobileApp\EmailVerification;
use Closure;

class CheckToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next) {

        if(is_string($request['email']) != null) {

            if (isset($request['token'])) {

                $tokenData = EmailVerification::where([
                                                        ['email', $request['email']],
                                                        ['verify_token', $request['token']],
                                                        ['verify_status', 1]
                                                      ])->get()->count();

                if ($tokenData != 0){

                    return $next($request);

                } else {

                    $data = [
                        'error' => 'Invalid Email, Token or Email not Activated'
                    ];
                    
                    return response($data, 401);
                }


            } else {

                $data = [
                  'error' => 'Token Required as a parameter'
                ];

                return response($data, 401);
            }

        } else {

            $data = [
                'error' => 'User Email Required as a parameter'
            ];

            return response($data, 401);
        }

    }

}
