<?php

namespace App\Http\Middleware;

use App\Model\Hotel\Hotel;
use App\Model\Hotel\Rates;
use App\Model\Place\Attraction;
use App\Model\Place\CityTour;
use App\Model\Place\Excursion;
use App\Model\Quotation\QuotationHotel;
use Carbon\Carbon;
use Closure;
use Exception;
use Session;
use Validator;

/**
 * Class QuotationMiddleware
 * @package App\Http\Middleware
 */
class QuotationMiddleware
{
    /**
     * @param $request
     * @param Closure $next
     * @return mixed
     * @throws Exception
     * @throws \Throwable
     */
    public function handle($request, Closure $next)
    {
        $Data = $request->all();//data array
        $QuotationArray = Session::get('quotation');//data array


        $CurrentSlide = isset($Data['current_slide']) ? intval($Data['current_slide']) : 0;
        $NextSlide = isset($Data['next_slide']) ? intval($Data['next_slide']) : 0;

        $Market = Session::get('quotation.market');

        if ($CurrentSlide > $NextSlide) {
            return $next($request);
        }

        if (empty($Data['current_slide'])) {
            return $next($request);
        }


        //check slider sets
        $Validator = Validator::make($Data, ['current_slide' => 'required|numeric']);
        if ($Validator->fails()) {
            $messages = $Validator->messages();
            return response($messages);
        }

        $Slide = $Data['current_slide'];//get slider


        $rules[1] = [
            'country' => 'required'
        ];

        $rules[2] = [
            'pax' => 'required|array',
            'arrival_date' => 'required|array',
            'pax.adult' => 'numeric|required|min:1'
        ];

        $rules[3] = [
            'place' => 'required|array',
            'place.*' => 'numeric|exists:apple_places,ID',
        ];

        $rules[4] = [];
        $rules[5] = [];
        $rules[6] = [
            'meal' => 'array|required',
        ];
        $rules[7] = [];
        $rules[8] = [];


        $rules[9] = ['markup_type' => 'numeric'];

        $rules[10] = [
            'client_title' => 'numeric|required',
            'client_name' => 'string|required',
            'remark' => 'array',
            'confirm_note' => 'array'
        ];

        $rules[17] = [
            'night' => 'required'
        ];


        switch ($Slide) {
            case 1://welcome
                $Validator = Validator::make(arrayMapMulti('getActualDataType', $Data), $rules[$Slide]);
                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }
                break;
            case 2://pax

                $Validator = Validator::make(arrayMapMulti('getActualDataType', $Data), $rules[2]);
                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }

                break;
            case 3://transport
                $Validator = Validator::make($Data, $rules[3]);


                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }

                break;
            case 4:////hotel
                $Validator = Validator::make($Data, $rules[4]);
                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }

                if (!empty($Data['hotel_setting'])) {
                    $accommodation = Session::get('quotation.accommodation');
                    foreach ($Data['hotel_setting'] as $HotelIndex => $HotelData) {

                        if (!empty($HotelData) && $accommodation[$HotelIndex] == 1) {
                            $HotelDecode = QuotationHotel::getDecodeArray($HotelData);

                            if (isset($HotelDecode['provider']) && $HotelDecode['provider'] == 'local') {
                                if (!empty($HotelDecode['hotel'])) {
                                    
                                    $HotelStatus = Hotel::getStatus($HotelDecode['hotel'], $HotelDecode['check_in'], $HotelDecode['room_type'], $HotelDecode['meal_type']??false, $HotelDecode['room_category'], $Market);
                                    if(!\Auth::user()->can("change_hotel_rates") && $HotelStatus[0] == 3) {
                                        throw new Exception("Rates not available for select date range for hotel: " . Hotel::getHotelFromAll($HotelDecode['hotel'])->name . "<br>");
                                    }
                                }
                            }
                        }
                    }
                }


                break;
            case 5://attr
                $Validator = Validator::make($Data, $rules[5]);
                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }

                break;
            case 6://meal

                $Validator = Validator::make($Data, $rules[6]);
                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }


                break;
            case 7://
                $Validator = Validator::make($Data, $rules[7]);
                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }

                break;
            case 8:
                if (!empty($Data['hotel_setting'])) {
                    $accommodation = Session::get('quotation.accommodation');
                    foreach ($Data['hotel_setting'] as $HotelIndex => $HotelData) {

                        if (!empty($HotelData) && $accommodation[$HotelIndex] == 1) {
                            $HotelDecode = QuotationHotel::getDecodeArray($HotelData);

                            if (isset($HotelDecode['provider']) && $HotelDecode['provider'] == 'local') {
                                if (!empty($HotelDecode['hotel'])) {
                                    
                                    $HotelStatus = Hotel::getStatus($HotelDecode['hotel'], $HotelDecode['check_in'], $HotelDecode['room_type'], $HotelDecode['meal_type']??false, $HotelDecode['room_category'], $Market);
                                    if(!\Auth::user()->can("change_hotel_rates") && $HotelStatus[0] == 3) {
                                        throw new Exception("Rates not available for select date range for hotel: " . Hotel::getHotelFromAll($HotelDecode['hotel'])->name . "<br>");
                                    }

                                    $rate = $this->getQuotationHotelRate($HotelIndex, $HotelDecode,$QuotationArray);
                                    
                                    if (($QuotationArray['pax']['cwb']) || ($QuotationArray['pax']['cnb'])) {
                                        $RateStatusChild = Hotel::hasRateChild($HotelDecode['hotel'], $HotelDecode['check_in'], $HotelDecode['room_type'], $HotelDecode['meal_type'], $HotelDecode['room_category'], $request->session()->get('quotation.market'));
                                        $RateStatusStatusChild = Hotel::hasRateChildSession($HotelIndex, $HotelDecode['hotel'], $HotelDecode['check_in'], $HotelDecode['room_type'], $HotelDecode['meal_type'], $HotelDecode['room_category'], $request->session()->get('quotation.market'));

                                        if ((!$RateStatusChild && !$RateStatusStatusChild)) {
                                            $msg = ("No child rates for hotel: " . Hotel::getHotelFromAll($HotelDecode['hotel'])->name . " ({$HotelDecode['hotel']})");
                                            request()->attributes->add(['warn' => $msg]);
                                        }

                                    }
                                }
                            }
                        }
                    }
                }

                //check attraction rates
                $Attraction = new Attraction();//Excursion cost
                if (!$Attraction->hasRateList($QuotationArray)) {
                    throw new Exception("Rates is missing for: " . implode("<br>", $Attraction->Error));
                }
                $CityTour = new CityTour();//Excursion cost
                if (!$CityTour->hasRateList($QuotationArray)) {
                    throw new Exception("Rates is missing for: " . implode("<br>", $CityTour->Error));
                }
                $Excursion = new Excursion();//Excursion cost
                if (!$Excursion->hasRateList($QuotationArray)) {
                    throw new Exception("Rates is missing for: " . implode("<br>", $Excursion->Error));
                }

                $Validator = Validator::make($Data, $rules[8]);
                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }

                break;
            case 9://
                $Validator = Validator::make($Data, $rules[9]);
                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }
                break;
            case 10://confirm

                $Validator = Validator::make($Data, $rules[10]);

                if ($Validator->fails()) {
                    $messages = $Validator->messages();
                    throw new Exception($messages);
                }
                break;
        }


        return $next($request);
    }

    function getQuotationHotelRate($HotelItemIndex, $HotelSettings,$QuotationArray)
    {
        $QuotationHotel = new QuotationHotel();
        $Rates = new Rates();


        // $HotelSettings = arrayMapMulti('getActualDataType',$output);

        if (Session::has("quotation.rate.hotel.$HotelItemIndex")) {


            $RateArray = objectToArray(Session::get("quotation.rate.hotel.$HotelItemIndex"));

            if (!$Rates->searchInRateArrayHotelSettings(objectToArray($RateArray), $HotelSettings)) {
                $RateArray = objectToArray($QuotationHotel->getHotelCost(Session::get("quotation"), $HotelSettings));
            }

        } else {
            $RateArray = objectToArray($QuotationHotel->getHotelCost(Session::get("quotation"), $HotelSettings));
        }

        $QuotationHotel = new QuotationHotel();
        $Bookdates = $QuotationHotel->getBookdatesNightCheckIn($HotelSettings['check_in'], $HotelSettings['check_out']);
        $AdultRate = 0;
        
        foreach ($Bookdates as $Day => $Bookdate) {
            // Adult Start
            $RateDayItem = $RateArray['adult'][$Day];
            if(!empty($RateDayItem)) {
                foreach ($RateDayItem as $CurrentRoomType => $RateItem) {
                    if (isset($RateItem['modified_rate']) && $RateItem['is_modified']) {
                        $AdultRate = $RateItem['modified_rate'];
                    } else if (isset($RateItem['rate'])) {
                        $AdultRate = $RateItem['rate'];
                    }

                    if ($AdultRate == 0) {
                        $msg = "No adult rates for hotel: " . Hotel::getHotelFromAll($HotelSettings['hotel'])->name . "<br>";
                        if(isset($RateItem['year'])) {
                            $msg .= Carbon::create($RateItem['year'], $RateItem['month'], $RateItem['day'])->toFormattedDateString();
                        }
                        throw new Exception($msg);
                    }
                }
            } else {
                $msg = "No adult rates for hotel: " . Hotel::getHotelFromAll($HotelSettings['hotel'])->name . "<br>";
                throw new Exception($msg);
            }
            // Adult END

            // Start cwb
            $CWBRate = 0;
            $RateDayItem = $RateArray['child']['cwb'][$Day];
            if((Session::get("quotation.pax.cwb") > 0)) {
                if(!empty($RateDayItem)) {
                    if (isset($RateDayItem['modified_rate']) && $RateDayItem['is_modified']) {
                        $CWBRate = $RateDayItem['modified_rate'];
                    } else if (isset($RateDayItem['rate'])) {
                        $CWBRate = $RateDayItem['rate'];
                    }

                    if ($CWBRate == 0) {
                        $msg = "No cwb rates for hotel: " . Hotel::getHotelFromAll($HotelSettings['hotel'])->name . "<br>";
                        if(isset($RateDayItem['year'])) {
                            $msg .= Carbon::create($RateDayItem['year'], $RateDayItem['month'], $RateDayItem['day'])->toFormattedDateString();
                        }
                        throw new Exception($msg);
                    }
                } else {
                    $msg = "No cwb rates for hotel: " . Hotel::getHotelFromAll($HotelSettings['hotel'])->name . "<br>";
                    throw new Exception($msg);
                }
            }
            // END cwb

            // Start cnb
            $CNBRate = 0;
            $RateDayItem = $RateArray['child']['cnb'][$Day];
            if((Session::get("quotation.pax.cnb") > 0)) {
                if(!empty($RateDayItem)) {
                    if (isset($RateDayItem['modified_rate']) && $RateDayItem['is_modified']) {
                        $CNBRate = $RateDayItem['modified_rate'];
                    } else if (isset($RateDayItem['rate'])) {
                        $CNBRate = $RateDayItem['rate'];
                    }

                    if ($CNBRate == 0) {
                        $msg = "No cnb rates for hotel: " . Hotel::getHotelFromAll($HotelSettings['hotel'])->name . "<br>";
                        if(isset($RateDayItem['year'])) {
                            $msg .= Carbon::create($RateDayItem['year'], $RateDayItem['month'], $RateDayItem['day'])->toFormattedDateString();
                        }
                        throw new Exception($msg);
                    }
                } else {
                    $msg = "No cnb rates for hotel: " . Hotel::getHotelFromAll($HotelSettings['hotel'])->name . "<br>";
                    throw new Exception($msg);
                }
            }
            // END cnb
        }
        return true;
    }
}
