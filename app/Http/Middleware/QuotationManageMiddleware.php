<?php

namespace App\Http\Middleware;

use Closure;

/**
 * Class QuotationManageMiddleware
 * @package App\Http\Middleware
 */
class QuotationManageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        return $next($request);
    }
}
