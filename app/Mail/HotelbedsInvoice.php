<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Barryvdh\DomPDF\PDF;
use Dompdf\Dompdf;
use Dompdf\Options;

/**
 * Class HotelbedsInvoice
 * @package App\Mail
 */
class HotelbedsInvoice extends Mailable  implements ShouldQueue
{
    public $tries = 10;

    use Queueable, SerializesModels;
    var $Subject;
    var $CSS;
    var $InvoiceHtml;


    /**
     * HotelbedsInvoice constructor.
     * @param $InvoiceHtml
     * @param $Subject
     * @param $CSS
     */
    public function __construct($InvoiceHtml, $Subject, $CSS)
    {
        $this->InvoiceHtml = $InvoiceHtml;
        $this->Subject = $Subject;
        $this->CSS = $CSS;



    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $HtmlData = \View::make('quotation.email.main',['QuotationHTML' => $this->InvoiceHtml, "CSS" => $this->CSS])->render();

        $options = new Options();
        $options->set('isRemoteEnabled', TRUE);

        $dompdf = new Dompdf($options);

        $dompdf->loadHtml($HtmlData);
        $dompdf->setPaper('A4');
        $dompdf->render();
        $output = $dompdf->output();

        return $this->view('quotation.email.data',['data' => $HtmlData])
            ->attachData($output, str_replace("#","-",$this->Subject).".pdf")
            ->subject($this->Subject);
    }
}
