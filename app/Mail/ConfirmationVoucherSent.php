<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

/**
 * Class ConfirmationVoucherSent
 * @package App\Mail
 */
class ConfirmationVoucherSent extends Mailable implements ShouldQueue
{
    public $tries = 5;

    use Queueable, SerializesModels;

    var $EmailList;
    var $Subject;
    var $CSS;
    var $QuotationHTML;
    var $quotation_no;
    var $update_number;
    var $QuotationArray;

    /**
     * ConfirmationVoucherSent constructor.
     * @param $EmailList
     * @param $Subject
     * @param $CSS
     * @param $QuotationHTML
     */
    public function __construct($EmailList, $Subject, $CSS, $QuotationHTML)
    {
        $this->EmailList = $EmailList;
        $this->Subject = $Subject;
        $this->CSS = $CSS;
        $this->QuotationHTML = $QuotationHTML;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('quotation.email.main',['QuotationHTML' => $this->QuotationHTML, "CSS" => $this->CSS])
            ->replyTo($this->EmailList['reply_to'])
            ->subject($this->Subject);
    }
}
