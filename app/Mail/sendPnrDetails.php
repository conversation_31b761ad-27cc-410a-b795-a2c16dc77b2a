<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class sendPnrDetails extends Mailable implements ShouldQueue
{
    public $tries = 5;
    use Queueable, SerializesModels;

    var $emails;
    var $user_name;
    var $message;
    var $date_time;
    var $subject;
    var $data;
    var $ref_id;
    var $apple_ref_id;
    var $pnr_all_data;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($emails, $user_name, $message, $subject, $apple_ref_id, $pnr_all_data) {
        $this->emails = $emails;
        $this->user_name = $user_name;
        $this->message = $message;
        $this->subject = $subject;
        $this->apple_ref_id = $apple_ref_id;
        $this->pnr_all_data = $pnr_all_data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build() {
        return $this->view('flight.email.pnr')->subject($this->subject);
    }
}
