<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class fdQuotationNotification extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;
    public $tries = 5;
    var $emails;
    var $subject;
    var $data;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($emails, $subject,$data)
    {
        $this->emails = $emails;
        $this->subject = $subject;
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if (env("APP_DEBUG"))
            $CSS = file_get_contents(public_path('assets/css/apple.css'));
        else
            $CSS = file_get_contents(public_path('assets/css/apple.min.css'));
        return $this->view('quotation.email.main', ['QuotationHTML' => $this->data, "CSS" => $CSS])->subject($this->subject);
    }
}
