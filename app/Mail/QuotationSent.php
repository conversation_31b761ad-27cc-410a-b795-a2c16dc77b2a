<?php

namespace App\Mail;

use App\Notifications\EmailSent;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Barryvdh\DomPDF\PDF;
use Dompdf\Dompdf;
use Dompdf\Options;

/**
 * Class QuotationSent
 * @package App\Mail
 */
class QuotationSent extends Mailable implements ShouldQueue
{
    public $tries = 5;

    use Queueable, SerializesModels;

    var $EmailList;
    var $Subject;
    var $CSS;
    var $QuotationHTML;
    var $quotation_no;
    var $update_number;
    var $QuotationArray;
    var $User;
    var $HtmlData;

    /**
     * QuotationSent constructor.
     * @param $User
     * @param $EmailList
     * @param $Subject
     * @param $CSS
     * @param $QuotationHTML
     * @param $quotation_no
     * @param $update_number
     * @param $QuotationArray
     */
    public function __construct($User, $EmailList, $Subject, $CSS, $QuotationHTML, $quotation_no, $update_number, $QuotationArray, $HtmlData)
    {
        $this->User = $User;
        $this->EmailList = $EmailList;
        $this->Subject = $Subject;
        $this->CSS = $CSS;
        $this->QuotationHTML = $QuotationHTML;
        $this->quotation_no = $quotation_no;
        $this->update_number = $update_number;
        $this->QuotationArray = $QuotationArray;
        $this->HtmlData = $HtmlData;
    }


    /**
     * @return QuotationSent
     */
    public function build()
    {
        $options = new Options();
        $options->set('isRemoteEnabled', TRUE);

        $dompdf = new Dompdf($options);

        $dompdf->loadHtml($this->HtmlData);
        $dompdf->setPaper('A4');
        $dompdf->render();
        $output = $dompdf->output();

        return $this->view('quotation.email.main',['QuotationHTML' => $this->QuotationHTML, "CSS" => $this->CSS])
            ->attachData($output, "$this->quotation_no-$this->update_number-Quotation.pdf")
            ->subject($this->Subject);
    }
}
