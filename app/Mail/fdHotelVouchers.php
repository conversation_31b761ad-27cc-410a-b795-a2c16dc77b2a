<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Barryvdh\DomPDF\PDF;
use Dompdf\Dompdf;
use Dompdf\Options;
use View;

class fdHotelVouchers extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;
    public $tries = 5;
    var $emails;
    var $subject;
    var $data;
    var $quotationData;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($emails, $subject,$data,$quotationData)
    {
        $this->emails = $emails;
        $this->subject = $subject;
        $this->data = $data;
        $this->quotationData = $quotationData;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if (env("APP_DEBUG"))
            $CSS = file_get_contents(public_path('assets/css/apple.css'));
        else
            $CSS = file_get_contents(public_path('assets/css/apple.min.css'));

        $options = new Options();
        $options->set('isRemoteEnabled', TRUE);
        $html = View::make('quotation.email.main', ['QuotationHTML' => $this->data, "CSS" => $CSS])->render();
        $dompdf = new Dompdf($options);

        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4');
        $dompdf->render();
        $output = $dompdf->output();
        $pdfFileName = $this->quotationData['confirmData']['quotationNo']."-".$this->quotationData['confirmData']['statusCode'];

        return $this->view('quotation.email.main', ['QuotationHTML' => $this->data, "CSS" => $CSS])
            ->attachData($output, $pdfFileName.".pdf")
            ->subject($this->subject);
    }
}
