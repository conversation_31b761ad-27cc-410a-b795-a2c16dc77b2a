<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class VerifyEmailForMobileApp extends Mailable
{
    use Queueable, SerializesModels;

    protected $token;
    protected $email;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($email, $token)
    {
        $this->token = $token;
        $this->email = $email;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $data = [
          'email' => $this->email,
          'token' => $this->token
        ];

        return $this->from('<EMAIL>')
                    ->markdown('emails.mobileApp.verify-email')
                    ->with($data);
    }
}
