<?php

namespace App\Mail;

use App\Model\Hotel\Hotel;
use App\Model\QuotationManage\QuotationHotelVoucher;
use App\Notifications\EmailSent;
use Barryvdh\DomPDF\PDF;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

/**
 * Class HotelVoucherSent
 * @package App\Mail
 */
class HotelVoucherSent extends Mailable implements ShouldQueue
{
    public $tries = 10;

    use Queueable, SerializesModels;

    var $EmailList;
    var $ToEmails;
    var $Subject;
    var $CSS;
    var $html;
    var $HotelID;
    var $User;
    var $quotation_no;
    var $update_number;

    /**
     * HotelVoucherSent constructor.
     * @param $User
     * @param $ToEmails
     * @param $HotelID
     * @param $CSS
     * @param $html
     * @param $quotation_no
     * @param $update_number
     * @param $EmailList
     */
    public function __construct($User, $ToEmails, $HotelID, $CSS, $html, $quotation_no, $update_number, $EmailList)
    {

        $this->User = $User;
        $this->Subject = 'Hotel Voucher: ' . Hotel::find($HotelID)->name;
        $this->CSS = $CSS;
        $this->html = $html;
        $this->HotelID = $HotelID;
        $this->quotation_no = $quotation_no;
        $this->update_number = $update_number;
        $this->ToEmails = $ToEmails;
        $this->EmailList = $EmailList;
    }

    /**
     * @return HotelVoucherSent
     */
    public function build()
    {

        /*$HotelVoucher = new QuotationHotelVoucher();
        $HotelVoucher->quotation_no = $this->quotation_no;
        $HotelVoucher->hotel = $this->HotelID;
        $HotelVoucher->details = "Pending";
        $HotelVoucher->status = "P";
        $HotelVoucher->save();*/

        $this->Subject .= "/$this->quotation_no#";//.$HotelVoucher->id;


        $options = new Options();
        $options->set('isRemoteEnabled', TRUE);

        $dompdf = new Dompdf($options);

        $dompdf->loadHtml($this->html);
        $dompdf->setPaper('A4');
        $dompdf->render();
        $output = $dompdf->output();

        return $this->view('quotation.email.data',['data' => $this->html])
            ->attachData($output, "$this->quotation_no-$this->update_number-$this->HotelID.pdf")
            ->cc($this->EmailList['cc'])
            ->bcc($this->EmailList['bcc'])
            ->replyTo($this->EmailList['reply_to'])
            ->subject($this->Subject);
    }
}
