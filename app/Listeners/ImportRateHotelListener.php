<?php

namespace App\Listeners;

use App\Events\ImportRateHotel;
use App\Model\Hotel\Rates;
use App\Model\Hotel\RatesChild;
use Carbon\Carbon;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\DB;

/**
 * Class ImportRateHotelListener
 * @package App\Listeners
 */
class ImportRateHotelListener
{

    public function __construct()
    {
        //
    }

    /**
     * @param ImportRateHotel $Data
     * @return mixed
     * @throws \Throwable
     */
    public function handle(ImportRateHotel $Data)
    {
        $UpdateID = substr( time().\Auth::user()->id,-9);

        $Counts = DB::transaction(function () use ($Data,$UpdateID) {

            $Counts = ['adult'=>0,'cwb'=>0,'cnb'=>0];
            $Counts['upload_id'] = $UpdateID;


            foreach ($Data->Data as $RateItem) {



                $StartDate = Carbon::parse($RateItem->start_date);
                $EndDate = Carbon::parse($RateItem->end_date);


                $Rate = new Rates();

                $Rate->start_year = $StartDate->year;
                $Rate->start_month = $StartDate->month;
                $Rate->start_day = $StartDate->day;
                $Rate->end_year = $EndDate->year;
                $Rate->end_month = $EndDate->month;
                $Rate->end_day = $EndDate->day;


                $Rate->meal = $RateItem->meal;
                $Rate->hotel = $RateItem->hotel;
                $Rate->rate = $RateItem->price;
                $Rate->room_type = $RateItem->room_type;
                $Rate->room_category = $RateItem->room_category;
                $Rate->market = $RateItem->market;

                $Rate->created_by = \Auth::user()->id;
                $Rate->upload_id = $UpdateID;

                $Rate->special = $RateItem->special;

                $Rate->save();
                $Counts['adult']++;


                $RateChild = new RatesChild();//child with bed

                $RateChild->start_year = $StartDate->year;
                $RateChild->start_month = $StartDate->month;
                $RateChild->start_day = $StartDate->day;
                $RateChild->end_year = $EndDate->year;
                $RateChild->end_month = $EndDate->month;
                $RateChild->end_day = $EndDate->day;

                $RateChild->age_from = 2;
                $RateChild->age_to = 12;

                $RateChild->meal = $RateItem->meal;
                $RateChild->hotel = $RateItem->hotel;
                $RateChild->rate = $RateItem->child_with_bed;
                $RateChild->room_type = $RateItem->room_type;
                $RateChild->room_category = $RateItem->room_category;
                $RateChild->market = $RateItem->market;

                $RateChild->created_by = \Auth::user()->id;
                $RateChild->upload_id = $UpdateID;
                $RateChild->special = $RateItem->special;


                $RateChild->save();
                $Counts['cwb']++;


                $RateChild = new RatesChild();

                $RateChild->start_year = $StartDate->year;
                $RateChild->start_month = $StartDate->month;
                $RateChild->start_day = $StartDate->day;
                $RateChild->end_year = $EndDate->year;
                $RateChild->end_month = $EndDate->month;
                $RateChild->end_day = $EndDate->day;

                $RateChild->age_from = 0;
                $RateChild->age_to = 2;

                $RateChild->meal = $RateItem->meal;
                $RateChild->hotel = $RateItem->hotel;
                $RateChild->rate = $RateItem->child_with_no_bed;
                $RateChild->room_type = $RateItem->room_type;
                $RateChild->room_category = $RateItem->room_category;
                $RateChild->market = $RateItem->market;

                $RateChild->created_by = \Auth::user()->id;
                $RateChild->upload_id = $UpdateID;

                $RateChild->special = $RateItem->special;



                $RateChild->save();
                $Counts['cnb']++;


            }

            return $Counts;


        });

        return $Counts;
    }
}
