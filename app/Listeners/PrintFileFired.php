<?php

namespace App\Listeners;

use App\Events\PrintFile;
use Dompdf\Dompdf;
use Dompdf\Options;

/**
 * Class PrintFileFired
 * @package App\Listeners
 */
class PrintFileFired
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  PrintFile $event
     * @return void
     */
    public function handle(PrintFile $event)
    {


        $QuotationHTML = View::make('quotation.qoute', ['QuotationArray' => $QuotationArray, 'Email' => true])->render();
        $CSS = file_get_contents(public_path('assets/css/apple.css'));

        $dompdf = new Dompdf();


        $html = View::make('quotation.email.main', ['QuotationHTML' => $QuotationHTML, "CSS" => $CSS])->render();
        $dompdf->loadHtml($html);

        $options = new Options();
        $options->setIsRemoteEnabled(true);

        $dompdf->setOptions($options);

        $dompdf->render();
        $dompdf->setPaper('A4', 'landscape');
        $dompdf->stream();
        sd();
    }
}
