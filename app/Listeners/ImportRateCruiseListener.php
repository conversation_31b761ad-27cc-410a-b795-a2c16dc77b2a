<?php

namespace App\Listeners;

use App\Events\ImportRateCruise;
use App\Model\Cruise\CruiseCabinRate;
use App\Model\Cruise\CruiseCabinRateChild;
use Carbon\Carbon;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\DB;

/**
 * Class ImportRateCruiseListener
 * @package App\Listeners
 */
class ImportRateCruiseListener
{

    public function __construct()
    {
        //
    }

    /**
     * @param ImportRateCruise $Data
     * @return mixed
     * @throws \Throwable
     */
    public function handle(ImportRateCruise $Data)
    {
        $UpdateID = substr( time().\Auth::user()->id,-9);

        $Counts = DB::transaction(function () use ($Data,$UpdateID) {

            $Counts = ['adult'=>0,'cwb'=>0,'cnb'=>0];
            $Counts['upload_id'] = $UpdateID;


            foreach ($Data->Data as $RateItem) {



                $StartDate = Carbon::parse($RateItem->start_date);
                $EndDate = Carbon::parse($RateItem->end_date);


                $Rate = new CruiseCabinRate();

                $Rate->start_year = $StartDate->year;
                $Rate->start_month = $StartDate->month;
                $Rate->start_day = $StartDate->day;
                $Rate->end_year = $EndDate->year;
                $Rate->end_month = $EndDate->month;
                $Rate->end_day = $EndDate->day;


                $Rate->meal = $RateItem->meal;
                $Rate->cruise = $RateItem->cruise;
                $Rate->rate = $RateItem->price;
                $Rate->cabin_type = $RateItem->cabin_type;
                $Rate->cabin_occupancy_type = $RateItem->cabin_occupancy_type;
                $Rate->market = $RateItem->market;
                $Rate->package = $RateItem->package;

                $Rate->created_by = \Auth::user()->id;
                $Rate->upload_id = $UpdateID;

                $Rate->save();
                $Counts['adult']++;


                $RateChild = new CruiseCabinRateChild();//child with bed

                $RateChild->start_year = $StartDate->year;
                $RateChild->start_month = $StartDate->month;
                $RateChild->start_day = $StartDate->day;
                $RateChild->end_year = $EndDate->year;
                $RateChild->end_month = $EndDate->month;
                $RateChild->end_day = $EndDate->day;

                $RateChild->age_from = 6;
                $RateChild->age_to = 12;

                $RateChild->adult_reference = $Rate->id;
                $RateChild->meal = $RateItem->meal;
                $RateChild->cruise = $RateItem->cruise;
                $RateChild->rate = $RateItem->child_with_bed;
                $RateChild->cabin_type = $RateItem->cabin_type;
                $RateChild->cabin_occupancy_type = $RateItem->cabin_occupancy_type;
                $RateChild->market = $RateItem->market;
                $RateChild->package = $RateItem->package;
                $RateChild->child_type = "cwb";

                $RateChild->created_by = \Auth::user()->id;
                $RateChild->upload_id = $UpdateID;


                $RateChild->save();
                $Counts['cwb']++;


                $RateChild = new CruiseCabinRateChild();

                $RateChild->start_year = $StartDate->year;
                $RateChild->start_month = $StartDate->month;
                $RateChild->start_day = $StartDate->day;
                $RateChild->end_year = $EndDate->year;
                $RateChild->end_month = $EndDate->month;
                $RateChild->end_day = $EndDate->day;

                $RateChild->age_from = 0;
                $RateChild->age_to = 5;

                $RateChild->adult_reference = $Rate->id;
                $RateChild->meal = $RateItem->meal;
                $RateChild->cruise = $RateItem->cruise;
                $RateChild->rate = $RateItem->child_with_no_bed;
                $RateChild->cabin_type = $RateItem->cabin_type;
                $RateChild->cabin_occupancy_type = $RateItem->cabin_occupancy_type;
                $RateChild->market = $RateItem->market;
                $RateChild->package = $RateItem->package;
                $RateChild->child_type = "cnb";

                $RateChild->created_by = \Auth::user()->id;
                $RateChild->upload_id = $UpdateID;



                $RateChild->save();
                $Counts['cnb']++;


            }

            return $Counts;


        });

        return $Counts;
    }
}
