<?php

namespace App\Listeners;

use App\Events\Notice;

/**
 * Class NoticeSent
 * @package App\Listeners
 */
class NoticeSent
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  Notice  $event
     * @return void
     */
    public function handle(Notice $event)
    {
        //
    }
}
