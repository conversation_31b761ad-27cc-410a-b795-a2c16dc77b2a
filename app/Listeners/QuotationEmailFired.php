<?php

namespace App\Listeners;

use App\Events\QuotationEmail;
use App\Http\Controllers\fd_packages\fdPackagesController;
use App\Mail\ConfirmationVoucherSent;
use App\Mail\HotelVoucherSent;
use App\Mail\QuotationSent;
use App\Model\FdPackages\apple_fd_packages_quotation;
use App\Model\Hotel\Hotel;
use App\Model\Quotation\Quotation as QuotationModel;
use App\Model\QuotationManage\Quotation;
use App\Model\QuotationManage\EmailSent;
use App\Model\Place\Place;
use App\User;
use App\UserHierarchy;
use Dompdf\Dompdf;
use Dompdf\Options;
use Mail;
use View;

/**
 * Class QuotationEmailFired
 * @package App\Listeners
 */
class QuotationEmailFired
{


    protected $From = ['email' => '<EMAIL>', 'name' => 'Apple Holidays'];

    public function __construct()
    {
        //
    }


    /**
     * @param QuotationEmail $Data
     * @return bool|null
     * @throws \ReflectionException
     */
    public function handle(QuotationEmail $Data): ?bool
    {
        ini_set('max_execution_time', 0);
        $Data = $Data->Data;
        $EmailList['to'] = [];
        $EmailList['cc'] = [];
        $EmailList['bcc'] = [];

        if (isset($Data['quotation_no'])) {
            if(isset($Data["package_type"]) and $Data["package_type"] == "fd") {
                $this->FDMailFunction($Data);
                return true;
            }

            if (isset($Data['reference_id']))
                $Quot = Quotation::withTrashed()->where('ID', $Data['reference_id']);
            else
                $Quot = Quotation::withTrashed()->where('quotation_no', $Data['quotation_no'])->orderBy('ID', 'desc');

            $quotation_no = $Data['quotation_no'];
            $LastQuotationID = $Quot->first();
            $OldQuotationID = $Quot->skip(1)->take(1)->first();//get before last one


            $update_number = Quotation::withTrashed()->where('quotation_no', $quotation_no)->where('ID', '<=', $LastQuotationID->ID)->count();

            $QuotationArray = Quotation::getQuotation($LastQuotationID->ID, $quotation_no);//get the quotation


            $QuotationModel = new QuotationModel();

            
            $EmailList['reply_to'] =  User::find($QuotationArray['user'])->email;

            if (isset($Data['to_emails'])) {
                $EmailList['to'] = explode(",", $Data['to_emails']);
            }

            if (isset($Data['cc_emails'])) {
                $EmailList['cc'] = explode(",", $Data['cc_emails']);
            }

            if (isset($Data['bcc_emails'])) {
                $EmailList['bcc'] = explode(",", $Data['bcc_emails']);
            }

            $RoleName = \Auth::user()->roles->toArray()[0]["name"];
            $ID = \Auth::user()->id;

            if($RoleName == "agent" &&
                $RoleName == "agent_supervisor" &&
                $RoleName == "agent_team-lead_with_confirmation" &&
                $RoleName == "agent_staff_with_confirmation" &&
                $RoleName == "agent_staff_without_confirmation") {
                $upperUser = UserHierarchy::where("under", $ID)->get()->toArray();
                foreach ($upperUser as $usr) {
                    if(isset($usr["user"])) {
                        $Email = User::find($usr["user"])->email;
                        $EmailList['cc'][] = $Email;
                    }
                }
            }

            if ($Data['type'] === 'quote') {
                if (env("APP_DEBUG")) {
                    $CSS = file_get_contents(public_path('assets/css/apple.css'));
                } else {
                    $CSS = file_get_contents(public_path('assets/css/apple.min.css'));
                }

                $QuotationHTML = View::make('quotation.qoute', ['QuotationArray' => $QuotationArray, 'Email' => true])->render();
                $HtmlData = \View::make('quotation.email.main', ['QuotationHTML' => $QuotationHTML, 'CSS' => $CSS])->render();

                $Subject = "Quotation reference:#$quotation_no/R$update_number";


                Mail::to($EmailList['to'])
                    ->cc($EmailList['cc'])
                    ->bcc($EmailList['bcc'])
                    ->send(new QuotationSent(User::find($QuotationArray['user']), $EmailList['to'], $Subject, $CSS, $QuotationHTML, $quotation_no, $update_number, $QuotationArray, $HtmlData));


                if($QuotationArray['base_currency'] != $QuotationArray['ch_currency']) {
                    if (env("APP_DEBUG")) {
                        $CSS = file_get_contents(public_path('assets/css/apple.css'));
                    } else {
                        $CSS = file_get_contents(public_path('assets/css/apple.min.css'));
                    }

                    $QuotationArray['ch_currency'] = $QuotationArray['base_currency'];
                    $QuotationHTML = View::make('quotation.qoute', ['QuotationArray' => $QuotationArray, 'Email' => true])->render();
                    $HtmlData = \View::make('quotation.email.main', ['QuotationHTML' => $QuotationHTML, 'CSS' => $CSS])->render();

                    $Subject = "Quotation reference:#$quotation_no/R$update_number";


                    Mail::to($EmailList['to'])
                        ->cc($EmailList['cc'])
                        ->bcc($EmailList['bcc'])
                        ->send(new QuotationSent(User::find($QuotationArray['user']), $EmailList['to'], $Subject, $CSS, $QuotationHTML, $quotation_no, $update_number, $QuotationArray, $HtmlData));

                }

            } elseif ($Data['type'] === 'tour_confirmation') {

                $QuotationHTML = View::make('quotation.voucher.tour-confirmation-voucher', ['QuotationArray' => $QuotationArray, 'Email' => true])->render();

                if (env("APP_DEBUG"))
                    $CSS = file_get_contents(public_path('assets/css/apple.css'));
                else
                    $CSS = file_get_contents(public_path('assets/css/apple.min.css'));


                if ($QuotationArray['status'] == 2)
                    $Subject = "TOUR CONFIRMATION VOUCHER:#$quotation_no";
                else
                    $Subject = "TOUR CANCELLATION:#$quotation_no";

                /*if(isset($LastQuotationID->country) && $LastQuotationID->country == 62) {
                    $EmailList['reply_to'] =  "<EMAIL>";
                }*/

                Mail::to($EmailList['to'])
                    ->cc($EmailList['cc'])
                    ->bcc($EmailList['bcc'])
                    ->send(new ConfirmationVoucherSent($EmailList, $Subject, $CSS, $QuotationHTML));

            } elseif ($Data['type'] === 'tour_confirmation_voucher') {

                $QuotationHTML = View::make('quotation.voucher.tour-confirmation-voucher', ['QuotationArray' => $QuotationArray, 'Email' => true, 'Client' => true])->render();

                if (env("APP_DEBUG"))
                    $CSS = file_get_contents(public_path('assets/css/apple.css'));
                else
                    $CSS = file_get_contents(public_path('assets/css/apple.min.css'));


                if ($QuotationArray['status'] == 2)
                    $Subject = "TOUR CONFIRMATION VOUCHER:#$quotation_no";
                else
                    $Subject = "TOUR CANCELLATION:#$quotation_no";

                array_push($EmailList['cc'],"<EMAIL> ","<EMAIL>");

                Mail::to($EmailList['to'])
                    ->cc($EmailList['cc'])
                    ->bcc($EmailList['bcc'])
                    ->send(new ConfirmationVoucherSent($EmailList, $Subject, $CSS, $QuotationHTML));

            } elseif ($Data['type'] === 'hotel_vouchers') {
                if (env('APP_DEBUG')) {
                    $CSS = file_get_contents(public_path('assets/css/apple.css'));
                } else {
                    $CSS = file_get_contents(public_path('assets/css/apple.min.css'));
                }

                if (empty($Data['voucher_type'])) {
                    $VoucherType = 'cancel';
                } else {
                    $VoucherType = $Data['voucher_type'];
                }

                $HotelID = false;
                if (isset($Data['send_individual'])) {

                    if (is_array($Data['hotel_id'])) {
                        if (empty($Data['hotel_id'][0])) {
                            $HotelID = false;
                        } else {
                            if (empty($Data['hotel_id'][0])) {
                                unset($Data['hotel_id'][0]);
                            }
                            $HotelID = array_values(arrayMapMulti('getActualDataType', $Data['hotel_id']));
                        }
                    } else {
                        $HotelID = $Data['hotel_id'];
                    }
                }

                $country = $LastQuotationID->country;
                $hotelCCEmails = Place::findOrFail($country)->hotel_vouchers_email ?? "<EMAIL>";
                $hotelCCEmails = explode(",", $hotelCCEmails);
                foreach($hotelCCEmails as $email)
                    $EmailList['cc'][] = trim($email);
                
                $EmailList['cc'][] = "<EMAIL>";
                $EmailList['cc'][] = "<EMAIL>";
                $EmailList['cc'][] = "<EMAIL>";

                if(isset($LastQuotationID->country) && $LastQuotationID->country == 62) {
                    $EmailList['reply_to'] =  "<EMAIL>";
                }

                foreach ($QuotationModel->getHotelVouchers($quotation_no, $LastQuotationID, $OldQuotationID, true, $VoucherType, $HotelID, 'hotel_vouchers') as $Index => $VoucherArray) {
                    // $EmailList = [];

                    foreach ($VoucherArray as $HotelID => $VoucherHtml) {

                        $HtmlData = \View::make('quotation.email.main', ['QuotationHTML' => $VoucherHtml, 'CSS' => $CSS])->render();

                        $HotelItem = Hotel::where('ID', $HotelID)->first();



                        if ((int) $HotelItem->provider === 1) {//if it's a local hotel

                            if (Hotel::find($HotelID) && !array_key_exists('no_hotel_voucher', $Data)) {
                                $EmailListsHotel = Hotel::find($HotelID)->Contact()->where('type', 2)->pluck('contact_id')->toArray();
                                $ToEmails = array_combine2($EmailList['to'], $EmailListsHotel);
                            } else {
                                $ToEmails = $EmailList['to'];
                            }

                            //current log user
                            if (empty($EmailList['cc'])) {
                                $EmailList['cc'][] = User::find($QuotationArray['user'])->email;
                            }

                            if (!empty($ToEmails)) {//if emails available

                                Mail::to($ToEmails)
                                    ->send(new HotelVoucherSent(User::find($QuotationArray['user']), $ToEmails, $HotelID, $CSS, $HtmlData, $quotation_no, $update_number, $EmailList));
                            }


                        }

                    }
                }
            } elseif ($Data['type'] === 'pnl') {

                if (env("APP_DEBUG"))
                    $CSS = file_get_contents(public_path('assets/css/apple.css'));
                else
                    $CSS = file_get_contents(public_path('assets/css/apple.css'));

                $Subject = "PNL:#$quotation_no";


                $PNLHTML = View::make("quotation.lost-profit", ['QuotationArray' => $QuotationArray, 'Email' => true, 'requested_currency' => 'USD'])->render();


                $EmailList['to'][0] = User::find($QuotationArray['user'])->email;

                Mail::to(User::find($QuotationArray['user'])->email)
                    ->send(new ConfirmationVoucherSent($EmailList, $Subject, $CSS, $PNLHTML));
            } 

        } else {
            if ($Data['type'] === 'restaurant_voucher') {
                $EmailList['reply_to'] = "<EMAIL>";
                if (env("APP_DEBUG"))
                    $CSS = file_get_contents(public_path('assets/css/apple.css'));
                else
                    $CSS = file_get_contents(public_path('assets/css/apple.css'));

                $Subject = "Restaurant Voucher";

                Mail::to($Data['to_emails'])
                    ->send(new ConfirmationVoucherSent($EmailList, $Subject, $CSS, $Data['html']));
            }
        }

        return empty(Mail::failures()) ? true : false;

    }

    public function FDMailFunction($Data) {
        $fdPackagesController = new fdPackagesController();

        if(isset($Data['reference_no'])) {
            $Quot = apple_fd_packages_quotation::select("*")->where('id', $Data['reference_no']);
        } else {
            $Quot = apple_fd_packages_quotation::select("*")->where('quotation_no', $Data['quotation_no'])->orderBy('id', 'desc');
        }


        $quotation_no = $Data['quotation_no'];
        $LastQuotationID = $Quot->first();
        $OldQuotationID = $Quot->skip(1)->take(1)->first();//get before last one

        $update_number = apple_fd_packages_quotation::select("*")->where('quotation_no', $quotation_no)->where('ID', '<=', $LastQuotationID->id)->count();

        $QuotationArray = $fdPackagesController->getQuotation($LastQuotationID->id, $quotation_no);//get the quotation

        // $QuotationModel = new QuotationModel();

        $EmailList['to'] = [];
        $EmailList['cc'] = [];
        $EmailList['bcc'] = [];
        $EmailList['reply_to'] =  User::find($QuotationArray['confirmData']['userID'])->email;


        if (isset($Data['to_emails'])) {
            $EmailList['to'] = explode(",", $Data['to_emails']);
        }

        if (isset($Data['cc_emails'])) {
            $EmailList['cc'] = explode(",", $Data['cc_emails']);
        }

        if (isset($Data['bcc_emails'])) {
            $EmailList['bcc'] = explode(",", $Data['bcc_emails']);
        }


        if ($Data['type'] === 'quote') {

            $QuotationHTML = View::make('quotation.qoute', ['QuotationArray' => $QuotationArray, 'Email' => true])->render();

            if (env("APP_DEBUG")) {
                $CSS = file_get_contents(public_path('assets/css/apple.css'));
            } else {
                $CSS = file_get_contents(public_path('assets/css/apple.min.css'));
            }
            $HtmlData = \View::make('quotation.email.main', ['QuotationHTML' => $QuotationHTML, 'CSS' => $CSS])->render();
            $Subject = "Quotation reference:#$quotation_no/R$update_number";


            Mail::to($EmailList['to'])
                ->cc($EmailList['cc'])
                ->bcc($EmailList['bcc'])
                ->send(new QuotationSent(User::find($QuotationArray['user']), $EmailList['to'], $Subject, $CSS, $QuotationHTML, $quotation_no, $update_number, $QuotationArray, $HtmlData));

        } elseif ($Data['type'] === 'tour_confirmation') {

            $QuotationHTML = View::make('quotation.voucher.tour-confirmation-voucher', ['QuotationArray' => $QuotationArray, 'Email' => true, 'Client' => true])->render();

            if (env("APP_DEBUG"))
                $CSS = file_get_contents(public_path('assets/css/apple.css'));
            else
                $CSS = file_get_contents(public_path('assets/css/apple.min.css'));


            if ($QuotationArray['status'] == 2)
                $Subject = "TOUR CONFIRMATION VOUCHER:#$quotation_no";
            else
                $Subject = "TOUR CANCELLATION:#$quotation_no";


            Mail::to($EmailList['to'])
                ->cc($EmailList['cc'])
                ->bcc($EmailList['bcc'])
                ->send(new ConfirmationVoucherSent($EmailList, $Subject, $CSS, $QuotationHTML));

        } elseif ($Data['type'] === 'tour_confirmation_voucher') {

            $QuotationHTML = View::make('quotation.voucher.tour-confirmation-voucher', ['QuotationArray' => $QuotationArray, 'Email' => true, 'Client' => true])->render();

            if (env("APP_DEBUG"))
                $CSS = file_get_contents(public_path('assets/css/apple.css'));
            else
                $CSS = file_get_contents(public_path('assets/css/apple.min.css'));


            if ($QuotationArray['status'] == 2)
                $Subject = "TOUR CONFIRMATION VOUCHER:#$quotation_no";
            else
                $Subject = "TOUR CANCELLATION:#$quotation_no";


            Mail::to($EmailList['to'])
                ->cc($EmailList['cc'])
                ->bcc($EmailList['bcc'])
                ->send(new ConfirmationVoucherSent($EmailList, $Subject, $CSS, $QuotationHTML));

        } elseif ($Data['type'] === 'hotel_vouchers') {
            // dd($Data);
            if (env('APP_DEBUG')) {
                $CSS = file_get_contents(public_path('assets/css/apple.css'));
            } else {
                $CSS = file_get_contents(public_path('assets/css/apple.min.css'));
            }

            if (empty($Data['voucher_type'])) {
                $VoucherType = 'cancel';
            } else {
                $VoucherType = $Data['voucher_type'];
            }

            $HotelID = false;
            if (isset($Data['send_individual'])) {

                if (is_array($Data['hotel_id'])) {
                    if (empty($Data['hotel_id'][0])) {
                        $HotelID = false;
                    } else {
                        if (empty($Data['hotel_id'][0])) {
                            unset($Data['hotel_id'][0]);
                        }
                        $HotelID = array_values(arrayMapMulti('getActualDataType', $Data['hotel_id']));
                    }
                } else {
                    $HotelID = $Data['hotel_id'];
                }
            }

            $EmailList['bcc'][] = "<EMAIL>";
            $EmailList['bcc'][] = "<EMAIL>";
            $EmailList['bcc'][] = "<EMAIL>";

            // dd($fdPackagesController->getHotelVouchers($QuotationArray, $HotelID));
            foreach ($fdPackagesController->getHotelVouchers($QuotationArray, $HotelID) as $Index => $VoucherArray) {
                // $EmailList = [];
                foreach ($VoucherArray as $HotelID => $VoucherHtml) {

                    $HtmlData = \View::make('quotation.email.main', ['QuotationHTML' => $VoucherHtml, 'CSS' => $CSS])->render();

                    $HotelItem = Hotel::where('ID', $HotelID)->first();

                    if ((int) $HotelItem->provider === 1) {//if it's a local hotel

                        if (Hotel::find($HotelID) && !array_key_exists('no_hotel_voucher', $Data)) {
                            $EmailListsHotel = Hotel::find($HotelID)->Contact()->where('type', 2)->pluck('contact_id')->toArray();
                            $ToEmails = array_combine2($EmailList['to'], $EmailListsHotel);
                        } else {
                            $ToEmails = $EmailList['to'];
                        }

                        //current log user
                        if (empty($EmailList['cc'])) {
                            $EmailList['cc'][] = User::find($QuotationArray['confirmData']['userID'])->email;
                        }

                        if (!empty($ToEmails)) {//if emails available
                            Mail::to($ToEmails)
                                ->send(new HotelVoucherSent(User::find($QuotationArray['confirmData']['userID']), $ToEmails, $HotelID, $CSS, $HtmlData, $quotation_no, $update_number, $EmailList));

                        }
                    }

                }
            }
        }

        return empty(Mail::failures()) ? true : false;
    }
}
