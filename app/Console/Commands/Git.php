<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

/**
 * Class Git
 * @package App\Console\Commands
 */
class Git extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'git:upload';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $currentBranch = shell_exec('git rev-parse --abbrev-ref HEAD');

        $Message = $this->ask('Commit Message?', "Uploads to site!");

        $this->info($currentBranch);

        $this->info(shell_exec("git add -A"));
        $this->info(shell_exec('git commit -m "' . $Message . '"'));
        $this->info(shell_exec('git push'));


    }
}
