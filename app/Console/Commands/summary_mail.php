<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\dashboard\dashboardEmailController;

class summary_mail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'custom:summary_mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Daily Summary Mail';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $dashboardEmailController = new dashboardEmailController();
        $sendMail = $dashboardEmailController->getReport();
        return $sendMail;

    }
}
