<?php
namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Model\QuotationManage\Quotation;
use Illuminate\Support\Facades\DB;
use Mail;

class SendFirstQuoteEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'first-quote-email:send';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send first quotation email.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $data =[];
        $Quotations = Quotation::withTrashed()
                ->where("follow_up_status", "=", 0)
                ->where("created_at", ">", Carbon::now()->subDays(2))->get();

        foreach ($Quotations as $key => $Quotation) {

            if($Quotation["ID"] == $Quotation["quotation_no"]) {
                $quotationNO = $Quotation->toArray()["quotation_no"];
                $agentEmail = $Quotation->Status->toArray()["agent_email"];
                $agentEmailSub = $Quotation->Status->toArray()["follow_up_email_subject"];
                if(!isset($agentEmailSub) || empty($agentEmailSub)) {
                    $agentEmailSub = 'Following your query (Ref:' . $quotationNO . ")";
                }
                $fileHandlerEmail = $Quotation->Main->users->toArray()["email"];
                $fileHandlerName = $Quotation->Main->users->toArray()["name"];
                $data['fileHandlerName'] = $fileHandlerName;

                if(isset($agentEmail) && !empty($agentEmail)) {
                    Mail::send('email.agent-send-email', ['data'=> $data], function ($message) use($quotationNO, $agentEmail, $fileHandlerEmail, $fileHandlerName, $agentEmailSub) {
                        $message->subject($agentEmailSub);
                        $message->from("<EMAIL>", "Apple Holidays PVT Ltd");
                        $message->replyTo($fileHandlerEmail, $fileHandlerName);
                        $message->bcc($fileHandlerEmail, "<EMAIL>", "<EMAIL>");
                        $message->to($agentEmail);
                    });

                    $editQuote = Quotation::withTrashed()->find($Quotation->ID);
                    $editQuote->follow_up_status = 1;
                    $editQuote->save();

                }
            }
        }
    }
}

?>