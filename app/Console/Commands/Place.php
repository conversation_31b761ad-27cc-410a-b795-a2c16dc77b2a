<?php

namespace App\Console\Commands;

use App\Model\QuotationManage\QuotationPlace;
use Illuminate\Console\Command;

/**
 * Class Place
 * @package App\Console\Commands
 */
class Place extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'place:setpop';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {


        \App\Model\Place\Place::query()->update(['popularity' => 1]);//set all to zero first
        $PlaceList = QuotationPlace::select(\DB::raw("count(*) as count,place"))->GroupBy('place')->orderBy('count', 'desc')->get();

        foreach ($PlaceList as $PlaceItem) {
            \App\Model\Place\Place::where("ID", $PlaceItem->place)->update(["popularity" => $PlaceItem->count]);
            $this->info($PlaceItem);

        }


    }
}
