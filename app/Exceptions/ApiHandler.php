<?php

namespace App\Exceptions;

use Carbon\Carbon;
use Exception;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Class ApiHandler
 * @package App\Exceptions
 */
class ApiHandler extends Exception
{
    private $request;
    private $exception;

    /**
     * ApiHandler constructor.
     * @param $request
     * @param $exception
     */
    public function __construct($request, $exception)
    {
        $this->request = $request;
        $this->exception = $exception;
        parent::__construct($exception->getMessage());
    }

    /**
     * @return string
     */
    function getError(){

        $error['message'] = $this->getMessage();

        if ($this->exception instanceof NotFoundHttpException) {
            $error['message'] = "Invalid api endpoint!";
        }elseif(!strlen($error['message'])){
            $error['message'] = "Server Error!";
        }

        if(env('APP_DEBUG'))
            $error['debug'] = $this->exception->getTrace();

        return $error;
    }


    /**
     * @return array|string
     */
    public function render()
    {
        return app()->make('ApiResponse', [null, $this->getError()]);
    }


}
