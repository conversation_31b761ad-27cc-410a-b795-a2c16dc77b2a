<?php

namespace App\Model\Pay;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Pay\Bank
 *
 * @property int $id
 * @property string $name
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Pay\Bank whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Pay\Bank whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Pay\Bank whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Pay\Bank whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Pay\Bank whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Pay\Bank whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Pay\Bank whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Pay\Bank whereUploadId($value)
 * @mixin \Eloquent
 */
class Bank extends Model
{
    protected $table = "apple_banks";
}
