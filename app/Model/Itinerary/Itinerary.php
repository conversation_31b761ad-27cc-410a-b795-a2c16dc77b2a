<?php

namespace App\Model\Itinerary;

use App\Model\Quotation\Quotation;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

/**
 * App\Model\Itinerary\Itinerary
 *
 * @mixin \Eloquent
 */
class Itinerary extends Model
{
    /**
     * @param $QuotationArray
     * @return array
     */
    function getItinerary($QuotationArray)
    {


        $ItineraryDaysSaved = false;
        $ItinerarysSavedAttractionText = false;

        $ItineraryDaysSaved = empty($QuotationArray['itinerary']['days']) ? false : $QuotationArray['itinerary']['days'];
        $ItinerarysSavedAttractionText = empty($QuotationArray['itinerary']['attraction_text']) ? false : $QuotationArray['itinerary']['attraction_text'];
        $ItinerarysSavedExcursionText = empty($QuotationArray['itinerary']['excursion_text']) ? false : $QuotationArray['itinerary']['excursion_text'];
        $ItinerarysSavedCityTourText = empty($QuotationArray['itinerary']['city_tour_text']) ? false : $QuotationArray['itinerary']['city_tour_text'];

        $Quotation = new Quotation();
        $DaysDetail = $Quotation->getTourDaysDetail($QuotationArray);
        // dd($DaysDetail);
        $ItineraryDays = [];

        foreach ($DaysDetail as $Day => $DayDetails) {
            $ItineraryDays[$Day] = [];
            if (isset($QuotationArray['attraction'][$Day]))
                $ItineraryDays[$Day]['attraction'] = array_fill_keys($QuotationArray['attraction'][$Day],  false);
            // dd($ItineraryDays[$Day]['attraction']);
            if (isset($QuotationArray['city_tour'][$Day]))
                $ItineraryDays[$Day]['city_tour'] = array_fill_keys($QuotationArray['city_tour'][$Day], false);
            if (isset($QuotationArray['excursion'][$Day]))
                $ItineraryDays[$Day]['excursion'] = array_fill_keys($QuotationArray['excursion'][$Day], false);


            if (isset($QuotationArray['other_rate'])) {

                foreach ($QuotationArray['other_rate'] as $OtherRateItem) {

                    if (isset($OtherRateItem['details']['attraction_day']) && $OtherRateItem['details']['attraction_day'] == $Day &&  $OtherRateItem['rate_type'] == 1) {
                        $sightseeingItem['name'] = $OtherRateItem['text'];
                        $sightseeingItem['description'] = $OtherRateItem['details']['attraction_description'];
                        $ItineraryDays[$Day]['sightseeing'][] = $sightseeingItem;
                    }
                }

            }
        }

        if(isset($ItineraryDaysSaved) && !empty($ItineraryDaysSaved)) {
            foreach ($ItineraryDaysSaved as $dayKey => $dayValue) {
                if (isset($dayValue) && !empty($dayValue)) {
                    foreach ($dayValue as $typeKey => $typeValue) {
                        if (isset($typeValue) && !empty($typeValue) && is_array($typeValue)) {
                            foreach ($typeValue as $idKey => $idValue) {
                                if (isset($idKey) && !empty($idKey)) {
                                    if(!isset($ItineraryDays[$dayKey][$typeKey][$idKey])) {
                                        unset($ItineraryDaysSaved[$dayKey][$typeKey][$idKey]);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return [
            "default" => $ItineraryDays,
            "save" => $ItineraryDaysSaved,
            'attraction_text' => $ItinerarysSavedAttractionText,
            'excursion_text' => $ItinerarysSavedExcursionText,
            'city_tour_text' => $ItinerarysSavedCityTourText
        ];

    }

    /**
     * @param $QuotationArray
     * @return array
     */
    function getFormattedItinerary($QuotationArray, $type= 'all')
    {


        $Quotation = new Quotation();
        $DaysDetail = $Quotation->getTourDaysDetail($QuotationArray);
        $TimeDetail = $Quotation->getTimeDetails($QuotationArray);
        // dd($TimeDetail);
        $ItineraryDays = [];

        foreach ($DaysDetail as $Day => $DayDetails) {
            $Date = Carbon::create($DaysDetail[$Day]['date']['year'], $DaysDetail[$Day]['date']['month'], $DaysDetail[$Day]['date']['day'], 14, 15, 16);
            $Date = $Date->format('Y-m-d');

            $hotel = self::getHotelFromPlace($DaysDetail[$Day]['place'], $QuotationArray['hotel'] ?? $QuotationArray['cruise']);
            $ItineraryDays[$Day] = [];

            if(!empty($TimeDetail)) {
                foreach($TimeDetail as $index => $timesD) {
                    $time = (isset($QuotationArray['transport_time']) && !empty($QuotationArray['transport_time']) && isset($QuotationArray['transport_time']['transport'][$index])) ? $QuotationArray['transport_time']['transport'][$index]: 0;
                    if($timesD['from'] == 0 && array_key_first($TimeDetail) == $index && array_key_first($DaysDetail) == $Day) {
                        $ItineraryDays[$Day][] = ['type' => "transport",
                                            'id' => 'arrival',
                                            'hotel' => $hotel,
                                            'date_time' => $Date . " " . $time,
                                            'time' => $time ];
                    }
                }
            }

            if (isset($QuotationArray['attraction'][$Day])) {
                foreach($QuotationArray['attraction'][$Day]??[] as $attr) {
                    $time = (isset($QuotationArray['time']) && !empty($QuotationArray['time']) && isset($QuotationArray['time']['attraction']['attraction'][$attr]['start'])) ? $QuotationArray['time']['attraction']['attraction'][$attr]['start'] : null;
                    $ItineraryDays[$Day][] = ['type' => "attraction",
                                            'hotel' => $hotel,
                                            'id' => $attr,
                                            'date_time' => $Date . " " . $time,
                                            'time' => $time];
                }
            }
            
            if (isset($QuotationArray['city_tour'][$Day])) {
                foreach($QuotationArray['city_tour'][$Day]??[] as $attr) {
                    $time = (isset($QuotationArray['time']) && !empty($QuotationArray['time']) && isset($QuotationArray['time']['attraction']['city_tour'][$attr]['start'])) ? $QuotationArray['time']['attraction']['city_tour'][$attr]['start'] : null;
                    $ItineraryDays[$Day][] = ['type' => "city_tour",
                                            'hotel' => $hotel,
                                            'id' => $attr,
                                            'date_time' => $Date . " " . $time,
                                            'time' => $time];
                }
            }
            if (isset($QuotationArray['excursion'][$Day])) {
                foreach($QuotationArray['excursion'][$Day]??[] as $attr) {
                    $time = (isset($QuotationArray['time']) && !empty($QuotationArray['time']) && isset($QuotationArray['time']['attraction']['excursion'][$attr]['start'])) ? $QuotationArray['time']['attraction']['excursion'][$attr]['start'] : null;
                    $ItineraryDays[$Day][] = ['type' => "excursion",
                                            'hotel' => $hotel,
                                            'id' => $attr,
                                            'date_time' => $Date . " " . $time,
                                            'time' => $time];
                }
            }
            
            if(!empty($TimeDetail)) {
                foreach($TimeDetail as $index => $timesD) {
                    $time = (isset($QuotationArray['transport_time']) && !empty($QuotationArray['transport_time']) && isset($QuotationArray['transport_time']['transport'][$index])) ? $QuotationArray['transport_time']['transport'][$index]: 0;
                    if($timesD['to'] == 0 && array_key_last($TimeDetail) == $index && array_key_last($DaysDetail) == $Day) {
                        $ItineraryDays[$Day][] = ['type' => "transport",
                                            'id' => 'departure',
                                            'hotel' => $hotel,
                                            'date_time' => $Date . " " . $time,
                                            'time' => $time];
                    } 
                    else if(($timesD['to'] == $DayDetails['place']  && (array_key_first($DaysDetail) != $Day || array_key_last($DaysDetail) != $Day)) && $type == "all") {
                        
                        if(isset($QuotationArray['accommodation'][$index-1]) && $QuotationArray['accommodation'][$index-1] == 1) {
                            $ItineraryDays[$Day][] = ['type' => "hotel",
                                'id' => $timesD['to'],
                                'date_time' => $Date . " " . $time,
                                'time' => $time];
                        }
                        
                    }
                }
            }

            usort($ItineraryDays[$Day], function ($a, $b) {
                return strtotime($a['date_time']) - strtotime($b['date_time']);
            });
        }
        
        return $ItineraryDays;
    }

    /**
     * @param $QuotationArray
     * @return array
     */
    function getFormattedItineraryByQuotation($QuotationSession, $type= 'all')
    {
        $data = [];
        if(isset($QuotationSession['activity']) && !empty($QuotationSession['activity'])) {
            foreach ($QuotationSession['activity']['type'] as $index => $type) {
                $previous = "";
                $Activity = $QuotationSession['activity'];
                $Date = Carbon::create($Activity['date']['year'][$index], $Activity['date']['month'][$index], $Activity['date']['day'][$index], 14, 15, 16);
                $d = [];
                $d['date']['formatted'] = $Date->toFormattedDateString();
                $d['date']['year'] = $Activity['date']['year'][$index];
                $d['date']['month'] = $Activity['date']['month'][$index];
                $d['date']['day'] = $Activity['date']['day'][$index];
                $d['type'] = $Activity['type'][$index];
                if($Activity['type'][$index] == "arrival" || $Activity['type'][$index] == "departure") {
                    $d['name'] = \App\Model\Place\Place::find($Activity['id'][$index])->name;
                } else if ($Activity['type'][$index] == "hotel") {
                    $d['name'] = \App\Model\Hotel\Hotel::find($Activity['id'][$index])->name;
                } else if ($Activity['type'][$index] == "attraction") {
                    $d['name'] = \App\Model\Place\Attraction::find($Activity['id'][$index])->name;
                } else if ($Activity['type'][$index] == "city_tour") {
                    $d['name'] = \App\Model\Place\CityTour::find($Activity['id'][$index])->name;
                } else if ($Activity['type'][$index] == "excursion") {
                    $d['name'] = \App\Model\Place\Excursion::find($Activity['id'][$index])->name;
                }
                $d['id'] = $Activity['id'][$index];
                $d['arrival_time'] = $Activity['arrival_time'][$index];
                $d['departure_time'] = $Activity['departure_time'][$index];
                $data[$d['date']['formatted']][] = $d;
                $previous = $d['date']['formatted'];
            }
        }

        return $data;
    }

    static function getHotelFromPlace($place, $hotels) {
        foreach($hotels as $hotel) {
            if($hotel['place'] == $place) {
                return $hotel['hotel'] ?? $hotel['cruise'] ;
            }
        }
    }

    /**
     * @param $DaysDetail
     * @return array
     */
    function getDayList($DaysDetail)
    {

        $DayList = array_keys($DaysDetail);
        $DayListCaption = [];

        foreach ($DayList as $Day) {
            $DayListCaption[$Day] = "Day " . $Day;
        }
        return $DayListCaption;

    }

    /**
     * @param $ID
     * @param $SightseeingType
     * @param $SavedData
     * @return bool
     */
    function isAvailableInSaved($ID, $SightseeingType, $SavedData)
    {

        if (is_array($SavedData)) {
            foreach ($SavedData as $Day => $DayItem) {
                if (isset($DayItem[$SightseeingType][$ID])) {
                    return $DayItem[$SightseeingType][$ID];
                }
            }
        }
        return false;

    }
}
