<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\Chauffeur
 *
 * @property int $id
 * @property int $member_id
 * @property int|null $type
 * @property string|null $license_no
 * @property int|null $uniform
 * @property string|null $remarks
 * @property float|null $rate_per_day
 * @property float|null $tip_per_day
 * @property int|null $nationality
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Operation\ChauffeurLanguage[] $language
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereLicenseNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereMemberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereNationality($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereRatePerDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereRemarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereTipPerDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereUniform($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Chauffeur whereUploadId($value)
 * @mixin \Eloquent
 */
class Chauffeur extends Model
{
    protected $fillable = ['member_id'];
    protected $table = "apple_transport_chauffeur";

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    function language()
    {
        return $this->hasMany('App\Model\Operation\ChauffeurLanguage', 'chauffeur_id', 'id');

    }
}
