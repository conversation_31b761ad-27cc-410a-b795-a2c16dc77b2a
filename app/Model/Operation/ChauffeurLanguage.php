<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\ChauffeurLanguage
 *
 * @property int $id
 * @property int $chauffeur_id
 * @property int $language_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \App\Model\Operation\Chauffeur $chauffeur
 * @property-read \App\Model\Language\Language $language
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereChauffeurId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereLanguageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurLanguage whereUploadId($value)
 * @mixin \Eloquent
 */
class ChauffeurLanguage extends Model
{
    protected $table = "apple_transport_chauffeur_languages";

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    function chauffeur()
    {
        return $this->hasOne('App\Model\Operation\Chauffeur', 'id', 'chauffeur_id');

    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    function language()
    {
        return $this->hasOne('App\Model\Language\Language', 'ID', 'language_id');

    }
}
