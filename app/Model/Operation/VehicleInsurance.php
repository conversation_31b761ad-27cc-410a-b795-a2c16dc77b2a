<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\VehicleInsurance
 *
 * @property int $id
 * @property string|null $insurance_type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInsurance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInsurance whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInsurance whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInsurance whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInsurance whereInsuranceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInsurance whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInsurance whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInsurance whereUploadId($value)
 * @mixin \Eloquent
 */
class VehicleInsurance extends Model
{
    protected $table = 'apple_transport_vehicle_insurance';

}
