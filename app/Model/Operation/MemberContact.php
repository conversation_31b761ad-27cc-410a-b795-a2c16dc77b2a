<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\MemberContact
 *
 * @property int $id
 * @property int|null $member_id
 * @property string|null $contact
 * @property int $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereContact($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereMemberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberContact whereUploadId($value)
 * @mixin \Eloquent
 */
class MemberContact extends Model
{
    protected $table = "apple_transport_member_contact";
}
