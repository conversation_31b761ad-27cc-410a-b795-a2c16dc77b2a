<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\VehicleType
 *
 * @property int $id
 * @property string|null $vehicle_type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleType whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleType whereVehicleType($value)
 * @mixin \Eloquent
 */
class VehicleType extends Model
{
    protected $table = 'apple_transport_vehicle_type';

    protected $guarded = 'id';
}
