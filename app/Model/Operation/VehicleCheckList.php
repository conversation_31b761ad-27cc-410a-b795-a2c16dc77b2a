<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\VehicleCheckList
 *
 * @property int $id
 * @property int $vehicle_id
 * @property int $checklist_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereChecklistId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleCheckList whereVehicleId($value)
 * @mixin \Eloquent
 */
class VehicleCheckList extends Model
{
    protected $table = 'apple_transport_vehicle_checklist';
    protected $fillable = ['vehicle_id'];

}
