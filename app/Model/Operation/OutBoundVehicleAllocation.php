<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;
use Wildside\Userstamps\Userstamps;

/**
 * App\Model\Operation\VehicleAllocation
 *
 * @property int $ID
 * @property int|null $reference_id
 * @property int|null $vehicle_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \App\Model\QuotationManage\Quotation|null $Quotation
 * @property-read \App\Model\Operation\Vehicle|null $Vehicle
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereVehicleId($value)
 * @mixin \Eloquent
 * @property int $id
 * @property int|null $driver
 * @property int|null $chauffer
 * @property string|null $remarks
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereChauffer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereDriver($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleAllocation whereRemarks($value)
 */
class OutBoundVehicleAllocation extends Model
{
    use Userstamps;

    protected $table = 'apple_transport_vehicle_outbound_allocation';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Quotation()
    {
        return $this->hasOne('App\Model\QuotationManage\Quotation', 'ID', 'reference_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function Vehicle()
    {

        return $this->belongsTo('App\Model\Operation\Vehicle', 'vehicle_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Guide()
    {

        return $this->hasOne('App\Model\Operation\Member', 'id', 'chauffeur');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Driver()
    {

        return $this->hasOne('App\Model\Operation\Member', 'id', 'driver');
    }
}
