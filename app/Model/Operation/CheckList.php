<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\CheckList
 *
 * @property int $id
 * @property string|null $check
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\CheckList whereCheck($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\CheckList whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\CheckList whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\CheckList whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\CheckList whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\CheckList whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\CheckList whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\CheckList whereUploadId($value)
 * @mixin \Eloquent
 */
class CheckList extends Model
{
    protected $table = "apple_transport_checklist";
}
