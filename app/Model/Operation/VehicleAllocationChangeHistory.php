<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

class VehicleAllocationChangeHistory extends Model
{
    protected $table = 'apple_transport_vehicle_allocation_history';

    public function vehicleAllocation(){

        return $this->hasOne('App\Model\Operation\VehicleAllocation', 'id', 'vehicle_allocation_id');
    }

    public function OldVehicle() {

        return $this->belongsTo('App\Model\Operation\Vehicle', 'old_vehicle_id', 'id');
    }

    public function NewVehicle() {

        return $this->belongsTo('App\Model\Operation\Vehicle', 'new_vehicle_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function OldGuide(){

        return $this->hasOne('App\Model\Operation\Member', 'id', 'old_chauffeur');
    }

    public function NewGuide(){

        return $this->hasOne('App\Model\Operation\Member', 'id', 'new_chauffeur');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function OldDriver() {

        return $this->hasOne('App\Model\Operation\Member', 'id', 'old_driver_id');
    }

    public function NewDriver() {

        return $this->hasOne('App\Model\Operation\Member', 'id', 'new_driver_id');
    }

    public function User()
    {
        return $this->hasOne('App\User', 'id', 'user_id');
    }

}
