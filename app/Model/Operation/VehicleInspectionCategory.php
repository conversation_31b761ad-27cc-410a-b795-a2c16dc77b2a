<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\VehicleInspectionCategory
 *
 * @property int $id
 * @property string|null $inspection_category
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInspectionCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInspectionCategory whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInspectionCategory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInspectionCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInspectionCategory whereInspectionCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInspectionCategory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInspectionCategory whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleInspectionCategory whereUploadId($value)
 * @mixin \Eloquent
 */
class VehicleInspectionCategory extends Model
{
    protected $table = 'apple_transport_vehicle_inspection_category';
}
