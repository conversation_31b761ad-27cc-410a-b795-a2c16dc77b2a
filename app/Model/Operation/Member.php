<?php
/**
 * Supun Praneeth 2017.  All rights reserved.
 * @link https://github.com/spmsupun
 */

/**
 * Supun Praneeth 2017.  All rights reserved.
 * @link https://github.com/spmsupun
 */

/**
 * Supun Praneeth 2017.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\Operation;

use App\Model\Quotation\QuotationHotel;
use App\Model\QuotationManage\Quotation as QuotationManage;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

;

/**
 * App\Model\Operation\Member
 *
 * @property int $id
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $nick_name
 * @property string|null $nic
 * @property string|null $email
 * @property string|null $address
 * @property int|null $city
 * @property string|null $remarks
 * @property int|null $registration_date
 * @property int|null $member_type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @mixin \Eloquent
 * @property-read \App\Model\Operation\MemberType $Type
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Operation\Vehicle[] $Vehicle
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereMemberType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereNic($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereNickName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereRemarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Member whereUploadId($value)
 * @property-read \App\Model\Operation\MemberBank $bank
 * @property-read \App\Model\Operation\Chauffeur $chauffeur
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Operation\MemberContact[] $contact
 * @property-read \App\Model\Operation\Driver $driver
 */
class Member extends Model
{
    protected $fillable = ['id'];

    protected $table = 'apple_transport_members_details';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Vehicle()
    {
        return $this->hasMany('App\Model\Operation\Vehicle', 'driver', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function contact()
    {
        return $this->hasMany('App\Model\Operation\MemberContact', 'member_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function bank()
    {
        return $this->hasOne('App\Model\Operation\MemberBank', 'member_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function driver()
    {
        return $this->hasOne('App\Model\Operation\Driver', 'member_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function chauffeur()
    {
        return $this->hasOne('App\Model\Operation\Chauffeur', 'member_id', 'id');
    }

    /**
     * @param Carbon $date
     * @return \Illuminate\Support\Collection
     */
    function isAvailable(Carbon $date)
    {

        $VehicleAllocation = new VehicleAllocation();
        $QuotationHotel = new QuotationHotel();
        $Driver = $this->id;


        return $VehicleAllocation
            ->where(function ($q) use ($Driver) {
                $q->where('driver', $Driver);
                $q->orWhere('chauffeur', $Driver);
            })
            ->get()
            ->map(function ($q) use ($QuotationHotel, $date, $Driver) {

                $Quotation = $q->Quotation()->withTrashed()->first();

                $Dates = $QuotationHotel->getHotelBookDates(["year" => $Quotation->Main()->first()->arrival_year, "month" => $Quotation->Main()->first()->arrival_month, "day" => $Quotation->Main()->first()->arrival_day], $Quotation->place()->select('place')->get()->toArray());

                $arrival = (reset($Dates)['check_in']);
                $departure = (end($Dates)['check_out']);

                $ArrivalDate = Carbon::create($arrival['year'], $arrival['month'], $arrival['day'],0,0,0);
                $DepartureDate = Carbon::create($departure['year'], $departure['month'], $departure['day'],0,0,0);


                if ($date->between($ArrivalDate, $DepartureDate)) {

                    $driver = $Quotation->VehicleAllocation()->first()->Vehicle()->first()->owner()->first();

                    return [
                        'tour' => QuotationManage::getReferenceID($Quotation->quotation_no)[2] . " ($driver->first_name $driver->last_name)",
                        'start' => $ArrivalDate->toDateString(),
                        'end' => $DepartureDate->toDateString(),
                        'current_date' => $date->toDateString(),
                        'member' => $q->Driver()->first()

                    ];
                }


            });

    }

}
