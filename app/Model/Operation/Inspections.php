<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\Inspections
 *
 * @property int $id
 * @property string|null $inspections
 * @property int|null $category
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereInspections($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Inspections whereUploadId($value)
 * @mixin \Eloquent
 */
class Inspections extends Model
{
    protected $table = "apple_transport_inspections";
}
