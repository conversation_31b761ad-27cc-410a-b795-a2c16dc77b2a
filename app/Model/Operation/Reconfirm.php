<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\Driver
 *
 * @property int $id
 * @property int|null $member_id
 * @property string|null $license_start_date
 * @property string|null $license_end_date
 * @property string|null $license_category
 * @property string|null $license_no
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereLicenseCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereLicenseEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereLicenseNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereLicenseStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereMemberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Driver whereUploadId($value)
 * @mixin \Eloquent
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Operation\Vehicle[] $Vehicle
 * @property-read \App\Model\Operation\Member $Member
 */
class Reconfirm extends Model
{    protected $fillable = ['id'];

    protected $table = 'apple_reconfirm';


}
