<?php

namespace App\Model\Operation;

use App\Model\Image\Image;
use App\Model\Quotation\QuotationHotel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Wildside\Userstamps\Userstamps;

/**
 * App\Model\Operation\Vehicle
 *
 * @property int $id
 * @property int|null $owner
 * @property int|null $driver
 * @property int|null $color
 * @property int $vehicle_type
 * @property int|null $insurance
 * @property int|null $vehicle_brand
 * @property string|null $registration_year
 * @property string|null $registration_no
 * @property string|null $agreement_expire_at
 * @property string|null $insurance_date
 * @property int|null $model_year
 * @property string|null $last_inspection_date
 * @property int|null $inspection_by
 * @property string|null $remarks
 * @property string|null $vehicle_number
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \App\Model\Vehicle\Vehicle $vehicleType
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereAgreementExpireAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereDriver($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereInspectionBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereInsurance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereInsuranceDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereLastInspectionDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereModelYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereOwner($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereRegistrationNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereRegistrationYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereRemarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereVehicleBrand($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereVehicleNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\Vehicle whereVehicleType($value)
 * @mixin \Eloquent
 * @property-read \App\Model\Operation\VehicleBrand|null $Brand
 */
class Vehicle extends Model
{
    use Userstamps;

    protected $table = 'apple_transport_vehicles';


    /**
     * Vehicle constructor.
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        $this->hidden = getHiddenColumn();
    }

    /**
     * @param $id
     * @return array
     */
    static function getVehicle($id)
    {

        $self = self::with('Owner', 'vehicleType.vehicleType', 'Brand', 'vehicleType')->find($id);

        $data = $self->toArray();
        $data['image'] = $self->Image();

        return $data;

    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Driver()
    {
        return $this->hasOne('App\Model\Operation\Member', 'id', 'driver');
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Owner()
    {
        return $this->hasOne('App\Model\Operation\Member', 'id', 'owner');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Brand()
    {
        return $this->hasOne('App\Model\Operation\VehicleBrand', 'id', 'vehicle_brand');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function CheckList()
    {
        return $this->hasMany('App\Model\Operation\VehicleCheckList', 'vehicle_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Inspection()
    {
        return $this->hasMany('App\Model\Operation\VehicleInspection', 'vehicle_id', 'id');
    }

    /**
     * @param bool $id
     * @param bool $date
     * @return bool|mixed
     */
    static public function isAvailable($id = false, $date = false)
    {
        $VehicleAllocation = new VehicleAllocation();
        $QuotationHotel = new QuotationHotel();
        if (!$date) {
            $date = Carbon::now();
        }

        $available = $VehicleAllocation->where('vehicle_id', $id)->first();

        if ($available) {
            $available = $available->Quotation()->get()->map(function ($q) use ($QuotationHotel, $date) {

                $Dates = $QuotationHotel->getHotelBookDates(["year" => $q->main()->first()->arrival_year, "month" => $q->main()->first()->arrival_month, "day" => $q->main()->first()->arrival_day], $q->place()->select('place')->get()->toArray());

                $departure = (end($Dates)['check_out']);
                $DepartureDate = Carbon::create($departure['year'], $departure['month'], $departure['day']);

                if ($DepartureDate > $date) {
                    return ['on_going' => [
                        'quotation_no' => $q->quotation_no,
                        'departure_date' => $DepartureDate->toDateString()
                    ]
                    ];
                } else {
                    return true;
                }

            });
        } else
            return true;

        return $available['on_going'] ?? true;
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function vehicleType()
    {

        return $this->hasOne('App\Model\Vehicle\Vehicle', 'vehicle', 'vehicle_type');
    }

    /**
     * @return array
     */
    public function Image()
    {

        $Image = new Image();
        return $Image->getImage($this->id, '3x', "transport/vehicle", 1);
    }


}
