<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\MemberBank
 *
 * @property int $id
 * @property int|null $member_id
 * @property int|null $vehicle_id
 * @property string|null $bank_name
 * @property string|null $branch
 * @property string|null $account_number
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereAccountNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereBankName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereBranch($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereMemberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereVehicleId($value)
 * @mixin \Eloquent
 * @property int $bank
 * @property string|null $account_name
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereAccountName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\MemberBank whereBank($value)
 */
class MemberBank extends Model
{    protected $fillable = ['id'];

    protected $table = 'apple_transport_member_bank_details';

}
