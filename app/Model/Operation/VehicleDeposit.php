<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\VehicleDeposit
 *
 * @property int $id
 * @property int|null $vehicle_id
 * @property int|null $deposit_type
 * @property int|null $tour_id
 * @property int|null $installment
 * @property string|null $amount
 * @property string|null $date
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereDepositType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereInstallment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereTourId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\VehicleDeposit whereVehicleId($value)
 * @mixin \Eloquent
 */
class VehicleDeposit extends Model
{
    protected $table = 'apple_transport_vehicle_deposit';
}
