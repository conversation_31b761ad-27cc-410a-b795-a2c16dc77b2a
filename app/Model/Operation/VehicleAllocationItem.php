<?php
/**
 * Supun Praneeth 2017.  All rights reserved.
 * @link https://github.com/spmsupun
 */

/**
 * Supun Praneeth 2017.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;
use Wildside\Userstamps\Userstamps;

/**
 * Class VehicleAllocationItem
 * @package App\Model\Operation
 */
class VehicleAllocationItem extends Model
{
    protected $table = 'apple_transport_vehicle_allocation_items';
    protected $fillable = ['allocation'];
    use Userstamps;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function allocation()
    {
        return $this->hasOne('App\Model\Operation\VehicleAllocation', 'id', 'allocation');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Item()
    {
        return $this->hasOne('App\Model\Operation\VehicleAllocationItemList', 'id', 'item');
    }

}
