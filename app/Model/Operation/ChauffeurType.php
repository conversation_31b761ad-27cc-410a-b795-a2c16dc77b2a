<?php

namespace App\Model\Operation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Operation\ChauffeurType
 *
 * @property int $id
 * @property string $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurType whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Operation\ChauffeurType whereUploadId($value)
 * @mixin \Eloquent
 */
class ChauffeurType extends Model
{
    protected $table = "apple_transport_chauffeur_type";
}
