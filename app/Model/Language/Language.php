<?php

namespace App\Model\Language;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Language\Language
 *
 * @property int $ID
 * @property string $code
 * @property string $code_2t
 * @property string $name
 * @property string $native_name
 * @property int $prefer
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereCode2t($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereNativeName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language wherePrefer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Language\Language whereUploadId($value)
 * @mixin \Eloquent
 */
class Language extends Model
{
    protected $table = 'apple_languages';
	
	
}
