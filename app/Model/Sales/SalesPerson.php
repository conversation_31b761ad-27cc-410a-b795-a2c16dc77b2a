<?php

namespace App\Model\Sales;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Agent\Agent
 *
 * @property int $ID
 * @property string $name
 * @property string $address
 * @property int $agent_type
 * @property int $status
 * @property int $user
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @mixin \Eloquent
 */
class SalesPerson extends Model
{
    protected $table = "apple_sales_person";
    protected $primaryKey = 'ID';

}
