<?php

namespace App\Model\Home;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Home\Map
 *
 * @mixin \Eloquent
 */
class Map extends Model
{


    /**
     * @param $Type
     * @param $Waypoints
     * @return bool|string
     */
    function getMapboxDirection($Type, $Waypoints){
		
		$url = "https://api.tiles.mapbox.com/v4/directions/mapbox.$Type/";
		$apiKey = "pk.eyJ1Ijoic3Btc3VwdW4iLCJhIjoiY2lyM2d5Z2E2MDAwZDIzbm1qaHA3M2wwdSJ9.K9cPAHNdG61RNsHVAmUZdQ";
		
		return file_get_contents("$url$Waypoints.json?instructions=true&alternatives=true&geometry=polyline&access_token=$apiKey");
		
	}

    /**
     * @param $Type
     * @param $Waypoints
     * @return bool|string
     */
    function getORSMDirection($Type, $Waypoints){
		
		$url = "https://router.project-osrm.org/route/v1/$Type/$Waypoints?overview=false&alternatives=true&steps=true";
		
		return file_get_contents($url);
		
	}


    /**
     * @param $q
     * @param bool $city
     * @param bool $country
     * @param bool $street
     * @param bool $state
     * @return array
     */
    function searchOpenStreetMap($q, $city = false, $country = false, $street = false, $state = false){
		
		$Query = array();
		$List = array();
		
		if($q)
			$Query["q"] = $q ;
			
		if($city)
			$Query["city"] = $city;
			
		if($country)
			$Query["country"] = $country;
			
		if($street)
			$Query["street"] = $street;

		if($state)
			$Query["state"] = $state;
			
		
		$url = "http://nominatim.openstreetmap.org/search?format=json&".http_build_query($Query);
		//echo $url;
		
		$ListArray = json_decode(file_get_contents($url));
		
 		
		foreach($ListArray  as $PlaceItem){
			
			$List[] = array(
							"ID"=>$PlaceItem->place_id,
							"value"=>$PlaceItem->display_name,
							"desc"=>$PlaceItem->display_name,
							"label"=>$PlaceItem->display_name,
							"name"=>$PlaceItem->display_name,
							
							"lat"=>$PlaceItem->lat,
							"lon"=>$PlaceItem->lon
							
							);
						
		}
		
		return $List;
		
	}
	
	
}
