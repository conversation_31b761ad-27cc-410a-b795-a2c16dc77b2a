<?php

namespace App\Model\FdPackages;

use Illuminate\Database\Eloquent\Model;

class apple_fd_packages_quotation extends Model
{
    public function findPax() {
        return $this->hasOne('App\Model\FdPackages\apple_fd_packages_quotation_pax','reference_id','id');
    }
    public function findFdPackage() {
        return $this->hasOne('App\Model\FdPackages\apple_fd_packages','id','fd_package_id');
    }
    public function findFdCost() {
        return $this->hasOne('App\Model\FdPackages\apple_fd_packages_quotation_cost','reference_id','id');
    }

    public function findHotelRates() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_quotation_hotel_rates','reference_id','id');
    }
    public function findHotelVoucher() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_quotation_hotel_vouchers_info','reference_id','id');
    }
    public function findCancelledHotelVoucher() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_quotation_cancelled_hotels','reference_id','id');
    }

    public function findUser() {
        return $this->hasOne('App\User','id','user_id');
    }

    public function findStatus() {
        return $this->hasOne('App\Model\QuotationManage\QuotationStatusList','ID','status');
    }
    public function findHonorific() {
        return $this->hasOne('App\Model\Tool\Honorific','ID','honorific_id');
    }
    public function findAgent() {
        return $this->hasOne('App\Model\Agent\Agent','ID','agent_id');
    }
}
