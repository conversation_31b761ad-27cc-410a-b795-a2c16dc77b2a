<?php

namespace App\Model\FdPackages;

use Illuminate\Database\Eloquent\Model;

class apple_fd_packages extends Model
{
    public function findUser() {
        return $this->hasOne('App\User','id','user_id');
    }
    public function findAllowedUsers() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_user','fd_package_id','id');
    }
    public function findAttractions() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_attractions','fd_package_id','id');
    }
    public function findHotels() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_hotels','fd_package_id','id');
    }
    public function findImages() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_images','fd_package_id','id')->orderBy('priority','ASC');
    }
    public function findOthersCosts() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_other_costs','fd_package_id','id');
    }
    public function findPackagePrice() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_package_price','fd_package_id','id')->orderBy('id','DESC');
    }

    public function findTransportCost() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_transport_cost','fd_package_id','id');
    }
    public function findPlaces() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_places','fd_package_id','id');
    }

    public function findAllowedPersons() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_user','fd_package_id','id');
    }

    public function findMealCost() {
        return $this->hasOne('App\Model\FdPackages\apple_fd_packages_meal_cost','fd_package_id','id');
    }

    public function findSuppliment() {
        return $this->hasMany('App\Model\FdPackages\apple_fd_packages_suppliments','fd_package_id','id');
    }

}
