<?php

namespace App\Model\FdPackages;

use Illuminate\Database\Eloquent\Model;

class apple_fd_packages_hotels extends Model
{
    //
    public function findHotel() {
        return $this->hasMany('App\Model\Hotel\Hotel','ID','hotel_id');
    }

    public function findMealType() {
        return $this->hasMany('App\Model\Hotel\Meal','ID','meal_type');
    }

    public function findRoomCategory() {
        return $this->hasOne('App\Model\Hotel\RoomCategory','ID','room_category');
    }
    public function findPlace() {
        return $this->hasOne('App\Model\Place\Place','ID','place_id');
    }
}
