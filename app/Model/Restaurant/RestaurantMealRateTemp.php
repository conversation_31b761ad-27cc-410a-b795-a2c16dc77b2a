<?php

namespace App\Model\Restaurant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Restaurant\RestaurantMealRateTemp
 *
 * @property int $ID
 * @property int $restaurant
 * @property int $meal_time
 * @property float $rate
 * @property int $user
 * @property string $from
 * @property string $to
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $upload_id
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Restaurant\RestaurantMealRateTemp onlyTrashed()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereMealTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereRestaurant($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRateTemp whereUser($value)
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Restaurant\RestaurantMealRateTemp withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Restaurant\RestaurantMealRateTemp withoutTrashed()
 * @mixin \Eloquent
 */
class RestaurantMealRateTemp extends Model
{
    protected $table = 'apple_restaurant_meal_rate_temp';
    use SoftDeletes;

}
