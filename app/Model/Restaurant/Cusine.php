<?php

namespace App\Model\Restaurant;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Restaurant\Cusine
 *
 * @property int $ID
 * @property string $cusine
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Cusine whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Cusine whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Cusine whereCusine($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Cusine whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Cusine whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Cusine whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Cusine whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Cusine whereUploadId($value)
 * @mixin \Eloquent
 */
class Cusine extends Model
{
    protected $table = 'apple_cuisine';

}
