<?php

namespace App\Model\Restaurant;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Restaurant\Expensive
 *
 * @property int $ID
 * @property string $expensive
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Expensive whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Expensive whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Expensive whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Expensive whereExpensive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Expensive whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Expensive whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Expensive whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Expensive whereUploadId($value)
 * @mixin \Eloquent
 */
class Expensive extends Model
{
    protected $table = 'apple_restaurant_expensive';
    protected $primaryKey = "ID";

}
