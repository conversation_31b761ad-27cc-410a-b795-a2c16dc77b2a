<?php

namespace App\Model\Restaurant;

use App\Model\Meal\MealTime;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Restaurant\RestaurantMealRate
 *
 * @property int $ID
 * @property int $restaurant
 * @property int $meal_time
 * @property float $rate
 * @property string|null $from
 * @property int|null $user
 * @property string|null $to
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereMealTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereRestaurant($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\RestaurantMealRate whereUser($value)
 * @mixin \Eloquent
 */
class RestaurantMealRate extends Model
{
    protected $table = 'apple_restaurant_meal_rate';
    protected $primaryKey = "ID";
    // protected $visible = ["restaurant", "meal_time", "rate", "child_rate", "from", "user", "to"];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function mealTime()
    {
        return $this->hasOne(MealTime::class,'id','meal_time');
    }
}
