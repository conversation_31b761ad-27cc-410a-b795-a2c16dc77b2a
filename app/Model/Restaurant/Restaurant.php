<?php

namespace App\Model\Restaurant;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Restaurant\Restaurant
 *
 * @property int $ID
 * @property int $from
 * @property int|null $to
 * @property string|null $name
 * @property int|null $expensive
 * @property string|null $description
 * @property string|null $remarks
 * @property string|null $sales_person
 * @property string|null $tel
 * @property string|null $email
 * @property string|null $restaurant_contact
 * @property string|null $restaurant_tel
 * @property string|null $restaurant_email
 * @property string $address
 * @property string|null $web
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Restaurant\RestaurantMealRate[] $Rate
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereExpensive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereRemarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereRestaurantContact($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereRestaurantEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereRestaurantTel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereSalesPerson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereTel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Restaurant\Restaurant whereWeb($value)
 * @mixin \Eloquent
 */
class Restaurant extends Model
{
    protected $table = 'apple_restaurant';

    protected $primaryKey = "ID";

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function RateRef()
    {
        return $this->hasMany(RestaurantMealRate::class, 'restaurant', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function expensive()
    {
        return $this->hasOne(Expensive::class, 'id', 'expensive')->select(['id', 'expensive']);
    }

}
