<?php

namespace App\Model\Tool;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Tool\Honorific
 *
 * @property int $ID
 * @property string $honorific
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Honorific whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Honorific whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Honorific whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Honorific whereHonorific($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Honorific whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Honorific whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Honorific whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Honorific whereUploadId($value)
 * @mixin \Eloquent
 */
class Honorific extends Model
{
    protected $table = 'apple_honorifics';
}
