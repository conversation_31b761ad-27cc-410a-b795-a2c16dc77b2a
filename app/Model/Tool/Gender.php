<?php

namespace App\Model\Tool;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Tool\Gender
 *
 * @property int $ID
 * @property string $gender
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Gender whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Gender whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Gender whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Gender whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Gender whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Gender whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Gender whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Tool\Gender whereUploadId($value)
 * @mixin \Eloquent
 */
class Gender extends Model
{
    protected $table = 'apple_gender';
}
