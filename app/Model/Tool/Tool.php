<?php

namespace App\Model\Tool;

use App\Model\Place\PlaceQuotationCount;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Tool\Tool
 *
 * @mixin \Eloquent
 */
class Tool extends Model
{

    /**
     * @param $chars
     * @param int $size
     * @param array $combinations
     * @return array
     */
    function getPermutaion($chars, $size = 2, $combinations = array())
    {

        # if it's the first iteration, the first set
        # of combinations is the same as the set of characters
        if (empty($combinations)) {
            $combinations = $chars;
        }

        # we're done if we're at size 1
        if ($size == 1) {
            return $combinations;
        }

        # initialise array to put new values in
        $new_combinations = array();

        # loop through existing combinations and character set to create strings
        foreach ($combinations as $combination) {
            foreach ($chars as $char) {
                if ($combination != $char)
                    $new_combinations[] = array($combination, $char);
            }
        }

        # call same function again for the next iteration
        return $this->getPermutaion($chars, $size - 1, $new_combinations);

    }

    /**
     * @param $PlaceMarkerPermutation
     * @param $PickupStop
     * @param $DropOffStop
     * @return array
     */
    function setPlaceAirportFirstLast($PlaceMarkerPermutation, $PickupStop, $DropOffStop){


        $data = [];

        foreach($PlaceMarkerPermutation as $Item){

            $first = reset($Item);
            $last = end($Item);

            if($PickupStop==$first && $DropOffStop==$last)
                $data[] = $Item;
        }

        return $data;

    }

    /**
     * @param $PlaceMarkerPermutation
     * @param $ArrayPlace
     * @param $PlaceIndex
     * @param $Place
     * @return array
     */
    function setPlaceSpeiclPlace($PlaceMarkerPermutation, $ArrayPlace, $PlaceIndex, $Place)
    {
        $FilterArray = [];
        $MainIndex = false;
        $ReturnArray = [];

        //sd($PlaceIndex);

        if ($ArrayPlace == 'last') {

            foreach ($PlaceMarkerPermutation as $IndexBrace) {

                if (in_array(-1, $IndexBrace) && reset($IndexBrace) != -1)
                    continue;
                if (in_array(-2, $IndexBrace) && end($IndexBrace) != -2)
                    continue;


                $FilterArray[] = $IndexBrace;

            }

            foreach ($PlaceIndex as $Index => $PlaceID) {

                if ($PlaceID == $Place) {
                    $MainIndex = $Index;
                    break;
                }
            }

            if (in_array('-2', $FilterArray[0]))
                $LastIndex = count($FilterArray[0]) - 2;
            else
                $LastIndex = count($FilterArray[0]) - 2;


            foreach ($FilterArray as $FilterArrayItem) {
                if ($FilterArrayItem[$LastIndex] == $MainIndex)
                    $ReturnArray[] = $FilterArrayItem;
            }

        }

        return $ReturnArray;
    }


    /**
     * @param $set
     * @return array
     */
    function getPermutaionList($set)
    {
        $storedAr = $set;
        $solutions = array();
        $n = count($set);
        $p = array_keys($set);
        $i = 1;

        while ($i < $n) {
            if ($p[$i] > 0) {

                $p[$i]--;
                $j = 0;
                if ($i % 2 == 1) {
                    $j = $p[$i];
                }
                // swap
                $tmp = $set[$j];
                $set[$j] = $set[$i];
                $set[$i] = $tmp;
                $i = 1;
                $solutions[] = $set;
            } elseif ($p[$i] == 0) {
                $p[$i] = $i;
                $i++;
            }
        }
        array_unshift($solutions, $storedAr);
        return array_values($solutions);


    }


    /**
     * @param $encoded
     * @return array
     */
    function decodePolylineToArray($encoded)
    {
        $length = strlen($encoded);
        $index = 0;
        $points = array();
        $lat = 0;
        $lng = 0;

        while ($index < $length) {
            // Temporary variable to hold each ASCII byte.
            $b = 0;

            // The encoded polyline consists of a latitude value followed by a
            // longitude value.  They should always come in pairs.  Read the
            // latitude value first.
            $shift = 0;
            $result = 0;
            do {
                // The `ord(substr($encoded, $index++))` statement returns the ASCII
                //  code for the character at $index.  Subtract 63 to get the original
                // value. (63 was added to ensure proper ASCII characters are displayed
                // in the encoded polyline string, which is `human` readable)
                $b = ord(substr($encoded, $index++)) - 63;

                // AND the bits of the byte with 0x1f to get the original 5-bit `chunk.
                // Then left shift the bits by the required amount, which increases
                // by 5 bits each time.
                // OR the value into $results, which sums up the individual 5-bit chunks
                // into the original value.  Since the 5-bit chunks were reversed in
                // order during encoding, reading them in this way ensures proper
                // summation.
                $result |= ($b & 0x1f) << $shift;
                $shift += 5;
            }
                // Continue while the read byte is >= 0x20 since the last `chunk`
                // was not OR'd with 0x20 during the conversion process. (Signals the end)
            while ($b >= 0x20);

            // Check if negative, and convert. (All negative values have the last bit
            // set)
            $dlat = (($result & 1) ? ~($result >> 1) : ($result >> 1));

            // Compute actual latitude since value is offset from previous value.
            $lat += $dlat;

            // The next values will correspond to the longitude for this point.
            $shift = 0;
            $result = 0;
            do {
                $b = ord(substr($encoded, $index++)) - 63;
                $result |= ($b & 0x1f) << $shift;
                $shift += 5;
            } while ($b >= 0x20);

            $dlng = (($result & 1) ? ~($result >> 1) : ($result >> 1));
            $lng += $dlng;

            // The actual latitude and longitude values were multiplied by
            // 1e5 before encoding so that they could be converted to a 32-bit
            // integer representation. (With a decimal accuracy of 5 places)
            // Convert back to original values.
            $points[] = array("lat" => $lat * 1e-5, "lng" => $lng * 1e-5);
        }

        return $points;
    }

    function createImage()
    {

        $im = @imagecreate(110, 20)
        or die("Cannot Initialize new GD image stream");
        $background_color = imagecolorallocate($im, 0, 0, 0);
        $text_color = imagecolorallocate($im, 233, 14, 91);
        imagestring($im, 1, 5, 5, "A Simple Text String", $text_color);
        imagepng($im);
        imagedestroy($im);

    }

    /**
     * @param $Title
     */
    function getWikiContent($Title)
    {

        $r = json_decode(@file_get_contents("https://en.wikipedia.org/w/api.php?format=json&action=query&prop=extracts&titles=".urlencode($Title)));

        if(!empty($r->query)){
            sd(reset($r->query->pages)->extract );
        }

    }
    function setPlaceQuotationCount(){






    }
}
