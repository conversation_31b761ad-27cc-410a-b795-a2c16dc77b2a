<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Place\AttractionType
 *
 * @property int $ID
 * @property string $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionType whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionType whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionType whereUploadId($value)
 * @mixin \Eloquent
 */
class AttractionType extends Model
{
    protected $primaryKey = 'ID';
    protected $table = 'apple_attraction_types';

}
