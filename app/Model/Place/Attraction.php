<?php
/**
 * Supun Praneeth 2018.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\Place;

use App\CountryCurrency;
use App\Currency;
use App\Model\Quotation\Quotation;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Place\Attraction
 *
 * @property int $ID
 * @property int $place
 * @property int $duration
 * @property string|null $address
 * @property string|null $description
 * @property string|null $point
 * @property string|null $name
 * @property string|null $operning
 * @property string|null $closing
 * @property int|null $distance
 * @property int|null $time
 * @property float|null $longitude
 * @property float|null $latitude
 * @property int|null $type
 * @property int $en_route
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Place\AttractionRate[] $Rate
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereClosing($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereEnRoute($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereOperning($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction wherePlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction wherePoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereUploadId($value)
 * @mixin \Eloquent
 */
class Attraction extends Model
{
    use SoftDeletes;
    protected $table = 'apple_attraction';

    protected $fillable = ['place'];
    Protected $primaryKey = "ID";
    var $Error = [];

    /**
     * @param $longitude
     * @param $latitude
     * @param float $AreaDistance
     * @return bool|\Illuminate\Support\Collection
     */
    function getMapAreaPlaces($longitude, $latitude, $AreaDistance = 0.3)
    {

        $Places = DB::table($this->table)
            ->select("ID", "name", "longitude", "latitude")
            ->where("longitude", "<", $longitude + $AreaDistance)
            ->where("latitude", "<", $latitude + $AreaDistance)
            ->where("longitude", ">", $longitude - $AreaDistance)
            ->where("latitude", ">", $latitude - $AreaDistance)
            ->limit(10)
            ->get();

        if (!empty($Places))
            return $Places;
        else
            return false;

    }

    /**
     * @param bool $Quotation
     * @param bool $Direct
     * @return array|mixed
     */
    static function getAttraction($Quotation = false, $Direct = false)
    {

        if ((session()->has('quotation.attraction') && session()->has('quotation.rate.attraction.attraction')) && !$Direct) {

            $FinalCurrency = CountryCurrency::getFinalCurrency(session()->get('quotation'));

//            $AttractionCost = Attraction::currencyConvener(session()->get('quotation.rate.attraction.attraction'), $FinalCurrency);
            $AttractionCost = session()->get('quotation.rate.attraction.attraction');

            $AttrKeyRate = array_unique(array_keys($AttractionCost));
            $AttrKeyDetails = array_unique(flatten(session()->get('quotation.attraction')));

            if (!empty(array_diff_assoc($AttrKeyDetails, $AttrKeyRate))) {// not empty mean there are diffrenct elemet so they change rates
                return Attraction::getAttraction($Quotation, true);
            }

        } elseif (isset($Quotation['rate']['attraction']['attraction']) && isset($Quotation['attraction'])) {

            $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);

            if($Direct) {
                $AttractionCost = $Quotation['rate']['attraction']['attraction'];
                // return $AttractionCost;
            } else {
                $AttractionCost = Attraction::currencyConvener($Quotation['rate']['attraction']['attraction'], $FinalCurrency);
            }

            $AttrKeyRate = array_unique(array_keys($AttractionCost));
            $AttrKeyDetails = array_unique(flatten($Quotation['attraction']));

            if (!empty(array_diff_assoc($AttrKeyDetails, $AttrKeyRate))) {
                unset($Quotation['rate']['attraction']['attraction']);
                return Attraction::getAttraction($Quotation, true);

            }

        } else {

            if (!$Quotation)
                $Quotation = session()->get('quotation');

            if (!isset($Quotation['attraction']))
                return [];

            $AttractionList = [];
            $AttractionListByCity = $Quotation['attraction'];//get attaction that selected

            foreach ($AttractionListByCity as $CityID => $AttractionArray) {
                foreach ($AttractionArray as $AttractionID) {
                    $AttractionList[] = $AttractionID;
                }
            }

            $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);
            $AttractionCost = Attraction::getAttractionCost($AttractionList, $Quotation['market'], $Quotation['pax']['cwb'] + $Quotation['pax']['cnb'], $FinalCurrency, $Direct);

        }

        return $AttractionCost;

    }

    /**
     * @param bool $Quotation
     * @param bool $Direct
     * @return array|mixed
     */
    static function getAttractionRatesVal($Quotation = false, $Direct = false)
    {
        if (!$Quotation)
            $Quotation = session()->get('quotation');

        if (!isset($Quotation['attraction']))
            return [];

        $AttractionList = [];
        $AttractionListByCity = $Quotation['attraction'];//get attaction that selected

        foreach ($AttractionListByCity as $CityID => $AttractionArray) {
            foreach ($AttractionArray as $AttractionID) {
                $AttractionList[] = $AttractionID;
            }
        }

        $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);
        $AttractionCost = Attraction::getAttractionCost($AttractionList, $Quotation['market'], $Quotation['pax']['cwb'] + $Quotation['pax']['cnb'], $FinalCurrency, $Direct);

        return $AttractionCost;
    }

    static function getAttractionTime($Quotation = false, $ID) {

        $attraction_time = [];
        if (session()->has('quotation.attraction')) {
            if(isset($Quotation['time']['attraction']['attraction'][(int)$ID])) {
                $attraction_time['time'] = $Quotation['time']['attraction']['attraction'][(int)$ID];
            } else {
                $attraction_time['time'] = ['start'=> 0, 'end'=> 0];
            }
        }
        return $attraction_time;
    }

    static function getAttractionTimeName($ID) {
        if(!isset($ID)) {
            return "N/S";
        }
        $AttractionTimeName = AttractionTime::where("ID", $ID)->first();
        $AttractionTimeName = Carbon::parse($AttractionTimeName['starting'])->format('g:i A') . " ~ " . Carbon::parse($AttractionTimeName['ending'])->format('g:i A');
        return $AttractionTimeName;
    }

    static function getAttractionStartTime($ID) {
        if(!isset($ID)) {
            return "N/S";
        }
        $AttractionStartTime = AttractionTime::where("ID", $ID)->first();
        $AttractionStartTime = Carbon::parse($AttractionStartTime['starting']);
        return $AttractionStartTime;
    }

    /**
     * @param $AttractionList
     * @param $Market
     * @param bool $child
     * @param $FinalCurrency
     * @return array
     */
    static function getAttractionCost($AttractionList, $Market, $child, $FinalCurrency, $Direct = false)
    {

        $Market = $Market == 1 || $Market == 3 || $Market == 4 ? 2 : 1;
        $AttractionCost = [];
        foreach ($AttractionList as $AttractionId) {

            $Attraction = Attraction::find($AttractionId);
            $AttractionRate = $Attraction->Rate()
                ->where('market', $Market)
                ->first();
            $AttractionCost[$AttractionId]['adult'] = $AttractionRate ? $AttractionRate->adult : 0;
            $AttractionCost[$AttractionId]['child'] = $AttractionRate ? $child ? $AttractionRate->child : 0 : 0;
            $AttractionCost[$AttractionId]['transfer_rate'] = $AttractionRate ? $AttractionRate->transfer_rate : 0;
            $AttractionCost[$AttractionId]['adult_entrance_rate'] = $AttractionRate ? $AttractionRate->adult_entrance_rate : 0;
            $AttractionCost[$AttractionId]['child_entrance_rate'] = $AttractionRate ? $AttractionRate->child_entrance_rate : 0;
            $AttractionCost[$AttractionId]['pnl_type'] = $Attraction->pnl_type??"";
        }

        if($Direct) {
            return $AttractionCost;
        } else {
            return Attraction::currencyConvener($AttractionCost, $FinalCurrency);
        }
    }

    static function getAttractionBreakdown($VehicleID, $ID, $Market) {
        $Attraction = ExcursionRate::where("excursion", '=', $ID)
            ->where("market", "=", $Market)
            ->where("vehicle", "=", $VehicleID)
            ->first();

        return $Attraction;
    }

    /**
     * @param $AttractionCost
     * @param $from
     * @param $to
     * @return mixed
     */
    static function currencyConvener($AttractionCost, $to, $from = null)
    {

        foreach ($AttractionCost as $attrID => $attrItem) {

            $Attraction = Attraction::find($attrID);
            $AttractionCurrency = ($from ?? CountryCurrency::getPlaceCurrency($Attraction->place, 'attraction'));

            $AttractionCost[$attrID]['adult'] = currency($attrItem['adult'], $AttractionCurrency->code, $to->code, false);

            if (isset($AttractionCost[$attrID]['child']))
                $AttractionCost[$attrID]['child'] = currency($attrItem['child'], $AttractionCurrency->code, $to->code, false);

            $AttractionCost[$attrID]['currency']['from'] = $AttractionCurrency;
            $AttractionCost[$attrID]['currency']['to'] = $to;

        }

        return $AttractionCost;
    }


    /**
     * @param $Quotation
     * @return bool
     */
    function hasRateList($Quotation)
    {

        $Rates = $this->getAttraction($Quotation, true);
        $noError = true;

        foreach (($Quotation['attraction'] ?? []) as $Day => $ItemArray) {
            foreach ($ItemArray as $Item) {
                if (!isset($Rates[$Item])) {
                    $this->Error[] = "<i>" . $this->find($Item)->name . '</i>';
                    $noError = false;
                }
            }
        }

        return $noError;

    }

    /**
     * @param $Quotation
     * @return bool
     */
    static function attractionCurrencyConvertSingle($attrID, $Quotation)
    {
        $AttractionCost = [];
        $Market = $Quotation['market'] == 1 || $Quotation['market'] == 3 || $Quotation['market'] == 4 ? 2 : 1;

        $Attraction = Attraction::find($attrID);

        $AttractionRate = $Attraction->Rate()
            ->where('market', $Market)
            ->first();

        $AttractionCost[$attrID]['adult'] = $AttractionRate ? $AttractionRate->adult : 0;
        $AttractionCost[$attrID]['child'] = $AttractionRate ? $AttractionRate->child : 0;

        $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation)->code;

        //check quotation has multiple currency
        $CurrencyList = Quotation::getCurrency($Quotation);
        if (count($CurrencyList) > 1)
            $FinalCurrency = "USD";

        if(isset($Quotation['ch_currency'])) {
            $FinalCurrency = Currency::find($Quotation['ch_currency'])->code;
        }

        $AttractionCurrency = ($from ?? CountryCurrency::getPlaceCurrency($Attraction->place, 'attraction'));

        $AttractionCost[$attrID]['adult'] = currency($AttractionCost[$attrID]['adult'], $AttractionCurrency->code, $FinalCurrency, false);

        if (isset($AttractionCost[$attrID]['child']))
            $AttractionCost[$attrID]['child'] = currency($AttractionCost[$attrID]['child'], $AttractionCurrency->code, $FinalCurrency, false);

        $AttractionCost[$attrID]['currency']['from'] = $AttractionCurrency;
        $AttractionCost[$attrID]['currency']['to'] = $FinalCurrency;

        return $AttractionCost;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\Place\AttractionRate', 'attraction', 'ID');
    }

    function type(){
        return $this->hasOne(AttractionType::class,'id','type')->select('id','type');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Time()
    {
        return $this->hasMany('App\Model\Place\AttractionTime', 'attraction', 'ID');
    }

}
