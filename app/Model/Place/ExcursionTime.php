<?php
/**
 * Supun Praneeth 2018.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\Place;

use App\CountryCurrency;
use App\Currency;
use App\Model\Quotation\Quotation;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Place\Attraction
 *
 * @property int $ID
 * @property int $place
 * @property int $duration
 * @property string|null $address
 * @property string|null $description
 * @property string|null $point
 * @property string|null $name
 * @property string|null $operning
 * @property string|null $closing
 * @property int|null $distance
 * @property int|null $time
 * @property float|null $longitude
 * @property float|null $latitude
 * @property int|null $type
 * @property int $en_route
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Place\AttractionRate[] $Rate
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereClosing($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereEnRoute($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereOperning($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction wherePlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction wherePoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Attraction whereUploadId($value)
 * @mixin \Eloquent
 */
class ExcursionTime extends Model
{

    protected $table = 'apple_excursion_time';

    Protected $primaryKey = "ID";


}
