<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Place\AttractionRate
 *
 * @property int $ID
 * @property int $attraction
 * @property int $market
 * @property float $adult
 * @property float $child
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereAdult($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereAttraction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereChild($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereMarket($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\AttractionRate whereUploadId($value)
 * @mixin \Eloquent
 */
class AttractionRate extends Model
{
    use SoftDeletes;
    protected $table = 'apple_attraction_rate';
    // protected $visible = ['ID','attraction','market','adult','child'];


    protected $guarded = 'ID';
}
