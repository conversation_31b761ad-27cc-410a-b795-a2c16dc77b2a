<?php

namespace App\model\Place;

use Illuminate\Database\Eloquent\Model;

/**
 * App\model\place\ExtraRate
 *
 * @property int $ID
 * @property int $city
 * @property int $rate
 * @property int $extra
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereExtra($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereMileage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereUploadId($value)
 * @mixin \Eloquent
 */
class ExtraRate extends Model
{
    protected $table = 'apple_city_extra_rate';
    protected $guarded = [];
    protected $fillable = ['city', 'vehicle', 'rate', 'extra', 'created_at', 'updated_at'];
    protected $hidden = [];
}
