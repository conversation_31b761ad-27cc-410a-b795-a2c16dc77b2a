<?php

namespace App\Model\Place;

use App\CountryCurrency;
use App\Currency;
use App\Model\Quotation\Quotation;
use App\Model\Vehicle\Vehicle;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Place\Excursion
 *
 * @property int $ID
 * @property int $from
 * @property int $to
 * @property string|null $name
 * @property string|null $description
 * @property int $duration
 * @property string|null $operning
 * @property string|null $closing
 * @property int|null $distance
 * @property int|null $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Place\ExcursionRate[] $Rate
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereClosing($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereOperning($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Excursion whereUploadId($value)
 * @mixin \Eloquent
 */
class Excursion extends Model
{
    use SoftDeletes;
    protected $table = 'apple_excursion';
    protected $primaryKey = 'ID';

    var $Error = [];

    /**
     * @param bool $Quotation
     * @param bool $Direct
     * @return array|mixed
     */
    static function getExcursion($Quotation = false, $Direct = false)
    {


        if (session()->has('quotation.rate.attraction.excursion') && session()->has('quotation.excursion') && !$Direct) {

            $FinalCurrency = CountryCurrency::getFinalCurrency(session()->get('quotation'));
//            $AttractionCost = Excursion::currencyConvener(session()->get('quotation.rate.attraction.excursion'), $FinalCurrency);
            $AttractionCost = session()->get('quotation.rate.attraction.excursion');
            $AttrKeyRate = array_unique(array_keys($AttractionCost));
            $AttrKeyDetails = array_unique(flatten(session()->get('quotation.excursion') ?? []));
            if (!empty(array_diff_assoc($AttrKeyDetails, $AttrKeyRate))) {// not empty mean there are diffrenct elemet so they change rates
                return Excursion::getExcursion($Quotation, true);
            }
        } elseif (isset($Quotation['rate']['attraction']['excursion']) && isset($Quotation['excursion'])) {
            $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);

            if($Direct) {
                $AttractionCost = $Quotation['rate']['attraction']['excursion'];
                // return $AttractionCost;
            } else {
                $AttractionCost = Excursion::currencyConvener($Quotation['rate']['attraction']['excursion'],$FinalCurrency);
            }

            $AttrKeyRate = array_unique(array_keys($AttractionCost));
            $AttrKeyDetails = array_unique(flatten($Quotation['excursion']));

            if (!empty(array_diff_assoc($AttrKeyDetails, $AttrKeyRate))) {
                unset($Quotation['rate']['attraction']['excursion']);
                return Excursion::getExcursion($Quotation, true);
            }

        } else {

            if (!$Quotation)
                $Quotation = session()->get('quotation');

            if (!isset($Quotation['excursion']))
                return [];

            $AttractionList = [];
            $AttractionListByCity = $Quotation['excursion'];//get attaction that selected

            foreach ($AttractionListByCity as $CityID => $AttractionArray) {
                foreach ($AttractionArray as $AttractionID) {
                    $AttractionList[] = $AttractionID;
                }
            }

            $Vehicle = new \App\Model\Vehicle\Vehicle();
            #Vehicle
            $VehicleType = $Vehicle->getPaxToVehicle($Quotation, true, "first");//get vehicle vehicle
            if (!$VehicleType)
                return false;
            $VehicleID = $VehicleType->ID;

            $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);
            $AttractionCost = Excursion::getExcursionCost($AttractionList, $Quotation['market'], $Quotation['pax']['cwb'] + $Quotation['pax']['cnb'], $FinalCurrency, $Quotation, $Direct);
        }
        return $AttractionCost;
    }

    /**
     * @param bool $Quotation
     * @param bool $Direct
     * @return array|mixed
     */
    static function getExcursionRatesVal($Quotation = false, $Direct = false)
    {
        if (!$Quotation)
            $Quotation = session()->get('quotation');

        if (!isset($Quotation['excursion']))
            return [];

        $AttractionList = [];
        $AttractionListByCity = $Quotation['excursion'];//get attaction that selected

        foreach ($AttractionListByCity as $CityID => $AttractionArray) {
            foreach ($AttractionArray as $AttractionID) {
                $AttractionList[] = $AttractionID;
            }
        }

        $Vehicle = new \App\Model\Vehicle\Vehicle();
        #Vehicle
        $VehicleType = $Vehicle->getPaxToVehicle($Quotation, true, "first");//get vehicle vehicle
        if (!$VehicleType)
            return false;
        $VehicleID = $VehicleType->ID;

        $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);
        $AttractionCost = Excursion::getExcursionCost($AttractionList, $Quotation['market'], $Quotation['pax']['cwb'] + $Quotation['pax']['cnb'], $FinalCurrency, $Quotation, $Direct);
        
        return $AttractionCost;
    }

    static function getExcursionTime($Quotation = false, $ID) {
        $attraction_time = [];
        if (session()->has('quotation.excursion')) {
            if(isset($Quotation['time']['attraction']['excursion'][(int)$ID])) {
                $attraction_time['time'] = $Quotation['time']['attraction']['excursion'][(int)$ID];
            } else {
                $attraction_time['time'] = ['start'=> 0, 'end'=> 0];
            }
        }
        return $attraction_time;
    }

    static function getExcursionTimeName($ID) {
        if(!isset($ID)) {
            return "N/S";
        }
        $AttractionTimeName = ExcursionTime::where("ID", $ID)->first();
        $AttractionTimeName = Carbon::parse($AttractionTimeName['starting'])->format('g:i A') . " ~ " . Carbon::parse($AttractionTimeName['ending'])->format('g:i A');
        return $AttractionTimeName;
    }

    static function getExcursionStartTime($ID) {
        if(!isset($ID)) {
            return "N/S";
        }
        $ExcursionStartTime = ExcursionTime::where("ID", $ID)->first();
        $ExcursionStartTime = Carbon::parse($ExcursionStartTime['starting']);
        return $ExcursionStartTime;
    }

    /**
     * @param $AttractionCost
     * @param $to
     * @param null $from
     * @return mixed
     */
    static function currencyConvener($AttractionCost, $to, $from = null)
    {
        foreach ($AttractionCost as $attrID => $attrItem) {

            $Attraction = Excursion::find($attrID);

            $AttractionCurrency = ($from ?? CountryCurrency::getPlaceCurrency($Attraction->from, 'attraction'));

            $AttractionCost[$attrID]['adult'] = currency($attrItem['adult'], $AttractionCurrency->code, $to->code, false);

            if (isset($AttractionCost[$attrID]['child']))
                $AttractionCost[$attrID]['child'] = currency($attrItem['child'], $AttractionCurrency->code, $to->code, false);

            $AttractionCost[$attrID]['currency']['from'] = $AttractionCurrency;
            $AttractionCost[$attrID]['currency']['to'] = $to;
        }

        return $AttractionCost;
    }

    /**
     * @param $Quotation
     * @return bool
     */
    function hasRateList($Quotation)
    {

        $Rates = Excursion::getExcursion($Quotation, true);
        $noError = true;

        foreach (($Quotation['excursion'] ?? []) as $Day => $ItemArray) {
            foreach ($ItemArray as $Item) {
                if (!isset($Rates[$Item])) {
                    $this->Error[] = "<i>" . $this->find($Item)->name . '</i>';
                    $noError = false;
                }
            }
        }

        return $noError;

    }


    /**
     * @param $AttractionList
     * @param $Market
     * @param bool $child
     * @param $FinalCurrency
     * @return array
     */

    static function getExcursionCost($AttractionList, $Market, $child, $FinalCurrency, $Quotation, $Direct = false)
    {

        $Market = $Market == 1 || $Market == 3 || $Market == 4 ? 2 : 1;

        $AttractionCost = [];
        foreach ($AttractionList as $AttractionId) {

            $Attraction = Excursion::find($AttractionId);
            if(isset($Attraction)) {
                $place = Place::find($Attraction->from);
                if(isset($place)) {
                    $country = $place->country;
                }
            }

            $Vehicle = new Vehicle();
            #Vehicle
            $VehicleType = $Vehicle->getPaxToVehicle($Quotation, true, "first", $country);//get vehicle vehicle
            if (!$VehicleType)
                return false;
            $VehicleID = $VehicleType->ID;

            $AttractionRate = $Attraction->Rate()
                ->where('market', $Market)
                ->where('vehicle', $VehicleID)
                ->first();

            $AttractionCost[$AttractionId]['adult'] = $AttractionRate ? $AttractionRate->adult : 0;
            $AttractionCost[$AttractionId]['child'] = $AttractionRate ? $child ? $AttractionRate->child : 0 : 0;
            $AttractionCost[$AttractionId]['transfer_rate'] = $AttractionRate ? $AttractionRate->transfer_rate : 0;
            $AttractionCost[$AttractionId]['adult_entrance_rate'] = $AttractionRate ? $AttractionRate->adult_entrance_rate : 0;
            $AttractionCost[$AttractionId]['child_entrance_rate'] = $AttractionRate ? $AttractionRate->child_entrance_rate : 0;
        }

        if($Direct) {
            return $AttractionCost;
        } else {
            return Excursion::currencyConvener($AttractionCost,$FinalCurrency);
        }
    }

    static function getExcursionBreakdown($VehicleID, $ID, $Market) {
        $Attraction = ExcursionRate::where("excursion", '=', $ID)
            ->where("market", "=", $Market)
            ->where("vehicle", "=", $VehicleID)
            ->first();

        return $Attraction;
    }

    /**
     * @param $Quotation
     * @return bool
     */
    static function excursionCurrencyConvertSingle($attrID, $Quotation)
    {
        $AttractionCost = [];
        $Market = $Quotation['market'] == 1 || $Quotation['market'] == 3 || $Quotation['market'] == 4 ? 2 : 1;

        $Attraction = Excursion::find($attrID);

        if(isset($Attraction)) {
            $place = Place::find($Attraction->from);
            if(isset($place)) {
                $country = $place->country;
            }
        }

        if(isset($country)) {
            $Vehicle = new Vehicle();
            #Vehicle
            $VehicleType = $Vehicle->getPaxToVehicle($Quotation, true, "first", $country);//get vehicle vehicle
            if (!$VehicleType)
                return false;
            $VehicleID = $VehicleType->ID;
        }

        if(isset($VehicleID)) {
            $AttractionRate = $Attraction->Rate()
                ->where('market', $Market)
                ->where('vehicle', $VehicleID)
                ->first();
        }

        if(isset($AttractionRate)) {
            $AttractionCost[$attrID]['adult'] = $AttractionRate ? $AttractionRate->adult : 0;
            $AttractionCost[$attrID]['child'] = $AttractionRate ? $AttractionRate->child : 0;

            $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation)->code;

            //check quotation has multiple currency
            $CurrencyList = Quotation::getCurrency($Quotation);
            if (count($CurrencyList) > 1)
                $FinalCurrency = "USD";

            if(isset($Quotation['ch_currency'])) {
                $FinalCurrency = Currency::find($Quotation['ch_currency'])->code;
            }

            $AttractionCurrency = ($from ?? CountryCurrency::getPlaceCurrency($Attraction->from, 'attraction'));

            $AttractionCost[$attrID]['adult'] = currency($AttractionCost[$attrID]['adult'], $AttractionCurrency->code, $FinalCurrency, false);

            if (isset($AttractionCost[$attrID]['child']))
                $AttractionCost[$attrID]['child'] = currency($AttractionCost[$attrID]['child'], $AttractionCurrency->code, $FinalCurrency, false);

            $AttractionCost[$attrID]['currency']['from'] = $AttractionCurrency;
            $AttractionCost[$attrID]['currency']['to'] = $FinalCurrency;
        }

        return $AttractionCost;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\Place\ExcursionRate', 'excursion', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Type()
    {
        return $this->hasOne(AttractionType::class, 'id', 'type')->select('id','type');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Time()
    {
        return $this->hasMany('App\Model\Place\ExcursionTime', 'excursion', 'ID');
    }

}

