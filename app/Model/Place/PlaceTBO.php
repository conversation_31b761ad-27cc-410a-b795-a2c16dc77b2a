<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Place\PlaceTBO
 *
 * @property int $ID
 * @property string $name
 * @property int $country
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceTBO whereUploadId($value)
 * @mixin \Eloquent
 */
class PlaceTBO extends Model
{
    protected $table = 'apple_places_tbo';
}
