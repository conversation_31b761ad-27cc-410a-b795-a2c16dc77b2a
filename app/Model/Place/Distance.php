<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Place\Distance
 *
 * @property int $ID
 * @property int $from
 * @property int $to
 * @property float $distance
 * @property float $time
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Distance whereUploadId($value)
 * @mixin \Eloquent
 */
class Distance extends Model
{
    protected $table = "apple_place_distances";
    protected $fillable = ['ID'];
    protected $primaryKey = "ID";
}
