<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Place\PlaceType
 *
 * @property int $ID
 * @property string $name
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceType whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceType whereUploadId($value)
 * @mixin \Eloquent
 */
class PlaceType extends Model
{
   protected $table = "apple_direction_types";


}
