<?php

namespace App\model\place;

use Illuminate\Database\Eloquent\Model;

/**
 * App\model\Place\ExtraMileage
 *
 * @property int $ID
 * @property int $city
 * @property int $mileage
 * @property int $extra
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereExtra($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereMileage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\model\Place\ExtraMileage whereUploadId($value)
 * @mixin \Eloquent
 */
class ExtraMileage extends Model
{
    protected $table = 'apple_city_extra_mileage';
//    protected $sortable = ['mileage'];
    protected $guarded = [];
    protected $fillable = ['city', 'mileage', 'extra', 'created_at', 'updated_at'];
    protected $hidden = [];
}
