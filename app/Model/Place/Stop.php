<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Place\Stop
 *
 * @property int $ID
 * @property \App\Model\Place\Place $place
 * @property int $country
 * @property int $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop wherePlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Stop whereUploadId($value)
 * @mixin \Eloquent
 */
class Stop extends Model
{
    protected $table = "apple_places_stops";


    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function place()
    {
        return $this->belongsTo('App\Model\Place\Place','ID','place');
    }
}
