<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Place\ExcursionRate
 *
 * @property int $ID
 * @property int $excursion
 * @property int $market
 * @property int $vehicle
 * @property float $adult
 * @property float $child
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereAdult($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereChild($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereExcursion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereMarket($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\ExcursionRate whereVehicle($value)
 * @mixin \Eloquent
 */
class ExcursionRate extends Model
{
    // protected $visible = ['id','market','adult','child','vehicle'];

    use SoftDeletes;
    protected $table = 'apple_excursion_rate';

}
