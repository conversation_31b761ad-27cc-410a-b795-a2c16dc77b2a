<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Place\CityTourRate
 *
 * @property int $ID
 * @property int $tour
 * @property int $market
 * @property int $vehicle
 * @property float $adult
 * @property float $child
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereAdult($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereChild($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereMarket($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereTour($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTourRate whereVehicle($value)
 * @mixin \Eloquent
 */
class CityTourRate extends Model
{
    // protected $visible = ['id','market','adult','child','vehicle'];

    use SoftDeletes;
    protected $table = 'apple_city_tour_rate';

}
