<?php
/**
 * Supun Praneeth 2018.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\Place;

use App\CountryCurrency;
use App\Currency;
use App\Model\Quotation\Quotation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Model\Vehicle\Vehicle;

/**
 * App\Model\Place\CityTour
 *
 * @property int $ID
 * @property int $place
 * @property string|null $name
 * @property string|null $description
 * @property int $duration
 * @property string|null $start_time
 * @property string|null $end_time
 * @property int|null $distance
 * @property int|null $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Place\CityTourRate[] $Rate
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour wherePlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\CityTour whereUploadId($value)
 * @mixin \Eloquent
 */
class CityTour extends Model
{
    use SoftDeletes;
    protected $table = 'apple_city_tour';
    var $Error = [];


    /**
     * @param bool $Quotation
     * @param bool $Direct
     * @return array|mixed
     */
    static function getCityTour($Quotation = false, $Direct = false)
    {

        if (session()->has('quotation.rate.attraction.city_tour') && session()->has('quotation.city_tour') && !$Direct) {

            $FinalCurrency = CountryCurrency::getFinalCurrency(session()->get('quotation'));

//            $AttractionCost = CityTour::currencyConvener(session()->get('quotation.rate.attraction.city_tour'), $FinalCurrency);
            $AttractionCost = session()->get('quotation.rate.attraction.city_tour');
            $AttrKeyRate = array_unique(array_keys($AttractionCost));
            $AttrKeyDetails = array_unique(flatten(session()->get('quotation.city_tour')));


            if (!empty(array_diff_assoc($AttrKeyDetails, $AttrKeyRate))) {// not empty mean there are diffrenct elemet so they change rates
                return CityTour::getCityTour($Quotation, true);
            }

        } elseif (isset($Quotation['rate']['attraction']['city_tour']) && isset($Quotation['city_tour'])) {

            $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);

            if($Direct) {
                $AttractionCost = $Quotation['rate']['attraction']['city_tour'];
                // return $AttractionCost;
            } else {
                $AttractionCost = CityTour::currencyConvener($Quotation['rate']['attraction']['city_tour'], $FinalCurrency);
            }

            $AttrKeyRate = array_unique(array_keys($AttractionCost));
            $AttrKeyDetails = array_unique(flatten($Quotation['city_tour']));

            if (!empty(array_diff_assoc($AttrKeyDetails, $AttrKeyRate))) {
                unset($Quotation['rate']['attraction']['city_tour']);
                return CityTour::getCityTour($Quotation, true);
            }

        } else {
            if (!$Quotation)
                $Quotation = session()->get('quotation');

            if (!isset($Quotation['city_tour']))
                return [];

            $AttractionList = [];
            $AttractionListByCity = $Quotation['city_tour'];//get attraction that selected

            foreach ($AttractionListByCity as $CityID => $AttractionArray) {
                foreach ($AttractionArray as $AttractionID) {
                    $AttractionList[] = $AttractionID;
                }
            }

            $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);
            $AttractionCost = CityTour::getCityTourCost($AttractionList, $Quotation['market'], $Quotation['pax']['cwb'] + $Quotation['pax']['cnb'], $FinalCurrency, $Quotation, $Direct);
        }

        return $AttractionCost;

    }

    /**
     * @param bool $Quotation
     * @param bool $Direct
     * @return array|mixed
     */
    static function getCityTourRatesVal($Quotation = false, $Direct = false)
    {
        if (!$Quotation)
            $Quotation = session()->get('quotation');

        if (!isset($Quotation['city_tour']))
            return [];

        $AttractionList = [];
        $AttractionListByCity = $Quotation['city_tour'];//get attraction that selected

        foreach ($AttractionListByCity as $CityID => $AttractionArray) {
            foreach ($AttractionArray as $AttractionID) {
                $AttractionList[] = $AttractionID;
            }
        }

        $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);
        $AttractionCost = CityTour::getCityTourCost($AttractionList, $Quotation['market'], $Quotation['pax']['cwb'] + $Quotation['pax']['cnb'], $FinalCurrency, $Quotation, $Direct);
        
        return $AttractionCost;
    }

    static function getCitytourTime($Quotation = false, $ID) {
        $attraction_time = [];
        if (session()->has('quotation.city_tour')) {
            if(isset($Quotation['time']['attraction']['city_tour'][(int)$ID])) {
                $attraction_time['time'] = $Quotation['time']['attraction']['city_tour'][(int)$ID];
            } else {
                $attraction_time['time'] = ['start'=> 0, 'end'=> 0];
            }
        }
        return $attraction_time;
    }

    static function getCitytourTimeName($ID) {
        if(!isset($ID)) {
            return "N/S";
        }
        $AttractionTimeName = CitytourTime::where("ID", $ID)->first();
        $AttractionTimeName = Carbon::parse($AttractionTimeName['starting'])->format('g:i A') . " ~ " . Carbon::parse($AttractionTimeName['ending'])->format('g:i A');
        return $AttractionTimeName;
    }

    static function getCitytourStartTime($ID) {
        if(!isset($ID)) {
            return "N/S";
        }
        $CitytourStartTime = CitytourTime::where("ID", $ID)->first();
        $CitytourStartTime = Carbon::parse($CitytourStartTime['starting']);
        return $CitytourStartTime;
    }

    /**
     * @param $AttractionCost
     * @param $to
     * @param null $from
     * @return mixed
     */
    static function currencyConvener($AttractionCost, $to, $from = null)
    {
        foreach ($AttractionCost as $attrID => $attrItem) {

            $Attraction = CityTour::find($attrID);
            $AttractionCurrency = ($from ?? CountryCurrency::getPlaceCurrency($Attraction->place, 'attraction'));

            $AttractionCost[$attrID]['adult'] = currency($attrItem['adult'], $AttractionCurrency->code, $to->code, false);

            if (isset($AttractionCost[$attrID]['child']))
                $AttractionCost[$attrID]['child'] = currency($attrItem['child'], $AttractionCurrency->code, $to->code, false);

            $AttractionCost[$attrID]['currency']['from'] = $AttractionCurrency;
            $AttractionCost[$attrID]['currency']['to'] = $to;
        }

        return $AttractionCost;
    }

    /**
     * @param $AttractionList
     * @param $Market
     * @param bool $child
     * @param $FinalCurrency
     * @return array
     */

    static function getCityTourCost($AttractionList, $Market, $child, $FinalCurrency, $Quotation, $Direct = false)
    {
        $Market = $Market == 1 || $Market == 3 || $Market == 4 ? 2 : 1;

        $AttractionCost = [];
        foreach ($AttractionList as $AttractionId) {

            $Attraction = CityTour::find($AttractionId);

            if(isset($Attraction)) {
                $place = Place::find($Attraction->place);
                if(isset($place)) {
                    $country = $place->country;
                }
            }

            $Vehicle = new Vehicle();
            #Vehicle
            $VehicleType = $Vehicle->getPaxToVehicle($Quotation, true, "first", $country);//get vehicle vehicle
            if (!$VehicleType)
                return false;
            $VehicleID = $VehicleType->ID;

            $AttractionRate = $Attraction->Rate()
                ->where('market', $Market)
                ->where('vehicle', $VehicleID)
                ->first();

            $AttractionCost[$AttractionId]['adult'] = $AttractionRate ? $AttractionRate->adult : 0;
            $AttractionCost[$AttractionId]['child'] = $AttractionRate ? $child ? $AttractionRate->child : 0 : 0;
            $AttractionCost[$AttractionId]['transfer_rate'] = $AttractionRate ? $AttractionRate->transfer_rate : 0;
            $AttractionCost[$AttractionId]['adult_entrance_rate'] = $AttractionRate ? $AttractionRate->adult_entrance_rate : 0;
            $AttractionCost[$AttractionId]['child_entrance_rate'] = $AttractionRate ? $AttractionRate->child_entrance_rate : 0;
        }

        if($Direct) {
            return $AttractionCost;
        } else {
            return CityTour::currencyConvener($AttractionCost, $FinalCurrency);
        }
    }

    static function getCitytourBreakdown($VehicleID, $ID, $Market) {
        $Attraction = CityTourRate::where("tour", '=', $ID)
            ->where("market", "=", $Market)
            ->where("vehicle", "=", $VehicleID)
            ->first();

        return $Attraction;
    }

    /**
     * @param $Quotation
     * @return bool
     */
    function hasRateList($Quotation)
    {
        $Rates = $this->getCityTour($Quotation, true);
        $noError = true;

        foreach (($Quotation['city_tour'] ?? []) as $Day => $ItemArray) {
            foreach ($ItemArray as $Item) {
                if (!isset($Rates[$Item])) {
                    $this->Error[] = "<i>" . $this->find($Item)->name . '</i>';
                    $noError = false;
                }
            }
        }

        return $noError;

    }

    /**
     * @param $Quotation
     * @return bool
     */
    static function citytourCurrencyConvertSingle($attrID, $Quotation)
    {
        $AttractionCost = [];
        $Market = $Quotation['market'] == 1 || $Quotation['market'] == 3 || $Quotation['market'] == 4 ? 2 : 1;

        $Attraction = CityTour::find($attrID);

        if(isset($Attraction)) {
            $place = Place::find($Attraction->place);
            if(isset($place)) {
                $country = $place->country;
            }
        }

        $Vehicle = new Vehicle();
        #Vehicle
        $VehicleType = $Vehicle->getPaxToVehicle($Quotation, true, "first", $country);//get vehicle vehicle
        
        if (!$VehicleType)
            return false;
        $VehicleID = $VehicleType->ID;

        $AttractionRate = $Attraction->Rate()
            ->where('market', $Market)
            ->where('vehicle', $VehicleID)
            ->first();

        $AttractionCost[$attrID]['adult'] = $AttractionRate ? $AttractionRate->adult : 0;
        $AttractionCost[$attrID]['child'] = $AttractionRate ? $AttractionRate->child : 0;

        $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation)->code;

        //check quotation has multiple currency
        $CurrencyList = Quotation::getCurrency($Quotation);
        if (count($CurrencyList) > 1)
            $FinalCurrency = "USD";

        if(isset($Quotation['ch_currency'])) {
            $FinalCurrency = Currency::find($Quotation['ch_currency'])->code;
        }

        $AttractionCurrency = ($from ?? CountryCurrency::getPlaceCurrency($Attraction->place, 'attraction'));

        $AttractionCost[$attrID]['adult'] = currency($AttractionCost[$attrID]['adult'], $AttractionCurrency->code, $FinalCurrency, false);

        if (isset($AttractionCost[$attrID]['child']))
            $AttractionCost[$attrID]['child'] = currency($AttractionCost[$attrID]['child'], $AttractionCurrency->code, $FinalCurrency, false);

        $AttractionCost[$attrID]['currency']['from'] = $AttractionCurrency;
        $AttractionCost[$attrID]['currency']['to'] = $FinalCurrency;

        return $AttractionCost;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\Place\CityTourRate', 'tour', 'ID');
    }
    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Type()
    {
        return $this->hasOne(AttractionType::class, 'id', 'type')->select('id','type');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Time()
    {
        return $this->hasMany('App\Model\Place\CitytourTime', 'tour', 'ID');
    }

}

