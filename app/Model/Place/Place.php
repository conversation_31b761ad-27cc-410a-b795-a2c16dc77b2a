<?php

namespace App\Model\Place;

use App\Model\Quotation\Quotation;
use App\Model\Tool\Tool;
use App\Model\Vehicle\SubVehicleCity;
use App\Model\Vehicle\Vehicle;
use DB;
use Http\Adapter\Guzzle6\Client;
use Http\Message\MessageFactory\GuzzleMessageFactory;
use Illuminate\Database\Eloquent\Model;
use Ivory\GoogleMap\Service\Base\Location\AddressLocation;
use Ivory\GoogleMap\Service\DistanceMatrix\DistanceMatrixService;
use Ivory\GoogleMap\Service\DistanceMatrix\Request\DistanceMatrixRequest;
use Polyline;

/**
 * App\Model\Place\Place
 *
 * @property int $ID
 * @property string $name
 * @property string|null $description
 * @property int $status
 * @property int|null $popularity
 * @property float|null $longitude
 * @property float|null $latitude
 * @property int $type
 * @property int $country
 * @property string|null $A2
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Place\Stop[] $stop
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereA2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place wherePopularity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\Place whereUploadId($value)
 * @mixin \Eloquent
 */
class Place extends Model
{

    /**
     * @var string
     */
    protected $table = 'apple_places';
    /**
     * @var array
     */
    protected $fillable = ['popularity'];
    /**
     * @var string
     */
    protected $primaryKey = 'ID';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function country()
    {
        return $this->hasOne('App\Model\Place\Place', 'ID', 'country');
    }

    public function FindCountry()
    {
        return $this->hasOne('App\Model\Place\Place', 'ID', 'country');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function currency()
    {
        return $this->hasOne('App\Currency', 'id', 'currency');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function type()
    {
        return $this->hasOne(PlaceCityType::class, 'id', 'type');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function SubVehicleCity()
    {
        return $this->hasMany(SubVehicleCity::class, 'city', 'ID');
    }

    /**
     * @param $ID
     * @return bool|mixed
     */
    function getPlace($ID)
    {
        $Place = DB::table('apple_places')->where("ID", '=', $ID)->get();

        if (!empty($Place))
            return $Place[0];
        else
            return false;

    }

    /**
     * @param $ID
     * @return bool|mixed
     */
    static function getPlaceBYID($ID)
    {
        $Place = DB::table('apple_places')->where("ID", '=', $ID)->get();

        if (!empty($Place))
            return $Place[0];
        else
            return false;

    }

    /**
     * @return \Illuminate\Support\Collection
     */
    static function getCityList()
    {
        return Place::where('type', 1)->get();
    }


    /**
     * @param $name
     * @return \Illuminate\Support\Collection
     */
    function searchPlace($name)
    {
        return DB::table('apple_places')
            ->where("name", 'LIKE', '%' . $name . "%")
            ->select("name as label", "ID as value")
            ->get();
    }

    /**
     * @param $Limit
     * @return \Illuminate\Support\Collection
     */
    function getPlaceList($Limit)
    {
        return DB::table('apple_places')->take($Limit)->get();

    }


    /**
     * @return \Illuminate\Support\Collection
     */
    function getDefaultPlacesJson()
    {
        $Country = \Session::get('quotation.country');
        #$places = DB::table('apple_places')->join('apple_places_default', 'apple_places.ID', '=', 'apple_places_default.place')->select('apple_places.*')->get();
        $places = $this->whereNotNull('longitude')->where('status', '=', 1);

        if ($Country) {
            $places = $places->where('country', $Country);
        }
        return $places->get();
    }

    /**
     * @param int $Country
     * @return \Illuminate\Support\Collection
     */
    function getAirportPlaces($Country = 1)
    {

        $places = DB::table('apple_places')->join('apple_places_stops', 'apple_places.ID', '=', 'apple_places_stops.place')->select('apple_places.*')->get();
        return $places;

    }

    /**
     * @return Place|mixed
     */
    function getDefaultAirport()
    {

        if (\Session::has('quotation.country')) {
            $AirportID = DB::table('apple_places_stops')->where('country', \Session::get('quotation.country'))->select('place')->first();
            return Place::find($AirportID->place);

        } else
            return Place::find(1);
    }

    /**
     * @param $from
     * @param $to
     * @return Distance|bool|Model|null|object
     */
    function getPlaceDistance($from, $to)
    {
        if ($from != $to) {
            $place = Distance::where('from', '=', $from)->where('to', '=', $to)->first();

            if (!$place || !$place->distance) {

                $PlaceItem1 = Place::find($from);
                $PlaceItem2 = Place::find($to);

                $distanceMatrix = new DistanceMatrixService(new Client(), new GuzzleMessageFactory());

                $request = new DistanceMatrixRequest(
                    [new AddressLocation($PlaceItem1->name . ", " . $PlaceItem1->country()->first()->name)],
                    [new AddressLocation($PlaceItem2->name . ", " . $PlaceItem2->country()->first()->name)]
                );

                $response = $distanceMatrix->process($request);

                $DistanceValue = 0;
                $DurationValue = 0;

                if (isset($response->getRows()[0])) {
                    $responseElement = $response->getRows()[0]->getElements()[0];

                    if ($responseElement->getDistance()) {
                        $DistanceValue = $responseElement->getDistance()->getValue();
                        $DurationValue = $responseElement->getDuration()->getValue();
                    }
                }

                if ($place)//if distance already exist but disntance is 0
                    $Distance = Distance::firstOrNew(['ID' => $place->ID]);
                else
                    $Distance = new Distance();

                $Distance->from = $from;
                $Distance->to = $to;
                $Distance->distance = $DistanceValue;
                $Distance->time = $DurationValue;
                $Distance->save();


                return Distance::where('from', '=', $from)->where('to', '=', $to)->first();


            } else {
                return $place;
            }
        } else
            return false;


    }

    /**
     * @param $lat1
     * @param $lon1
     * @param $lat2
     * @param $lon2
     * @return float|int
     */
    function getBetweenDistance($lat1, $lon1, $lat2, $lon2)
    {

        $pi80 = M_PI / 180;
        $lat1 *= $pi80;
        $lon1 *= $pi80;
        $lat2 *= $pi80;
        $lon2 *= $pi80;

        $r = 6372.797; // mean radius of Earth in km
        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;
        $a = sin($dlat / 2) * sin($dlat / 2) + cos($lat1) * cos($lat2) * sin($dlon / 2) * sin($dlon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $km = $r * $c;

        //echo '<br/>'.$km;
        return $km;
    }

    /**
     * @param $longitude
     * @param $latitude
     * @param $Country
     * @return bool|mixed
     */
    function getNearestPlace($longitude, $latitude, $Country)
    {


        $Place = DB::table('apple_places')->select(DB::raw("( 3959 * acos( cos( radians($latitude) ) * cos( radians( latitude) ) 
										   * cos( radians(longitude) - radians($longitude)) + sin(radians($latitude)) 
										   * sin( radians(latitude)))) as difference,
								  apple_places.*"))
            //->where(DB::raw("round(longitude)") ,"=", DB::raw("round($longitude)"))
            //->where(DB::raw("round(latitude)") ,"=", DB::raw("round($latitude)"))
            ->where('type', '1')
            #->where('country', $Country)
            ->where('status', '1')
            ->whereNotNull('longitude')
            ->whereNotNull('latitude')
            ->orderBy("difference")->limit(1)->get();
        if (!empty($Place))
            return $Place[0];
        else
            return false;

    }

    /**
     * @param $PlaceID
     * @return Place[]|bool|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     */
    function getNearestStop($PlaceID)
    {

        $Place = Place::find($PlaceID);
        $Country = $Place->country;
        $limit = 10;
        if($PlaceID == 9451 ) {
            $limit = 70;
        }

        $NearestStop = Place::has("stop")->with('stop')
            ->select(DB::raw("( 3959 * acos( cos( radians($Place->latitude) ) * cos( radians( latitude) )
										   * cos( radians(longitude) - radians($Place->longitude)) + sin(radians($Place->latitude)) 
										   * sin( radians(latitude)))) as difference"), $this->table . ".*")
            ->where('country', $Country)
            ->orderBy("difference")
            ->limit($limit)
            ->get();


        if (!empty($NearestStop))
            return $NearestStop;
        else
            return false;

    }

    /**
     * @param $longitude
     * @param $latitude
     * @param float $AreaDistance
     * @return bool|\Illuminate\Support\Collection
     */
    function getAreaPlaces($longitude, $latitude, $AreaDistance = 0.3)
    {


        $Places = DB::table('apple_places')->where("longitude", "<", $longitude + $AreaDistance)->where("latitude", "<", $latitude + $AreaDistance)->where("longitude", ">", $longitude - $AreaDistance)->where("latitude", ">", $latitude - $AreaDistance)->limit(10)->get();

        if (!empty($Places))
            return $Places;
        else
            return false;

    }

    /**
     * @param $Text
     * @return array|bool
     */
    function isCorditante($Text)
    {

        if (substr_count($Text, ",") == 1) {

            $lnglat = explode(",", $Text);
            $lat = trim($lnglat[0]);
            $lng = trim($lnglat[1]);

            if (floatval($lng) && floatval($lat)) {

                return array("lng" => $lng, "lat" => $lat);

            } else
                return false;

        }

        return false;

    }


    /**
     * @param $Indexes
     * @param $PlaceArray
     * @return array
     */
    function getCombinedMarkerPlace($Indexes, $PlaceArray)
    {

        $ReturnArray = array();

        foreach ($Indexes as $key => $index) {

            $ReturnArray[$index] = $PlaceArray[$key];
        }

        return $ReturnArray;
    }

    /**
     * @param $Indexes
     * @param $PlaceArray
     * @return array
     */
    function getIndexToPlaces($Indexes, $PlaceArray)
    {

        $ReturnArray = array();
        foreach ($Indexes as $index) {

            $ReturnArray[] = $PlaceArray[$index];
        }

        return $ReturnArray;
    }


    /**
     * @param $Places
     * @param $Indexes
     * @return array
     */
    function getShortestPath($Places, $Indexes)
    {

        $FirstEndAirports = $this->isFirstLastAirport($Places);
        $numAirports = $this->getNumberOfAirports($Places);

        if ($numAirports > 2 || !$FirstEndAirports)//only sri lanka
            return ["status" => false];


        $PickupStop = reset($Indexes);
        $DropOffStop = end($Indexes);


        $Tool = new Tool();
        $PlaceMarkerPermutation = $Tool->getPermutaionList($Indexes);//get place markers permutation
        $PlaceIndex = $this->getCombinedMarkerPlace($Indexes, $Places);//set colombo last
        if(in_array(10, $Places)) {
            $PlaceMarkerPermutation = $Tool->setPlaceSpeiclPlace($PlaceMarkerPermutation, 'last', $PlaceIndex, 10);//get place markers permutation
        }
        $PlaceMarkerPermutation = $Tool->setPlaceAirportFirstLast($PlaceMarkerPermutation, $PickupStop, $DropOffStop);


        $MinDistance = false;
        $SameDistance = [];
        $PlaceBranchMin = false;
        $EachDetails = [];//each place between details
        $MinIndexBrace = false;

        foreach ($PlaceMarkerPermutation as $IndexedBranch) {

            if (in_array(-1, $IndexedBranch) && reset($IndexedBranch) != -1)
                continue;

            if (in_array(-2, $IndexedBranch) && end($IndexedBranch) != -2)
                continue;

            $PlaceBranch = $this->getIndexToPlaces($IndexedBranch, $PlaceIndex);
            $CurrentDistance = $this->getPathDistance($PlaceBranch);

            if (!$MinDistance || $MinDistance > $CurrentDistance['disatnce']) {# min distance
                $MinDistance = $CurrentDistance['disatnce'];

                #rmover airport if it's set
                $tempBranch = $PlaceBranch;

                if (in_array(-1, $IndexedBranch)) {
                    array_shift($tempBranch);
                    array_shift($IndexedBranch);
                }

                if (in_array(-2, $IndexedBranch)) {
                    array_pop($tempBranch);
                    array_pop($IndexedBranch);
                }

                $PlaceBranchMin = $tempBranch;
                $EachDetails = $CurrentDistance['each_details'];
                $MinIndexBrace = $IndexedBranch;
                $SameDistance = array();
            } elseif ($MinDistance && $MinDistance == $CurrentDistance['disatnce']) {# same distance

                $tempBranch = array_splice($PlaceBranch, 0, 1);
                $tempBranch = array_pop($tempBranch);

                $SameDistance[] = [
                    "distance" => $CurrentDistance,
                    "path" => $tempBranch,
                    "index" => $IndexedBranch
                ];

                $EachDetails = $CurrentDistance['each_details'];

            }
        }


        return array(
            "path" => $PlaceBranchMin,
            "index" => $MinIndexBrace,
            'distance' => $MinDistance,
            'distance_formatted' => formatDistance($MinDistance),
            'same_paths' => $SameDistance,
            'each_details' => $EachDetails
        );


    }

    /**
     * @param $Places
     * @return bool
     */
    function isFirstLastAirport($Places)
    {
        return (Place::find($Places[0])->stop()->first() && Place::find(end($Places))->stop()->first());
    }

    /**
     * @param $Places
     * @return int
     */
    function getNumberOfAirports($Places)
    {
        $num = 0;
        foreach ($Places as $PlaceItem)
            if (Place::find($PlaceItem)->stop()->first()) {
                $num++;
            }
        return $num;
    }


    /**
     * @param $From
     * @param $To
     * @return array
     */
    function getDirectionGoogle($From, $To)
    {
        ini_set('max_execution_time', 0);


        $FromDetails = Place::find($From);
        $ToDetails = Place::find($To);


        $Json = json_decode(file_get_contents("https://maps.googleapis.com/maps/api/directions/json?alternatives=false&origin=$FromDetails->latitude+$FromDetails->longitude&destination=$ToDetails->latitude+$ToDetails->longitude&key=" . env('GOOGLE_API_KEY')));
        $DirectionArray = array();

        // sd("https://maps.googleapis.com/maps/api/directions/json?alternatives=false&origin=$FromDetails->latitude+$FromDetails->longitude&destination=$ToDetails->latitude+$ToDetails->longitude&key=" . env('GOOGLE_API_KEY'));

        foreach ($Json->routes as $route) {
            foreach ($route->legs as $leg) {
                foreach ($leg->steps as $step) {
                    $DirectionArray[] = $step;
                }
            }

        }

        return $DirectionArray;

    }

    /**
     * @param $lat1
     * @param $lon1
     * @param $lat2
     * @param $lon2
     * @return array
     */
    function getCurveLocation($lat1, $lon1, $lat2, $lon2)
    {


        $MidPoint = $this->getMidPoint($lat1, $lon1, $lat2, $lon2);
        $Distance = $this->getTwoLocationDistance($lat1, $lon1, $lat2, $lon2);

        return $MidPoint;


        $lat = $MidPoint[0];
        $long = $MidPoint[1];
        $meters = $Distance / 2; //Number of meters to calculate coords for north/south/east/west

        $equator_circumference = 6371000; //meters
        $polar_circumference = 6356800; //meters

        $m_per_deg_long = 360 / $polar_circumference;

        $rad_lat = ($lat * M_PI / 180); //convert to radians, cosine takes a radian argument and not a degree argument
        $m_per_deg_lat = 360 / (cos($rad_lat) * $equator_circumference);

        $deg_diff_long = $meters * $m_per_deg_long;  //Number of degrees latitude as you move north/south along the line of longitude
        $deg_diff_lat = $meters * $m_per_deg_lat; //Number of degrees longitude as you move east/west along the line of latitude

        //changing north/south moves along longitude and alters latitudinal coordinates by $meters * meters per degree longitude, moving east/west moves along latitude and changes longitudinal coordinates in much the same way.

        $coordinates['north']['lat'] = $lat + $deg_diff_long;
        $coordinates['north']['long'] = $long;
        $coordinates['south']['lat'] = $lat - $deg_diff_long;
        $coordinates['south']['long'] = $long;

        $coordinates['east']['lat'] = $lat;
        $coordinates['east']['long'] = $long + $deg_diff_lat;  //Might need to swith the long equations for these two depending on whether coordinates are east or west of the prime meridian
        $coordinates['west']['lat'] = $lat;
        $coordinates['west']['long'] = $long - $deg_diff_lat;


        $MidPoint[0] = $coordinates['east']['lat'];
        $MidPoint[1] = $coordinates['east']['long'];


        return $MidPoint;

    }

    /**
     * @param $lat1
     * @param $lon1
     * @param $lat2
     * @param $lon2
     * @return int
     */
    function getRhumbLineBearing($lat1, $lon1, $lat2, $lon2)
    {
        //difference in longitudinal coordinates
        $dLon = deg2rad($lon2) - deg2rad($lon1);

        //difference in the phi of latitudinal coordinates
        $dPhi = log(tan(deg2rad($lat2) / 2 + pi() / 4) / tan(deg2rad($lat1) / 2 + pi() / 4));

        //we need to recalculate $dLon if it is greater than pi
        if (abs($dLon) > pi()) {
            if ($dLon > 0) {
                $dLon = (2 * pi() - $dLon) * -1;
            } else {
                $dLon = 2 * pi() + $dLon;
            }
        }
        //return the angle, normalized
        return (rad2deg(atan2($dLon, $dPhi)) + 360) % 360;
    }

    /**
     * @param $lat1
     * @param $lon1
     * @param $lat2
     * @param $lon2
     * @param string $unit
     * @return float
     */
    function getTwoLocationDistance($lat1, $lon1, $lat2, $lon2, $unit = 'M')
    {

        $theta = $lon1 - $lon2;
        $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $dist = acos($dist);
        $dist = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;
        $unit = strtoupper($unit);

        if ($unit == "K") {
            return ($miles * 1.609344);
        } else if ($unit == "N") {
            return ($miles * 0.8684);
        } else if ($unit == "M") {
            return ($miles * 1609.34);
        } else {
            return $miles;
        }
    }


    /**
     * @param $lat1
     * @param $lng1
     * @param $lat2
     * @param $lng2
     * @return array
     */
    function getMidPoint($lat1, $lng1, $lat2, $lng2)
    {

        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        $dlng = $lng2 - $lng1;
        $Bx = cos($lat2) * cos($dlng);
        $By = cos($lat2) * sin($dlng);
        $lat3 = atan2(sin($lat1) + sin($lat2),
            sqrt((cos($lat1) + $Bx) * (cos($lat1) + $Bx) + $By * $By));
        $lng3 = $lng1 + atan2($By, (cos($lat1) + $Bx));
        $pi = pi();
        return [($lat3 * 180) / $pi, ($lng3 * 180) / $pi];
    }

    /**
     * @param $RouteID
     * @param $TravelBy
     * @return array|bool|\Illuminate\Support\Collection
     */
    function getDirectionDB($RouteID, $TravelBy)
    {


        $Direction = DB::table('apple_place_direction')->where('distance_id', '=', $RouteID)->get();


        if ($Direction->isNotEmpty()) {


            if ($TravelBy == 1) {//stop type is 1 mean flights
                $FlightTravel[0] = $Direction->first();


                $Mid = $this->getCurveLocation($Direction->first()->start_location_lat, $Direction->first()->start_location_lng, $Direction->first()->end_location_lat, $Direction->first()->end_location_lng);


                $FlightTravel[0]->polyline = Polyline::encode([
                    [$Direction->first()->start_location_lat, $Direction->first()->start_location_lng], $Mid,
                    [$Direction->last()->end_location_lat, $Direction->last()->end_location_lng]
                ]);


                return $FlightTravel;

            }//if not send normal direction by vehicle
            return $Direction;

        } elseif ($TravelBy == 1) {# direction not found but flight


            $Distance = Distance::where("ID", $RouteID)->first();

            $Mid = $this->getCurveLocation(Place::where("ID", $Distance->from)->first()->latitude, Place::where("ID", $Distance->from)->first()->longitude, Place::where("ID", $Distance->to)->first()->latitude, Place::where("ID", $Distance->to)->first()->longitude);


            $FlightTravel = [];
            $FlightTravel[0] = (object)$FlightTravel;
            $FlightTravel[0]->polyline = Polyline::encode([
                [Place::where("ID", $Distance->from)->first()->latitude, Place::where("ID", $Distance->from)->first()->longitude], $Mid,
                [Place::where("ID", $Distance->to)->first()->latitude, Place::where("ID", $Distance->to)->first()->longitude]
            ]);

            return $FlightTravel;

        } else {
            return false;
        }

    }

    /**
     * @param $DistanceID
     * @param $DirectionArray
     */
    function setDirection($DistanceID, $DirectionArray)
    {

        foreach ($DirectionArray as $Step => $StepDirection) {


            DB::table('apple_place_direction')->insert(
                [
                    'distance_id' => $DistanceID,
                    'step' => $Step,
                    'distance' => $StepDirection->distance->value,
                    'duration' => $StepDirection->duration->value,
                    'polyline' => $StepDirection->polyline->points,
                    'travel_mode' => $StepDirection->travel_mode,
                    'instructions' => $StepDirection->html_instructions,

                    'start_location_lat' => $StepDirection->start_location->lat,
                    'start_location_lng' => $StepDirection->start_location->lng,

                    'end_location_lat' => $StepDirection->end_location->lat,
                    'end_location_lng' => $StepDirection->end_location->lng

                ]
            );
        }
    }

    /**
     * @param $Places
     * @param $TravelType
     * @return array|bool
     */
    function getDirection($Places, $TravelType)
    {

        if (empty($Places))
            return false;

        $RoutePareArray = [];
        $RoutePareTypeArray = [];
        $LastPlace = false;
        $LastType = false;


        foreach ($Places as $Index => $PlaceID) { // make pares

            if (!$LastPlace) { //if its first time
                $LastPlace = $PlaceID;
                $LastType = $TravelType[$Index];
                continue;
            }
            $RoutePareArray[] = ["from" => $LastPlace, "to" => $PlaceID];


            if ($TravelType[$Index] == 2 && $LastType == 3)
                $RoutePareTypeArray[] = Stop::where('place', $LastPlace)->first()->type;
            else
                $RoutePareTypeArray[] = 0;


            $LastPlace = $PlaceID;
            $LastType = $TravelType[$Index];

        }
        $DirectionArray = array();


        foreach ($RoutePareArray as $Index => $PairedCity) {


            $Distance = $this->getPlaceDistance($PairedCity['from'], $PairedCity['to']);


            if ($Distance) {

                $currentDBDirection['direction'] = $this->getDirectionDB($Distance->ID, $RoutePareTypeArray[$Index]); //check distance from database;

                if (empty($currentDBDirection['direction'])) { //check direction is exist

                    $GoogleDirection = $this->getDirectionGoogle($PairedCity['from'], $PairedCity['to']); //if not get it form google
                    $this->setDirection($Distance->ID, $GoogleDirection); //save it in db

                }


                $currentDBDirection['direction'] = $this->getDirectionDB($Distance->ID, $RoutePareTypeArray[$Index]); //check again
                if (empty($currentDBDirection['direction'])) { //check direction is exist
                    $currentDBDirection['direction'] = [];
                }

                $currentDBDirection['travel_by'] = $RoutePareTypeArray[$Index];
                $DirectionArray[] = $currentDBDirection;


            } else {
                continue;
            }
        }


        return $DirectionArray;

    }

    /**
     * @param $Places
     * @param $TravelType
     * @return array|bool
     */
    function getDirectionPolyline($Places, $TravelType)
    {

        if (empty($Places))
            return false;

        $Direction = $this->getDirection($Places, $TravelType);
        $Polyline = array();


        foreach ($Direction as $DirectionItem) {

            foreach ($DirectionItem['direction'] as $DirectionSteps) {
                $Polyline[] = ['polyline' => $DirectionSteps->polyline, 'travel_by' => $DirectionItem['travel_by']];
            }

        }


        return $Polyline;

    }


    /**
     * @param $PlaceList
     * @return array
     */
    function getPathDuration($PlaceList)
    {


        $LastPlace = false; //store last city  in the loop
        $Duration = 0;
        $EachDetails = array();

        foreach ($PlaceList as $CurrentPlace) {
            if ($LastPlace) {
                $DisatnceRow = @$this->getPlaceDistance($CurrentPlace, $LastPlace);

                if ($DisatnceRow) {
                    $Duration += $DisatnceRow->time;
                    $EachDetails[$CurrentPlace][$LastPlace] = $DisatnceRow;
                }
            }

            $LastPlace = $CurrentPlace;
        }


        return ['duration' => $Duration, 'each_details' => $EachDetails];

    }

    /**
     * @param $PlaceList
     * @return array
     */
    function getPathPair($PlaceList, $PlaceType=[])
    {


        $LastPlace = false;
        $PathPairArray = array();

        foreach ($PlaceList as $key => $CurrentPlace) {
            if ($LastPlace) {
                if($PlaceType[$key-1] != 3) {
                    $PathPairArray[] = ['from' => $LastPlace, 'to' => $CurrentPlace];
                }
            }

            $LastPlace = $CurrentPlace;
        }
        return $PathPairArray;

    }

    /**
     * @param $PlaceList
     * @return array
     */
    function getPathDistance($PlaceList)
    {

        $LastPlace = false; //store last city  in the loop
        $distance = 0;
        $Time = 0;
        $EachDetails = array();


        foreach ($PlaceList as $CurrentPlace) {

            if ($LastPlace) {
                $DistanceRow = $this->getPlaceDistance($LastPlace, $CurrentPlace);

                if ($DistanceRow) {

                    $distance += $DistanceRow->distance;
                    $Time += $DistanceRow->time;
                    $EachDetails[$CurrentPlace][$LastPlace] = $DistanceRow;

                }
            }

            $LastPlace = $CurrentPlace;
        }


        return ['disatnce' => $distance, 'time' => $Time, 'each_details' => $EachDetails];

    }

    /**
     * @param $QuotationArray
     * @return int|mixed
     */
    function getExtraMileage($QuotationArray)
    {


        $Quotation = new Quotation();
        $DaysDetail = $Quotation->getTourDaysDetail($QuotationArray);

        $Mileage = 0;
        $LastPlace = false;

        unset($DaysDetail[count($DaysDetail)]);

        foreach ($DaysDetail as $DayItem) {

            if ($LastPlace != $DayItem['place']) {
                $Mileage += isset(ExtraMileage::where("city", $DayItem['place'])->first()->mileage) ? ExtraMileage::where("city", $DayItem['place'])->first()->mileage : 0;

            } else {
                $Mileage += isset(ExtraMileage::where("city", $DayItem['place'])->first()->extra) ? ExtraMileage::where("city", $DayItem['place'])->first()->extra : 0;
            }

            $LastPlace = $DayItem['place'];
        }

        return $Mileage;
    }

    /**
     * @param $QuotationArray
     * @return int|mixed
     */
    function getExtraRate($QuotationArray)
    {
        $Vehicle = new Vehicle();
        $VehicleType = $Vehicle->getPaxToVehicle($QuotationArray,true, "", $QuotationArray["country"]);
        // dd($VehicleType);
        if (!empty($VehicleType)){
            $VehicleID = $VehicleType->ID;
        } else {
            $VehicleID = '-';
        }

        $Quotation = new Quotation();
        $DaysDetail = $Quotation->getTourDaysDetail($QuotationArray);

        $Rate = 0;
        $LastPlace = false;
        $nights = 0;

        unset($DaysDetail[count($DaysDetail)]);

        foreach ($DaysDetail as $day => $DayItem) {

            if ($LastPlace != $DayItem['place']) {
                $Rate += isset(ExtraRate::where("city", $DayItem['place'])->where("vehicle", $VehicleID)->first()->rate_first) ? ExtraRate::where("city", $DayItem['place'])->where("vehicle", $VehicleID)->first()->rate_first : 0;
                if($QuotationArray["tour_type"] == 6) {
                    $nights += $QuotationArray["night"][$DayItem["index"]]??0;
                } else {
                    $nights += $QuotationArray["hotel"][$DayItem["index"]]["nights"]??0;
                }
            } else {
                if($day == $nights) {
                    $Rate += isset(ExtraRate::where("city", $DayItem['place'])->where("vehicle", $VehicleID)->first()->rate_last) ? ExtraRate::where("city", $DayItem['place'])->where("vehicle", $VehicleID)->first()->rate_last : 0;
                } else {
                    $Rate += isset(ExtraRate::where("city", $DayItem['place'])->where("vehicle", $VehicleID)->first()->extra) ? ExtraRate::where("city", $DayItem['place'])->where("vehicle", $VehicleID)->first()->extra : 0;
                }
            }

            $LastPlace = $DayItem['place'];
        }

        return $Rate;
    }

    /**
     * @return int
     */
    function getAirportMileage($country = false, $QuotationArray)
    {
        if(in_array(2, $QuotationArray["place_type"])) {
            if ($country == 256) {
                return 120;
            } else {
                return 80;
            }
        }
    }

    /**
     * @param $PlaceID
     * @param int $Limit
     * @return \Illuminate\Database\Query\Builder
     */
    function getPlacesByNear($PlaceID, $Limit = 10)
    {

        $PlaceObj = $this->find($PlaceID);

        $NearPlace = DB::table(DB::raw($this->table))
            ->select(DB::raw("*,            
            round(( 111.111 * DEGREES(acos( cos( radians($PlaceObj->latitude) ) * cos( radians( latitude) ) 
										   * cos( radians(longitude) - radians($PlaceObj->longitude)) + sin(radians($PlaceObj->latitude)) 
										   * sin( radians(latitude))))),2) as distance_from_city"))
            ->whereNotNull('latitude')
            ->where('country', $PlaceObj->country)
            ->where('status', 1)
            ->where('type', $PlaceObj->type)
            ->limit($Limit)
            ->orderBy('distance_from_city', 'asc');


        return $NearPlace;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function stop()
    {
        return $this->hasMany('App\Model\Place\Stop', 'place', 'ID');
    }

    /**
     * @param $StopType
     * @return string
     */
    static function getStopType($StopType)
    {

        switch ($StopType) {
            case 1:
                return '<i class="fas fa-plane  fa-sm" ></i>';
                break;
            case 2:
                return '<i class="fas fa-plane  fa-sm" ></i>';
                break;
            default:
                return '';
        }


    }

    /**
     * @param $Places
     * @param $Types
     * @return array
     */
    static function removeStops($Places, $Types)
    {

        $Place = [];
        foreach ($Places as $Index => $PlaceID) {
            if ($Types[$Index] == 1)
                $Place[] = $PlaceID;
        }
        return $Place;
    }


}
