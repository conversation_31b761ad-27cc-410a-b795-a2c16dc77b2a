<?php

namespace App\Model\Place;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Place\PlaceHotelbed
 *
 * @property int $ID
 * @property string $name
 * @property int $country
 * @property string|null $code
 * @property float|null $longitude
 * @property float|null $latitude
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Place\PlaceHotelbed whereUploadId($value)
 * @mixin \Eloquent
 */
class PlaceHotelbed extends Model
{
    protected $table = 'apple_places_hotelbed';


}
