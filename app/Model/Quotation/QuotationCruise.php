<?php
/**
 * Supun Praneeth 2018.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\Quotation;

use App\Model\Cruise\Cruise;
use App\Model\Cruise\CruiseCabinRate;
use App\Model\Cruise\CruiseCabinType;
use App\Model\Cruise\CruiseOccupancyType;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\Meal;
use App\Model\Hotel\Rates;
use App\Model\Hotel\Room;
use App\Model\Hotel\Supplement;
use App\Model\Markup;
use Carbon\Carbon;
use DB;
use hotelbeds\hotel_api_sdk\model\Pax;
use Illuminate\Database\Eloquent\Model;
use Session;

/**
 * App\Model\Quotation\QuotationHotel
 *
 * @mixin \Eloquent
 */
class QuotationCruise extends Model
{

    protected $table = 'apple_quotation_cruise';

    /**
     * @var array
     */
    var $AvailableCruiseIndex = [];
    /**
     * @var
     */
    var $SelectedRates;
    /**
     * @var
     */
    var $Error;


    /**
     * @param $ArrivalDate
     * @param $Places
     * @param int $addEach
     * @param array $Nights
     * @return array
     */
    static function getCruiseBookDates($ArrivalDate, $Places, $addEach = 1, $Nights = [])
    {
        $dt = Carbon::create($ArrivalDate['year'], $ArrivalDate['month'], $ArrivalDate['day'], 0);
        $Dates = [];
        foreach ($Places as $Key => $PlaceID) {

            $Night = $Nights[$Key] ?? $addEach;

            $Dates[$Key]['index'] = $Key;
            $Dates[$Key]['place'] = $PlaceID;
            $Dates[$Key]['check_in'] = array('year' => $dt->year, 'month' => $dt->month, 'day' => $dt->day);
            $dt->addDays($Night);
            $Dates[$Key]['check_out'] = array('year' => $dt->year, 'month' => $dt->month, 'day' => $dt->day);

        }

        return $Dates;
    }

    /**
     * @param $CruiseID
     * @param $Query
     * @return string
     */
    function getChangeCruiseQuery($CruiseID, $Query)
    {

        parse_str($Query, $output);
        $output['cruise'] = $CruiseID;

        return http_build_query($output);

    }

    /**
     * @param bool $MainArray
     * @param bool $only_dates
     * @return string
     */
    static function getCruiseQueryFromArray($MainArray = false, $only_dates = false)
    {
        if (!empty($MainArray['cruise'])) {

            $validator = \Validator::make($MainArray, [
                'cruise' => 'exists:apple_cruise,ID',
                'place' => 'required|exists:apple_places,ID',
                'check_in' => 'required',
                'check_out' => 'required',
                'cabin_type' => 'required|exists:apple_cruise_cabin_type,ID',
                'meal_type' => 'required|exists:apple_meal_plan,ID'
            ]);


            if ($validator->fails()) {
                return false;
            }
        } else {

            $validator = \Validator::make($MainArray, [
                'place' => 'required|exists:apple_places,ID',
                'check_in' => 'required',
                'check_out' => 'required'
            ]);


            if ($validator->fails()) {
                return false;
            }
        }

        $ID = isset($MainArray['cruise']) ? $MainArray['cruise'] : false;
        $Place = isset($MainArray['place']) ? $MainArray['place'] : false;

        $check_in = $MainArray['check_in'];
        $check_out = $MainArray['check_out'];

        $room_category = isset($MainArray['cabin_type']) ? $MainArray['cabin_type'] : false;
        $meal_type = isset($MainArray['meal_type']) ? $MainArray['meal_type'] : false;

        $packge = isset($MainArray['package']) ? $MainArray['package'] : false;
        $night = isset($MainArray['nights']) ? $MainArray['nights'] : false;


        if (!empty($MainArray['cabin_occupancy_type'])) {
            foreach ($MainArray['cabin_occupancy_type'] as $RoomTypeID => $RoomCOunt) {
                $room_type[$RoomTypeID] = $RoomCOunt;
            }
        } else {
            $room_type[1] = false;
            $room_type[2] = false;
            $room_type[3] = false;
            $room_type[4] = false;
            $room_type[5] = false;
        }


        $extrabed = isset($MainArray['extrabed']) ? $MainArray['extrabed'] : false;
        return QuotationCruise::getEncodeArray($ID, $check_in, $check_out, $room_category, $meal_type, $room_type, $extrabed, $Place,"Local", $packge, $night);

    }

    /**
     * @param $ID
     * @param $check_in
     * @param $check_out
     * @param int $room_category
     * @param int $meal_type
     * @param array $room_type
     * @param int $extrabed
     * @param bool $Place
     * @param string $Provider
     * @return string
     */
    static function getEncodeArray($ID, $check_in, $check_out, $room_category = 9, $meal_type = 2, $room_type = ['1' => 1, '2' => 0, '3' => 0, '4' => 0, '5' => 0], $extrabed = 0, $Place = false, $Provider = 'local', $package=false, $night=false)
    {


        $MainArray = array();
        $MainArray['cruise'] = $ID;
        $MainArray['place'] = $Place;


        $MainArray['check_in'] = $check_in;
        $MainArray['check_out'] = $check_out;

        $CheckInDate = Carbon::create($MainArray['check_in']['year'], $MainArray['check_in']['month'], $MainArray['check_in']['day'], 0);
        $CheckOutDate = Carbon::create($MainArray['check_out']['year'], $MainArray['check_out']['month'], $MainArray['check_out']['day'], 0);

        $MainArray['nights'] = $night ?? $CheckInDate->diffInDays($CheckOutDate);

        $MainArray['provider'] = $Provider;
        $MainArray['package'] = $package;


        $MainArray['cabin_type'] = $room_category;
        $MainArray['meal_type'] = $meal_type;

        if (empty($room_type))
            $MainArray['cabin_occupancy_type'] = ['1' => 1, '2' => 0, '3' => 0, '4' => 0, '5' => 0];
        else
            $MainArray['cabin_occupancy_type'] = $room_type;


        $MainArray['extrabed'] = $extrabed;

        return (http_build_query($MainArray));

    }

    /**
     * @param $CruiseDataQuery
     * @return mixed
     */
    static function getDecodeArray($CruiseDataQuery)
    {

        parse_str($CruiseDataQuery, $output_f);
        parse_str(QuotationCruise::getCruiseQueryFromArray($output_f), $output);
        return $output_f;

    }

    /**
     * @param $Quotation
     * @return float|int
     */
    static function getAllNight($Quotation)
    {
        $Night = 0;
        if (isset($Quotation['hotel'])) {
            foreach ($Quotation['hotel'] as $HotelSettingItem)
                $Night += QuotationHotel::getNightHotelBook($HotelSettingItem['check_in'], $HotelSettingItem['check_out']);

        } else {
            foreach ($Quotation['place'] as $PlaceID)
                $Night++;
        }
        return $Night;
    }

    /**
     * @param $checkin
     * @param $checkout
     * @return float|int
     */
    static function getNightCruiseBook($checkin, $checkout)
    {
        $FirstDate = strtotime($checkin['year'] . '/' . $checkin['month'] . '/' . $checkin['day']);
        $SecondDate = strtotime($checkout['year'] . '/' . $checkout['month'] . '/' . $checkout['day']);

        return ($SecondDate - $FirstDate) / 86400;

    }

    /**
     * @param $checkin
     * @param $checkout
     * @return array
     */
    function getBookdatesNightCheckIn($checkin, $checkout)
    {
        $checkInObject = Carbon::create($checkin['year'], $checkin['month'], $checkin['day'], 0);

        $FirstDate = $checkInObject;
        $BookDateList = [];

        $nights = 1;
        $CurrentDate = $FirstDate;


        for ($c = 0; $c < $nights; $c++) {

            $BookDateList[] = [
                "year" => $CurrentDate->year,
                "month" => $CurrentDate->month,
                "day" => $CurrentDate->day,
            ];
            $CurrentDate = $FirstDate->addDays(1);

        }
        return $BookDateList;
    }


    /**
     * @param $Quotation
     * @param $HotelArray
     * @param bool $OnlyHotelBeds
     * @return array
     */
    static function getCruiseCost($Quotation, $CruiseArray, $OnlyHotelBeds = false)
    {

        $Rates = new CruiseCabinRate();
        $QuotationCruise = new QuotationCruise();
        $CurrencyList = Quotation::getCurrency($Quotation);
        $Market = $Quotation['market'];
        $CabinOccupancyID = 0;

        $ID = $CruiseArray['cruise'];

        $check_in = $CruiseArray['check_in'];
        $check_out = $CruiseArray['check_out'];

        $RoomCategory = $CruiseArray['cabin_type'];
        $Meal = $CruiseArray['meal_type'];
        $Package = $CruiseArray['package'];

        foreach ($CruiseArray['cabin_occupancy_type'] as $RoomTypeID => $RoomCount) {
            $room_type[$RoomTypeID] = intval($RoomCount);
        }

        //$extrabed = $HotelArray['extrabed'];
        $Bookdates = $QuotationCruise->getBookdatesNightCheckIn($check_in, $check_out);
        $DateRates = [];
        $MainRate = 0;

        foreach ($Bookdates as $Day => $Bookdate) { //loop all the book dates
            foreach ($room_type as $RoomTypeID => $RoomCount) {//loop all the rooms

                if ($RoomCount) {//only if room have
                    //adult
                    $DateRates['adult'][$Day][$RoomTypeID] = $Rates->getRate($ID, $Bookdate, $RoomTypeID, $Meal, $RoomCategory, $Market, false, $OnlyHotelBeds, 500, $Package)->toArray();

                    if (isset($DateRates['adult'][$Day][$RoomTypeID][0])) {
                        $DateRates['adult'][$Day][$RoomTypeID] = $DateRates['adult'][$Day][$RoomTypeID][0];
                    }
                    $CabinOccupancyID = $RoomTypeID;
                }
            }

            if (!empty($room_type['room_type'][2])) {
                $RoomTypeChild = 2;
            } elseif (!empty($room_type['room_type'][1])) {
                $RoomTypeChild = 1;
            } elseif (!empty($room_type['room_type'][3])) {
                $RoomTypeChild = 3;
            } else {
                $RoomTypeChild = 2;
            }

            if(isset(array_slice($DateRates['adult'][$Day], 0)["0"]->ID)) {
                $RateReferenceID = array_slice($DateRates['adult'][$Day], 0)["0"]->ID;

                $MainRate = $DateRates['adult'][$Day][$CabinOccupancyID]->rate??0;

                // child
                // if(isset($Quotation['child_age']) && !empty($Quotation['child_age']) && (!in_array(null, $Quotation['child_age']['cwb']) || !in_array(null, $Quotation['child_age']['cwb']))) {
                    if(isset($Quotation['child_age'])) {
                        if(isset($Quotation['child_age']['cwb'])) {
                            foreach ($Quotation['child_age']['cwb'] as $key => $value) {
                                $value = (isset($value) && $value!=0) ? $value : 8;
                                $DateRates['child']['cwb'][$Day][] = $Rates->getRateChild($RateReferenceID, $value)->toArray();
                            }
                        }

                        if(isset($Quotation['child_age']['cnb'])) {
                            foreach ($Quotation['child_age']['cnb'] as $key => $value) {
                                $value = (isset($value) && $value!=0) ? $value : 3;
                                $DateRates['child']['cnb'][$Day][] = $Rates->getRateChild($RateReferenceID, $value)->toArray();
                            }
                        }
                    }

                // }

                break; // Cruise having package price no need to calculate by days
            }
// dd($DateRates['child']['cwb'][$Day]);
        }

        //Check if quotation has multiple currency
        if (count($CurrencyList) > 1) {
            foreach ($DateRates['adult'] as $Day => $RateItem) {
                foreach ($RateItem as $RT => $RateArray) {

                    if (!empty($RateArray)) {
                        $CruiseCurrency = Cruise::getCruiseCurrency($RateArray->cruise);
                        $DateRates['adult'][$Day][$RT]->rate = currency($RateArray->rate, $CruiseCurrency->code, 'USD');
                    }
                }
            }
            foreach ($DateRates['child']['cwb'] as $Day => $RateItem) {
                if (!empty($RateItem)) {
                    $CruiseCurrency = Cruise::getCruiseCurrency($RateItem->cruise);
                    $DateRates['child']['cwb'][$Day]->rate = currency($RateItem->rate, $CruiseCurrency->code, 'USD');
                    $DateRates['child']['cwb'][$Day]->rateChild = currency($RateItem->rate, $CruiseCurrency->code, 'USD');
                }
            }
            foreach ($DateRates['child']['cnb'] as $Day => $RateItem) {
                if (!empty($RateItem)) {
                    $CruiseCurrency = Cruise::getCruiseCurrency($RateItem->cruise);
                    $DateRates['child']['cnb'][$Day]->rate = currency($RateItem->rate, $CruiseCurrency->code, 'USD');
                    $DateRates['child']['cnb'][$Day]->rateChild = currency($RateItem->rate, $CruiseCurrency->code, 'USD');
                }
            }
        }

        return $DateRates;

    }
    #rating
    ####################################################################################################################################
    /**
     * @param $Quotation
     * @return array|bool
     * @throws \ReflectionException
     */
    function getCruiseRatePlane($Quotation)
    {

        if (!isset($Quotation['cruise']))
            return false;

        $CruiseList = $Quotation['cruise'];
        $this->SelectedRates = $this->getCriuseListCost($Quotation);

        $HotelPlanRates = [];

        foreach ($this->SelectedRates as $CruiseIndex => $CruiseItem) {

            /*if (!isset($Quotation['accommodation'][$HotelIndex]) || $Quotation['accommodation'][$HotelIndex] == 2)//get only hotesl
                continue;*/

            $CurrentCruisePlanRates = $this->getSingleCruiseRatePlane($CruiseItem, $CruiseList[$CruiseIndex], $CruiseIndex, $Quotation['pax']);

            if ($CurrentCruisePlanRates)
                $CruisePlanRates[$CruiseIndex] = $CurrentCruisePlanRates;
        }

        return $this->Rate($CruisePlanRates, $Quotation);

    }

    /**
     * @param $HotelRateItem
     * @param $HotelRequestItem
     * @param $HotelIndex
     * @param $Pax
     * @return array
     * @throws \ReflectionException
     */
    function getSingleCruiseRatePlane($CruiseRateItem, $CruiseRequestItem, $CruiseIndex, $Pax)
    {

        $CruiseRequestItem = arrayMapMulti("intval", $CruiseRequestItem);

        $CruiseRateItem = objectToArray($CruiseRateItem);
        $RatePlane = array('child' => array('cwb' => 0, 'cnb' => 0));

        foreach (CruiseOccupancyType::pluck('ID', 'type') as $RoomTypeDB) {
            $RatePlane['adult'][$RoomTypeDB] = false;
            $RatePlane['per_room'][$RoomTypeDB] = false;
        }

        #for adult
        if ($CruiseRateItem) { //check if there's a hotel

            foreach ($CruiseRateItem['adult'] ?? [] as $Day => $RateItem) {#database rate list

                if (empty($CruiseRequestItem['cabin_occupancy_type']))
                    continue;

                foreach ($CruiseRequestItem['cabin_occupancy_type'] as $RequestRoomType => $RequestRoomCount) {#requested room type list with count

                    if (!$RequestRoomCount) continue; //if there are no room request check next

                    if ($RateItem[$RequestRoomType]) {

                        //first check rate is changed
                        if (empty($RateItem[$RequestRoomType]['is_modified'])) {
                            $RatePlane['adult'][$RequestRoomType] += $RateItem[$RequestRoomType]['rate'] * $RequestRoomCount;
                            $RatePlane['per_room'][$RequestRoomType] += $RateItem[$RequestRoomType]['rate'];
                        } else {
                            $RatePlane['adult'][$RequestRoomType] += $RequestRoomCount * ($RateItem[$RequestRoomType]['modified_rate'] ?? 0);
                            $RatePlane['per_room'][$RequestRoomType] += ($RateItem[$RequestRoomType]['modified_rate'] ?? 0);
                        }
                    } else {
                        $RatePlane['adult'][$RequestRoomType] += 0;
                        $RatePlane['per_room'][$RequestRoomType] += 0;
                    }
                }
            }
        }

        #for child
        if ($CruiseRateItem && !empty($CruiseRateItem['child'])) { //check if there's a hotel
            foreach ($CruiseRateItem['child'] as $ChildType => $RateItem) {

                if (!$Pax[$ChildType])
                    continue;

                foreach ($RateItem as $Day => $RateDayItem) {
                    if (isset($RateDayItem['rateChild'])) {// check rate is change

                        if (empty($RateDayItem['is_modified'])) {
                            $RatePlane['child'][$ChildType] = $RateDayItem['rateChild']??0;
                        } else {
                            $RatePlane['child'][$ChildType] = $RateDayItem['modified_rate']??0;
                        }
                    }
                }
            }
        }

        return $RatePlane;

    }


    /**
     * @param $HotelSettings
     * @param $Pax
     * @return array|bool
     */
    function getCostType($HotelSettings, $Pax)
    {


        $RoomTypeArray = array();

        foreach ($HotelSettings as $HotelItem) {

            if (empty($HotelItem['hotel']))
                continue;

            if (isset($HotelItem['transportation']))
                continue;

            foreach ($HotelItem['room_type'] as $RoomType => $RoomCount) {
                if (intval($RoomCount))
                    $RoomTypeArray['adult'][$RoomType] = "Hotel Cost";
            }
        }


        if (!isset($RoomType))
            return false;

        if (intval($Pax['cwb']))
            $RoomTypeArray['cwb'][$RoomType] = "Hotel Cost";

        if (intval($Pax['cnb']))
            $RoomTypeArray['cnb'][$RoomType] = "Hotel Cost";


        return $RoomTypeArray;
    }


    /**
     * @param $Quotation
     * @return array
     */
    public function getCriuseListCost($Quotation): array
    {

        $Rates = new CruiseCabinRate();

        $ArrayList = [];
        $AccommodationList = $Quotation['accommodation'] ?? [];


        foreach (($Quotation['cruise'] ?? []) as $ID => $CruiseItem) {

                if ($AccommodationList[$ID] == 2 || empty($CruiseItem))
                    continue;

                if (isset($Quotation['rate']['cruise'][$ID])) {
                    $RateArraySession = $Quotation['rate']['cruise'][$ID];
                } else {
                    $RateArraySession = arrayToobject(Session::get("quotation.rate.cruise.$ID"));
                }

                if (isset($RateArraySession) && $Rates->searchInRateArrayCruiseSettings(objectToArray($RateArraySession), $CruiseItem)) {//chec if there are rates in the session that changed
                    $ArrayList[$ID] = objectToArray($RateArraySession);//if there is rate set it
                } else {
                    // dd($CruiseItem, $Quotation);
                    $ArrayList[$ID] = objectToArray(QuotationCruise::getCruiseCost($Quotation, $CruiseItem));
                }

        }

        return $ArrayList;

    }


    /**
     * @param $Quotation
     * @return mixed
     */
    function getAvailableRoomTypes($Quotation)
    {
        $RoomTypeList = [];

        if ($Quotation['cruise']) {
            foreach ($Quotation['cruise'] as $index => $CruiseItem) {
                foreach ($CruiseItem['cabin_occupancy_type'] as $roomType => $RoomCount) {
                    if ($RoomCount) {
                        if (empty($RoomTypeList[$roomType])) {
                            $RoomTypeList[$roomType] = [
                                'no_room_type' => 1,
                                'no_room_count' => $RoomCount
                            ];
                        } else {
                            $RoomTypeList[$roomType]['no_room_type']++;
                            $RoomTypeList[$roomType]['no_room_count'] += $RoomCount;

                        }

                    }

                }
            }
            return $RoomTypeList;
        } else
            return false;

    }


    /**
     * @param $CruisePlanRates
     * @param $Quotation
     * @return array
     */
    function Rate($CruisePlanRates, $Quotation): array
    {

        $Total = [];

        $Total['cost'] = 0;
        $Total['cost_pp'] = 0;
        $Total['cost_type'] = $this->getCostType($Quotation['cruise'], $Quotation['pax']);
        $Total['room_type_cost'] = [];


        #Child
        $Total['child_cost']['cwb'] = 0;
        $Total['child_cost']['cnb'] = 0;


        foreach (Room::pluck('ID', 'type') as $RoomTypeDB)
            $Total['room_type_cost'][$RoomTypeDB] = false;


        foreach ($CruisePlanRates as $CruiseIndex => $CruiseRate) {

            $CruiseRoomTypeCost = 0;

            foreach ($CruiseRate['adult'] as $RoomType => $RoomCost) {
                if ($RoomCost !== false) {

                    $PerRoomRate = $CruiseRate['per_room'][$RoomType] / $RoomType;
                    $CruiseRoomTypeCost += $RoomCost;//this is the room cost so add this to total
                    //
                    //for PP rate divide it by room type ex Double room divide by 2
                    $Total['room_type_cost'][$RoomType] += $PerRoomRate;
                }
            }
            #Child
            foreach ($CruiseRate['child'] as $ChildType => $RoomCost) {

                $CruiseRoomTypeCost += $RoomCost * $Quotation['pax'][$ChildType];//child come with pp rate, so for total u have to divide it form child count
                $Total['child_cost'][$ChildType] += $RoomCost;//for pp rate we can get directly rate in db
            }

            if ($CruiseRoomTypeCost)//check whether current hotel has cost
                $this->AvailableCruiseIndex[] = $CruiseIndex;


            $Total['cost'] += $CruiseRoomTypeCost;//set to total cost
        }

        //markup add o total
        $Total['cost_pp'] = $Total['cost'] / $Quotation['pax']['adult'];

        return $Total;

    }


    /**
     * @return array
     */
    function getAvailableCruiseIndex()
    {
        return $this->AvailableCruiseIndex;
    }

    /**
     * @param $Quotation
     * @return array
     */
    function getCruiseIndexByCity($Quotation)
    {
        $SessionCruise = $Quotation['cruise'] ?? false;
        $SessionPlace = $Quotation['place'];
        $SessionAccommodation = $Quotation['accommodation'] ?? false;

        $IndexCruiseCity = [];
        if ($SessionAccommodation) {
            foreach ($SessionAccommodation as $Index => $Settings) {
                if (!empty($SessionCruise[$Index]['hotel']) && !empty($IndexHotelCity) && !empty($SessionPlace[$Index])) {
                    $IndexCruiseCity[$Index][$SessionPlace[$Index]][] = $Index;
                } elseif (!empty($SessionCruise[$Index]['place']))
                    $IndexCruiseCity[$Index][$SessionCruise[$Index]['place']][] = $Index;
                else {
                    $IndexCruiseCity[$Index] = false;
                }
            }
        }
        return $IndexCruiseCity;
    }

    /**
     * @param $HotelArray
     * @param $ArrivalDate
     * @param int $addEach
     * @return array
     */
    function getCheckinCheckoutDatesFromCruiseArray($HotelArray, $ArrivalDate, $addEach = 1)
    {
        $CheckinCheckoutDates = array();
        $ArrivalDateInUnix = strtotime($ArrivalDate['year'] . "/" . $ArrivalDate['month'] . "/" . $ArrivalDate['day']);

        foreach ($HotelArray as $HotelIndex => $HotelSettingsQuery) {

            if ($HotelSettingsQuery) {
                $DecordedSetting = $this->getDecodeArray($HotelSettingsQuery);
                $CheckinCheckoutDates[$HotelIndex]['check_in'] = $DecordedSetting['check_in'];
                $CheckinCheckoutDates[$HotelIndex]['check_out'] = $DecordedSetting['check_out'];
            } else {

                if (empty($CheckinCheckoutDates)) {//if this is the first place hotel
                    $CheckInDate = array('year' => date('o', $ArrivalDateInUnix), 'month' => date('m', $ArrivalDateInUnix), 'day' => date('d', $ArrivalDateInUnix));//set arival date to check in
                    $CheckInDateWithaddedDays = strtotime("+$addEach days", $ArrivalDateInUnix);
                    $CheckOutDate = array('year' => date('o', $CheckInDateWithaddedDays), 'month' => date('m', $CheckInDateWithaddedDays), 'day' => date('d', $CheckInDateWithaddedDays));//set arival date to check in
                } else {//if not the first city

                    $LastCheckoutDateUnix = strtotime($CheckinCheckoutDates[$HotelIndex - 1]['check_out']['year'] . "/" . $CheckinCheckoutDates[$HotelIndex - 1]['check_out']['month'] . "/" . $CheckinCheckoutDates[$HotelIndex - 1]['check_out']['day']);

                    $CheckInDate = array('year' => date('o', $LastCheckoutDateUnix), 'month' => date('m', $LastCheckoutDateUnix), 'day' => date('d', $LastCheckoutDateUnix));//set arival date to check in
                    $LastCheckoutDateUnixAddDays = strtotime("+$addEach days", $LastCheckoutDateUnix);
                    $CheckOutDate = array('year' => date('o', $LastCheckoutDateUnixAddDays), 'month' => date('m', $LastCheckoutDateUnixAddDays), 'day' => date('d', $LastCheckoutDateUnixAddDays));//set arival
                }
                $CheckinCheckoutDates[$HotelIndex]['check_in'] = $CheckInDate;
                $CheckinCheckoutDates[$HotelIndex]['check_out'] = $CheckOutDate;
            }
        }

        return $CheckinCheckoutDates;
    }


    /**
     * @param $QuotationArray
     * @param bool $AddStops
     * @return array
     */
    static function getTourDaysDetail($QuotationArray, $AddStops = false)
    {

        $DaysArray = [];
        $Day = 1;
        $LastPlace = false; //last place in destination

        $HotelArray = $QuotationArray['hotel'];
        if ($AddStops)
            $Places = $QuotationArray['place_full'];
        else
            $Places = $QuotationArray['place'];

        $Date = Carbon::create(reset($HotelArray)['check_in']['year'], reset($HotelArray)['check_in']['month'], reset($HotelArray)['check_in']['day'], 0);


        //if need stops
        if (count($Places) != count($HotelArray) && $AddStops) {

            $HotelArrayTemp = [];
            $HotelIndexFix = 0;


            foreach ($QuotationArray['place_type'] as $TypeIndex => $PlaceTypeID) {
                if ($PlaceTypeID > 1) {//if it's a stop

                    $HotelArrayTemp[$TypeIndex] = [
                        'place' => $Places[$TypeIndex],
                        'place_type' => $PlaceTypeID
                    ];
                } else {
                    $HotelArrayTemp[$TypeIndex] = $HotelArray[$HotelIndexFix];
                    $HotelArrayTemp[$TypeIndex]['place_type'] = $PlaceTypeID;
                    $HotelIndexFix++;
                }
            }

            $HotelArray = $HotelArrayTemp;
        }

        // var_dump($Places);
        foreach ($HotelArray as $HotelIndex => $HotelSettings) {

            $place_type = $HotelSettings['place_type'] ?? 1;

            if ($place_type == 1) {//if its a destination

                //$DaysArray = [];
                $check_in = Carbon::create($HotelSettings['check_in']['year'], $HotelSettings['check_in']['month'], $HotelSettings['check_in']['day'], 0);
                $check_out = Carbon::create($HotelSettings['check_out']['year'], $HotelSettings['check_out']['month'], $HotelSettings['check_out']['day'], 0);


                $Days = $check_out->diffInDays($check_in);
                //$Date = $check_in;


                for ($c = 1; $c <= $Days; $c++) {//loop all the days

                    if (!isset($Places[$HotelIndex]))
                        continue;


                    $DaysArray[$Day]['date'] = ['year' => $Date->year, 'month' => $Date->month, 'day' => $Date->day];
                    $DaysArray[$Day]['place'] = $Places[$HotelIndex];
                    $DaysArray[$Day]['index'] = $HotelIndex;


                    $Day++;
                    $Date->addDays(1);
                }


                if (!$HotelSettings) {

                    $DaysArray[$Day]['date'] = ['year' => $Date->year, 'month' => $Date->month, 'day' => $Date->day];
                    $DaysArray[$Day]['place'] = $Places[$HotelIndex];
                }

                if (isset($Places[$HotelIndex + 1]) && $QuotationArray['place_type'][$HotelIndex + 1] == 1) {
                    $DaysArray[$Day]['route'] = ['from' => $Places[$HotelIndex], 'to' => $Places[$HotelIndex + 1]];
                }
                // var_dump($HotelIndex);
                if(isset($Places[$HotelIndex])) {
                    $LastPlace = $Places[$HotelIndex];
                }

            } else { //if its a stop

                #add extra day
                $DaysArray[$Day][$place_type]['place'] = $Places[$HotelIndex];
                $DaysArray[$Day][$place_type]['index'] = $HotelIndex;
                $DaysArray[$Day][$place_type]['type'] = $place_type;

            }
        }

        #add extra day
        $DaysArray[$Day]['date'] = ['year' => $Date->year, 'month' => $Date->month, 'day' => $Date->day];
        $DaysArray[$Day]['place'] = $LastPlace;
        $DaysArray[$Day]['index'] = $HotelIndex;

        return $DaysArray;
    }

    /**
     * @param $RoomTypeArray
     * @return array
     */
    function getAvailableRoomType($RoomTypeArray)
    {

        $RoomTypeList = CruiseCabinType::pluck('short_name', 'ID');
        $AvailableRoomTypes = [];


        foreach ($RoomTypeArray as $RoomType => $RoomCount) {
            if (intval($RoomCount))
                $AvailableRoomTypes[$RoomType] = $RoomTypeList[$RoomType] . "($RoomCount)";
        }

        return $AvailableRoomTypes;
    }

    /**
     * @param $Quotation
     * @return array|bool
     */
    function getAvailableMealType($Quotation)
    {

        if (empty($Quotation['hotel']))
            return false;

        $MealTypeList = Meal::pluck('plan', 'ID');
        $AvailableMealTypes = [];
        $HotelArray = $Quotation['hotel'];
        $AccommodationArray = $Quotation['accommodation'];


        foreach ($HotelArray as $Index => $HotelItem) {

            if ($AccommodationArray[$Index] == 2)
                continue;

            if (empty($HotelItem['meal_type']))
                $CurrentMeal = $HotelItem['meal'];
            else
                $CurrentMeal = $HotelItem['meal_type'];


            if (empty($AvailableRoomTypes[$CurrentMeal]) && $CurrentMeal)
                $AvailableMealTypes[$CurrentMeal] = $MealTypeList[$CurrentMeal];

        }
        return $AvailableMealTypes;


    }


    /**
     * @param $Quotation
     * @param $RequestedCurrency
     * @return array
     */
    static function getCruiseBudgetArray($Quotation, $RequestedCurrency)
    {
        $pax = $Quotation["pax"];
        $QuotationCruise = new QuotationCruise();

        $CruiseRate = $QuotationCruise->getCriuseListCost($Quotation);
        $HotelPlanRates = [];

        foreach ($CruiseRate as $HotelIndex => $HotelItem) {

            if (!isset($Quotation['accommodation'][$HotelIndex]) || $Quotation['accommodation'][$HotelIndex] == 2)//get only hotesl
                continue;


            $CurrentTotal = 0;

            foreach ($HotelItem['adult'][0] as $RoomType => $RateArray) {//if rate is modifies set ratr to modified rate

                $HotelItem['adult'][0][$RoomType]['rate'] = currency(($HotelItem['adult'][0][$RoomType]['rate'] ?? 0), 'USD', $RequestedCurrency, false);
                if(isset($Quotation['cruise'][$HotelIndex]['cabin_occupancy_type'][$RoomType])) {
                    // /*

                    $SubTotal=0;
                    for ($i=0; $i<$Quotation['cruise'][$HotelIndex]['nights'];$i++) {

                        if (!empty($HotelItem['adult'][$i][$RoomType]['is_modified'])) {
                            $HotelItem['adult'][$i][$RoomType]['rate'] = $HotelItem['adult'][$i][$RoomType]['modified_rate'];
                        }

                        $SubTotal += $HotelItem['adult'][$i][$RoomType]['rate'] * intval($Quotation['cruise'][$HotelIndex]['cabin_occupancy_type'][$RoomType]);

                        break; // no need to cruise calculate days conut.
                    }

                    $CurrentTotal += $SubTotal;
                }
            }

            foreach ($HotelItem['child'] as $ChildType => $RateArray) {//if rate is modifies set ratr to modified rate
                if (!empty($RateArray[0]['is_modified'])) {
                    $HotelItem['child'][$ChildType][0]['rate'] = $RateArray[0]['modified_rate'];
                }

                if (!empty($HotelItem['child'][$ChildType][0]['rate']))
                    $HotelItem['child'][$ChildType][0]['rate'] = currency($HotelItem['child'][$ChildType][0]['rate'], 'USD', $RequestedCurrency, false);
                else
                    $HotelItem['child'][$ChildType][0]['rate'] = 0;

                if (!empty($Quotation['pax'][$ChildType]))
                    $CurrentTotal += $HotelItem['child'][$ChildType][0]['rate'] * $pax[$ChildType];
            }

            $HotelPlanRates[$HotelIndex]['cruise_settings'] = $Quotation['cruise'][$HotelIndex];
            $HotelPlanRates[$HotelIndex]['rate']['adult'] = $HotelItem['adult'];
            $HotelPlanRates[$HotelIndex]['rate']['cwb'] = $HotelItem['child']['cwb'];
            $HotelPlanRates[$HotelIndex]['rate']['cnb'] = $HotelItem['child']['cnb'];
            $HotelPlanRates[$HotelIndex]['rate']['total'] = $CurrentTotal;

        }

        return $HotelPlanRates;


    }

    /**
     * @param $PaxList
     * @return array
     */
    function getPaxesHotelBeds($PaxList)
    {

        $Hotel = new Hotel();
        $HotelBed = new HotelBed();


        //sd($PaxList);

        $RemainingAdult = $PaxList['adult'];
        $Paxes = [];
        $RoomList = $Hotel->getHotelRoomCountWithPax($PaxList);

        $TotalChildren = $PaxList['cwb'] + $PaxList['cnb'];
        $TotalRooms = array_sum($RoomList);


        $ChildCountArray = $HotelBed->childrenToRoom($TotalChildren, $TotalRooms, $RoomList);


        foreach ($RoomList as $RoomType => $RoomCount) {

            for ($i = 1; $i <= $RoomCount; $i++) {
                $Paxes[$RoomType][] = new Pax(Pax::AD, 30, "Ssds", "sds", 1);
                if (!empty($ChildCountArray[$RoomType][$i])) {
                    $Paxes[$RoomType][] = new Pax(Pax::CH, 6, "Ssds", "sds", 1);
                }

            }

        }

        return $Paxes;


    }

    /**
     * @param $Quotation
     * @return array
     */
    static function getSupplementRatePlane($Quotation)
    {

        $QuotationHotel = new QuotationHotel();

        $Rate = [];
        $Cost = [];
        $Cost['adult'] = 0;
        $Cost['child'] = 0;
        $Cost['total'] = 0;

        if (isset($Quotation['hotel'])) {

            #sd($Quotation);

            foreach ($Quotation['hotel'] as $k => $HotelSettings) {


                if ($Quotation['accommodation'][$k] != 1)
                    continue;


                $BookDates = $QuotationHotel->getBookdatesNightCheckIn($HotelSettings['check_in'], $HotelSettings['check_out']);

                foreach ($BookDates as $DateItem) {

                    $CheckInDate = Carbon::create($DateItem['year'], $DateItem['month'], $DateItem['day'], 0);


                    $Supplement = Supplement::where('hotel', $HotelSettings['hotel'])
                        ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $CheckInDate)
                        ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $CheckInDate)
                        ->where('meal_type', $HotelSettings['meal_type']);


                    if (!$Supplement->get()->isEmpty()) {
                        $SupRate = $Supplement->first();

                        if ($SupRate->type == 1) {#Per Room

                            $RoomCount = array_sum($HotelSettings['room_type']);
                            $Cost['adult'] += @($SupRate->adult_rate * $RoomCount / $Quotation['pax']['adult']);
                            $Cost['total'] += $SupRate->adult_rate * $RoomCount;


                        } elseif ($SupRate->type == 2) {#per Person ;

                            $AdjustPax = 0;
                            foreach ($HotelSettings['room_type'] as $RoomTypeID => $RoomTypeCount) {
                                $AdjustPax += $RoomTypeID * $RoomTypeCount;
                            }


                            $Cost['adult'] += $SupRate->adult_rate;
                            $Cost['child'] += $SupRate->child_rate;

                            $Cost['total'] += $SupRate->adult_rate * $AdjustPax;
                            $Cost['total'] += @($SupRate->child_rate * $Quotation['pax']['cwb']);

                        } elseif ($SupRate->type == 3) {#Per day
                            //--

                        }


                    }

                }

            }


        }

        return $Cost;


    }

}





