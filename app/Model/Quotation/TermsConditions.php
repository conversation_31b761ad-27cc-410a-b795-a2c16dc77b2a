<?php

namespace App\Model\Quotation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Quotation\TermsConditions
 *
 * @property int $ID
 * @property string $name
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\TermsConditions whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\TermsConditions whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\TermsConditions whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\TermsConditions whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\TermsConditions whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\TermsConditions whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\TermsConditions whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\TermsConditions whereUploadId($value)
 * @mixin \Eloquent
 */
class TermsConditions extends Model
{
    protected $table = "apple_terms_conditions";

    /**
     * @param $Quotation
     * @return bool|\Illuminate\Support\Collection
     */
    static function getQuotationList($Quotation)
    {
        $country = $Quotation['country'];
        $tour_type = $Quotation['tour_type'];

        $country = $Quotation['country'];
        $tour_type = $Quotation['tour_type'];

        $data = self::where(function ($q) use($country){
            $q->where('country',$country);
            $q->OrWhereNull('country');
        })->where(function ($q) use($tour_type){
            $q->where('tour_type',$tour_type);
            $q->OrWhereNull('tour_type');
        })->get();

        return ($data->isNotEmpty()?$data:false);

    }
}
