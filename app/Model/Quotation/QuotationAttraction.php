<?php

namespace App\Model\Quotation;

use App\Model\Costcut\Costcut;
use App\Model\Place\Attraction;
use App\Model\Place\CityTour;
use App\Model\Place\Excursion;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Quotation\QuotationAttraction
 *
 * @mixin \Eloquent
 */
class QuotationAttraction extends Model
{
    /**
     * @param $Quotation
     * @return array
     */
    static function getAttractionRatePlane($Quotation, $Direct = false)
    {
        //check if any attraction
        if (!isset($Quotation['attraction']))
            return [];

        $AttractionRates = Attraction::getAttraction($Quotation, $Direct);
        $ReturnAttractionCost = ['pax_cost' => ['adult' => 0], 'cost' => '0'];
        if ($Quotation['pax']['cwb'] || $Quotation['pax']['cnb']) {
            $ReturnAttractionCost['child'] = 0;
        }

        foreach ($AttractionRates as $AttractionID => $AttractionRatesItem) {
            
            foreach ($AttractionRatesItem as $PaxType => $Amount) {
                
                if (!empty($ReturnAttractionCost['pax_cost'][$PaxType])) {//check if its pax type is selected
                    $ReturnAttractionCost['pax_cost'][$PaxType] += floatval($Amount);
                } else {
                    $ReturnAttractionCost['pax_cost'][$PaxType] = floatval($Amount);
                }
                
                if ($PaxType == 'adult')//if it's adult
                    $ReturnAttractionCost['cost'] += floatval($Amount) * $Quotation['pax']['adult'];
                elseif ($PaxType == 'child')//if its a child
                    $ReturnAttractionCost['cost'] += floatval($Amount) * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);

                    
            }
            
        }
        return $ReturnAttractionCost;
    }


    /**
     * @param $Quotation
     * @return array
     */
    static function getExcursionRatePlane($Quotation, $Direct = false)
    {

        //check if any attraction
        if (!isset($Quotation['excursion']))
            return [];

        $ExcursionRates = Excursion::getExcursion($Quotation, $Direct);

        $ReturnAttractionCost = ['pax_cost' => ['adult' => 0], 'cost' => '0'];
        if ($Quotation['pax']['cwb'] || $Quotation['pax']['cnb'])
            $ReturnAttractionCost['child'] = 0;

        foreach ($ExcursionRates as $AttractionID => $ExcursionRatesRatesItem) {
            foreach ($ExcursionRatesRatesItem as $PaxType => $Amount) {

                if (!empty($ReturnAttractionCost['pax_cost'][$PaxType]))//check if itspax type is selected
                    $ReturnAttractionCost['pax_cost'][$PaxType] += floatval($Amount);
                else
                    $ReturnAttractionCost['pax_cost'][$PaxType] = floatval($Amount);

                if ($PaxType == 'adult')//if its adult
                    $ReturnAttractionCost['cost'] += floatval($Amount) * $Quotation['pax']['adult'];
                elseif ($PaxType == 'child')//if its a child
                    $ReturnAttractionCost['cost'] += floatval($Amount) * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);
            }
        }

        return $ReturnAttractionCost;
    }


    /**
     * @param $Quotation
     * @return array
     */
    static function getCityTourRatePlane($Quotation, $Direct = false)
    {
        $Costcut = new Costcut();

        //check if any attraction
        if (!isset($Quotation['city_tour']))
            return [];

        $CityTourRates = CityTour::getCityTour($Quotation, $Direct);

        $ReturnAttractionCost = ['pax_cost' => ['adult' => 0], 'cost' => '0'];
        if ($Quotation['pax']['cwb'] || $Quotation['pax']['cnb'])
            $ReturnAttractionCost['child'] = 0;


        foreach ($CityTourRates as $AttractionID => $CityTourRatesRatesItem) {
            foreach ($CityTourRatesRatesItem as $PaxType => $Amount) {

                if (!empty($ReturnAttractionCost['pax_cost'][$PaxType]))//check if its pax type is selected
                    $ReturnAttractionCost['pax_cost'][$PaxType] += floatval($Amount);
                else
                    $ReturnAttractionCost['pax_cost'][$PaxType] = floatval($Amount);

                if ($PaxType == 'adult')//if its adult
                    $ReturnAttractionCost['cost'] += floatval($Amount) * $Quotation['pax']['adult'];
                elseif ($PaxType == 'child')//if its a child
                    $ReturnAttractionCost['cost'] += floatval($Amount) * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);
            }
        }

        $CostCutRate = $Costcut->getCostCutRate($Quotation);
        $CostCutPkgRate = $Costcut->getCostCutPkgRate($Quotation);
        $CostCutPkgRateObj = json_decode($CostCutPkgRate);

        if(isset($CostCutPkgRate) && !empty($CostCutPkgRate)) {
            $ReturnAttractionCost['pax_cost']["adult"] = $ReturnAttractionCost['pax_cost']["adult"] - ($CostCutRate["break_down"]["C"]["pp"]) + ($CostCutPkgRateObj->adult_ticket)??0;
            $ReturnAttractionCost['pax_cost']["child"] = $ReturnAttractionCost['pax_cost']["child"] - ($CostCutRate["break_down"]["C"]["child"]) + ($CostCutPkgRateObj->child_ticket)??0;
            $ReturnAttractionCost['cost'] = $ReturnAttractionCost['pax_cost']["adult"]*$Quotation['pax']['adult'] + $ReturnAttractionCost['pax_cost']["child"]*($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);
        }

        return $ReturnAttractionCost;
    }


}
