<?php

namespace App\Model\Quotation;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
/**
 * App\Model\Quotation\QuotationInclude
 *
 * @property int $ID
 * @property string $include
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationInclude whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationInclude whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationInclude whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationInclude whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationInclude whereInclude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationInclude whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationInclude whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationInclude whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationInclude extends Model
{
    protected $table = 'apple_include';

    /**
     * @param $Quotation
     * @return array|bool
     */
    static function getQuotationList($Quotation)
    {

        $DaysDetail = Quotation::getTourDaysDetail($Quotation);
        $day = $Quotation['arrival_date']['day'];
        $month = $Quotation['arrival_date']['month'];

        $date = Carbon::parse('0000-'.$month.'-'.$day);

        $country = $Quotation['country'];
        $tour_type = $Quotation['tour_type'];

        $data = [];

        $includes = self::where(function ($q) use ($country) {
            $q->where('country', $country);
            $q->OrWhereNull('country');
        })->where(function ($q) use ($tour_type) {
            $q->where('tour_type', $tour_type);
            $q->OrWhereNull('tour_type');
        })->where(function ($q) use($date){ //
            $q->where(function($s) use($date){
                $s->whereDate('from','<=',$date);
                $s->whereDate('to','>=',$date);
            });
            $q->OrWhereNull('from');

        });
        $includes->whereNotIn('ID',[7,9,12]);
//        if($country != 62){
//            $includes->whereNotIn('ID',[7,9,12]);
//        }

        $includes = $includes->get()->toArray();


        foreach (($includes ?? []) as $includeItem) {
            $data[] = $includeItem['include'];
        }

        foreach (($Quotation['other_rate'] ?? []) as $extraItem) {
            $data[] = $extraItem['text'];
        }


        //Meal
        $BreakFast       = $Lunch       = $Dinner       = 0;
        $BreakFastPacked = $LunchPacked = $DinnerPacked = 0;

        foreach (($Quotation['meal'] ?? []) as $index=>$MealItem) {

            if(empty($DaysDetail[$index])) continue;

            if ($MealItem[1]['meal_where'] == 2)
                $BreakFast++;
            if ($MealItem[2]['meal_where'] == 2)
                $Lunch++;
            if ($MealItem[3]['meal_where'] == 2)
                $Dinner++;
            if ($MealItem[1]['meal_where'] == 3)
                $BreakFastPacked++;
            if ($MealItem[2]['meal_where'] == 3)
                $LunchPacked++;
            if ($MealItem[3]['meal_where'] == 3)
                $DinnerPacked++;
        }

        $MealTimesB = "";
        $MealTimesL = "";
        $MealTimesD = "";
        
        /*foreach(isset($Quotation['transport_time']) && $Quotation['transport_time']['meal_transfer'] ?? [] as $index => $time) {
            if(isset($Quotation['meal'][$index+1]) && $Quotation['meal'][$index+1][1]['meal_where'] == 2) {
                $MealTimesB .= date("g:i a", strtotime($time)) . " / ";
            }
            if(isset($Quotation['meal'][$index+1]) && $Quotation['meal'][$index+1][2]['meal_where'] == 2) {
                $MealTimesL .= date("g:i a", strtotime($time)) . " / ";
            }
            if(isset($Quotation['meal'][$index+1]) && $Quotation['meal'][$index+1][3]['meal_where'] == 2) {
                $MealTimesD .= date("g:i a", strtotime($time)) . " / ";
            }
            
        }*/
        $MealTimesB = trim($MealTimesB, " / ");
        $MealTimesL = trim($MealTimesL, " / ");
        $MealTimesD = trim($MealTimesD, " / ");
        $BreakFast ? $data[] = "Breakfast at Outside Restaurant (Set Menus Only) ($BreakFast B) - [$MealTimesB]" : true;
        $Lunch ? $data[]     = "Lunch at Outside Restaurant (Set Menus Only) ($Lunch L) - [$MealTimesL]"  : true;
        $Dinner ? $data[]    = "Dinner at Outside Restaurant (Set Menus Only) ($Dinner D) - [$MealTimesD]"  : true;

        $BreakFastPacked ? $data[] = "Packet of Breakfast Restaurant (Set Menus Only) ($BreakFastPacked B)" : true;
        $LunchPacked     ? $data[] = "Packet of Lunch Restaurant (Set Menus Only) ($LunchPacked L)" : true;
        $DinnerPacked    ? $data[] = "Packet of Dinner Restaurant (Set Menus Only) ($DinnerPacked D)" : true;


        return ($data ? $data : false);

    }

    /**
     * @param $Quotation
     * @return array|bool
     */
    static function getQuotationListLimited($Quotation)
    {
        $DaysDetail = Quotation::getTourDaysDetail($Quotation);
        $data = [];

        foreach (($Quotation['other_rate'] ?? []) as $extraItem) {
            $data[] = $extraItem['text'];
        }


        //Meal
        $BreakFast       = $Lunch       = $Dinner       = 0;
        $BreakFastPacked = $LunchPacked = $DinnerPacked = 0;

        foreach (($Quotation['meal'] ?? []) as $index=>$MealItem) {

            if(empty($DaysDetail[$index])) continue;

            if ($MealItem[1]['meal_where'] == 2)
                $BreakFast++;
            if ($MealItem[2]['meal_where'] == 2)
                $Lunch++;
            if ($MealItem[3]['meal_where'] == 2)
                $Dinner++;
            if ($MealItem[1]['meal_where'] == 3)
                $BreakFastPacked++;
            if ($MealItem[2]['meal_where'] == 3)
                $LunchPacked++;
            if ($MealItem[3]['meal_where'] == 3)
                $DinnerPacked++;
        }

        $BreakFast ? $data[] = "Breakfast at Outside Restaurant (Set Menus Only) ($BreakFast B)" : true;
        $Lunch ? $data[]     = "Lunch at Outside Restaurant (Set Menus Only) ($Lunch L)" : true;
        $Dinner ? $data[]    = "Dinner at Outside Restaurant (Set Menus Only) ($Dinner D)" : true;

        $BreakFastPacked ? $data[] = "Packet of Breakfast Restaurant (Set Menus Only) ($BreakFastPacked B)" : true;
        $LunchPacked     ? $data[] = "Packet of Lunch Restaurant (Set Menus Only) ($LunchPacked L)" : true;
        $DinnerPacked    ? $data[] = "Packet of Dinner Restaurant (Set Menus Only) ($DinnerPacked D)" : true;

        return ($data ? $data : false);

    }
}
