<?php

namespace App\Model\Quotation;


use App\Model\Costcut\Costcut;
use App\Model\Place\DistanceCharges;
use App\Model\Place\Place;
use App\Model\Transport\Transport;
use App\Model\Vehicle\TransportType;
use App\Model\Vehicle\Vehicle;
use App\Model\Vehicle\VehicleCityRate;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Quotation\QuotationTransport
 *
 * @mixin \Eloquent
 */
class QuotationTransport extends Model
{

    /**
     * @param $Quotation
     * @return mixed
     */
    static function getTransportRatePlane($Quotation)
    {

        $AdjustPax = 0;

        //get pax according to room count
        if (isset($Quotation['accommodation'][0]) && $Quotation['accommodation'][0] == 1 && !empty($Quotation['hotel'][0])) {
            foreach ($Quotation['hotel'][0]['room_type'] as $RoomTypeID => $RoomTypeCount) {
                $AdjustPax += $RoomTypeID * $RoomTypeCount;
            }
        } else
            $AdjustPax = $Quotation['pax']['adult'];


        $Nights = Quotation::getNights($Quotation);
        if(isset($Quotation['quarantine_type']) && $Quotation['quarantine_type'] == 1) {
            $Days = Quotation::getDaysForQuarantine($Quotation);
        } else {
            $Days = Quotation::getDays($Quotation);
        }

        $TransportCost = Transport::getTransport($Quotation);

        $Data['transport_data'] = $TransportCost;

        $Data['cost'] = QuotationTransport::getTransportCost($Quotation, $TransportCost, $Days);

        #GUIDE
        if (isset($Quotation['guide'])) {
            $Data['guide'] = QuotationTransport::getGuideCostList($Quotation['pax'], @$Quotation['guide'], $Nights, $Days);
            $Data['cost']['total'] += $Data['guide'] ['total'];
            $Data['cost']['per_person'] = $Data['cost']['total'] / $Quotation['pax']['adult'];

        }

        #stupid //get hotel adult not actual pax
        $Data['cost']['total'] = $Data['cost']['total'] / $Quotation['pax']['adult'] * $AdjustPax;

        return $Data;
    }


    /**
     * @param $Quotation
     * @param $TransportCost
     * @param $Days
     * @return array
     */
    static function getTransportCost($Quotation, $TransportCost, $Days)
    {

        $QuotationHotel = new QuotationHotel();
        $Costcut = new Costcut();

        $Country = $Quotation['country'];
        if($Quotation['tour_type'] == 6) {

            $nights = 0;

            foreach ($Quotation['night'] as $night):

                $nights += $night;

            endforeach;

            $Days = $nights + 1;
        }
        
        if (empty($TransportCost['rates'])) { //by km
            $BataHWRate = QuotationTransport::getCityviceBataHWRate($TransportCost, $Quotation); // get city vice Bata Highway Rates.
            $BataHWRate = array_filter($BataHWRate);
            $Distance = $TransportCost['mileage']['actual_distance'];
			
            $Distance += $TransportCost['mileage']['additional_distance'];
			
            if(TransportType::getType($Country) == 4) {
                $Total = QuotationTransport::getCityviceTransportRate($TransportCost, $Quotation);
            } else {
                $Total = $Distance * $TransportCost['vehicle']['rate']; #Rate
            }

	        if($Quotation["country"] == "256") {
	            if(isset($BataHWRate) && !empty($BataHWRate)) {
                    $TransportCost['vehicle']['bata'] = 0;
	                foreach ($BataHWRate as $rate) {
	                    if(isset($rate['bata'])) {
                            $TransportCost['vehicle']['bata'] += $TransportCost['vehicle']['bata'] + $rate['bata'];
                        }
                    }
                }
                $Total += $TransportCost['vehicle']['bata'];
	        } else {
		        $Total += $TransportCost['vehicle']['bata'] * $Days; #bata
	        }
            
	        $Total += $TransportCost['vehicle']['paging']; #Paging
            
            // High way changes

            if(isset($BataHWRate) && !empty($BataHWRate)) {
                foreach ($BataHWRate as $rate) {
                    if(isset($rate['highway_charges'])) {
                        $TransportCost['vehicle']['highway_charges'] += $rate['highway_charges'];
                    }
                }
            }

            $Total += $TransportCost['vehicle']['highway_charges'];
            
            if ($Quotation['tour_type'] == 3 && isset($Quotation['accommodation']))
	        {

                $counts = array_count_values($Quotation['accommodation']);
                /*
		        if (isset($counts[2]))
		        {

                    foreach ($Quotation['accommodation'] as $key => $accommodation) {
                        if($accommodation == 2) {
                            $ownData = $Quotation['hotel'][$key];
                            $count = $QuotationHotel->getNightHotelBook($ownData['check_in'], $ownData['check_out']);
                            $Total += $TransportCost['vehicle']['driver_accommodation'] * $count;

                        }
                    }

		        } else {
			        $Total += $TransportCost['vehicle']['driver_accommodation'] * ($Days - 1); // get night
		        }
                */

                foreach ($Quotation['accommodation'] as $key => $accommodation) {
                    $ownData = $Quotation['hotel'][$key];
                    $rates = $Quotation['rate']['hotel'][$key] ?? null;
                    if(isset($ownData['driver_accommodation']) && $ownData['driver_accommodation'] == 1) {
                        if(isset($rates) && isset($rates['driver_accommodation'])) {
                            // dd($rates['driver_accommodation']);
                            foreach($rates['driver_accommodation'] as $day => $details) {
                                $Total += $details['modified_rate'];
                            }
                        } else if(isset($ownData)) {
                            $count = $QuotationHotel->getNightHotelBook($ownData['check_in'], $ownData['check_out']);
                            $Total += 8 * $count; // 8 usd driver acc.
                        }
                    }
                    
                }
	        }
	        //Add meal transfer
	        $Total += $TransportCost['meal_transfer']['cost'] ?? 0;

            $CostCutRate = $Costcut->getCostCutRate($Quotation);
            $CostCutPkgRate = $Costcut->getCostCutPkgRate($Quotation);
            $CostCutPkgRateObj = json_decode($CostCutPkgRate);

            if(isset($CostCutPkgRate) && !empty($CostCutPkgRate)) {
                $Total += ($CostCutPkgRateObj->vehicle - $CostCutRate["break_down"]["PP"]["total"]);
            }

            $PerPerson = $Total / $Quotation['pax']['adult'];

            return [
                'total' => $Total,
                'per_person' => $PerPerson
            ];

        } else { //By rates

            $PerPerson = $TransportCost['rates']['total'] / $Quotation['pax']['adult'];

            //Add meal transfer
            $PerPerson += $TransportCost['meal_transfer']['pp'];


            return [
                'total' => $TransportCost['rates']['total'],
                'per_person' => $PerPerson
            ];

        }
    }

    static function getCityviceTransportRate($TransportCost, $QuotationArray) {
        $Total = 0;
        $Place = new Place();
        $Country = $QuotationArray["country"];

        if(isset($TransportCost["mileage"]["each_details"])) {
            foreach ($TransportCost["mileage"]["each_details"] as $each_detail) {
                $distance = reset($each_detail)['distance'];
                $place = key($each_detail);

                $cityRate = Vehicle::find((int)$TransportCost["vehicle"]["vehicle_type"])->CityRate()->get();
                $rate = $TransportCost["vehicle"]["rate"];
                if(isset($cityRate) && !empty($cityRate)) {
                    foreach ($cityRate as $rateObj) {
                        if(in_array($place, explode(',', $rateObj['city']))) {
                            $rate = $rateObj['rate'];
                            break;
                        }
                    }
                }

                $Total += ($rate * ($distance / 1000));
            }
        }

        $ExtraMileage = $Place->getExtraMileage($QuotationArray);
        $AirportMileage = $Place->getAirportMileage($Country, $QuotationArray);

        $Total += ($ExtraMileage + $AirportMileage + $TransportCost["mileage"]["additional_distance"]) * $TransportCost["vehicle"]["rate"];

        return $Total;
    }

    static function getCityviceBataHWRate($TransportCost, $Quotation) {
        $DistanceCharges = [];
        foreach ($TransportCost["mileage"]["each_details"]??[] as $item) {
            $DistanceID = reset($item)["ID"];
            $VehicleID = isset($TransportCost["vehicle_type_data"]) ? $TransportCost["vehicle_type_data"] : $TransportCost["vehicle"]["vehicle_type"];

            $DistanceCharges[] = DistanceCharges::where("distance_id", $DistanceID)->where("vehicle", $VehicleID)->first();
        }
        return $DistanceCharges;
    }

    /**
     * @param $Pax
     * @param $TransportCost
     * @param $Nights
     * @param $Days
     * @return array|bool
     */
    static function getGuideCostList($Pax, $TransportCost, $Nights, $Days)
    {

        if (!$TransportCost)
            return false;

        $GuideCost = [];
        $Total = 0;

        foreach ($TransportCost as $GuideItem) {
            $CurrentGuideCost = QuotationTransport::getGuideCost($Pax, $GuideItem['type'], $GuideItem['rate'], $GuideItem['accommodation'], $GuideItem['tip'], $Nights, $Days);
            $GuideCost[] = $CurrentGuideCost;
            $Total += $CurrentGuideCost['total'];
        }
        $GuideCost['total'] = $Total;
        $GuideCost['per_person'] = $GuideCost['total'] / $Pax['adult'];

        return $GuideCost;

    }

    /**
     * @param $Pax
     * @param $Type
     * @param $Rate
     * @param $Accommodation
     * @param $Tip
     * @param $Nights
     * @param $Days
     * @return mixed
     */
    static function getGuideCost($Pax, $Type, $Rate, $Accommodation, $Tip, $Nights, $Days)
    {

        $TotalFee = $Days * $Rate;
        $TotalAccommodation = $Nights * $Accommodation;
        $TotalTip = $Pax['adult'] * $Tip;

        $Cost['fee'] = currency($TotalFee, 'LKR', 'USD', false);
        $Cost['accommodation'] = currency($TotalAccommodation, 'LKR', 'USD', false);
        $Cost['tip'] = currency($TotalTip, 'LKR', 'USD', false);
        $Cost['total'] = currency($TotalFee + $TotalAccommodation + $TotalTip, 'LKR', 'USD', false);

        return $Cost;
    }


    /**
     * @param $Quotation
     * @return array
     */
    static function getTourDaysDetail($Quotation)
    {

        $ArrivalDate = Carbon::create($Quotation['arrival_date']['year'], $Quotation['arrival_date']['month'], $Quotation['arrival_date']['day'], 0);
        $DaysArray = [];
        $Nights = $Quotation['night'] ?? array_map(function () {
                return 1;
            }, $Quotation['place']);
        $day = 1;


        foreach ($Nights as $Index => $NoNight) {

            for ($i = 1; $i <= $NoNight; $i++) {

                $DaysArray[$day]['place'] = $Quotation['place'][$Index];

                $DaysArray[$day]['date']['year'] = $ArrivalDate->year;
                $DaysArray[$day]['date']['month'] = $ArrivalDate->month;
                $DaysArray[$day]['date']['day'] = $ArrivalDate->day;
                $DaysArray[$day]['index'] = $Index;
                $ArrivalDate->addDay();
                $day++;

            }
        }

        $DaysArray[$day]['place'] = $Quotation['place'][$Index];

        $DaysArray[$day]['date']['year'] = $ArrivalDate->year;
        $DaysArray[$day]['date']['month'] = $ArrivalDate->month;
        $DaysArray[$day]['date']['day'] = $ArrivalDate->day;
        $DaysArray[$day]['index'] = $Index;

        return $DaysArray;
    }

    /**
     * @param $Quotation
     * @return mixed
     */
    function getPathWithAirport($Quotation)
    {
        $Path = $Quotation['place'];
        return $Path;

    }
}
