<?php

namespace App\Model\Quotation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Quotation\QuotationValid
 *
 * @mixin \Eloquent
 */
class QuotationValid extends Model
{

    /**
     * @param $Data
     * @return array|bool
     * @throws \ReflectionException
     */
    static function paxValid($Data){//2
		
		$returnData = array();
		
		 if(isset($Data['arrival_date']) && 
			isset($Data['pax']) && 
			isset($Data['market'])){
				
				
				$returnData = arrayMapMulti('getActualDataType',$Data);
				return $returnData;
				
				
			}
			
		return false;
			
		
	}

    /**
     * @param $Data
     * @return array|bool
     * @throws \ReflectionException
     */
    static function transportValid($Data){//2
		
		$returnData = array();
		
		if(!isset($Data['languages']))
			$Data['languages'] = false;
		
		
		 if(isset($Data['place'])){
				$returnData = arrayMapMulti('getActualDataType',$Data);
				return $returnData;
				
				
			}
			
		return false;
			
		
	}

}
