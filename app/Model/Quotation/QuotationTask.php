<?php

namespace App\Model\Quotation;

use App\Model\Hotel\Rates;
use App\Model\Place\Place;
use App\Model\Hotel\Availability;
use App\Model\Hotel\Hotel;
use App\Model\Profile\Company;
use App\Model\Transport\Transport;
use Illuminate\Database\Eloquent\Model;
use Session;
use Auth;

/**
 * App\Model\Quotation\QuotationTask
 *
 * @mixin \Eloquent
 */
class QuotationTask extends Model
{

    /**
     * @var array
     */
    var $AvailableSlide = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 19, 20, 21, 23, 24, 26, 27, 30, 31, "hotel_rate", "cruise_rate", "transport_rate", "attraction_rate", "meal_rate", "itinerary", "other_rate", "transport_time", "attraction_time"];

    /**
     * @var bool
     */
    var $ReturnStatus = false;

    /**
     * @var bool
     */
    var $warning = false;
    /**
     * @var bool
     */
    var $error = false;

    /**
     * @param $Slide
     * @param $Data
     * @return array|bool
     * @throws \Exception
     * @throws \ReflectionException
     */
    function setSession($Slide, $Data)
    {
        //Tour array
        if ($Slide === 0)
            return $this->ReturnStatus = $this->setSlideArray($Data);

        if (in_array($Slide, $this->AvailableSlide)) {
            if ($Slide == 1) {
                $this->ReturnStatus = $this->setSessionWelcome($Data);
            } elseif ($Slide == 2)
                $this->ReturnStatus = $this->setSessionPax($Data);
            elseif ($Slide == 3)
                $this->ReturnStatus = $this->setTransport($Data);
            elseif ($Slide == 4)
                $this->ReturnStatus = $this->setSessionHotel($Data);
            elseif ($Slide == 5)
                $this->ReturnStatus = $this->setSessionAttraction($Data);
            elseif ($Slide == 6)
                $this->ReturnStatus = $this->setSessionMeal($Data);
            elseif ($Slide == 7)
                $this->ReturnStatus = $this->setSessionFlightSearch($Data);
            elseif ($Slide == 8)
                $this->ReturnStatus = $this->setSessionHotel($Data);
            elseif ($Slide == 9)
                $this->ReturnStatus = $this->setSessionSave($Data);
            elseif ($Slide == 10)
                $this->ReturnStatus = $this->setSessionConfirm($Data);
            elseif ($Slide == 12)
                $this->ReturnStatus = $this->setSessionFlightRecommandation($Data);
            elseif ($Slide == 13) //HotelTBO
                $this->ReturnStatus = $this->setSessionHotelAPI($Data);
            elseif ($Slide == 14)//HotelTBO book room
                $this->ReturnStatus = $this->setSessionHotelAPI($Data);
            elseif ($Slide == 16)
                $this->ReturnStatus = true;
            elseif ($Slide == 17)
                $this->ReturnStatus = $this->setSessionTransportNight($Data);
            elseif ($Slide == 30)
                $this->ReturnStatus = $this->setSessionCostCutting($Data);

            elseif ($Slide == "hotel_rate")
                $this->ReturnStatus = $this->setSessionHotelRate($Data);
            elseif ($Slide == "cruise_rate")
                $this->ReturnStatus = $this->setSessionCruiseRate($Data);
            elseif ($Slide == "transport_rate" || $Slide == 18)
                $this->ReturnStatus = $this->setSessionTransportRate($Data);
            elseif ($Slide == "attraction_rate")
                $this->ReturnStatus = $this->setSessionAttractionRate($Data);
            elseif ($Slide == "meal_rate")
                $this->ReturnStatus = $this->setSessionMealRate($Data);
            elseif ($Slide == "itinerary")
                $this->ReturnStatus = $this->setSessionItinerary($Data);
            elseif ($Slide == "other_rate" || $Slide == 19)
                $this->ReturnStatus = $this->setSessionOtherRate($Data);
            elseif ($Slide == "transport_time" || $Slide == 31)
                $this->ReturnStatus = $this->setSessionTransportTime($Data);
            elseif ($Slide == "attraction_time")
                $this->ReturnStatus = $this->setSessionAttractionTime($Data);

            elseif ($Slide == 20)
                $this->ReturnStatus = $this->setSessionFlightPNR($Data);


            if (isset($Data['next_slide'])) {

                $next_slide = $Data['next_slide'];
                $TourArray = Session::get('quotation.slide_array') ?? [];
                $next_active_slide = $TourArray[array_search($next_slide, $TourArray) + 1];

                if ($next_active_slide) {
                    Session::put("quotation.slide.$next_active_slide", true);
                }
                Session::put("quotation.slide.{$Data['next_slide']}", true);
            }

            Session::put("quotation.slide.$Slide", true);
        }


        if ($this->ReturnStatus !== false) {
            $this->ReturnStatus['slide'] = Session::get("quotation.slide");
            return $this->ReturnStatus;
        } else
            return false;

    }

    /**
     * @param $Slide
     * @return mixed
     */
    static function resetSlide($Slide)
    {
        Session::forget("quotation.slide.$Slide");


        switch ($Slide) {
            case 1:
                Session::forget('quotation.country');
                break;

            case 2:
                Session::forget('quotation.arrival_date');
                Session::forget('quotation.pax');
                Session::forget('quotation.market');
                break;

            case 3:
                Session::forget('quotation.languages');
                Session::forget('quotation.place_full');
                Session::forget('quotation.place_type');
                Session::forget('quotation.place');
                Session::forget('quotation.guide');
                Session::forget('quotation.rate.transport');
                break;

            case 4:
                Session::forget('quotation.accommodation');
                Session::forget('quotation.hotel');
                Session::forget('quotation.rate.hotel');
                break;

            case 5:
                Session::forget('quotation.attraction');
                Session::forget('quotation.city_tour');
                Session::forget('quotation.excursion');
                Session::forget('quotation.rate.attraction');
                break;
            case 6:
                Session::forget('quotation.meal');
                Session::forget('quotation.rate.meal');
                break;
            case 7:
                Session::forget('quotation.flight');
                break;
            case 8:
                break;
            case 9:
                break;
            case 10:
                break;
            case 11:
                break;
        }

        return Session::get('quotation');
    }

    /**
     * @return mixed
     */
    function getSession()
    {

        $quotation = Session::get('quotation');
        return $quotation;


    }

    /**
     * @param bool $tour_type
     * @return array|mixed
     */
    static function getCompletedSlides($tour_type = false)
    {
        if (Session::has('quotation.slide')) {
            return Session::get('quotation.slide');
        } elseif ($tour_type) {
            $TourArray = QuotationTask::getTourSlideArray($tour_type);
            return [$TourArray[0] => true, $TourArray[1] => true];
        } else {
            return [1 => true];
        }
    }

    /**
     * @param $Data
     * @return bool
     * @throws \ReflectionException
     */
    function setSlideArray($Data)
    {
        Session::put('quotation.tour_session', time());
        Session::put('quotation.slide_array', arrayMapMulti('getActualDataType', $Data['slide_array']));
        Session::put('quotation.tour_type', intval($Data['tour_type']));
        Session::put('quotation.transport_only', intval($Data['transport_only']));
        Session::put('quotation.slide', $Data['slide']);
        return true;

    }

    /**
     * @param $Data
     * @param string $Type
     * @return array|bool
     */
    function setSessionWelcome($Data, $Type = '1')
    {

        try {
            if (Session::get('quotation.status') != 2)
                Session::put('quotation.status', intval($Type));

            Session::put('quotation.country', intval($Data['country']));
            Session::put('quotation.currency', intval($Data['currency']));

            return [
                "status" => Session::get('quotation.status'),
                "country" => Session::get('quotation.country')
            ];

        } catch (\Exception $e) {

            $this->error = $e->getMessage();
            return false;
        }

    }


    /**
     * @param $Data
     * @return array|bool
     * @throws \ReflectionException
     */
    function setSessionPax($Data)
    {//2


        $Pax = QuotationValid::paxValid($Data);
        if (!$Pax)
            return false;
        // Get old session data
        $oldPax = Session::get('quotation.pax', []);

        // Convert new data to integer array
        $newPax = array_map('intval', $Pax['pax']);
        if ($oldPax != $newPax) {
            Session::put('system.isPaxChanged', true);
        }
        Session::put('quotation.arrival_date', array_map('intval', $Pax['arrival_date']));
        Session::put('quotation.pax', array_map('intval', $Pax['pax']));
        Session::put('quotation.market', intval($Pax['market']));
        Session::put('quotation.nationality', intval($Pax['nationality']));
        Session::put('quotation.quarantine_type', intval($Pax['quarantine_type']));

        if(isset($Pax['child_age'])) {
            Session::put('quotation.child_age', $Pax['child_age']);
        }


        if (Session::has('quotation.hotel')) {

            $Quotation = new Quotation();
            $ArrivalDate = Session::get('quotation.arrival_date');
            $AdjustArray = $Quotation->getHotelDatesAdjust(Session::get('quotation.hotel'), $ArrivalDate);
            Session::put('quotation.hotel', $AdjustArray);

        }

        $country = Session::get('quotation.country');

        if ((($Pax['pax']['adult'] + $Pax['pax']['cwb'] + $Pax['pax']['cnb']) > 9) && $country == 62) {

            $Guides [] = [
                'type' => 1,
                'rate' => 6000,
                'accommodation' => 5000,
                'tip' => 0
            ];

            Session::put('quotation.guide', $Guides);

        } elseif (!Session::has('quotation.rate.transport')) {
            Session::forget('quotation.guide');
        }

        return [
            'arrival_date' => Session::get('quotation.arrival_date'),
            'pax' => Session::get('quotation.pax'),
            'market' => Session::get('quotation.market'),
            'nationality' => Session::get('quotation.nationality'),
            'quarantine_type' => Session::get('quotation.quarantine_type'),
            'child_age' => Session::get('quotation.child_age')
        ];
    }


    /**
     * @param $Data
     * @return array|bool
     */
    function setTransport($Data)
    {
        $Transport = new Transport();
        $QuotationArray = \Session::get('quotation');

        if (!$Transport->isValidRoute($Data['place'], $QuotationArray['country'], $Data['type'])) {
            $this->warning = $Transport->Errors['message'];
        }

        $Transport = QuotationValid::transportValid($Data);
        $PlaceWithoutStops = Place::removeStops($Transport['place'], $Transport['type']);

        if (!$Transport)
            return false;

        Session::put('quotation.languages', $Transport['languages']);
        Session::put('quotation.place', array_map('intval', $PlaceWithoutStops));
        Session::put('quotation.place_full', array_map('intval', $Transport['place']));
        Session::put('quotation.place_type', array_map('intval', $Transport['type']));
        Session::put('quotation.place_vehicle', array_map('intval', $Transport['place_vehicle']));

        return [
            "languages" => Session::get('quotation.languages'),
            "place" => Session::get('quotation.place'),
            "place_full" => Session::get('quotation.place_full'),
            "place_type" => Session::get('quotation.place_type'),
            "place_vehicle" => Session::get('quotation.place_vehicle'),
        ];

    }

    /**
     * @param $Data
     * @return array
     * @throws \Exception
     * @throws \ReflectionException
     */
    function setSessionHotel($Data)
    {
        $hotel_selected_data = request('hotel_selected_data');
        $hotel_beds_json = request('hotel_beds_json');

        if (Auth::user()->hasRole('agent')) {
            Session::put("quotation.markup_amount", Company::find(\Auth::user()->Profile->company)->markup);
            Session::put("quotation.markup_type", 2);
        }

        $QuotationHotel = new QuotationHotel();
        $QuotationCruise = new QuotationCruise();

        Session::forget("quotation.hotel");
        Session::forget("quotation.cruise");

        if(isset($Data['accommodation'])) {
            foreach ($Data['accommodation'] as $HotelIndex => $Accommodation) {
                if($Accommodation == 1 || $Accommodation == 2) {
                    if (!empty($Data['hotel_setting'][$HotelIndex])) {
                        $HotelDecode = $QuotationHotel->getDecodeArray($Data['hotel_setting'][$HotelIndex]);

                        if (!$HotelDecode) {
                            throw new \Exception($QuotationHotel->Error);
                        }

                        if ($HotelDecode['provider'] == 'hotelbeds') {
                            
                            if (!empty($hotel_selected_data[$HotelIndex]) && !empty($hotel_beds_json[$HotelIndex])) {
                                Session::forget('quotation.api.hotelbeds.hotel.'.$HotelIndex);
                                Session::put("quotation.api.hotelbeds.hotel.$HotelIndex.selected", arrayMapMulti('getActualDataType', json_decode($hotel_selected_data[$HotelIndex], true)));
                                Session::put("quotation.api.hotelbeds.hotel.$HotelIndex.full", arrayMapMulti('getActualDataType', json_decode($hotel_beds_json[$HotelIndex], true)));
                            } else {
                                throw new \Exception("Please Re-select the hotel!");
                            }

                        } else {
                            $accommodation = Session::get('quotation.accommodation');
                            if (isset($Data['driver_accomodation_charges'][$HotelIndex]) && $accommodation[$HotelIndex] == 1  ) {
                                Session::put("quotation.accommodation-charges.$HotelIndex", $Data['driver_accomodation_charges'][$HotelIndex]);
                            } else{
                                Session::forget("quotation.accommodation-charges.$HotelIndex");
                            }
                        }

                        Session::put("quotation.hotel.$HotelIndex", arrayMapMulti('getActualDataType', $HotelDecode));
                        if($Accommodation == 1) {
                            $this->saveHotelRates($HotelIndex, $HotelDecode);
                        }
                    }
                } else if($Accommodation == 4) {
                    if (!empty($Data['cruise_setting'][$HotelIndex])) {
                        $CruiseDecode = $QuotationCruise->getDecodeArray($Data['cruise_setting'][$HotelIndex]);

                        if (!$CruiseDecode) {
                            throw new \Exception($QuotationCruise->Error);
                        }

                        Session::put("quotation.cruise.$HotelIndex", arrayMapMulti('getActualDataType', $CruiseDecode));
                    }
                }
            }
        }

        if (isset($Data['accommodation']))
            Session::put("quotation.accommodation", arrayMapMulti('getActualDataType', $Data['accommodation']));

        if (isset($Data['return_type']))
            Session::put("quotation.hotel_filter.return_type", $Data['return_type']);

        if (isset($Data['return_star']))
            Session::put("quotation.hotel_filter.return_star", $Data['return_star']);

        return [
            'hotel' => Session::get('quotation.hotel'),
            'cruise' => Session::get('quotation.cruise'),
            'accommodation' => Session::get('quotation.accommodation'),
            'accommodation-charges' => Session::get('quotation.accommodation-charges'),
            'hotel_filter' => Session::get('quotation.hotel_filter')
        ];

    }

    function saveHotelRates($HotelIndex, $HotelDecode) {
        $QuotationHotel = new QuotationHotel();
        $Rates = new Rates();

        if (Session::has("quotation.rate.hotel.$HotelIndex")) {

            $RateArray = objectToArray(Session::get("quotation.rate.hotel.$HotelIndex"));

            if (!$Rates->searchInRateArrayHotelSettings(objectToArray($RateArray), $HotelDecode)) {
                $RateArray = objectToArray($QuotationHotel->getHotelCost(Session::get("quotation"), $HotelDecode));
            }

        } else {
            $RateArray = objectToArray($QuotationHotel->getHotelCost(Session::get("quotation"), $HotelDecode));
            $RateArray = $this->getRateArray($RateArray, $HotelDecode);
        }

        Session::put("quotation.temp.hotel.$HotelIndex", $RateArray);
    }

    function getRateArray($RateArray, $HotelDecode) {
        $QuotationHotel = new QuotationHotel();
        $Bookdates = $QuotationHotel->getBookdatesNightCheckIn($HotelDecode['check_in'],$HotelDecode['check_out']);

        $NewHotelArray = [];
        if(isset($Bookdates)) {

            foreach ($Bookdates as $day => $date) {
                if(isset($HotelDecode["room_type"])) {
                    foreach ($HotelDecode["room_type"] as $roomType => $count) {
                        if($count > 0) {
                            $NewHotelArray["adult"][$day][$roomType]["hotel"] = $HotelDecode["hotel"];
                            $NewHotelArray["adult"][$day][$roomType]["year"] = $date["year"];
                            $NewHotelArray["adult"][$day][$roomType]["month"] = $date["month"];
                            $NewHotelArray["adult"][$day][$roomType]["day"] = $date["day"];
                            $NewHotelArray["adult"][$day][$roomType]["room_category"] = $HotelDecode["room_category"];
                            $NewHotelArray["adult"][$day][$roomType]["meal"] = $HotelDecode["meal_type"];
                            $NewHotelArray["adult"][$day][$roomType]["room_type"] = $roomType;
                            $NewHotelArray["adult"][$day][$roomType]["is_modified"] = 0;
                            $NewHotelArray["adult"][$day][$roomType]["rate"] = $RateArray["adult"][$day][$roomType]["rate"] ?? 0;
                            $NewHotelArray["adult"][$day][$roomType]["reason"] = "";
                            $NewHotelArray["adult"][$day][$roomType]["modified_rate"] = $RateArray["adult"][$day][$roomType]["rate"] ?? 0;
                        }
                    }
                }

                if(isset($HotelDecode["hotel"]) && !empty($HotelDecode["hotel"])) {
                    $NewHotelArray["child"]["cwb"][$day]["hotel"] = $HotelDecode["hotel"];
                    $NewHotelArray["child"]["cwb"][$day]["year"] = $date["year"];
                    $NewHotelArray["child"]["cwb"][$day]["month"] = $date["month"];
                    $NewHotelArray["child"]["cwb"][$day]["day"] = $date["day"];
                    $NewHotelArray["child"]["cwb"][$day]["room_category"] = $HotelDecode["room_category"];
                    $NewHotelArray["child"]["cwb"][$day]["meal"] = $HotelDecode["meal_type"];
                    $NewHotelArray["child"]["cwb"][$day]["is_modified"] = 0;
                    $NewHotelArray["child"]["cwb"][$day]["rate"] = $RateArray["child"]["cwb"][$day]["rate"] ?? 0;
                    $NewHotelArray["child"]["cwb"][$day]["reason"] = "";
                    $NewHotelArray["child"]["cwb"][$day]["modified_rate"] = $RateArray["child"]["cwb"][$day]["rate"] ?? 0;

                    $NewHotelArray["child"]["cnb"][$day]["hotel"] = $HotelDecode["hotel"];
                    $NewHotelArray["child"]["cnb"][$day]["year"] = $date["year"];
                    $NewHotelArray["child"]["cnb"][$day]["month"] = $date["month"];
                    $NewHotelArray["child"]["cnb"][$day]["day"] = $date["day"];
                    $NewHotelArray["child"]["cnb"][$day]["room_category"] = $HotelDecode["room_category"];
                    $NewHotelArray["child"]["cnb"][$day]["meal"] = $HotelDecode["meal_type"];
                    $NewHotelArray["child"]["cnb"][$day]["is_modified"] = 0;
                    $NewHotelArray["child"]["cnb"][$day]["rate"] = $RateArray["child"]["cnb"][$day]["rate"] ?? 0;
                    $NewHotelArray["child"]["cnb"][$day]["reason"] = "";
                    $NewHotelArray["child"]["cnb"][$day]["modified_rate"] = $RateArray["child"]["cnb"][$day]["rate"] ?? 0;
                }
            }
        }

        return $NewHotelArray;
    }

    /**
     * @param $Data
     * @return array
     * @throws \ReflectionException
     */
    function setSessionMeal($Data)
    {

        foreach ($Data['meal'] as $Day => $MealItem) {
            if ($MealItem) {
                foreach ($MealItem as $MealTime => $MealV) {
                    if ($MealV)
                        Session::put("quotation.meal.$Day.$MealTime", arrayMapMulti('getActualDataType', $MealV));
                }
            }
        }

        //Meal Rates
        if (isset($Data['meal_rate'])) {
            $this->setSessionMealRate($Data);
        } else {
            Session::forget("quotation.rate.meal");
        }


        return [
            'meal' => Session::get('quotation.meal')
        ];
    }

    /**
     * @param $Data
     * @return array
     * @throws \ReflectionException
     */
    function setSessionHotelRate($Data)
    {
        Session::put("quotation.rate.hotel.{$Data['hotel_details']['index']}", arrayMapMulti('getActualDataType', $Data['hotel_rate']));
        return [
            'rate' => Session::get('quotation.rate')
        ];
    }

    /**
     * @param $Data
     * @return array
     * @throws \ReflectionException
     */
    function setSessionCruiseRate($Data)
    {
        Session::put("quotation.rate.cruise.{$Data['hotel_details']['index']}", arrayMapMulti('getActualDataType', $Data['cruise_rate']));
        return [
            'rate' => Session::get('quotation.rate')
        ];
    }

    /**
     * @param $Data
     * @return array
     * @throws \ReflectionException
     */
    function setSessionAttractionRate($Data)
    {

        $Data['rate'] = arrayMapMulti('getActualDataType', $Data['rate']);


        if (!empty($Data['rate']))
            Session::put("quotation.rate.attraction", $Data['rate']);
        else
            Session::forget("quotation.rate.attraction");


        return [
            'rate' => Session::get('quotation.rate')
        ];

    }

    /**
     * @param $Data
     * @return array
     * @throws \ReflectionException
     */
    function setSessionAttractionTime($Data)
    {

        $Data['time'] = arrayMapMulti('getActualDataType', $Data['time']);

        if (!empty($Data['time']))
            Session::put("quotation.time.attraction", $Data['time']);
        else
            Session::forget("quotation.time.attraction");


        return [
            'time' => Session::get('quotation.time')
        ];

    }

    /**
     * @param $Data
     * @return array
     */

    function setSessionMealRate($Data)
    {

        Session::forget("quotation.rate.meal");

        foreach ($Data['meal_rate'] as $Day => $MealItem) {

            foreach (($MealItem ?? []) as $mealTime => $mealRate) {

                if ($mealRate) {

                    if ($Data['meal'][$Day][$mealTime]['meal_where'] == 2) {

                        Session::put("quotation.rate.meal.$Day.$mealTime", getActualDataType($mealRate));
                        Session::put("quotation.rate.meal_child.$Day.$mealTime", getActualDataType($Data['meal_child_rate'][$Day][$mealTime]));

                    } elseif ($Data['meal'][$Day][$mealTime]['meal_where'] == 3) {

                        Session::put("quotation.rate.meal.$Day.$mealTime", getActualDataType($mealRate));
                        Session::put("quotation.rate.meal_child.$Day.$mealTime", getActualDataType($Data['meal_child_rate'][$Day][$mealTime]));

                    } else {

                        Session::put("quotation.rate.meal.$Day.$mealTime", 0);
                        Session::put("quotation.rate.meal_child.$Day.$mealTime", 0);

                    }
                }
            }
        }

        return [
            'rate' => Session::get('quotation.rate')
        ];
    }

    /**
     * @param $Data
     * @return array
     * @throws \ReflectionException
     */
    function setSessionTransportRate($Data)
    {
        if (isset($Data['guide'])) {
            $Guide = arrayMapMulti('getActualDataType', $Data['guide']);
            unset($Data['guide']);
        }

        $NewPax = arrayMapMulti('getActualDataType', $Data['pax']);
        $NewChildAges = $Data['child_age']??[];
        $CurrentPax = Session::get('quotation.pax');

        if ($NewPax !== $CurrentPax)
            $Return['pax_change'] = true;
        else
            $Return['pax_change'] = false;


        Session::put("quotation.pax", $NewPax);
        Session::put("quotation.child_age", $NewChildAges);

        unset($Data['pax']);

        Session::put("quotation.rate.transport", $Data);

        if (isset($Guide)) {
            Session::put("quotation.guide", $Guide);
        }

        return [
            'pax' => Session::get('quotation.pax'),
            'child_age' => Session::get('quotation.child_age'),
            'guide' => Session::get('quotation.guide'),
            'rate' => Session::get('quotation.rate')
        ];
    }


    /**
     * @param $Data
     * @return array
     * @throws \ReflectionException
     */
    function setSessionAttraction($Data)
    {
        Session::forget("quotation.attraction");
        Session::forget("quotation.city_tour");
        Session::forget("quotation.excursion");
        Session::forget("quotation.attraction_time");
        Session::forget("quotation.city_tour_time");
        Session::forget("quotation.excursion_time");

        $i = 0;
        if (isset($Data['attraction'])) {
            foreach ($Data['attraction'] as $Day => $AttractionItem) {
                if ($AttractionItem) {
                    if(isset($AttractionItem)) {
                        Session::put("quotation.attraction.$Day", arrayMapMulti(['getActualDataType'], $AttractionItem));
                        $arrayItem = arrayMapMulti(['getActualDataType'], $AttractionItem);
                        $tempArr = [];
                        foreach($arrayItem as $key => $attr) {
                            Session::put("quotation.attraction_time.$i", array("id"=>$attr, "time" => $Data['time']['attraction'][$attr] ?? null, 'day'=>$Day));
                            $i++;
                        }
                    }
                }
            }
        }

        $i = 0;
        if (isset($Data['city_tour'])) {

            foreach ($Data['city_tour'] as $Day => $CityTourItem) {

                if ($CityTourItem) {
                    if(isset($CityTourItem)) {
                        Session::put("quotation.city_tour.$Day", arrayMapMulti('getActualDataType', $CityTourItem));
                        $arrayItem = arrayMapMulti(['getActualDataType'], $CityTourItem);
                        $tempArr = [];
                        foreach($arrayItem as $key => $attr) {
                            Session::put("quotation.city_tour_time.$i", array("id"=>$attr, "time" => $Data['time']['city_tour'][$attr] ?? null, 'day'=>$Day));
                            $i++;
                        }
                    }
                }
            }

        }

        $i = 0;
        if (isset($Data['excursion'])) {

            foreach ($Data['excursion'] as $Day => $ExcursionItem) {
                if ($ExcursionItem) {
                    if(isset($ExcursionItem)) {
                        Session::put("quotation.excursion.$Day", arrayMapMulti('getActualDataType', $ExcursionItem));
                        $arrayItem = arrayMapMulti(['getActualDataType'], $ExcursionItem);
                        $tempArr = [];
                        foreach($arrayItem as $key => $attr) {
                            Session::put("quotation.excursion_time.$i", array("id"=>$attr, "time" => $Data['time']['excursion'][$attr] ?? null, 'day'=>$Day));
                            $i++;
                        }
                    }
                }
            }

        }

        if (isset($Data['rate'])) {
            Session::forget('quotation.temp.attraction');
            Session::put('quotation.temp.attraction.attraction', array_filter($Data['rate']["attraction"]??[]));
            Session::put('quotation.temp.attraction.city_tour', array_filter($Data['rate']["city_tour"]??[]));
            Session::put('quotation.temp.attraction.excursion', array_filter($Data['rate']["excursion"]??[]));
        }

        return [
            'attraction' => Session::get('quotation.attraction'),
            'city_tour' => Session::get('quotation.city_tour'),
            'excursion' => Session::get('quotation.excursion')
        ];


    }


    /**
     * @param $Data
     * @return array
     */
    function setSessionItinerary($Data)
    {

        if ($Data['type'] == 'day_text') {
            Session::put("quotation.itinerary.days." . $Data['day'] . '.day_text', $Data['text']);
        } elseif ($Data['type'] == 'attraction_order') {

            //first remove all the attraction in the iternery
            if (Session::has("quotation.itinerary.days")) {
                foreach (Session::get("quotation.itinerary.days") as $Day => $DayItem) {
                    Session::put("quotation.itinerary.days.{$Day}.attraction", false);
                    Session::put("quotation.itinerary.days.{$Day}.excursion", false);
                    Session::put("quotation.itinerary.days.{$Day}.city_tour", false);
                }
            }


            //then set new order attraction
            $index = 0;
            foreach ($Data['attraction'] as $AttractionID => $Day) {
                Session::put("quotation.itinerary.days.{$Day}.attraction.$AttractionID", Session::get("quotation.itinerary.days.{$Day}.attraction.$AttractionID"));
                $index++;
            }

            $index = 0;
            foreach ($Data['excursion'] as $AttractionID => $Day) {
                Session::put("quotation.itinerary.days.{$Day}.excursion.$AttractionID", Session::get("quotation.itinerary.days.{$Day}.excursion.$AttractionID"));
                $index++;
            }

            $index = 0;

            // dd($Data['city_tour']);
            foreach ($Data['city_tour'] as $AttractionID => $Day) {
                Session::put("quotation.itinerary.days.{$Day}.city_tour.$AttractionID", Session::get("quotation.itinerary.days.{$Day}.city_tour.$AttractionID"));
                $index++;
            }

        } elseif ($Data['type'] == 'attraction_text') {

            $attrID = intval($Data['attraction_id']);

            if ($Data['attr_type'] == 'attraction')
                Session::put("quotation.itinerary.days.{$Data['day']}.attraction.$attrID", $Data['text']);
            elseif ($Data['attr_type'] == 'city_tour')
                Session::put("quotation.itinerary.days.{$Data['day']}.city_tour.$attrID", $Data['text']);
            elseif ($Data['attr_type'] == 'excursion')
                Session::put("quotation.itinerary.days.{$Data['day']}.excursion.$attrID", $Data['text']);


        } elseif ($Data['type'] == 'day_header') {

            Session::put("quotation.itinerary.days." . $Data['day'] . '.day_header', $Data['text']);

        } elseif ($Data['type'] == 'show_itinerary') {

            Session::put("quotation.itinerary.show_itinerary", $Data['show_itinerary']);

        }

        return [
            'itinerary' => Session::get('quotation.itinerary')
        ];

    }

    /**
     * @param $Data
     * @return array
     */
    function setSessionOtherRate($Data)
    {
        if (isset($Data['custom']))
            Session::put("quotation.other_rate", $Data['custom']);
        else
            Session::forget("quotation.other_rate");

        return [
            'other_rate' => Session::get('quotation.other_rate')
        ];
    }

    /**
     * @param $Data
     * @return array
     */
    function setSessionTransportTime($Data)
    {
        unset($Data['current_slide']);
        unset($Data['name']);

        if (isset($Data))
            Session::put("quotation.activity", $Data);
        else
            Session::forget("quotation.activity");

        return [
            'activity' => Session::get('quotation.activity')
        ];
    }
    

    /**
     * @param $Data
     * @return array
     */
    function setSessionSave($Data)
    {

        $QuotationArray = \Session::get('quotation');

        if (isset($Data["save_type"]) && $Data["save_type"] == "confirm") {
            $status = $this->checkHotelsStatus($QuotationArray);

            if ($status) {
                if ($status['status'] === 'error')
                    $this->error = $status['msg'];
                else
                    $this->warning = $status['msg'];

            }
        }

        if (Auth::user()->can('add_markup')) {
            Session::put("quotation.markup_amount", $Data['markup_amount']);
            Session::put("quotation.markup_type", $Data['markup_type']);
            Session::put("quotation.markup_amount_child", $Data['markup_amount_child']);
            Session::put("quotation.markup_type_child", $Data['markup_type_child']);
        } else {
            Session::put("quotation.markup_amount", Company::find(\Auth::user()->Profile->company)->markup);
            Session::put("quotation.markup_type", 2);
            Session::put("quotation.markup_amount_child", Company::find(\Auth::user()->Profile->company)->markup);
            Session::put("quotation.markup_type_child", 2);
        }

        Session::put("quotation.agent", $Data['agent']??"");
        Session::put("quotation.sales_person", $Data['sales_person']??"");
        Session::put("quotation.agent_email", $Data['agent_email']??"");
        Session::put("quotation.follow_up_email_subject", $Data['follow_up_email_subject']??"");
        Session::put("quotation.sales_tracking_id", $Data['sales_tracking_id']??"");
        Session::put("quotation.additional_emails", $Data['additional_emails']??"");

        if (Session::get('quotation.save_type') !== 'cancel')
            Session::put("quotation.save_type", 'save');

        if (!empty($Data['send_email']))
            Session::put("quotation.send_email", true);
        else
            Session::put("quotation.send_email", false);

        Session::put("quotation.note", $Data['note']);

        return [
            'markup_amount' => Session::get('quotation.markup_amount'),
            'markup_type' => Session::get('quotation.markup_type'),
            'agent_email' => Session::get('quotation.agent_email'),
            'follow_up_email_subject' => Session::get('quotation.follow_up_email_subject'),
            'sales_tracking_id' => Session::get('quotation.sales_tracking_id'),
            'additional_emails' => Session::get('quotation.additional_emails'),
            'save_type' => Session::get('quotation.save_type'),
            'note' => Session::get('quotation.note'),
            'send_email' => Session::get('quotation.send_email')
        ];

    }

    /**
     * @param $Data
     * @return array
     */
    function setSessionConfirm($Data)
    {

        Session::put("quotation.confirm.client_title", $Data['client_title']);
        Session::put("quotation.confirm.client_name", $Data['client_name']);
        Session::put("quotation.confirm.client_email", $Data['client_email']??"");

        if (Session::get('quotation.save_type') !== 'cancel')
            Session::put("quotation.save_type", 'confirm');

        if (Session::get('quotation.tour_type') == 3) {
            Session::put("quotation.confirm.remark", $Data['remark'] ?? "");
            Session::put("quotation.confirm.confirm_note", $Data['confirm_note'] ?? "");

            if(isset($Data['cancel_hotel'])) {
                Session::put("quotation.cancel.cancel_hotel", $Data['cancel_hotel'] ?? "");
                Session::put("quotation.cancel.cancel_remark", $Data['cancel_remark'] ?? "");
                Session::put("quotation.cancel.cancel_note", $Data['cancel_note'] ?? "");
            }

            if(isset($Data['dayused_remark'])) {
                Session::put("quotation.dayused.dayused_remark", $Data['dayused_remark'] ?? "");
                Session::put("quotation.dayused.dayused_note", $Data['dayused_note'] ?? "");
            }
        }

        return [
            'client_title' => Session::get('quotation.client_title'),
            'client_name' => Session::get('quotation.client_name'),
            'save_type' => Session::get('quotation.save_type'),

            'confirm' => Session::get('quotation.confirm'),
            'cancel' => Session::get('quotation.cancel'),
        ];
    }

    /**
     * @param $Data
     * @throws \ReflectionException
     */
    function setSessionTransportNight($Data)
    {

        $Data = arrayMapMulti('getActualDataType', $Data['night']);
        Session::put("quotation.night", $Data);

    }

    /**
     * @param $Data
     * @return bool
     */
    function setSessionHotelAPI($Data)
    {


        if (!empty($Data['api_name'])) {

            Session::put("quotation.api_hotel.api", $Data['api_name']);

            //TBO
            if ($Data['api_name'] == 'tbo') {

                if (!empty($Data['type']) && $Data['type'] == 'available_check') {

                    Session::put("quotation.api_hotel.hotel_tbo.session_id", $Data['session_id']);
                    Session::put("quotation.api_hotel.hotel_tbo.hotel.index", $Data['result_index']);
                    Session::put("quotation.api_hotel.hotel_tbo.hotel.code", $Data['hotel_code']);

                } elseif (!empty($Data['type']) && $Data['type'] == 'room_available_price_check') {

                    Session::put("quotation.api_hotel.hotel_tbo.RoomIndex", $Data['RoomIndex']);
                }
            } elseif ($Data['api_name'] == 'hotelbed') { //Hotelbeds

                if (!empty($Data['type']) && $Data['type'] == 'available_check') {

                    Session::put("quotation.api_hotel.hotelbed.hotel_code", $Data['hotel_code']);
                    Session::put("quotation.api_hotel.hotelbed.rooms", json_decode($Data['room']));

                } else {//room settings
                    Session::put("quotation.api_hotel.hotelbed.room_settings.room_category", $Data['room_category']);
                    Session::put("quotation.api_hotel.hotelbed.room_settings.meal", $Data['meal']);
                    Session::put("quotation.api_hotel.hotelbed.room_settings.rate", $Data['rate']);
                }

            }

            return true;


        } else
            return false;

    }

    function setSessionFlightSearch($Data)
    {//7

        Session::put('quotation.flight_search', $Data);

        return Session::get('quotation.flight_search');

    }


    function setSessionFlightRecommandation($Data)
    {//12

        // Session::put('quotation.flight_recommandation', $Data);

        // return Session::get('quotation.flight_recommandation');
        return $Data;
    }

    function setSessionFlightPNR($Data)
    {//20
        // Session::put('quotation.flight_recommandation', $Data);

        // return Session::get('quotation.flight_recommandation');
        return $Data;
    }

    function setSessionCostCutting($Data) {
        Session::forget("quotation.cost_cutting");
        Session::forget("quotation.cost_cut_rate");

        if (!empty($Data['cost_cutting'])) {
            foreach ($Data['cost_cutting'] as $key => $dayCostCut) {
                if(isset($dayCostCut)) {
                    Session::put('quotation.cost_cutting.'.$key, array_filter($dayCostCut));
                    foreach (Session::get('quotation.cost_cutting.'.$key) as $id => $data) {
                        Session::put('quotation.cost_cut_rate.'.$key.'.'.$id, $Data['cost_cut_rate'][$key][$id]??0);
                    }
                }
            }

        }
    }

    /**
     * @return array
     */
    static function getSlideNames()
    {
        return [
            1 => 'Welcome',
            2 => 'Pax',
            3 => 'Transport',
            4 => 'Hotels',
            5 => 'Attraction',
            6 => 'Meal',
            7 => 'Flight',
            8 => 'Quotation',
            9 => 'Save',
            10 => 'Confirmation',
            11 => 'Agree',
            12 => 'Flight Results',
            13 => 'Hotels (API)',
            14 => 'Hotels Book',
            15 => 'Room Book',
            16 => 'Final',
            17 => 'Nights',
            18 => 'Flight Information',
            20 => 'PNR',
            21 => 'Tickets'
        ];

    }

    /**
     * @param $TourType
     * @return mixed
     */
    static function getTourSlideArray($TourType)
    {
        /*if($TourType == "undefined") {
            $TourType = 3;
        }*/

        $TourArray = [
            1 => [7, 12, 20, 21, 22, 23, 24, 26, 27], // 1 => [7, 12, 18, 16],
            2 => [1, 13, 14, 15, 16],
            3 => [1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 16],//normal
            4 => [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 16],
            5 => [10, 11, 16],//cancel
            6 => [1, 2, 3, 17, 5, 8, 9, 10, 11, 16],// transport only
        ];


        return $TourArray[$TourType];

    }

    function checkHotelsStatus($QuotationArray)
    {
        $Market = $QuotationArray["market"];
        $status = "Available";

        if(isset($QuotationArray['hotel'])) {
            foreach ($QuotationArray['hotel'] as $HotelSettings) {
                $HotelStatus = Hotel::getStatus($HotelSettings['hotel']??0, $HotelSettings['check_in'], $HotelSettings['room_type'], $HotelSettings['meal_type'], $HotelSettings['room_category'], $Market);
                $HotelStatusText = Availability::find($HotelStatus[0])->status;

                if ($HotelStatusText != "Available") {
                    $status = $HotelStatusText;
                }
            }
        }
        
        if ($status != "Available") {
            if (Auth::user()->can('different_conformation_message')) {
                return ['msg' => "This is an On Request. Please contact our ground handling staff.", 'status' => 'error'];
            } else {
                return ['msg' => "This is an On Request. Please check the availability.", 'status' => 'warning'];
            }
        } else {
            return false;
        }
    }

}
