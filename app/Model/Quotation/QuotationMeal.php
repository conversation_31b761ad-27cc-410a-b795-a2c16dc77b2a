<?php

namespace App\Model\Quotation;

use App\CountryCurrency;
use App\Model\Meal\Meal;
use App\Model\Meal\MealType;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Quotation\QuotationMeal
 *
 * @mixin \Eloquent
 */
class QuotationMeal extends Model
{


    /**
     * @param $Food
     * @param $MealTime
     * @return int|string|\Torann\Currency\Currency
     */
    function getMealCost($Food, $MealTime)
    {
        $Cost = 0;
        if (empty($Food)) {//if food is not specified get default meal rates
            $Meal = new Meal();
            $Cost += currency($Meal->getDefaultOutsideResturentRate($MealTime)->rate, null, null, false);
        } else {//get default food rates
            $Cost += currency(MealType::find($Food)->rate, null, null, false);

        }

        return $Cost;
    }

    /**
     * @param $Quotation
     * @return mixed
     */
    static function getMealRatePlane($Quotation)
    {

        $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);
        $DaysDetail = Quotation::getTourDaysDetail($Quotation);

        $MealRate = Meal::getMeal($Quotation);

        $Rate = 0;
        $RateChild = 0;

        if (isset($Quotation['meal'])) {

            foreach ($Quotation['meal'] as $Index => $MealList) {

                if(empty($DaysDetail[$Index])) continue;

                $PlaceID = $DaysDetail[$Index]['place'];

                $MealCurrency = CountryCurrency::getPlaceCurrency($PlaceID, 'meal');
                if(isset($MealList)) {
                    foreach ($MealList as $MealTime => $MealItem) {

                        if(isset($MealRate['adult'][$Index][$MealTime]) && $MealRate['adult'][$Index][$MealTime] != 0) {
                            if(!is_null($MealRate['adult'][$Index][$MealTime])) {

                                $Rate += currency(($MealRate['adult'][$Index][$MealTime] ?? 0), $MealCurrency->code ?? 0, $FinalCurrency->code?? 0, false);
                            }
                        }

                        if(isset($MealRate['child'][$Index][$MealTime]) && $MealRate['child'][$Index][$MealTime] != 0) {
                            if(!is_null($MealRate['child'][$Index][$MealTime])) {
                                $RateChild = $RateChild + currency(($MealRate['child'][$Index][$MealTime] ?? 0), $MealCurrency->code ?? 0, $FinalCurrency->code?? 0, false);
                            }
                        } else {
                            if(isset($MealRate['adult'][$Index][$MealTime])) {
                                $RateChild = $RateChild + currency(($MealRate['adult'][$Index][$MealTime])/2, $MealCurrency->code ?? 0, $FinalCurrency->code?? 0, false);
                            }
                        }

                    }
                }
            }
        }
        $NumChild = $Quotation['pax']['cwb'] + $Quotation['pax']['cnb'];
        $ChildCost = 0;

        $Cost['pax_cost']['adult'] = $Rate;

        if ($NumChild)//if any child calculate child rate
            // $ChildCost = $Cost['pax_cost']['child'] = @($Rate / 2 * $NumChild / $NumChild);
            $ChildCost = $Cost['pax_cost']['child'] = $RateChild;

        $Cost['cost']['total'] = ($Rate * $Quotation['pax']['adult']) + ($ChildCost * $NumChild);
        return $Cost;
    }
}