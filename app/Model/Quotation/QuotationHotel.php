<?php
/**
 * Supun Praneeth 2018.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\Quotation;

use App\Model\Hotel\DiscountRates;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\Meal;
use App\Model\Hotel\Rates;
use App\Model\Hotel\Room;
use App\Model\Hotel\Supplement;
use App\Model\Markup;
use Carbon\Carbon;
use DB;
use hotelbeds\hotel_api_sdk\model\Pax;
use Illuminate\Database\Eloquent\Model;
use Session;

/**
 * App\Model\Quotation\QuotationHotel
 *
 * @mixin \Eloquent
 */
class QuotationHotel extends Model
{


    /**
     * @var array
     */
    var $AvailableHotelsIndex = [];
    /**
     * @var
     */
    var $SelectedRates;
    /**
     * @var
     */
    var $Error;


    /**
     * @param $ArrivalDate
     * @param $Places
     * @param int $addEach
     * @param array $Nights
     * @return array
     */
    static function getHotelBookDates($ArrivalDate, $Places, $addEach = 1, $Nights = [])
    {
        $dt = Carbon::create($ArrivalDate['year'], $ArrivalDate['month'], $ArrivalDate['day'], 0);
        $Dates = [];
        foreach ($Places as $Key => $PlaceID) {

            $Night = $Nights[$Key] ?? $addEach;

            $Dates[$Key]['index'] = $Key;
            $Dates[$Key]['place'] = $PlaceID;
            $Dates[$Key]['check_in'] = array('year' => $dt->year, 'month' => $dt->month, 'day' => $dt->day);
            $dt->addDays($Night);
            $Dates[$Key]['check_out'] = array('year' => $dt->year, 'month' => $dt->month, 'day' => $dt->day);

        }

        return $Dates;
    }

    /**
     * @param $HotelID
     * @param $Query
     * @return string
     */
    function getChangeHotelQuery($HotelID, $Query)
    {

        parse_str($Query, $output);
        $output['hotel'] = $HotelID;

        return http_build_query($output);

    }

    /**
     * @param bool $MainArray
     * @param bool $only_dates
     * @return string
     */
    static function getHotelQueryFromArray($MainArray = false, $only_dates = false)
    {
        if (!empty($MainArray['hotel'])) {

            $validator = \Validator::make($MainArray, [
                'hotel' => 'exists:apple_hotels,ID',
                'place' => 'required|exists:apple_places,ID',
                'check_in' => 'required',
                'check_out' => 'required',
                'room_category' => 'required|exists:apple_hotel_room_category,ID',
                'meal_type' => 'required|exists:apple_meal_plan,ID',
                'driver_accommodation' => 'required'
            ]);


            if ($validator->fails()) {
                return false;
            }
        } else {

            $validator = \Validator::make($MainArray, [
                'place' => 'required|exists:apple_places,ID',
                'check_in' => 'required',
                'check_out' => 'required'
            ]);


            if ($validator->fails()) {
                return false;
            }
        }


        $ID = isset($MainArray['hotel']) ? $MainArray['hotel'] : false;
        $Place = isset($MainArray['place']) ? $MainArray['place'] : false;

        $check_in = $MainArray['check_in'];
        $check_out = $MainArray['check_out'];

        $room_category = isset($MainArray['room_category']) ? $MainArray['room_category'] : false;
        $meal_type = isset($MainArray['meal_type']) ? $MainArray['meal_type'] : false;
        $driver_accommodation = isset($MainArray['driver_accommodation']) ? $MainArray['driver_accommodation'] : false;


        if (!empty($MainArray['room_type'])) {
            foreach ($MainArray['room_type'] as $RoomTypeID => $RoomCOunt) {
                $room_type[$RoomTypeID] = $RoomCOunt;
            }
        } else {
            $room_type[1] = false;
            $room_type[2] = false;
            $room_type[3] = false;
            $room_type[4] = false;
            $room_type[5] = false;
        }


        $extrabed = isset($MainArray['extrabed']) ? $MainArray['extrabed'] : false;
        $provider = isset($MainArray['provider']) ? $MainArray['provider'] : false;
        return QuotationHotel::getEncodeArray($ID, $check_in, $check_out, $room_category, $meal_type, $room_type, $extrabed, $Place, $provider, false, $driver_accommodation);

    }

    /**
     * @param $ID
     * @param $check_in
     * @param $check_out
     * @param int $room_category
     * @param int $meal_type
     * @param array $room_type
     * @param int $extrabed
     * @param bool $Place
     * @param string $Provider
     * @return string
     */
    static function getEncodeArray($ID, $check_in, $check_out, $room_category = 9, $meal_type = 2, $room_type = ['1' => 1, '2' => 0, '3' => 0, '4' => 0, '5' => 0], $extrabed = 0, $Place = false, $Provider = 'local', $nights = false, $driver_accommodation = 1)
    {


        $MainArray = array();
        $MainArray['hotel'] = $ID;
        $MainArray['place'] = $Place;


        $MainArray['check_in'] = $check_in;
        $MainArray['check_out'] = $check_out;

        $CheckInDate = Carbon::create($MainArray['check_in']['year'], $MainArray['check_in']['month'], $MainArray['check_in']['day'], 0);
        $CheckOutDate = Carbon::create($MainArray['check_out']['year'], $MainArray['check_out']['month'], $MainArray['check_out']['day'], 0);

        if($nights) {
            $MainArray['nights'] = $nights;
            $CheckOutDate = $CheckInDate->addDays($nights);
            $MainArray['check_out']['year'] = $CheckOutDate->year;
            $MainArray['check_out']['month'] = $CheckOutDate->month;
            $MainArray['check_out']['day'] = $CheckOutDate->day;
        } else {
            $MainArray['nights'] = $CheckInDate->diffInDays($CheckOutDate);
        }


        $MainArray['provider'] = $Provider;
        $MainArray['driver_accommodation'] = $driver_accommodation;


        $MainArray['room_category'] = $room_category;
        $MainArray['meal_type'] = $meal_type;

        if (empty($room_type))
            $MainArray['room_type'] = ['1' => 1, '2' => 0, '3' => 0, '4' => 0, '5' => 0];
        else
            $MainArray['room_type'] = $room_type;


        $MainArray['extrabed'] = $extrabed;


        return (http_build_query($MainArray));

    }

    /**
     * @param $ID
     * @param $check_in
     * @param $check_out
     * @param $Place
     * @param $SelectedRate
     * @return string
     */
    static function getEncodeArrayHotelbeds($ID, $check_in, $check_out, $Place, $SelectedRate)
    {

        $MainArray = [];
        $MainArray['hotel'] = $ID;
        $MainArray['place'] = $Place;


        $MainArray['check_in'] = $check_in;
        $MainArray['check_out'] = $check_out;
        $MainArray['nights'] = QuotationHotel::getNightHotelBook($MainArray['check_in'], $MainArray['check_out']);

        $MainArray['provider'] = "hotelbeds";

        //special
        $MainArray['room_type'] = HotelBed::getRoomTypesFromRate($SelectedRate);
        $MainArray['meal_type'] = $SelectedRate[0]['meal'];
        $MainArray['room_category'] = $SelectedRate[0]['category'];
        $MainArray['extrabed'] = 0;

        return (http_build_query($MainArray));

    }

    /**
     * @param $HotelDataQuery
     * @return mixed
     */
    static function getDecodeArray($HotelDataQuery)
    {
        parse_str($HotelDataQuery, $output_f);
        parse_str(QuotationHotel::getHotelQueryFromArray($output_f), $output);
        return $output_f;

    }

    /**
     * @param $Quotation
     * @return float|int
     */
    static function getAllNight($Quotation)
    {
        $Night = 0;
        if (isset($Quotation['hotel'])) {
            foreach ($Quotation['hotel'] as $HotelSettingItem)
                if(!empty($HotelSettingItem)) {
                    $Night += QuotationHotel::getNightHotelBook($HotelSettingItem['check_in'], $HotelSettingItem['check_out']);
                }

        } else {
            foreach ($Quotation['place'] as $PlaceID)
                $Night++;
        }
        return $Night;
    }

    /**
     * @param $checkin
     * @param $checkout
     * @return float|int
     */
    static function getNightHotelBook($checkin, $checkout)
    {
        $FirstDate = strtotime($checkin['year'] . '/' . $checkin['month'] . '/' . $checkin['day']);
        $SecondDate = strtotime($checkout['year'] . '/' . $checkout['month'] . '/' . $checkout['day']);

        return ($SecondDate - $FirstDate) / 86400;

    }

    /**
     * @param $checkin
     * @param $checkout
     * @return array
     */
    function getBookdatesNightCheckIn($checkin, $checkout)
    {
        $checkInObject = Carbon::create($checkin['year'], $checkin['month'], $checkin['day'], 0);

        $FirstDate = $checkInObject;
        $BookDateList = [];

        $nights = $this->getNightHotelBook($checkin, $checkout);
        $CurrentDate = $FirstDate;


        for ($c = 0; $c < $nights; $c++) {

            $BookDateList[] = [
                "year" => $CurrentDate->year,
                "month" => $CurrentDate->month,
                "day" => $CurrentDate->day,
            ];
            $CurrentDate = $FirstDate->addDays(1);

        }
        return $BookDateList;
    }


    /**
     * @param $Quotation
     * @param $HotelArray
     * @param bool $OnlyHotelBeds
     * @return array
     */
    static function getHotelCost($Quotation, $HotelArray, $OnlyHotelBeds = false)
    {

        $Rates = new Rates();
        $DiscountRates = new DiscountRates();
        $QuotationHotel = new QuotationHotel();
        $CurrencyList = Quotation::getCurrency($Quotation);
        $Market = $Quotation['market'];


        $ID = $HotelArray['hotel'];

        $check_in = $HotelArray['check_in'];
        $check_out = $HotelArray['check_out'];

        $RoomCategory = $HotelArray['room_category'];
        $Meal = $HotelArray['meal_type'];

        foreach ($HotelArray['room_type'] as $RoomTypeID => $RoomCount) {
            $room_type[$RoomTypeID] = intval($RoomCount);
        }

        //$extrabed = $HotelArray['extrabed'];
        $Bookdates = $QuotationHotel->getBookdatesNightCheckIn($check_in, $check_out);
        $DateRates = [];

        if(isset($HotelArray['provider']) && $HotelArray['provider'] == "local_special") {
            $nights = $QuotationHotel::getNightHotelBook($check_in, $check_out);
        }

        foreach ($Bookdates as $Day => $Bookdate) {//loop all the book dates
            foreach ($room_type as $RoomTypeID => $RoomCount) {//loop all the rooms

                if ($RoomCount) {//only if room have
                    //adult
                    if(isset($HotelArray['provider']) && $HotelArray['provider'] == "local_special") {
                        $DateRates['adult'][$Day][$RoomTypeID] = $DiscountRates->getRate($ID, $Bookdate, $RoomTypeID, $Meal, $RoomCategory, $Market, false, $OnlyHotelBeds, 500, $nights)->toArray();
                    } else {
                        $DateRates['adult'][$Day][$RoomTypeID] = $Rates->getRate($ID, $Bookdate, $RoomTypeID, $Meal, $RoomCategory, $Market, false, $OnlyHotelBeds)->toArray();
                    }

                    if (isset($DateRates['adult'][$Day][$RoomTypeID][0])) {
                        $DateRates['adult'][$Day][$RoomTypeID] = $DateRates['adult'][$Day][$RoomTypeID][0];
                    }

                }

            }


            if (!empty($room_type['room_type'][2])) {
                $RoomTypeChild = 2;
            } elseif (!empty($room_type['room_type'][1])) {
                $RoomTypeChild = 1;
            } elseif (!empty($room_type['room_type'][3])) {
                $RoomTypeChild = 3;
            } else {
                $RoomTypeChild = 2;
            }


            //child
            $DateRates['child']['cwb'][$Day] = $Rates->getRateChild($ID, $Bookdate, $Meal, $RoomCategory, $Market, 2, 12, $RoomTypeChild)->toArray();
            $DateRates['child']['cnb'][$Day] = $Rates->getRateChild($ID, $Bookdate, $Meal, $RoomCategory, $Market, 0, 2, $RoomTypeChild)->toArray();

            if (isset($DateRates['child']['cwb'][$Day][0])) {
                $DateRates['child']['cwb'][$Day] = $DateRates['child']['cwb'][$Day][0];
            }

            if (isset($DateRates['child']['cnb'][$Day][0])) {
                $DateRates['child']['cnb'][$Day] = $DateRates['child']['cnb'][$Day][0];
            }

            // Driver 
            $DateRates['driver_accommodation'][$Day]['rate'] = 8; // 8 usd driver acc
            if (isset($DateRates['driver_accommodation'][$Day][0])) {
                $DateRates['driver_accommodation'][$Day] = $DateRates['driver_accommodation'][$Day][0];
            }
        }

        //Check if quotation has multiple currency
        if (count($CurrencyList) > 1) {
            foreach ($DateRates['adult'] as $Day => $RateItem) {
                foreach ($RateItem as $RT => $RateArray) {
                    if (!empty($RateArray)) {
                        $HotelCurrency = Hotel::getHotelCurrency($RateArray->hotel);
                        $DateRates['adult'][$Day][$RT]->rate = currency($RateArray->rate, $HotelCurrency->code, 'USD');
                    }
                }
            }
            foreach ($DateRates['child']['cwb'] as $Day => $RateItem) {
                if (!empty($RateItem)) {
                    $HotelCurrency = Hotel::getHotelCurrency($RateItem->hotel);
                    $DateRates['child']['cwb'][$Day]->rate = currency($RateItem->rate, $HotelCurrency->code, 'USD');
                }
            }
            foreach ($DateRates['child']['cnb'] as $Day => $RateItem) {
                if (!empty($RateItem)) {
                    $HotelCurrency = Hotel::getHotelCurrency($RateItem->hotel);
                    $DateRates['child']['cnb'][$Day]->rate = currency($RateItem->rate, $HotelCurrency->code, 'USD');
                }
            }
        }

        return $DateRates;

    }

    /**
     * @param $Quotation
     * @param $HotelArray
     * @param bool $OnlyHotelBeds
     * @return array
     */
    static function setAccRates($Quotation, $HotelArray, $OnlyHotelBeds = false)
    {

        $Rates = new Rates();
        $DiscountRates = new DiscountRates();
        $QuotationHotel = new QuotationHotel();
        $CurrencyList = Quotation::getCurrency($Quotation);
        $Market = $Quotation['market'];


        $ID = $HotelArray['hotel'];

        $check_in = $HotelArray['check_in'];
        $check_out = $HotelArray['check_out'];

        //$extrabed = $HotelArray['extrabed'];
        $Bookdates = $QuotationHotel->getBookdatesNightCheckIn($check_in, $check_out);
        $DateRates = [];

        foreach ($Bookdates as $Day => $Bookdate) {//loop all the book dates
            
            // Driver 
            $DateRates[$Day]['rate'] = 8; // 8 usd driver acc
            if (isset($DateRates[$Day][0])) {
                $DateRates[$Day] = $DateRates[$Day][0];
            }
        }

        return $DateRates;

    }

    /**
     * @param $Quotation
     * @param $HotelArray
     * @param bool $OnlyHotelBeds
     * @return array
     */
    static function getAPIHotelCost($Data)
    {

        $Rates = new Rates();
        $QuotationHotel = new QuotationHotel();
        $Market = $Data['market'];
        $DateRatesAll = [];

        if(isset($Data['hotel'])) {
            foreach($Data['hotel'] as $index => $HotelArray) {
                $DateRates = [];
                $room_type= [];
                $ID = $HotelArray['hotel'];

                $check_in = $HotelArray['check_in'];
                $check_out = $HotelArray['check_out'];

                $RoomCategory = $HotelArray['room_category'];
                $Meal = $HotelArray['meal_type'];

                foreach ($HotelArray['room_type'] as $RoomTypeID => $RoomCount) {
                    $room_type[$RoomTypeID] = intval($RoomCount);
                }

                //$extrabed = $HotelArray['extrabed'];
                $Bookdates = $QuotationHotel->getBookdatesNightCheckIn($check_in, $check_out);
                $DateRates['hotel_id'] = $ID;
                foreach ($Bookdates as $Day => $Bookdate) {//loop all the book dates
                    foreach ($room_type as $RoomTypeID => $RoomCount) {//loop all the rooms

                        if ($RoomCount) {//only if room have
                            //adult
                            $DateRates['adult'][$Day][$RoomTypeID] = $Rates->getRate($ID, $Bookdate, $RoomTypeID, $Meal, $RoomCategory, $Market, false, false)->toArray();

                            if (isset($DateRates['adult'][$Day][$RoomTypeID][0])) {
                                $DateRates['adult'][$Day][$RoomTypeID] = $DateRates['adult'][$Day][$RoomTypeID][0];
                            }

                        }

                    }


                    if (!empty($room_type['room_type'][2])) {
                        $RoomTypeChild = 2;
                    } elseif (!empty($room_type['room_type'][1])) {
                        $RoomTypeChild = 1;
                    } elseif (!empty($room_type['room_type'][3])) {
                        $RoomTypeChild = 3;
                    } else {
                        $RoomTypeChild = 2;
                    }


                    //child
                    $DateRates['child']['cwb'][$Day] = $Rates->getRateChild($ID, $Bookdate, $Meal, $RoomCategory, $Market, 2, 12, $RoomTypeChild)->toArray();
                    $DateRates['child']['cnb'][$Day] = $Rates->getRateChild($ID, $Bookdate, $Meal, $RoomCategory, $Market, 0, 2, $RoomTypeChild)->toArray();

                    if (isset($DateRates['child']['cwb'][$Day][0])) {
                        $DateRates['child']['cwb'][$Day] = $DateRates['child']['cwb'][$Day][0];
                    }

                    if (isset($DateRates['child']['cnb'][$Day][0])) {
                        $DateRates['child']['cnb'][$Day] = $DateRates['child']['cnb'][$Day][0];
                    }
                }

                //Check if quotation has multiple currency
                // if (count($CurrencyList) > 1) {
                if(isset($DateRates['adult'])) {
                    foreach ($DateRates['adult'] as $Day => $RateItem) {
                        foreach ($RateItem as $RT => $RateArray) {
                            if (!empty($RateArray)) {
                                $HotelCurrency = Hotel::getHotelCurrency($RateArray->hotel);
                                unset($DateRates['adult'][$Day][$RT]);
                                $DateRates['adult'][$Day][$RT]["rate"] = currency($RateArray->rate, $HotelCurrency->code, 'USD');
                            }
                        }
                    }
                }
                if(isset($DateRates['child']['cwb'])) {
                    foreach ($DateRates['child']['cwb'] as $Day => $RateItem) {
                        if (!empty($RateItem)) {
                            $HotelCurrency = Hotel::getHotelCurrency($RateItem->hotel);
                            unset($DateRates['child']['cwb'][$Day]);
                            $DateRates['child']['cwb'][$Day]["rate"] = currency($RateItem->rate, $HotelCurrency->code, 'USD');
                        }
                    }
                }
                if(isset($DateRates['child']['cnb'])) {
                    foreach ($DateRates['child']['cnb'] as $Day => $RateItem) {
                        if (!empty($RateItem)) {
                            $HotelCurrency = Hotel::getHotelCurrency($RateItem->hotel);
                            unset($DateRates['child']['cnb'][$Day]);
                            $DateRates['child']['cnb'][$Day]["rate"] = currency($RateItem->rate, $HotelCurrency->code, 'USD');
                        }
                    }
                }

                // }

                array_push($DateRatesAll, $DateRates);
            }
        }

        return $DateRatesAll;

    }
    #rating
    ####################################################################################################################################
    /**
     * @param $Quotation
     * @return array|bool
     * @throws \ReflectionException
     */
    function getHotelRatePlane($Quotation)
    {

        if (!isset($Quotation['hotel']))
            return false;

        $HotelList = $Quotation['hotel'];
        $this->SelectedRates = $this->getHotelListCost($Quotation);

        $HotelPlanRates = [];

        foreach ($this->SelectedRates as $HotelIndex => $HotelItem) {

            /*if (!isset($Quotation['accommodation'][$HotelIndex]) || $Quotation['accommodation'][$HotelIndex] == 2)//get only hotesl
                continue;*/

            $CurrentHotelPlanRates = $this->getSingleHotelRatePlane($HotelItem, $HotelList[$HotelIndex], $HotelIndex, $Quotation['pax']);

            if ($CurrentHotelPlanRates)
                $HotelPlanRates[$HotelIndex] = $CurrentHotelPlanRates;
        }

        return $this->Rate($HotelPlanRates, $Quotation);

    }
    function getHotelRatePlaneCancel($Quotation)
    {

        if (!isset($Quotation['hotel']))
            return false;

        $HotelList = $Quotation['hotel'];
        $this->SelectedRates = $this->getHotelListCost($Quotation);

        $HotelPlanRates = [];

        foreach ($this->SelectedRates as $HotelIndex => $HotelItem) {

            /*if (!isset($Quotation['accommodation'][$HotelIndex]) || $Quotation['accommodation'][$HotelIndex] == 2)//get only hotesl
                continue;*/

            $CurrentHotelPlanRates = $this->getSingleHotelRatePlane($HotelItem, $HotelList[$HotelIndex], $HotelIndex, $Quotation['pax']);

            if ($CurrentHotelPlanRates)
                $HotelPlanRates[$HotelIndex] = $CurrentHotelPlanRates;
        }


        return $HotelPlanRates;

    }

    /**
     * @param $HotelRateItem
     * @param $HotelRequestItem
     * @param $HotelIndex
     * @param $Pax
     * @return array
     * @throws \ReflectionException
     */
    function getSingleHotelRatePlane($HotelRateItem, $HotelRequestItem, $HotelIndex, $Pax)
    {
        $provider = $HotelRequestItem['provider'];
        $HotelRequestItem = arrayMapMulti("intval", $HotelRequestItem);

        $HotelRateItem = objectToArray($HotelRateItem);
        $RatePlane = array('child' => array('cwb' => 0, 'cnb' => 0));

        foreach (Room::pluck('ID', 'type') as $RoomTypeDB) {
            $RatePlane['adult'][$RoomTypeDB] = false;
            $RatePlane['per_room'][$RoomTypeDB] = false;
        }


        #for adult
        if ($HotelRateItem) { //check if there's a hotel

            foreach ($HotelRateItem['adult'] ?? [] as $Day => $RateItem) {#database rate list

                if (empty($HotelRequestItem['room_type']))
                    continue;

                foreach ($HotelRequestItem['room_type'] as $RequestRoomType => $RequestRoomCount) {#requested room type list with count

                    if (!$RequestRoomCount) continue; //if there are no room request check next

                    if ($RateItem[$RequestRoomType] && is_numeric($RateItem[$RequestRoomType]['rate'])) {

                        //first check rate is changed
                        if (empty($RateItem[$RequestRoomType]['is_modified'])) {
                            if($provider === "hotelbeds"){
                                $RatePlane['adult'][$RequestRoomType] += $RateItem[$RequestRoomType]['rate'];
                            } else {
                                $RatePlane['adult'][$RequestRoomType] += $RateItem[$RequestRoomType]['rate'] * $RequestRoomCount;
                            }

                            $RatePlane['per_room'][$RequestRoomType] += $RateItem[$RequestRoomType]['rate'];
                        } else {
                            $RatePlane['adult'][$RequestRoomType] += $RequestRoomCount * ($RateItem[$RequestRoomType]['modified_rate'] ?? 0);
                            $RatePlane['per_room'][$RequestRoomType] += ($RateItem[$RequestRoomType]['modified_rate'] ?? 0);
                        }
                    } else {
                        $RatePlane['adult'][$RequestRoomType] += 0;
                        $RatePlane['per_room'][$RequestRoomType] += 0;
                    }
                    $RatePlane['room_count'][$RequestRoomType] = $RequestRoomCount;
                }

                     if($provider === "hotelbeds") {
                        if($Day ==  0) {
                            break;
                        }
                     }
            }
        }

        #for child
        if ($HotelRateItem && !empty($HotelRateItem['child'])) { //check if there's a hotel


            foreach ($HotelRateItem['child'] as $ChildType => $RateItem) {

                if (!$Pax[$ChildType])
                    continue;

                foreach ($RateItem as $Day => $RateDayItem) {

                    if (isset($RateDayItem['rate'])) {//check rate is change
                        if (empty($RateDayItem['is_modified']))//if not change
                            $RatePlane['child'][$ChildType] += $RateDayItem['rate'];
                        else//if changed
                            $RatePlane['child'][$ChildType] += $RateDayItem['modified_rate'];
                    } else
                        $RatePlane['child'][$ChildType] += 0;
                }
            }
        }
        return $RatePlane;

    }


    /**
     * @param $HotelSettings
     * @param $Pax
     * @return array|bool
     */
    function getCostType($HotelSettings, $Pax)
    {


        $RoomTypeArray = array();

        foreach ($HotelSettings as $HotelItem) {

            if (empty($HotelItem['hotel']))
                continue;

            if (isset($HotelItem['transportation']))
                continue;

            foreach ($HotelItem['room_type'] as $RoomType => $RoomCount) {
                if (intval($RoomCount))
                    $RoomTypeArray['adult'][$RoomType] = "Hotel Cost";
            }
        }


        if (!isset($RoomType))
            return false;

        if (intval($Pax['cwb']))
            $RoomTypeArray['cwb'][$RoomType] = "Hotel Cost";

        if (intval($Pax['cnb']))
            $RoomTypeArray['cnb'][$RoomType] = "Hotel Cost";


        return $RoomTypeArray;
    }


    /**
     * @param $Quotation
     * @return array
     */
    public function getHotelListCost($Quotation): array
    {

        $Rates = new Rates();

        $ArrayList = [];
        $AccommodationList = $Quotation['accommodation'] ?? [];

        foreach (($Quotation['hotel'] ?? []) as $ID => $HotelItem) {
            if(!empty($HotelItem)) {

                if ($HotelItem['provider'] === Hotel::PROVIDER_LOCAL || $HotelItem['provider'] === Hotel::PROVIDER_LOCAL_SPECIAL || $HotelItem['provider'] === 0) { // bugfix - added second condition

                    if ($AccommodationList[$ID] == 2 || empty($HotelItem))
                        continue;

                    if (isset($Quotation['rate']['hotel'][$ID])) {
                        $RateArraySession = $Quotation['rate']['hotel'][$ID];
                    } else {
                        $RateArraySession = arrayToobject(Session::get("quotation.rate.hotel.$ID"));
                    }
                    if ($Rates->searchInRateArrayHotelSettings(objectToArray($RateArraySession), $HotelItem)) {//chec if there are rates in the session that changed
                        $ArrayList[$ID] = objectToArray($RateArraySession);//if there is rate set it
                    } else {
                        $ArrayList[$ID] = objectToArray(QuotationHotel::getHotelCost($Quotation, $HotelItem));
                    }
                } else if (isset($HotelItem['provider']) && $HotelItem['provider'] === "hotelbeds") {
                    $RateArray = $Quotation['api']['hotelbeds']['hotel'][$ID]['selected'];
                    $ArrayList[$ID] = HotelBed::getHotelCost($HotelItem, collect($RateArray), $Quotation);
                }
            }

        }
        return $ArrayList;

    }


    /**
     * @param $Quotation
     * @return mixed
     */
    function getAvailableRoomTypes($Quotation)
    {
        $RoomTypeList = [];

        if ($Quotation['hotel']) {
            foreach ($Quotation['hotel'] as $index => $HotelItem) {
                foreach ($HotelItem['room_type'] as $roomType => $RoomCount) {
                    if ($RoomCount) {
                        if (empty($RoomTypeList[$roomType])) {
                            $RoomTypeList[$roomType] = [
                                'no_room_type' => 1,
                                'no_room_count' => $RoomCount
                            ];
                        } else {
                            $RoomTypeList[$roomType]['no_room_type']++;
                            $RoomTypeList[$roomType]['no_room_count'] += $RoomCount;

                        }

                    }

                }
            }
            return $RoomTypeList;
        } else
            return false;

    }


    /**
     * @param $HotelPlanRates
     * @param $Quotation
     * @return array
     */
    function Rate($HotelPlanRates, $Quotation): array
    {

        $Total = [];

        $Total['cost'] = 0;
        $Total['cost_pp'] = 0;
        $Total['cost_type'] = $this->getCostType($Quotation['hotel'], $Quotation['pax']);
        $Total['room_type_cost'] = [];


        #Child
        $Total['child_cost']['cwb'] = 0;
        $Total['child_cost']['cnb'] = 0;


        foreach (Room::pluck('ID', 'type') as $RoomTypeDB)
            $Total['room_type_cost'][$RoomTypeDB] = false;

        foreach ($HotelPlanRates as $HotelIndex => $HotelRate) {

            $HotelRoomTypeCost = 0;
            foreach ($HotelRate['adult'] as $RoomType => $RoomCost) {
                if ($RoomCost !== false) {
                    if($Quotation['hotel'][$HotelIndex]['provider'] === "hotelbeds") {
                        $PerRoomRate = ($HotelRate['per_room'][$RoomType] / $RoomType) / $HotelRate['room_count'][$RoomType];
                    } else {
                        $PerRoomRate = $HotelRate['per_room'][$RoomType] / $RoomType;
                    }
                    $HotelRoomTypeCost += $RoomCost;//this is the room cost so add this to total
                    //
                    //for PP rate divide it by room type ex Double room divide by 2
                    $Total['room_type_cost'][$RoomType] += $PerRoomRate;
                }
            }

            #Child
            foreach ($HotelRate['child'] as $ChildType => $RoomCost) {

                $HotelRoomTypeCost += $RoomCost * $Quotation['pax'][$ChildType];//child come with pp rate, so for total u have to divide it form child count
                $Total['child_cost'][$ChildType] += $RoomCost;//for pp rate we can get directly rate in db
            }

            if ($HotelRoomTypeCost)//check whether current hotel has cost
                $this->AvailableHotelsIndex[] = $HotelIndex;


            $Total['cost'] += $HotelRoomTypeCost;//set to total cost
        }
        //markup add o total
        $Total['cost_pp'] = $Total['cost'] / $Quotation['pax']['adult'];
        return $Total;

    }


    /**
     * @return array
     */
    function getAvailableHotelsIndex()
    {
        return $this->AvailableHotelsIndex;
    }

    /**
     * @param $Quotation
     * @return array
     */
    function getHotelIndexByCity($Quotation)
    {
        $SessionHotels = $Quotation['hotel'] ?? false;
        $SessionPlace = $Quotation['place'];
        $SessionAccommodation = $Quotation['accommodation'] ?? false;

        $IndexHotelCity = [];
        if ($SessionAccommodation) {
            foreach ($SessionAccommodation as $Index => $Settings) {
                if (!empty($SessionHotels[$Index]['hotel']) && !empty($IndexHotelCity) && !empty($SessionPlace[$Index])) {
                    $IndexHotelCity[$Index][$SessionPlace[$Index]][] = $Index;
                } elseif (!empty($SessionHotels[$Index]['place']))
                    $IndexHotelCity[$Index][$SessionHotels[$Index]['place']][] = $Index;
                else {
                    $IndexHotelCity[$Index] = false;
                }
            }
        }
        return $IndexHotelCity;
    }

    /**
     * @param $HotelArray
     * @param $ArrivalDate
     * @param int $addEach
     * @return array
     */
    function getCheckinCheckoutDatesFromHotelArray($HotelArray, $ArrivalDate, $addEach = 1)
    {
        $CheckinCheckoutDates = array();
        $ArrivalDateInUnix = strtotime($ArrivalDate['year'] . "/" . $ArrivalDate['month'] . "/" . $ArrivalDate['day']);

        foreach ($HotelArray as $HotelIndex => $HotelSettingsQuery) {

            if ($HotelSettingsQuery) {
                $DecordedSetting = $this->getDecodeArray($HotelSettingsQuery);
                $CheckinCheckoutDates[$HotelIndex]['check_in'] = $DecordedSetting['check_in'];
                $CheckinCheckoutDates[$HotelIndex]['check_out'] = $DecordedSetting['check_out'];
            } else {

                if (empty($CheckinCheckoutDates)) {//if this is the first place hotel
                    $CheckInDate = array('year' => date('o', $ArrivalDateInUnix), 'month' => date('m', $ArrivalDateInUnix), 'day' => date('d', $ArrivalDateInUnix));//set arival date to check in
                    $CheckInDateWithaddedDays = strtotime("+$addEach days", $ArrivalDateInUnix);
                    $CheckOutDate = array('year' => date('o', $CheckInDateWithaddedDays), 'month' => date('m', $CheckInDateWithaddedDays), 'day' => date('d', $CheckInDateWithaddedDays));//set arival date to check in
                } else { //if not the first city

                    $LastCheckoutDateUnix = strtotime($CheckinCheckoutDates[$HotelIndex - 1]['check_out']['year'] . "/" . $CheckinCheckoutDates[$HotelIndex - 1]['check_out']['month'] . "/" . $CheckinCheckoutDates[$HotelIndex - 1]['check_out']['day']);

                    $CheckInDate = array('year' => date('o', $LastCheckoutDateUnix), 'month' => date('m', $LastCheckoutDateUnix), 'day' => date('d', $LastCheckoutDateUnix));//set arival date to check in
                    $LastCheckoutDateUnixAddDays = strtotime("+$addEach days", $LastCheckoutDateUnix);
                    $CheckOutDate = array('year' => date('o', $LastCheckoutDateUnixAddDays), 'month' => date('m', $LastCheckoutDateUnixAddDays), 'day' => date('d', $LastCheckoutDateUnixAddDays));//set arival
                }
                $CheckinCheckoutDates[$HotelIndex]['check_in'] = $CheckInDate;
                $CheckinCheckoutDates[$HotelIndex]['check_out'] = $CheckOutDate;
            }
        }

        return $CheckinCheckoutDates;
    }


    /**
     * @param $QuotationArray
     * @param bool $AddStops
     * @return array
     */
    static function getTourDaysDetail($QuotationArray, $AddStops = false)
    {

        $DaysArray = [];
        $HotelArray = [];
        $Day = 1;
        $LastPlace = false; //last place in destination
        $HotelArray = array_combine2($QuotationArray['hotel']??[], $QuotationArray['cruise']??[]);
        ksort($HotelArray);

        if ($AddStops)
            $Places = $QuotationArray['place_full'];
        else
            $Places = $QuotationArray['place'];

        if(isset($HotelArray[0]['check_in']['year'])) {
            $Date = Carbon::create(reset($HotelArray)['check_in']['year'], reset($HotelArray)['check_in']['month'], reset($HotelArray)['check_in']['day'], 0);
        } else if(isset($CruiseArray[0]['check_in']['year'])) {
            $Date = Carbon::create(reset($CruiseArray)['check_in']['year'], reset($CruiseArray)['check_in']['month'], reset($CruiseArray)['check_in']['day'], 0);
        } else {
            $Date = Carbon::create($QuotationArray['arrival_date']['year'], $QuotationArray['arrival_date']['month'], $QuotationArray['arrival_date']['day'], 0);
        }

        //if need stops

        if (count($Places) != (count($HotelArray))  && $AddStops) {

            $HotelArrayTemp = [];
            $HotelIndexFix = 0;

            foreach ($QuotationArray['place_type'] as $TypeIndex => $PlaceTypeID) {
                if ($PlaceTypeID > 1) {//if it's a stop

                    $HotelArrayTemp[$TypeIndex] = [
                        'place' => $Places[$TypeIndex],
                        'place_type' => $PlaceTypeID
                    ];
                } else {
                    if(isset($HotelArray[$HotelIndexFix])) {
                        $HotelArrayTemp[$TypeIndex] = $HotelArray[$HotelIndexFix];
                        $HotelArrayTemp[$TypeIndex]['place_type'] = $PlaceTypeID;
                        $HotelIndexFix++;
                    }
                }
            }

            $HotelArray = $HotelArrayTemp;
        }

        $HotelIndex = 0;
        foreach ($HotelArray as $HotelIndex => $HotelSettings) {

            $place_type = $HotelSettings['place_type'] ?? 1;

            if ($place_type == 1) {//if its a destination

                if(!empty($HotelSettings)) {
                    $check_in = Carbon::create($HotelSettings['check_in']['year'], $HotelSettings['check_in']['month'], $HotelSettings['check_in']['day'], 0);
                    $check_out = Carbon::create($HotelSettings['check_out']['year'], $HotelSettings['check_out']['month'], $HotelSettings['check_out']['day'], 0);


                    $Days = $check_out->diffInDays($check_in);


                    for ($c = 1; $c <= $Days; $c++) {//loop all the days

                        if (!isset($Places[$HotelIndex]))
                            continue;


                        $DaysArray[$Day]['date'] = ['year' => $Date->year, 'month' => $Date->month, 'day' => $Date->day];
                        $DaysArray[$Day]['place'] = $Places[$HotelIndex];
                        $DaysArray[$Day]['index'] = $HotelIndex;


                        $Day++;
                        $Date->addDays(1);
                    }


                    if (!$HotelSettings) {

                        $DaysArray[$Day]['date'] = ['year' => $Date->year, 'month' => $Date->month, 'day' => $Date->day];
                        $DaysArray[$Day]['place'] = $Places[$HotelIndex];
                    }

                    if (isset($Places[$HotelIndex + 1]) && $QuotationArray['place_type'][$HotelIndex + 1] == 1 && isset($Places[$HotelIndex])) {
                        $DaysArray[$Day]['route'] = ['from' => $Places[$HotelIndex], 'to' => $Places[$HotelIndex + 1]];
                    }
                    // var_dump($HotelIndex);
                    if(isset($Places[$HotelIndex])) {
                        $LastPlace = $Places[$HotelIndex];
                    }
                }

            } else { //if its a stop

                #add extra day
                $DaysArray[$Day][$place_type]['place'] = $Places[$HotelIndex];
                $DaysArray[$Day][$place_type]['index'] = $HotelIndex;
                $DaysArray[$Day][$place_type]['type'] = $place_type;

            }
        }

        #add extra day
        $DaysArray[$Day]['date'] = ['year' => $Date->year, 'month' => $Date->month, 'day' => $Date->day];
        $DaysArray[$Day]['place'] = $LastPlace;
        $DaysArray[$Day]['index'] = $HotelIndex;

        return $DaysArray;
    }

    /**
     * @param $RoomTypeArray
     * @return array
     */
    function getAvailableRoomType($RoomTypeArray)
    {

        $RoomTypeList = Room::pluck('short_name', 'ID');
        $AvailableRoomTypes = [];


        foreach ($RoomTypeArray as $RoomType => $RoomCount) {
            if (intval($RoomCount))
                $AvailableRoomTypes[$RoomType] = $RoomTypeList[$RoomType] . "($RoomCount)";
        }

        return $AvailableRoomTypes;
    }

    /**
     * @param $Quotation
     * @return array|bool
     */
    function getAvailableMealType($Quotation)
    {

        if (empty($Quotation['hotel']))
            return false;

        $MealTypeList = Meal::pluck('plan', 'ID');
        $AvailableMealTypes = [];
        $HotelArray = $Quotation['hotel'];
        $AccommodationArray = $Quotation['accommodation'];


        foreach ($HotelArray as $Index => $HotelItem) {

            if ($AccommodationArray[$Index] == 2)
                continue;

            if (empty($HotelItem['meal_type']))
                $CurrentMeal = $HotelItem['meal'];
            else
                $CurrentMeal = $HotelItem['meal_type'];


            if (empty($AvailableRoomTypes[$CurrentMeal]) && $CurrentMeal)
                $AvailableMealTypes[$CurrentMeal] = $MealTypeList[$CurrentMeal];

        }
        return $AvailableMealTypes;


    }


    /**
     * @param $Quotation
     * @param $RequestedCurrency
     * @return array
     */
    static function getHotelBudgetArray($Quotation, $RequestedCurrency)
    {
        $pax = $Quotation["pax"];
        $QuotationHotel = new QuotationHotel();

        $HotelRate = $QuotationHotel->getHotelListCost($Quotation);
        $HotelPlanRates = [];

        foreach ($HotelRate as $HotelIndex => $HotelItem) {

            if (!isset($Quotation['accommodation'][$HotelIndex]) || $Quotation['accommodation'][$HotelIndex] == 2)//get only hotesl
                continue;


            $CurrentTotal = 0;

            foreach ($HotelItem['adult'][0] as $RoomType => $RateArray) {//if rate is modifies set ratr to modified rate

                $HotelItem['adult'][0][$RoomType]['rate'] = currency(($HotelItem['adult'][0][$RoomType]['rate'] ?? 0), 'USD', $RequestedCurrency, false);
                if(isset($Quotation['hotel'][$HotelIndex]['room_type'][$RoomType])) {
                    $SubTotal=0;
                    for ($i=0; $i<$Quotation['hotel'][$HotelIndex]['night'];$i++) {
	                    if (!empty($HotelItem['adult'][$i][$RoomType]['is_modified'])) {
		                    $HotelItem['adult'][$i][$RoomType]['rate'] = $HotelItem['adult'][$i][$RoomType]['modified_rate'];
	                    }
	                    if(isset($HotelItem['adult'][$i][$RoomType]['rate'])) {
		                    $SubTotal += $HotelItem['adult'][$i][$RoomType]['rate'] * intval($Quotation['hotel'][$HotelIndex]['room_type'][$RoomType]);
	                    }

                    }
                    $CurrentTotal += $SubTotal;
//                     $CurrentTotal += $HotelItem['adult'][0][$RoomType]['rate'] * intval($Quotation['hotel'][$HotelIndex]['room_type'][$RoomType]) * $Quotation['hotel'][$HotelIndex]['night'];
                }
            }

            foreach ($HotelItem['child'] as $ChildType => $RateArray) {//if rate is modifies set ratr to modified rate
                if (!empty($RateArray[0]['is_modified'])) {
                    $HotelItem['child'][$ChildType][0]['rate'] = $RateArray[0]['modified_rate'];
                }

                if (!empty($HotelItem['child'][$ChildType][0]['rate']))
                    $HotelItem['child'][$ChildType][0]['rate'] = currency($HotelItem['child'][$ChildType][0]['rate'], 'USD', $RequestedCurrency, false);
                else
                    $HotelItem['child'][$ChildType][0]['rate'] = 0;

                if (!empty($Quotation['pax'][$ChildType]))
                    $CurrentTotal += $HotelItem['child'][$ChildType][0]['rate'] * $pax[$ChildType] * $Quotation['hotel'][$HotelIndex]['night'];
            }

            $HotelPlanRates[$HotelIndex]['hotel_settings'] = $Quotation['hotel'][$HotelIndex];
            $HotelPlanRates[$HotelIndex]['rate']['adult'] = $HotelItem['adult'];
            $HotelPlanRates[$HotelIndex]['rate']['cwb'] = $HotelItem['child']['cwb'];
            $HotelPlanRates[$HotelIndex]['rate']['cnb'] = $HotelItem['child']['cnb'];
            $HotelPlanRates[$HotelIndex]['rate']['total'] = $CurrentTotal;

        }

        return $HotelPlanRates;
    }

    /**
     * @param $PaxList
     * @return array
     */
    function getPaxesHotelBeds($PaxList)
    {

        $Hotel = new Hotel();
        $HotelBed = new HotelBed();


        //sd($PaxList);

        $RemainingAdult = $PaxList['adult'];
        $Paxes = [];
        $RoomList = $Hotel->getHotelRoomCountWithPax($PaxList);

        $TotalChildren = $PaxList['cwb'] + $PaxList['cnb'];
        $TotalRooms = array_sum($RoomList);


        $ChildCountArray = $HotelBed->childrenToRoom($TotalChildren, $TotalRooms, $RoomList);


        foreach ($RoomList as $RoomType => $RoomCount) {

            for ($i = 1; $i <= $RoomCount; $i++) {
                $Paxes[$RoomType][] = new Pax(Pax::AD, 30, "Ssds", "sds", 1);
                if (!empty($ChildCountArray[$RoomType][$i])) {
                    $Paxes[$RoomType][] = new Pax(Pax::CH, 6, "Ssds", "sds", 1);
                }

            }

        }

        return $Paxes;


    }

    /**
     * @param $Quotation
     * @return array
     */
    static function getSupplementRatePlane($Quotation)
    {

        $QuotationHotel = new QuotationHotel();

        $Rate = [];
        $Cost = [];
        $Cost['adult'] = 0;
        $Cost['child'] = 0;
        $Cost['total'] = 0;

        if (isset($Quotation['hotel'])) {

            #sd($Quotation);

            foreach ($Quotation['hotel'] as $k => $HotelSettings) {


                if ($Quotation['accommodation'][$k] != 1)
                    continue;


                $BookDates = $QuotationHotel->getBookdatesNightCheckIn($HotelSettings['check_in'], $HotelSettings['check_out']);

                foreach ($BookDates as $DateItem) {

                    $CheckInDate = Carbon::create($DateItem['year'], $DateItem['month'], $DateItem['day'], 0);


                    $Supplement = Supplement::where('hotel', $HotelSettings['hotel'])
                        ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $CheckInDate)
                        ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $CheckInDate)
                        ->where('meal_type', $HotelSettings['meal_type']);


                    if (!$Supplement->get()->isEmpty()) {
                        $SupRate = $Supplement->first();

                        if ($SupRate->type == 1) {#Per Room

                            $RoomCount = array_sum($HotelSettings['room_type']);
                            $Cost['adult'] += @($SupRate->adult_rate * $RoomCount / $Quotation['pax']['adult']);
                            $Cost['total'] += $SupRate->adult_rate * $RoomCount;


                        } elseif ($SupRate->type == 2) {#per Person ;

                            $AdjustPax = 0;
                            foreach ($HotelSettings['room_type'] as $RoomTypeID => $RoomTypeCount) {
                                $AdjustPax += $RoomTypeID * $RoomTypeCount;
                            }


                            $Cost['adult'] += $SupRate->adult_rate;
                            $Cost['child'] += $SupRate->child_rate;

                            $Cost['total'] += $SupRate->adult_rate * $AdjustPax;
                            $Cost['total'] += @($SupRate->child_rate * $Quotation['pax']['cwb']);

                        } elseif ($SupRate->type == 3) {#Per day
                            //--

                        }


                    }

                }

            }


        }

        return $Cost;


    }

}





