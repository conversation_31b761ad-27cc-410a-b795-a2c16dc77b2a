<?php

namespace App\Model\Quotation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Quotation\CustomFieldType
 *
 * @property int $ID
 * @property string $type
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\CustomFieldType whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\CustomFieldType whereType($value)
 * @mixin \Eloquent
 */
class CustomFieldType extends Model
{
    protected $table = "apple_custom_field_type";


}
