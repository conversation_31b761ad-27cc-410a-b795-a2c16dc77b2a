<?php

namespace App\Model\Quotation;

use App\Currency;
use App\Model\Admin\OutboundVehicleRate;
use App\Model\Costcut\Costcut;
use App\Model\Hotel\Hotel;
use App\Model\Place\Attraction;
use App\Model\Place\CityTour;
use App\Model\Place\Excursion;
use App\Model\Place\Place;
use App\Model\QuotationManage\Quotation as QuotationClass;
use App\Model\QuotationManage\Quotation as QuotationManage;
use App\Model\Transport\Transport;
use App\Model\Vehicle\HotelTransportRate;
use App\Model\Vehicle\Vehicle;
use Auth;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use View;


/**
 * App\Model\Quotation\Quotation
 *
 * @mixin \Eloquent
 */
class Quotation extends Model
{

    var $AvailableHotelsIndex = [];
    var $AvailableCruisesIndex = [];

    /**
     * @param $Quotation
     * @param bool $Currency
     * @return mixed
     * @throws \ReflectionException
     */
    function getCost($Quotation, $CurrencyT = false, $Direct = false)
    {
        $Costcut = new Costcut();
        $Cost['total'] = 0;
        $RoomCounts = 0;

        //check quotation has multiple currency

        $Currency = Currency::find(Place::find($Quotation['country'])->currency)->code;
        $CurrencyList = Quotation::getCurrency($Quotation);
        if (count($CurrencyList) > 1)
            $Currency = "USD";

        $FromCurrency = $Currency;
        if(!$CurrencyT) {
            if(isset($Quotation['ch_currency'])) {
                $Currency = Currency::find($Quotation['ch_currency'])->code;
            }
        }

        if ($Currency) {
            currency()->setUserCurrency($Currency);
        } else {
            currency()->setUserCurrency(Currency::find(Place::find($Quotation['country'])->currency)->code);
        }

        if(!$FromCurrency) {
            $FromCurrency = Currency::find(Place::find($Quotation['country'])->currency)->code;
        }


        $CurrentCurrency = currency()->getCurrency(currency()->getUserCurrency());
        $Cost['currency'] = arrayMapMulti('getActualDataType', $CurrentCurrency, null);

        $CostHotel = Quotation::getHotelCost($Quotation);

        $CostCruise = Quotation::getCruiseCost($Quotation);

        $this->AvailableHotelsIndex = $CostHotel['indexes'];
        $this->AvailableCruiseIndex = $CostCruise['indexes'];
        $Cost['hotel'] = $CostHotel['cost'];

        $this->AvailableCruisesIndex = $CostCruise['indexes'];
        $Cost['cruise'] = $CostCruise['cost'];


        $Cost['attraction'] = Quotation::getAllTheAttractionCost(Quotation::getAttractionCost($Quotation, $Direct), Quotation::getCityTourCost($Quotation, $Direct), Quotation::getExcursionCost($Quotation, $Direct));
        $Cost['transport'] = Quotation::getTransportCost($Quotation);
        $Cost['hotel_transport'] = Quotation::getHotelTransportCost($Quotation);
        $Cost['meal'] = Quotation::getMealCost($Quotation);
        $Cost['supplement'] = Quotation::getSupplementCost($Quotation);
        $Cost['other'] = Quotation::getOtherCost($Quotation);
        $Cost['water_bottle'] = Quotation::getWaterBottle($Quotation);
        
        /*$Cost['cost_cut'] = $Costcut->getCostCutRate($Quotation);
        $Cost['cost_cut_pkg'] = $Costcut->getCostCutPkgRate($Quotation);*/

        //Per person cost gen
        $Cost['pp']['adult'] = [];
        $bookedHotels = 0;

        if($Quotation['tour_type'] == 3) {
            #PP COSTS
            if(isset($Quotation['hotel'])) {
                if(isset(reset($Quotation['hotel'])['room_type'])) {
                    $RoomCounts = (array_filter(reset($Quotation['hotel'])['room_type']));
                }
            } else if(isset($Quotation['cruise'])) {
                if(isset(reset($Quotation['cruise'])['room_type'])) {
                    $RoomCounts = (array_filter(reset($Quotation['cruise'])['room_type']));
                }
            }

            ////////////////////////////////////
            if(empty($RoomCounts)) {
                if(isset($Quotation['hotel'])) {
                    for($i=0; $i<count($Quotation['hotel']);$i++) {
                        next($Quotation['hotel']);
                        if(isset(current($Quotation['hotel'])['room_type'])) {
                            $RoomCounts = (array_filter(current($Quotation['hotel'])['room_type']));
                            if(!empty($RoomCounts)) {
                                break;
                            }
                        }
                    }
                }
            }

            $useDiffHotelRooms = false;
            $ConpareRoomCounts = [];
            $ConpareRoomCounts[] = $RoomCounts;
            if(isset($Quotation['hotel'])) {
                for($i=0; $i<count($Quotation['hotel']);$i++) {
                    next($Quotation['hotel']);
                    if(isset(current($Quotation['hotel'])['room_type'])) {
                        $RoomCountsTemp = (array_filter(current($Quotation['hotel'])['room_type']));
                        if(!empty($RoomCounts)) {
                            $ConpareRoomCounts[] = $RoomCountsTemp;
                            if(!empty(array_diff($RoomCountsTemp, $RoomCounts))) {
                                $useDiffHotelRooms = true;
                                break;
                            }
                        }
                    }
                }
            }

            // dd(array_diff_key($ConpareRoomCounts[2], $RoomCounts));
            //////////////////////////////////// Added by priyantha for first hotel own arrangement check room cont for 2nd, 3rd like vice
        }

        if ($Cost['hotel']) {

            $bookedHotels = 0;

            foreach ($Cost['hotel']['room_type_cost'] as $PerPersonRoomType => $AmountHotel) {
                if ($AmountHotel === false) {
                    continue;
                } else {
                    $Cost['pp']['adult'][$PerPersonRoomType] = $AmountHotel +
                        ($Cost['transport']['cost']['per_person']) +
                        ($Cost['hotel_transport']['pp_adult']) +
                        ($Cost['supplement']['adult']) +
                        ($Cost['attraction']['pax_cost']['adult'] ?? 0) +
                        ($Cost['meal']['pax_cost']['adult'] ?? 0) +
                        ($Cost['water_bottle']['PP']['adult']['rate'] ?? 0);

                    //other rates
                    
                    if ($Cost['other'] && !empty($Cost['other']['PP']['adult'])) {
                        foreach ($Cost['other']['PP']['adult'] as $PParray) {
                            $Cost['pp']['adult'][$PerPersonRoomType] += $PParray['rate'];
                        }
                    }
                    $bookedHotels = 1;
                }
            }

            // edit pri...
            if($bookedHotels == 0) {

                $Cost['pp']['adult'][1] = ($Cost['transport']['cost']['per_person']) +
                    ($Cost['hotel_transport']['pp_adult']) +
                    ($Cost['supplement']['adult']) +
                    ($Cost['attraction']['pax_cost']['adult'] ?? 0) +
                    ($Cost['meal']['pax_cost']['adult'] ?? 0) +
                    ($Cost['water_bottle']['PP']['adult']['rate'] ?? 0);

                //other rates
                if ($Cost['other'] && !empty($Cost['other']['PP']['adult'])) {
                    foreach ($Cost['other']['PP']['adult'] as $PParray) {
                        $Cost['pp']['adult'][1] += $PParray['rate'];
                    }
                }
            }
        }

        if ($Cost['cruise']) {
            foreach ($Cost['cruise']['room_type_cost'] as $PerPersonRoomType => $AmountHotel) {
                if ($AmountHotel === false) {
                    continue;
                } else {
                    if($bookedHotels == 0) {
                        $Cost['pp']['adult'][$PerPersonRoomType] = $AmountHotel +
                            ($Cost['transport']['cost']['per_person']) +
                            ($Cost['hotel_transport']['pp_adult']) +
                            ($Cost['supplement']['adult']) +
                            ($Cost['attraction']['pax_cost']['adult'] ?? 0) +
                            ($Cost['meal']['pax_cost']['adult'] ?? 0) +
                            ($Cost['water_bottle']['PP']['adult']['rate'] ?? 0);

                        //other rates
                        if ($Cost['other'] && !empty($Cost['other']['PP'])) {
                            foreach ($Cost['other']['PP'] as $PParray) {
                                $Cost['pp']['adult'][$PerPersonRoomType] += $PParray['rate'];
                            }
                        }

                    } else {
                        $Cost['pp']['adult'][$PerPersonRoomType] = $AmountHotel + ($Cost['pp']['adult'][$PerPersonRoomType] ?? 0);
                    }

                    $bookedHotels = 1;
                }
            }
        }

        $Cost['pp']['cwb'] = $Cost['hotel']['child_cost']['cwb']
            + ($Cost['cruise']['child_cost']['cwb'] ?? 0)
            + $Cost['supplement']['child']
            + ($Cost['attraction']['pax_cost']['child'] ?? 0)//attraction cost
            + ($Cost['hotel_transport']['pp_child'] ?? 0)//hotel transport cost
            + ($Cost['meal']['pax_cost']['child'] ?? 0)
            + ($Cost['water_bottle']['PP']['cwb']['rate'] ?? 0);//water cost

        if ($Cost['other'] && !empty($Cost['other']['PP']['cwb'])) { // other cost
            foreach ($Cost['other']['PP']['cwb'] as $PParray) {
                $Cost['pp']['cwb'] += $PParray['rate'];
            }
        }
            

        $Cost['pp']['cnb'] = $Cost['hotel']['child_cost']['cnb']
            + ($Cost['cruise']['child_cost']['cnb'] ?? 0)
            + ($Cost['attraction']['pax_cost']['child'] ?? 0)//attraction cost
            + ($Cost['meal']['pax_cost']['child'] ?? 0)//meal cost
            + ($Cost['water_bottle']['PP']['cnb']['rate'] ?? 0);//water cost

        if ($Cost['other'] && !empty($Cost['other']['PP']['cnb'])) { // other cost
            foreach ($Cost['other']['PP']['cnb'] as $PParray) {
                $Cost['pp']['cnb'] += $PParray['rate'];
            }
        }


        //Markup ##########################################################################
        $MarkupType = (!empty($Quotation['markup_type']) ? $Quotation['markup_type'] : 1);
        $Markup = (!empty($Quotation['markup_amount']) ? $Quotation['markup_amount'] : 0);

        $MarkupTypeChild = (!empty($Quotation['markup_type_child']) ? $Quotation['markup_type_child'] : 1);
        $MarkupChild = (!empty($Quotation['markup_amount_child']) ? $Quotation['markup_amount_child'] : 0);

        ##GENERATIONG TOTAL COSTS
        if ($Quotation['tour_type'] == 3) {

            if($bookedHotels == 0) {
                $Cost['pp']['adult'][1] = addMarkup($Cost['pp']['adult'][1], $Markup, $MarkupType);
                $Cost['total'] += $Cost['pp']['adult'][1] * $Quotation['pax']['adult'];
            } else {

                foreach ($Cost['pp']['adult'] as $RoomType => $PPAmount) {
                    $Cost['pp']['adult'][$RoomType] = addMarkup($Cost['pp']['adult'][$RoomType], $Markup, $MarkupType);
                    $Cost['total'] += $Cost['pp']['adult'][$RoomType] * $RoomType * ($RoomCounts[$RoomType] ?? 1);
                }
            }

            if($useDiffHotelRooms) {
                $Cost['total'] = $Cost['hotel']['cost'] +
                    $Cost['cruise']['cost'] +
                    $Cost['attraction']['cost'] +
                    $Cost['transport']['cost']['total'] +
                    $Cost['hotel_transport']['total'] +
                    $Cost['meal']['cost']["total"] +
                    $Cost['supplement']["total"] +
                    $Cost['water_bottle']["cost"];

                if($MarkupType == 1) {
                    $Markup = $Markup * $Quotation['pax']['adult'];
                }
                $Cost['total'] = addMarkup($Cost['total'], $Markup, $MarkupType);
            }

            $Cost['pp']['cwb'] = addMarkup($Cost['pp']['cwb'], $MarkupChild, $MarkupTypeChild);
            $Cost['pp']['cnb'] = addMarkup($Cost['pp']['cnb'], $MarkupChild, $MarkupTypeChild);

            //add all the other rates total
            $Cost['total'] += array_sum(array_map(function ($val) {
                return $val['rate'];
            }, ($Cost['other']['PQ'] ?? [])));

            $Cost['total'] += (($Cost['pp']['cwb'] * $Quotation['pax']['cwb']) + ($Cost['pp']['cnb'] * $Quotation['pax']['cnb']));

            /// Driver accommodation
            $driverAccommodationChargeTotal = 0;
            if(isset($Quotation['accommodation-charges'])){
                foreach ($Quotation['accommodation-charges']  as $key => $accCharge){
                    if(isset($accCharge)){
                            $Cost['total'] += $accCharge;
                            // $driverAccommodationChargeTotal += $accCharge;
                    }
                }
            }
            $Cost['total'] += $driverAccommodationChargeTotal;

        } elseif ($Quotation['tour_type'] == 6) {

            $Cost['total'] = $Cost['hotel']['cost'] +
                $Cost['cruise']['cost'] +
                $Cost['attraction']['cost'] +
                $Cost['transport']['cost']['total'] +
                $Cost['hotel_transport']['total'] +
                $Cost['supplement']['total'] +
                $Cost['meal']['cost']['total'] +
                $Cost['water_bottle']['cost'] +
                $Cost['other']['cost'];

            $Cost['total'] = addMarkup($Cost['total'], $Markup, $MarkupType);
        }
        ###RND OF TOTAL COST ################################################################

        if(!empty($Cost['cost_cut_pkg'])) {
            $Cost['TotalWRedution'] = $Cost['total'] - $Cost['cost_cut']['total'] + $Cost['cost_cut_pkg'];
            $Cost['TotalWRedution'] = currency($Cost['TotalWRedution'], $FromCurrency /*$CurrentCurrency['code']*/, $CurrentCurrency['code'], false);#Set currency;
        }

        $Cost['total'] = currency($Cost['total'], $FromCurrency /*$CurrentCurrency['code']*/, $CurrentCurrency['code'], false);#Set currency;

        return $Cost;
    }


    /**
     * @param $Quotation
     * @return array
     */
    static function getCurrency($Quotation)
    {
        $CountryLIst = [];
        foreach ($Quotation['place'] as $PlaceID) {
            $CountryLIst[Place::find($PlaceID)->country] = Place::find(Place::find($PlaceID)->country)->currency()->first()->code;
        }
        return $CountryLIst;
    }

    /**
     * @param $Quotation
     * @return array
     */
    static function  getFromToCurrency($Quotation) {
        $Currencies = [];

        $Currencies["from"] = "USD";
        $CurrencyList = Quotation::getCurrency($Quotation);
        if (count($CurrencyList) > 1)
            $Currencies["to"] = "USD";

        if(!isset($Currencies["to"])) {
            $Currencies["to"] = Currency::find(Place::find($Quotation['country'])->currency)->code;
        }

        $Currencies["from"] = $Currencies["to"];
        if(isset($Quotation['ch_currency'])) {
            $Currencies["to"] = Currency::find($Quotation['ch_currency'])->code;
        }

        return $Currencies;
    }


    /**
     * @param $Quotation
     * @param $RequestedCurrency
     * @return mixed
     */
    static function getBudget($Quotation, $RequestedCurrency)
    {
        $Budget['hotel'] = Quotation::getHotelBudget($Quotation, $RequestedCurrency);
        $Budget['cruise'] = Quotation::getCruiseBudget($Quotation, $RequestedCurrency);
        $Budget['transport'] = Quotation::getTransportBudget($Quotation, $RequestedCurrency);
        $Budget['attraction'] = Quotation::getAttractionBudget($Quotation, $RequestedCurrency);
        $Budget['other'] = Quotation::getOtherBudget($Quotation, $RequestedCurrency);

        return $Budget;
    }

    /**
     * @param $QuotationArray
     */
    static function getAttractionBreakDown($QuotationArray) {
        $Vehicle = new Vehicle();
        #Vehicle
        $VehicleType = $Vehicle->getPaxToVehicle($QuotationArray, true, "first", $QuotationArray['country']);//get vehicle vehicle

        if (!$VehicleType)
            return false;
        $VehicleID = $VehicleType->ID;
        $VehiclePax = $VehicleType->pax_min;
        $AttrBreakdown=[];

        if(isset($QuotationArray['attraction']) && !empty($QuotationArray['attraction'])) {
            foreach ($QuotationArray['attraction'] as $day => $item) {
                foreach ($item as $attractionItem) {
                    $AttrBreakdown['attraction'][$attractionItem] = Attraction::getAttractionBreakdown($VehicleID, $attractionItem, $QuotationArray['market']);
                    $AttrBreakdown['attraction'][$attractionItem]['pp_vehicle'] = round(($AttrBreakdown['attraction'][$attractionItem]["vehicle_rate"]/$VehiclePax), 2) ;
                }
            }
        }

        if(isset($QuotationArray['city_tour']) && !empty($QuotationArray['city_tour'])) {
            foreach ($QuotationArray['city_tour'] as $day => $item) {
                foreach ($item as $attractionItem) {
                    $AttrBreakdown['city_tour'][$attractionItem] = CityTour::getCitytourBreakdown($VehicleID, $attractionItem, $QuotationArray['market']);
                    $AttrBreakdown['city_tour'][$attractionItem]['pp_vehicle'] = round(($AttrBreakdown['city_tour'][$attractionItem]["vehicle_rate"]/$VehiclePax), 2) ;
                }
            }
        }

        if(isset($QuotationArray['excursion']) && !empty($QuotationArray['excursion'])) {
            foreach ($QuotationArray['excursion'] as $day=>$item) {
                foreach ($item as $attractionItem) {
                    $AttrBreakdown['excursion'][$attractionItem] = Excursion::getExcursionBreakdown($VehicleID, $attractionItem, $QuotationArray['market']);
                    $AttrBreakdown['excursion'][$attractionItem]['pp_vehicle'] = round(($AttrBreakdown['excursion'][$attractionItem]["vehicle_rate"]/$VehiclePax), 2);
                }
            }
        }

        return $AttrBreakdown;
    }

    /**
     * @param $Quotation
     * @return array
     */
    static function getSupplementCost($Quotation)
    {
        return QuotationHotel::getSupplementRatePlane($Quotation);
    }

    /**
     * @param $Quotation
     * @return array|bool
     * @throws \ReflectionException
     */
    static function getCruiseCost($Quotation)
    {
        $QuotationCruise = new QuotationCruise();
        $Cost['cost'] = $QuotationCruise->getCruiseRatePlane($Quotation);
        $Cost['indexes'] = $QuotationCruise->getAvailableCruiseIndex();

        return $Cost;
    }

    /**
     * @param $Quotation
     * @return array|bool
     * @throws \ReflectionException
     */
    static function getHotelCost($Quotation)
    {
        $QuotationHotel = new QuotationHotel();
        $Cost['cost'] = $QuotationHotel->getHotelRatePlane($Quotation);
        $Cost['indexes'] = $QuotationHotel->getAvailableHotelsIndex();
        return $Cost;
    }


    /**
     * @param $Quotation
     * @return array
     */
    static function getAttractionCost($Quotation, $Direct = false)
    {
        return QuotationAttraction::getAttractionRatePlane($Quotation, $Direct);
    }

    /**
     * @param $Quotation
     * @return mixed
     */
    static function getMealCost($Quotation)
    {
        return QuotationMeal::getMealRatePlane($Quotation);
    }

    /**
     * @param $Quotation
     * @return array
     */
    static function getCityTourCost($Quotation, $Direct = false)
    {
        return QuotationAttraction::getCityTourRatePlane($Quotation, $Direct);
    }

    /**
     * @param $Quotation
     * @return array
     */
    static function getExcursionCost($Quotation, $Direct = false)
    {
        return QuotationAttraction::getExcursionRatePlane($Quotation, $Direct);
    }

    /**
     * @param $Quotation
     * @return mixed
     */
    static function getTransportCost($Quotation)
    {
        return QuotationTransport::getTransportRatePlane($Quotation);
    }

    /**
     * @param $Quotation
     * @return array
     */
    static function getHotelTransportCost($Quotation)
    {
        return HotelTransportRate::getHotelTransportRate($Quotation);
    }

    /**
     * @param $Quotation
     * @return float|int
     */
    static function getNights($Quotation)
    {
        return QuotationHotel::getAllNight($Quotation);
    }

    /**
     * @param $Quotation
     * @return int
     */
    static function getDays($Quotation)
    {
        return Quotation::getNights($Quotation) + 1;
    }

    /**
     * @param $Quotation
     * @return int
     */
    static function getDaysForQuarantine($Quotation) {
        return isset($Quotation['hotel']) ? (count($Quotation['hotel']) + 1) : 0;
    }

    /**
     * @param $Quotation
     * @param bool $Stops
     * @return array
     */
    static function getTourDaysDetail($Quotation, $Stops = false)
    {
        if (isset($Quotation['hotel']) || isset($Quotation['cruise'])) {//if hotels are set
            return QuotationHotel::getTourDaysDetail($Quotation, $Stops);
        } elseif (isset($Quotation['night'])) //if transport are set
            return QuotationTransport::getTourDaysDetail($Quotation);
        else
            return QuotationTransport::getTourDaysDetail($Quotation);
    }


    /**
     * @param $Attraction
     * @param $CityTour
     * @param $Excursion
     * @return array
     */
    static function getAllTheAttractionCost($Attraction, $CityTour, $Excursion)
    {
        $Total = ['cost' => 0];
        if (isset($Attraction['pax_cost'])) {
            foreach ($Attraction['pax_cost'] as $PaxType => $Amount) {

                if (isset($Total['pax_cost'][$PaxType]))
                    $Total['pax_cost'][$PaxType] += floatval($Amount);
                else
                    $Total['pax_cost'][$PaxType] = floatval($Amount);

            }
            $Total['cost'] += $Attraction['cost'];
        }

        if (isset($CityTour['pax_cost'])) {
            foreach ($CityTour['pax_cost'] as $PaxType => $Amount) {

                if (isset($Total['pax_cost'][$PaxType]))
                    $Total['pax_cost'][$PaxType] += floatval($Amount);
                else
                    $Total['pax_cost'][$PaxType] = floatval($Amount);

            }


            $Total['cost'] += $CityTour['cost'];
        }

        if (isset($Excursion['pax_cost'])) {
            foreach ($Excursion['pax_cost'] as $PaxType => $Amount) {

                if (isset($Total['pax_cost'][$PaxType]))
                    $Total['pax_cost'][$PaxType] += floatval($Amount);
                else
                    $Total['pax_cost'][$PaxType] = floatval($Amount);

            }
            $Total['cost'] += $Excursion['cost'];
        }

        return $Total;
    }

    /**
     * @param $Quotation
     * @return array
     */
    static function getQuotationEmails($Quotation)
    {
        $EmailList = [];

        //get additional email
        if (isset($Quotation['additional_emails'])) {

            $EmailListTemp = explode(',', $Quotation['additional_emails']);

            foreach ($EmailListTemp as $EmailAddress) {
                if (!filter_var($EmailAddress, FILTER_VALIDATE_EMAIL) === false) {
                    $EmailList['to'][] = ['name' => 'no name', 'email' => $EmailAddress];
                }
            }
        }
        //get auth user email
        $EmailList['to'][] = ['name' => Auth::user()->name, 'email' => Auth::user()->email];

        return $EmailList;
    }

    /**
     * @param $QuotationArrayLast
     * @param $QuotationArrayOld
     * @param string $VoucherType
     * @param bool $HotelID
     * @param string $HotelType
     * @return array
     */
    function getAvailableVoucher($QuotationArrayLast, $QuotationArrayOld, $VoucherType = 'auto', $HotelID = false, $HotelType = "new")
    {
        if ($VoucherType != 'auto') {
            if ($HotelType == "new")#if user wants the quotation from last quotation
                $TypeQuote = $QuotationArrayLast;
            else
                $TypeQuote = $QuotationArrayOld;

            return static::getHotelVouchersByType($TypeQuote, $VoucherType, $HotelID);
        }

        //if first time quotation motherfucker
        if (!$QuotationArrayOld)
            return static::getHotelVouchersByType($QuotationArrayLast, "reservation", $HotelID);


        //pass the new quotation
        $Voucher = [];
        $NewQuotationHotels = [];
        $QuotationHotels = $QuotationArrayLast->hotel;

        if (isset($QuotationArrayLast->status) && $QuotationArrayLast->status != 3) {//if user click cancel quote
            #check that selected hotels in the new quotation available in the old one
            foreach ($QuotationHotels as $Index => $HotelItem) {//new hotels settings loop


                $NewQuotationHotels[] = $HotelItem->hotel;//set new quote hotels

                #reservation hotel ##########################################################################
                if (!empty($QuotationArrayLast->quotation_no)) {//if modifying confirmed quotation
                    $QuotationOldHotel = $QuotationArrayOld->hotel
                        ->where('hotel', intval($HotelItem->hotel));
                }

                //add reservation
                #if there are no any previce quotation
                #Or current quotation is status is save
                #Or previce quotation is not confirmed one
                if (empty($QuotationOldHotel) || empty($QuotationOldHotel->toArray()) || (!empty($QuotationArrayOld) && $QuotationArrayOld->status == 1)) {
                    $Voucher['reservation'][$Index] = [
                        'hotel' => $HotelItem->hotel,
                        'note' => ""
                    ];
                    continue;
                }

                #Ammendment ###############################################################################
                #find in the last quotation new settings //make sure all the number interger this motherfucker doesnt seacrh if it's not
                $QuotationOldHotel = $QuotationArrayOld->hotel
                    ->where('extra_bed', intval($HotelItem->extrabed))
                    ->where('room_category', intval($HotelItem->room_category))
                    ->where('hotel', intval($HotelItem->hotel))
                    ->where('meal', intval($HotelItem->meal_type))
                    ->where('check_in_year', intval($HotelItem->check_in_year))
                    ->where('check_in_month', intval($HotelItem->check_in_month))
                    ->where('check_in_day', intval($HotelItem->check_in_day))
                    ->where('check_out_year', intval($HotelItem->check_out_year))
                    ->where('check_out_month', intval($HotelItem->check_out_month))
                    ->where('check_out_day', intval($HotelItem->check_out_day))->first();


                if ($QuotationOldHotel) {//if there's is a hotel check room count and types

                    if (!empty($HotelItem->room_type)) {
                        foreach ($HotelItem->room_type as $RoomType => $RoomCount) {#check rooms are correct
                            if (empty(intval($RoomCount)))
                                continue;


                            $QuotationOldHotelRoom = $QuotationOldHotel->room
                                ->where('room_type', intval($RoomType))
                                ->where('room_count', intval($RoomCount))->first();
                            //if not set hotel
                            if (!$QuotationOldHotelRoom) {
                                $Voucher['amendment'][$Index] = [
                                    'hotel' => $HotelItem->hotel,
                                    'note' => "Room Type ($RoomType $RoomCount)"
                                ];

                            }
                        }
                    }
                }

                if (!$QuotationOldHotel) {
                    $Voucher['amendment'][$Index] = [
                        'hotel' => $HotelItem->hotel,
                        'note' => 'Main settings'
                    ];
                }
            }
        }


        #get cancel hotel ###############################################################################
        #if there's a extra hotel in last quotation but not in the new quotaion it need to cancel
        if (!empty($QuotationArrayOld->quotation_no) || $QuotationArrayOld->status == 3) {//don't cancel first confirmation
            foreach ($QuotationArrayOld->hotel as $Index => $OldHotelItem) {

                if (!in_array($OldHotelItem->hotel, $NewQuotationHotels)) {

                    $Voucher['cancel'][$Index] = [
                        'hotel' => $OldHotelItem->hotel,
                        'note' => ''
                    ];
                }

            }
        }


        if ($HotelID) {
            foreach ($Voucher as $k1 => $VoucherTypeArray) {
                foreach ($VoucherTypeArray as $k2 => $VoucherDetails) {

                    if (is_array($HotelID) && !in_array($VoucherDetails['hotel'], $HotelID))
                        unset($Voucher[$k1][$k2]);
                    elseif ((int)$VoucherDetails['hotel'] === $HotelID) {
                        unset($Voucher[$k1][$k2]);
                    }


                }
            }
        }


        #check removed hotels and amendment
        return $Voucher;


    }

    /**
     * @param $QuotationNo
     * @param $QuotationLast
     * @param $QuotationOld
     * @param bool $Email
     * @param string $VoucherType
     * @param bool $HotelID
     * @return array
     */
    function getHotelVouchers($QuotationNo, $QuotationLast, $QuotationOld, $Email = false, $VoucherType = 'auto', $HotelID = false, $HotelCat = "all", $send_individual = false)
    {

        $HotelType = "new";

        if ($HotelID) {
            if(!is_array($HotelID)) {
                $HotelTypeArray = explode(",", $HotelID);
                $HotelType = $HotelTypeArray[0];
                $HotelID = $HotelTypeArray[1];
            }
        }

        $AvailableVoucher = $this->getAvailableVoucher($QuotationLast, $QuotationOld, $VoucherType, $HotelID, $HotelType);

        if($QuotationLast->country == 64) { // Singapore
            $logoUrl[] = "http://applev2.appletechlabs.com/assets/image/logo/mega.jpg";
            $Address = "181 , Kitchener road #01 – 09/10 </br> New Park Hotel shopping arcade </br> Singapore 208533 </br>";
        } else if($QuotationLast->country == 63) { // Malaysia
            $logoUrl[] = "http://applev2.appletechlabs.com/assets/image/logo/two-logos.jpg";
            $Address = "Pinnacle Tower A , 21st floor No 09, </br> Jalan 51 A /223, Pjs 52, 46100 Petaling Jaya, </br> Selangor, Malaysia. </br>";
        } else {
            $logoUrl[] = "https://applev2.appletechlabs.com/assets/image/logo/log-long.png";
            $Address = "No:148 </br> Aluthmawatha Road </br> Colombo 15 </br>";
        }

        $QuotationArrayLast = QuotationManage::getQuotation($QuotationLast->ID, $QuotationLast->quotation_no);//get the quotation
        if(isset($QuotationOld->ID)) {
            $QuotationArrayOld = QuotationManage::getQuotation($QuotationOld->ID, $QuotationOld->quotation_no);//get the quotation
        }
        $QuotationHotel = new QuotationHotel();

        $VoucherHTML = [];
        $HotelRate = $QuotationHotel->getHotelListCost($QuotationArrayLast);

        //get hotels
        //amendment and reservation
        // dd($VoucherType, $HotelCat, $QuotationArrayLast);

        if($HotelCat == 'all' || $HotelCat == 'hotel_vouchers') {
            if(($VoucherType != "cancel" || $HotelCat == 'all')) {
                foreach (($QuotationArrayLast['hotel'] ?? []) as $Index => $HotelSettings) {

                    if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                        continue;

                    if($VoucherType == "cancel") {
                        $Status = 3;
                        if($HotelID != false) {
                            if ($HotelSettings['hotel'] != $HotelID)
                                continue;
                        }

                    } else {
                        //get status
                        if (!empty($AvailableVoucher['reservation'][$Index]['hotel']) && $AvailableVoucher['reservation'][$Index]['hotel'] == $HotelSettings['hotel'])
                            $Status = 1;
                        elseif (!empty($AvailableVoucher['amendment'][$Index]['hotel']) && $AvailableVoucher['amendment'][$Index]['hotel'] == $HotelSettings['hotel'])
                            $Status = 2;
                        else
                            continue;

                    }

                    $ConfirmNote = isset($QuotationArrayLast['confirm']['confirm_note'][$Index]) ? $QuotationArrayLast['confirm']['confirm_note'][$Index] : "";
                    $Remarks = isset($QuotationArrayLast['confirm']['remark'][$Index]) ? $QuotationArrayLast['confirm']['remark'][$Index] : "";

                    if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                        $VoucherHTML[$Index][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$Index], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                    }
                }
            } else if($VoucherType == "cancel" && $HotelCat == 'all') {

                if (isset($QuotationArrayLast['cancel']['cancel_hotel']) && !empty($QuotationArrayLast['cancel']['cancel_hotel'])) {

                    foreach ($QuotationArrayLast['cancel']['cancel_hotel'] as $key => $value) {


                        $HotelSettings = json_decode($QuotationArrayLast['cancel']['cancel_hotel'][$key], true);
                        $ConfirmNote = isset($QuotationArrayLast['cancel']['cancel_note'][$key]) ? $QuotationArrayLast['cancel']['cancel_note'][$key] : "";
                        $Remarks = isset($QuotationArrayLast['cancel']['cancel_remark'][$key]) ? $QuotationArrayLast['cancel']['cancel_remark'][$key] : "";
                        if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                            $VoucherHTML[$key][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => 3, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$key], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                        }
                    }
                } else {

                    foreach (($QuotationArrayLast['hotel'] ?? []) as $Index => $HotelSettings) {

                        if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                            continue;

                        if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                            continue;

                        $Status = 3;

                        $ConfirmNote = isset($QuotationArrayLast['confirm']['confirm_note'][$Index]) ? $QuotationArrayLast['confirm']['confirm_note'][$Index] : "";
                        $Remarks = isset($QuotationArrayLast['confirm']['remark'][$Index]) ? $QuotationArrayLast['confirm']['remark'][$Index] : "";

                        if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                            $VoucherHTML[$Index][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$Index], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                        }
                    }
                }
            } else if ($VoucherType == "cancel" && $HotelCat != 'all') {
                $sendMail = false;
                if (isset($QuotationArrayLast['cancel']['cancel_hotel']) && !empty($QuotationArrayLast['cancel']['cancel_hotel'])) {
                    if(isset($send_individual)) {
                        foreach ($QuotationArrayLast['cancel']['cancel_hotel'] as $key => $value) {
                            $HotelSettings = json_decode($QuotationArrayLast['cancel']['cancel_hotel'][$key], true);
                            if(isset($HotelSettings['hotel']) && $HotelSettings['hotel'] != $HotelID)
                                continue;

                            $Status = 3;

                            $ConfirmNote = isset($QuotationArrayLast['cancel']['cancel_note'][$key]) ? $QuotationArrayLast['cancel']['cancel_note'][$key] : "";
                            $Remarks = isset($QuotationArrayLast['cancel']['cancel_remark'][$key]) ? $QuotationArrayLast['cancel']['cancel_remark'][$key] : "";
                            if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                                $VoucherHTML[$key][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => 3, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$key], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                            }
                            $sendMail = true;
                        }

                        if(!$sendMail) {
                            if(!$HotelID) {
                                foreach (($QuotationArrayLast['hotel'] ?? []) as $Index => $HotelSettings) {
                                    if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                                        continue;

                                    $Status = 3;

                                    $ConfirmNote = isset($QuotationArrayLast['confirm']['confirm_note'][$Index]) ? $QuotationArrayLast['confirm']['confirm_note'][$Index] : "";
                                    $Remarks = isset($QuotationArrayLast['confirm']['remark'][$Index]) ? $QuotationArrayLast['confirm']['remark'][$Index] : "";

                                    if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                                        $VoucherHTML[$Index][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$Index], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                                    }
                                }
                            } else {

                                foreach (($QuotationArrayLast['hotel'] ?? []) as $Index => $HotelSettings) {
                                    if($HotelSettings['hotel'] != $HotelID)
                                        continue;

                                    if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                                        continue;

                                    $Status = 3;

                                    $ConfirmNote = isset($QuotationArrayLast['confirm']['confirm_note'][$Index]) ? $QuotationArrayLast['confirm']['confirm_note'][$Index] : "";
                                    $Remarks = isset($QuotationArrayLast['confirm']['remark'][$Index]) ? $QuotationArrayLast['confirm']['remark'][$Index] : "";

                                    if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                                        $VoucherHTML[$Index][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$Index], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                                    }
                                }

                                foreach (($QuotationArrayOld['hotel'] ?? []) as $Index => $HotelSettings) {
                                    if($HotelSettings['hotel'] != $HotelID)
                                        continue;

                                    if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                                        continue;

                                    $Status = 3;

                                    $ConfirmNote = isset($QuotationArrayOld['confirm']['confirm_note'][$Index]) ? $QuotationArrayOld['confirm']['confirm_note'][$Index] : "";
                                    $Remarks = isset($QuotationArrayOld['confirm']['remark'][$Index]) ? $QuotationArrayOld['confirm']['remark'][$Index] : "";

                                    if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                                        $VoucherHTML[$Index][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$Index], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                                    }
                                }
                            }
                        }

                    } else {
                        foreach ($QuotationArrayLast['cancel']['cancel_hotel'] as $key => $value) {
                            if(!isset($HotelRate[$key]))
                                continue;
                            $HotelSettings = json_decode($QuotationArrayLast['cancel']['cancel_hotel'][$key], true);
                            $ConfirmNote = isset($QuotationArrayLast['cancel']['cancel_note'][$key]) ? $QuotationArrayLast['cancel']['cancel_note'][$key] : "";
                            $Remarks = isset($QuotationArrayLast['cancel']['cancel_remark'][$key]) ? $QuotationArrayLast['cancel']['cancel_remark'][$key] : "";
                            if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                                $VoucherHTML[$key][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => 3, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$key], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                            }
                        }
                    }

                } else {
                    if(!$HotelID) {
                        foreach (($QuotationArrayLast['hotel'] ?? []) as $Index => $HotelSettings) {
                            if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                                continue;

                            $Status = 3;

                            $ConfirmNote = isset($QuotationArrayLast['confirm']['confirm_note'][$Index]) ? $QuotationArrayLast['confirm']['confirm_note'][$Index] : "";
                            $Remarks = isset($QuotationArrayLast['confirm']['remark'][$Index]) ? $QuotationArrayLast['confirm']['remark'][$Index] : "";

                            if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                                $VoucherHTML[$Index][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$Index], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                            }
                        }
                    } else {
                        foreach (($QuotationArrayLast['hotel'] ?? []) as $Index => $HotelSettings) {
                            if($HotelSettings['hotel'] != $HotelID)
                                continue;

                            if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                                continue;

                            if ($QuotationArrayLast['accommodation'][$Index] == 2)//if own arrangement
                                continue;

                            $Status = 3;

                            $ConfirmNote = isset($QuotationArrayLast['confirm']['confirm_note'][$Index]) ? $QuotationArrayLast['confirm']['confirm_note'][$Index] : "";
                            $Remarks = isset($QuotationArrayLast['confirm']['remark'][$Index]) ? $QuotationArrayLast['confirm']['remark'][$Index] : "";

                            if($HotelSettings['provider'] == QuotationClass::HOTEL_LOCAL) {
                                $VoucherHTML[$Index][$HotelSettings['hotel']] = View::make('quotation.voucher.hotel-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Rate' => $HotelRate[$Index], 'Logo' => $logoUrl, 'Address' => $Address])->render();
                            }
                        }
                    }
                }
            }
        }

        // Day used hotels
        if($HotelCat == 'all' || $HotelCat == 'day_use_vouchers') {

            foreach (($QuotationArrayLast["other_rate"] ?? []) as $Index => $HotelSettings) {

                if ($HotelSettings["type"] != 3)//if own arrangement
                    continue;

                if ($HotelCat != 'all') {
                    if ($HotelID && $HotelID != $HotelSettings["details"]["hotel_id"])
                        continue;
                }

                //get status

                $ids = \App\Model\QuotationManage\Quotation::withTrashed()->whereHas('Confirm')->where("quotation_no", $QuotationLast->ID)->count();

                if ($HotelCat == 'all') {
                    if (isset($ids) && $ids > 1) {
                        $Status = 2;
                    } else {
                        $Status = 1;
                    }
                } else {
                    if ($VoucherType == "reservation") {
                        $Status = 1;
                    } else if ($VoucherType == "amendment") {
                        $Status = 2;
                    } else {
                        $Status = 3;
                    }
                }

                $ConfirmNote = isset($QuotationArrayLast["dayused"]['dayused_note'][$Index]) ? $QuotationArrayLast["dayused"]['dayused_note'][$Index] : "";
                $Remarks = isset($QuotationArrayLast["dayused"]['dayused_remark'][$Index]) ? $QuotationArrayLast["dayused"]['dayused_remark'][$Index] : "";

                $VoucherHTML[$Index][$HotelSettings['details']['hotel_id']] = View::make('quotation.voucher.day-used-voucher', ['Email' => $Email, 'Remarks' => $Remarks, 'ConfirmNote' => $ConfirmNote, 'Status' => $Status, 'HotelSettings' => $HotelSettings, 'QuotationArray' => $QuotationArrayLast, 'Logo' => $logoUrl, 'Address' => $Address])->render();

            }
        }

        return $VoucherHTML;

    }


    /**
     * @param $Quotation
     * @param string $VoucherType
     * @param $HotelID
     * @return array
     */
    static function getHotelVouchersByType($Quotation, $VoucherType = 'auto', $HotelID)
    {
        $Voucher = [];
        foreach ($Quotation['hotel'] as $Index => $HotelItem) {

            $Voucher[$VoucherType][$Index] = [
                'hotel' => $HotelItem['hotel'],
                'note' => ''
            ];
        }
        if ($HotelID) {
            foreach ($Voucher as $k1 => $VoucherTypeArray) {
                foreach ($VoucherTypeArray as $k2 => $VoucherDetails) {

                    if (is_array($HotelID) && !in_array($VoucherDetails['hotel'], $HotelID))
                        unset($Voucher[$k1][$k2]);
                    elseif (intval($VoucherDetails['hotel']) != $HotelID)
                        unset($Voucher[$k1][$k2]);
                }
            }
        }

        return $Voucher;
    }


    /**
     * @param $Quotation
     * @param $RequestedCurrency
     * @return array
     */
    static function getHotelBudget($Quotation, $RequestedCurrency)
    {
        return QuotationHotel::getHotelBudgetArray($Quotation, $RequestedCurrency);
    }

    /**
     * @param $Quotation
     * @param $RequestedCurrency
     * @return array
     */
    static function getCruiseBudget($Quotation, $RequestedCurrency)
    {
        return QuotationCruise::getCruiseBudgetArray($Quotation, $RequestedCurrency);
    }

    /**
     * @param $Quotation
     * @param $requested_currency
     * @return mixed
     */
    static function getTransportBudget($Quotation, $requested_currency)
    {
        return Transport::getTransport($Quotation, $requested_currency);
    }

    /**
     * @param $Quotation
     * @param $requested_currency
     * @return bool
     */
    static function getOtherBudget($Quotation, $requested_currency)
    {

        $Budget = Quotation::getOtherCost($Quotation, "pnl");

        if ($Budget) {
            if(isset($Budget['PP'])) {
                foreach($Budget['PP'] as $j => $RateArr) {
                    foreach ($RateArr as $k => $PPRate) {
                        $Budget['PP'][$j][$k]['rate'] = currency($PPRate['rate'], 'USD', $requested_currency, false);
                    }
                }
            }

            if(isset($Budget['PQ'])) {
                foreach (($Budget['PQ'] ?? []) as $k => $PQRate) {
                    if(isset($PQRate['rate']) && !empty($PQRate['rate'])) {
                        $Budget['PQ'][$k]['rate'] = currency($PQRate['rate'], 'USD', $requested_currency, false);
                    }
                }
            }

            if(isset($Budget['DU'])) {
                foreach (($Budget['DU'] ?? []) as $k => $PQRate) {
                    if(isset($PQRate['rate']) && !empty($PQRate['rate'])) {
                        foreach ($PQRate['rate'] as $j => $DURate) {
                            $Budget['DU'][$k]['rate'][$j] = currency($DURate, 'USD', $requested_currency, false);
                            $Budget['DU'][$k]['room_count'][$j] = $PQRate['room_count'][$j];
                            $Budget['DU'][$k]['total'][$j] = $Budget['DU'][$k]['rate'][$j] * $Budget['DU'][$k]['room_count'][$j];
                        }

                    }
                }
            }

            $Budget['cost'] = currency($Budget['cost'], 'USD', $requested_currency, false);
        }

        return $Budget;
    }

    /**
     * @param $Quotation
     * @param $RequestedCurrency
     * @return array
     */
    static function getAttractionBudget($Quotation, $RequestedCurrency)
    {
        $Budget = [];
        $Budget['total'] = 0;
        $Budget['total_attraction'] = 0;
        $Budget['total_none_attraction'] = 0;
        $AttractionList = [];
        $CityTourList = [];
        $ExcursionList = [];

        if (!empty($Quotation['attraction']))
            $AttractionList = flatten($Quotation['attraction']);

        if (!empty($Quotation['city_tour']))
            $CityTourList = flatten($Quotation['city_tour']);

        if (!empty($Quotation['excursion']))
            $ExcursionList = flatten($Quotation['excursion']);

        // Get tour days detail to calculate dates for each day
        $DaysDetail = Quotation::getTourDaysDetail($Quotation);

        if (!empty($Quotation['attraction'])) {
            foreach ($Quotation['attraction'] as $Day => $Attraction) {
                // Add date information for this day
                if (isset($DaysDetail[$Day]['date'])) {
                    $Budget['ob_items'][$Day]['date'] = $DaysDetail[$Day]['date'];
                }

                foreach ($Attraction as $AttrItemKey => $AttrItem) {
                    $Budget['ob_items'][$Day]['attraction'][] = Attraction::where("ID", $AttrItem)->first()->toArray();
                }
            }
        }

        if (!empty($Quotation['city_tour'])) {
            foreach ($Quotation['city_tour'] as $Day => $Attraction) {
                // Add date information for this day
                if (isset($DaysDetail[$Day]['date'])) {
                    $Budget['ob_items'][$Day]['date'] = $DaysDetail[$Day]['date'];
                }

                foreach ($Attraction as $AttrItemKey => $AttrItem) {
                    $Budget['ob_items'][$Day]['city_tour'][] = CityTour::where("ID", $AttrItem)->first()->toArray();
                }
            }
        }

        if (!empty($Quotation['excursion'])) {
            foreach ($Quotation['excursion'] as $Day => $Attraction) {
                // Add date information for this day
                if (isset($DaysDetail[$Day]['date'])) {
                    $Budget['ob_items'][$Day]['date'] = $DaysDetail[$Day]['date'];
                }

                foreach ($Attraction as $AttrItemKey => $AttrItem) {
                    $Budget['ob_items'][$Day]['excursion'][] = Excursion::where("ID", $AttrItem)->first()->toArray();
                }
            }
        }

        foreach ($AttractionList as $AttrItem) {
            $Budget['items']['attraction'][$AttrItem] = Attraction::where("ID", $AttrItem)->first()->toArray();
        }
        foreach ($CityTourList as $AttrItem) {
            $Budget['items']['city_tour'][$AttrItem] = CityTour::where("ID", $AttrItem)->first()->toArray();
        }
        foreach ($ExcursionList as $AttrItem) {
            $Budget['items']['excursion'][$AttrItem] = Excursion::where("ID", $AttrItem)->first()->toArray();
        }

        $Budget['rates']['attraction'] = Attraction::getAttraction($Quotation, true);
        $Budget['rates']['attraction_breakdown'] = Attraction::getAttractionRatesVal($Quotation, true);
        $Budget['rates']['city_tour'] = CityTour::getCityTour($Quotation, true);
        $Budget['rates']['city_tour_breakdown'] = CityTour::getCityTourRatesVal($Quotation, true);
        $Budget['rates']['excursion'] = Excursion::getExcursion($Quotation, true);
        $Budget['rates']['excursion_breakdown'] = Excursion::getExcursionRatesVal($Quotation, true);

        foreach ($Budget['rates'] as $AttrType => $AttrItemArray) {
            if(!in_array($AttrType, ['attraction', 'city_tour', 'excursion'])) {
                continue;
            }
            foreach ($AttrItemArray as $AttrID => $AttrItem) {

                //COnverting
                $AttrItem['adult'] = ($Quotation['country'] == "64") ? $AttrItem['adult'] : currency($AttrItem['adult'], 'USD', $RequestedCurrency, false);
                    if (!empty($AttrItem['child']))
                        $AttrItem['child'] = ($Quotation['country'] == "64") ? $AttrItem['child']: currency($AttrItem['child'], 'USD', $RequestedCurrency, false);

                $Budget['total'] += $AttrItem['adult'] * $Quotation['pax']['adult'];
                if(in_array($AttrType, ['attraction'])) {
                    if($Budget['rates']['attraction_breakdown'][$AttrID]['pnl_type'] == "") {
                        $Budget['total_attraction'] += $AttrItem['adult'] * $Quotation['pax']['adult'];
                    } else {
                        $Budget['total_none_attraction'] += $AttrItem['adult'] * $Quotation['pax']['adult'];
                    }
                } else {
                    $Budget['total_none_attraction'] += $AttrItem['adult'] * $Quotation['pax']['adult'];
                }

                if (!empty($AttrItem['child'])) {
                    $Budget['total'] += $AttrItem['child'] * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);
                    if(in_array($AttrType, ['attraction'])) {
                        if($Budget['rates']['attraction_breakdown'][$AttrID]['pnl_type'] == "") {
                            $Budget['total_attraction'] += $AttrItem['child'] * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);
                        } else {
                            $Budget['total_none_attraction'] += $AttrItem['child'] * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);
                        }
                        
                    } else {
                        $Budget['total_none_attraction'] += $AttrItem['child'] * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);
                    }
                }
                    

                $Budget['attraction_individual'][$AttrType][$AttrID] = $Quotation['pax']['adult'] * $AttrItem['adult'];

                if (isset($AttrItem['child']))
                    $Budget['attraction_individual'][$AttrType][$AttrID] += $AttrItem['child'] * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);

            }
        }
        if(isset($Budget['ob_items'])) {
            ksort($Budget['ob_items']);
        }

        return $Budget;
    }

    /**
     * @param $Quotation
     * @param $RequestedCurrency
     * @return array
     */
    static function getDayCity($Quotation)
    {
        $dayCity = [];
        $Count=1;

        if(isset($Quotation["hotel"])) {
            foreach ($Quotation["hotel"] as $Hotel) {
                for ($i=1; $i<=$Hotel['night']; $i++) {
                    $dayCity[$Count]["city"] = $Hotel["place"];
                    $dayCity[$Count]["name"] = Place::find($Hotel["place"])["name"];
                    $Count++;
                }
            }
            $dayCity[$Count]["city"] = $Hotel["place"];
            $dayCity[$Count]["name"] = Place::find($Hotel["place"])["name"];
        }
        return $dayCity;
    }

    /**
     * @param $Quotation
     * @param $RequestedCurrency
     * @return array
     */
    static function getOBVehicleRate($Quotation)
    {
        $OBVehicleRate = [];
        $Vehicle = new \App\Model\Vehicle\Vehicle();
        $VehicleName = "";
        $VehicleID = "";

        $VehicleType = $Vehicle->getPaxToVehicle($Quotation);

        if(isset($Quotation['rate']['transport']['vehicle']['vehicle_type'])){
            if (isset($Quotation['rate']['transport']['vehicle']['vehicle_type'])){
                $VehicleName = Vehicle::find($Quotation['rate']['transport']['vehicle']['vehicle_type'])->vehicleType()->first()->name;
                $VehicleID = $Quotation['rate']['transport']['vehicle']['vehicle_type'];
            }
        } else {
            if (!empty($VehicleType)){
                $VehicleName = $VehicleType->vehicleType()->first()->name;
                $VehicleID = $VehicleType["ID"];
            } else {
                $VehicleName = '-';
            }
        }

        if(isset($Quotation['place'])){

            foreach ($Quotation['place'] as $KeyPlace => $Place) {
                $OBVehicleRateObj = OutboundVehicleRate::where('city', "=", $Place)->where('vehicle', "=", $VehicleID)->first();
                if(isset($OBVehicleRateObj) && !empty($OBVehicleRateObj)) {
                    $OBVehicleRate[$KeyPlace] = $OBVehicleRateObj->toArray();

                    $OBVehicleRate[$KeyPlace]["place_name"] = Place::find($Place)["name"];
                    $OBVehicleRate[$KeyPlace]["vehicle_name"] = $VehicleName;
                }
            }
        }

        return $OBVehicleRate;
    }

    /**
     * @param $Quotation
     * @return bool|Carbon
     */
    static function getDeadline($Quotation)
    {

        if (!empty($Quotation['confirm']) && !empty($Quotation['timestamp']))
            return $EndDate = Carbon::createFromTimestamp($Quotation['timestamp'])->addDays(14);
        else
            return false;


    }

    /**
     * @param $QuotationID
     * @return array
     */
    static function getReferenceID($QuotationID)
    {

        $Ref_in5 = sprintf("%05s", $QuotationID);
        $ID_in5 = sprintf("%05s", $QuotationID);


        return [
            $QuotationID,
            $Ref_in5 . "CNTL",
            "TMP-PO" . $Ref_in5,
            $ID_in5
        ];
    }

    /**
     * @return array
     */
    static function getBankCharges()
    {
        $CostArray = [];
        $CostArray['cost'] = 100;
        return $CostArray;
    }

    /**
     * @param $Quotation
     * @return bool
     */
    static function getOtherCost($Quotation, $cat="quote")
    {
        $Cost['cost'] = 0;

        if (isset($Quotation['other_rate'])) {
            foreach ($Quotation['other_rate'] as $k => $Item) {
                if($cat == "quote") {
                    if ($Item['type'] == "3") {
                        continue;
                    }
                }
                
                if($Item['type'] == 3) {

                    $text =  Hotel::find($Item['details']['hotel_id'])->name . " | Day Used Hotel";
                    $rateCount = 0;
                    $roomCount = array();
                    $rateTemp = array();

                    if(isset($Item['details']['single_bed_room_count'])) {
                        $roomCount["single"] = $Item['details']['single_bed_room_count'];
                        $rateTemp["single"] = $Item['details']['single_bed_rate'];
                        $rateCount += $Item['details']['single_bed_room_count'] * $Item['details']['single_bed_rate'];
                    }

                    if(isset($Item['details']['double_bed_room_count'])) {
                        $roomCount["double"] = $Item['details']['double_bed_room_count'];
                        $rateTemp["double"] = $Item['details']['double_bed_rate'];
                        $rateCount += $Item['details']['double_bed_room_count'] * $Item['details']['double_bed_rate'];
                    }

                    if(isset($Item['details']['triple_bed_room_count'])) {
                        $roomCount["triple"] = $Item['details']['triple_bed_room_count'];
                        $rateTemp["triple"] = $Item['details']['triple_bed_rate'];
                        $rateCount += $Item['details']['triple_bed_room_count'] * $Item['details']['triple_bed_rate'];
                    }

                    $Cost['DU'][] = [
                        'text' => $text,
                        'room_count' => $roomCount,
                        'rate' => $rateTemp];

                    $Cost['cost'] += $rateCount;
                } else {
                    $text = $Item['text'];
                    $type = $Item['type'];
                    $rate_type = $Item['rate_type'];
                    $rate = $Item['rate'];
                    $cwb_rate = $Item['cwb_rate'];
                    $cnb_rate = $Item['cnb_rate'];
                    $details = $Item['details'];

                    if ($rate_type == 1) {//per person
                        $Cost['PP']['adult'][] = [
                            'text' => $text,
                            'rate' => $rate];
                        
                        $Cost['cost'] += $rate * $Quotation['pax']['adult'];

                        if($cwb_rate != 0) {
                            $Cost['PP']['cwb'][] = [
                                'text' => $text,
                                'rate' => $cwb_rate];
                            $Cost['cost'] += $cwb_rate * $Quotation['pax']['cwb'];
                        }
                        if($cnb_rate != 0) {
                            $Cost['PP']['cnb'][] = [
                                'text' => $text,
                                'rate' => $cnb_rate];
                            $Cost['cost'] += $cnb_rate * $Quotation['pax']['cnb'];
                        }

                    } else {
                        $Cost['PQ'][] = [
                            'text' => $text,
                            'rate' => $rate];

                        $Cost['cost'] += $rate;

                    }
                }
                //dd($Cost);
                /*if ($Item['type'] == "3") {
                    continue;
                }

                $text = $Item['text'];
                $type = $Item['type'];
                $rate_type = $Item['rate_type'];
                $rate = $Item['rate'];
                $details = $Item['details'];

                if ($rate_type == 1) {//per person
                    $Cost['PP'][] = [
                        'text' => $text,
                        'rate' => $rate];

                    $Cost['cost'] += $rate * $Quotation['pax']['adult'];

                } else {
                    $Cost['PQ'][] = [
                        'text' => $text,
                        'rate' => $rate];

                    $Cost['cost'] += $rate;

                }*/
            }

            return $Cost;


        } else
            return false;
    }

    /**
     * @param $Quotation
     * @return bool
     */
    static function getWaterBottle($Quotation)
    {
        $Cost['cost'] = 0;
        $rate = self::getPerWaterBottle($Quotation);
        $days = self::getDays($Quotation);

        if ($Quotation['country'] == 62) {
            $Cost['PP']['adult'] = [
                'text' => "Water Bottle",
                'rate' => $rate * $days];
            
            $Cost['cost'] += $rate * $Quotation['pax']['adult'] * $days;

            if(isset($Quotation['pax']['cwb']) && $Quotation['pax']['cwb'] > 0) {
                $Cost['PP']['cwb'] = [
                    'text' => "Water Bottle",
                    'rate' => $rate * $days];
                $Cost['cost'] += $rate * $Quotation['pax']['cwb'] * $days;
            }
            if(isset($Quotation['pax']['cnb']) && $Quotation['pax']['cnb'] > 0) {
                $Cost['PP']['cnb'] = [
                    'text' => "Water Bottle",
                    'rate' => $rate * $days];
                $Cost['cost'] += $rate * $Quotation['pax']['cnb'] * $days;
            }

            return $Cost;
        } else
            return false;
    }

    /**
     * @param $Quotation
     * @return bool
     */
    static function getPerWaterBottle($Quotation)
    {
        $rate = 0.5;
        if(isset($Quotation['rate']['transport']['per_water_bottle'])) {
            $rate = $Quotation['rate']['transport']['per_water_bottle'];
        }
        return $rate;
    }


    /**
     * @param $HotelArray
     * @param $ArrivalDate
     * @return array
     */
    static function getHotelDatesAdjust($HotelArray, $ArrivalDate)
    {

        $ArrivalDate = Carbon::create($ArrivalDate['year'], $ArrivalDate['month'], $ArrivalDate['day'], 0);
        $LastDate = $ArrivalDate;
        $AdjustArray = [];

        foreach ($HotelArray as $Index => $HotelItem) {

            $CurrentCheckIn = Carbon::create($HotelItem['check_in']['year'], $HotelItem['check_in']['month'], $HotelItem['check_in']['day'], 0);
            $CurrentCheckOut = Carbon::create($HotelItem['check_out']['year'], $HotelItem['check_out']['month'], $HotelItem['check_out']['day'], 0);

            $DaysDif = $CurrentCheckIn->diffInDays($CurrentCheckOut);

            $CurrentAdjustArray = $HotelItem;

            $CurrentAdjustArray['check_in']['year'] = $LastDate->year;
            $CurrentAdjustArray['check_in']['month'] = $LastDate->month;
            $CurrentAdjustArray['check_in']['day'] = $LastDate->day;
            $LastDate->addDays($DaysDif);

            $CurrentAdjustArray['check_out']['year'] = $LastDate->year;
            $CurrentAdjustArray['check_out']['month'] = $LastDate->month;
            $CurrentAdjustArray['check_out']['day'] = $LastDate->day;

            $AdjustArray[$Index] = $CurrentAdjustArray;

        }
        return $AdjustArray;


    }

    static function getTimeDetails($Quotation) {
        $places = $Quotation['place_full'];
        $times = [];
        $ID = 0;
        $previous = 0;
        $previous_text = '';
        $index = 0;
        foreach($places as $key => $place) {
            if($Quotation['place_type'][$key] == 2) {
                $times[$ID]['index'] = $index++;
                $times[$ID]['from'] = 0;
                $times[$ID]['from_name'] = 'plane';
                $times[$ID]['to'] = $place;
                $times[$ID]['to_name'] = Place::find($place)->name;
            }

            if($Quotation['place_type'][$key] == 1 || $Quotation['place_type'][$key] == 3) {
                if($previous != 0) {
                    $times[$ID]['index'] = $index++;
                    $times[$ID]['from'] = $previous;
                    $times[$ID]['from_name'] = $previous_text;
                    $times[$ID]['to'] = $place;
                    $times[$ID]['to_name'] = Place::find($place)->name;
                }
            }

            

            $previous = $place;
            $previous_text = isset($times[$ID]) ? $times[$ID]['to_name'] : "";
            $ID++;
        }
        if($Quotation['place_type'][$key] == 3) {
            $times[$ID]['index'] = $index++;
            $times[$ID]['from'] = $place;
            $times[$ID]['from_name'] = Place::find($place)->name;
            $times[$ID]['to'] = 0;
            $times[$ID]['to_name'] = 'plane';
        }
        return $times;
    }

    static function getMealTransferDetails($reference_id, $quotation_no = false) {
        $SessionArray = [];
        $index = 0;
        $QuotationEdit = QuotationManage::withTrashed()->where('quotation_no', $quotation_no)->where('ID', $reference_id)->first();
        if (!empty($QuotationEdit->TransportTimeDetails())) {
            foreach ($QuotationEdit->TransportTimeDetails as $key => $Time) {
                $Single['index'] = $index++;
                $Single['date']['year'] = $Time->year;
                $Single['date']['month'] = $Time->month;
                $Single['date']['day'] = $Time->day;
                $Single['date']['formatted'] = Carbon::create($Single['date']['year'], $Single['date']['month'], $Single['date']['day'], 0)->toDateString();
                $Single['meal_time'] = $Time->meal_time;
                $Single['time'] = (isset($Time->time) && $Time->time != "00:00:00") ? substr($Time->time, 0, 5) : 0;
                $SessionArray[] = $Single;
            }
        }
        return $SessionArray;
    }
}
