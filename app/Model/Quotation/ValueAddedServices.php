<?php

namespace App\Model\Quotation;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Quotation\ValueAddedServices
 *
 * @property int $ID
 * @property string $service
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\ValueAddedServices whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\ValueAddedServices whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\ValueAddedServices whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\ValueAddedServices whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\ValueAddedServices whereService($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\ValueAddedServices whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\ValueAddedServices whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\ValueAddedServices whereUploadId($value)
 * @mixin \Eloquent
 */
class ValueAddedServices extends Model
{
    protected $table = "apple_vas";

    /**
     * @param $Quotation
     * @return bool|\Illuminate\Support\Collection
     */
    static function getQuotationList($Quotation)
    {
        $country = $Quotation['country'];
        $tour_type = $Quotation['tour_type'];

        $data = self::where(function ($q) use($country){
            $q->where('country',$country);
            $q->OrWhereNull('country');
        })->where(function ($q) use($tour_type){
            $q->where('tour_type',$tour_type);
            $q->OrWhereNull('tour_type');
        })->get();

        return ($data->isNotEmpty()?$data:false);

    }
}
