<?php

namespace App\Model\Quotation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Model\Quotation\QuotationExclude
 *
 * @property int $ID
 * @property string $exclude
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationExclude whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationExclude whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationExclude whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationExclude whereExclude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationExclude whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationExclude whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationExclude whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Quotation\QuotationExclude whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationExclude extends Model
{
    protected $table = 'apple_exclude';

    /**
     * @param $Quotation
     * @return bool|\Illuminate\Support\Collection
     */
    static function getQuotationList($Quotation)
    {
        $day = $Quotation['arrival_date']['day'];
        $month = $Quotation['arrival_date']['month'];

        $date = Carbon::parse('0000-'.$month.'-'.$day);

        $country = $Quotation['country'];
        $tour_type = $Quotation['tour_type'];

        $data = (new QuotationExclude)->where(function ($q) use($country){
            $q->where('country',$country);
            $q->OrWhereNull('country');
        })->where(function ($q) use($tour_type){
            $q->where('tour_type',$tour_type);
            $q->OrWhereNull('tour_type');
        })->where(function ($q) use($date){ //
            $q->where(function($s) use($date){
                $s->whereDate('from','<=',$date);
                $s->whereDate('to','>=',$date);
            });
            $q->OrWhereNull('from');
        })->get();

        return ($data->isNotEmpty()?$data:false);

    }

}
