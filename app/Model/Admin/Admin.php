<?php

namespace App\Model\Admin;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Admin\Admin
 *
 * @mixin \Eloquent
 */
class Admin extends Model
{

    function getDatabaseDetails()
    {


    }

    /**
     * @param $describeObject
     * @return array
     */
    static function describeDatabase($describeObject)
    {

        $returnArray = [];

        foreach ($describeObject as $ObjectItem) {

            $ItemArray['name'] = $ObjectItem->COLUMN_NAME;
            $ItemArray['data_type'] = $ObjectItem->DATA_TYPE;
            $ItemArray['position'] = $ObjectItem->ORDINAL_POSITION;
            $ItemArray['column_type'] = $ObjectItem->COLUMN_TYPE;
            $returnArray[$ObjectItem->TABLE_NAME][$ItemArray['name']] = $ItemArray;

        }

        return $returnArray;

    }

    /**
     * @param $array1
     * @param $array2
     * @return array
     */
    static function differenceStructureTables($array1, $array2)
    {
        //sd($array1);

        $dif_list = [];

        foreach ($array1 as $table => $array_item) {

            if (empty($array2[$table])) {
                $dif_list[$table] = [
                    "status" => false,
                    "code" => 1,
                    "msg" => "Table Not Exist"
                ];
                continue;
            }


            $col_dif = [];
            foreach ($array_item as $column_name => $column_array) {

                if (empty($array2[$table][$column_name])) {
                    $col_dif[$column_name] = "Column not found!";
                    continue;
                }

                $ar_dif = array_diff($array2[$table][$column_name], $column_array);

                if ($ar_dif) {
                    $col_dif[$column_name] = implode(", ", $ar_dif);
                }
            }
            if (!empty($col_dif)) {
                $dif_list[$table] = [
                    "status" => false,
                    "code" => 6,
                    "msg" => $col_dif
                ];
            } else {
                $dif_list[$table] = [
                    "status" => true,
                    "code" => 0,
                    "msg" => "No Errors"
                ];
            }


        }

        return $dif_list;
    }

    /**
     * @param $dblocal
     * @param $dblive
     * @return array
     */
    static function differenceTablesRowCounts($dblocal, $dblive)
    {

        $row_count1 = array_column(array_map(function ($table) use($dblocal) {
            return ["table" => $table, "count" => $dblocal->table($table)->count()];
        }, $dblocal->getDoctrineSchemaManager()->listTableNames()), 'count', "table");



        $row_count2 = array_column(array_map(function ($table) use($dblive) {
            return ["table" => $table, "count" => $dblive->table($table)->count()];
        }, $dblive->getDoctrineSchemaManager()->listTableNames()), 'count', "table");



        $dif_list = [];


        foreach ($row_count1 as $table => $array_item) {

            if (!isset($row_count2[$table])) {
                $dif_list[$table] = [
                    "status" => false,
                    "code" => 1,
                    "msg" => "Table Not Exist"
                ];
                continue;
            }

            if ($row_count1[$table] !== $row_count2[$table]) {
                $dif_list[$table] = [
                    "status" => false,
                    "code" => 1,
                    "msg" => $row_count1[$table] . ":" . $row_count2[$table]
                ];
            } else {
                $dif_list[$table] = [
                    "status" => true,
                    "code" => 0,
                    "msg" => "No Errors"
                ];
            }


        }


        return $dif_list;

    }


}
