<?php

namespace App\Model\Admin;

use Illuminate\Database\Eloquent\Model;
use DB;
use Carbon\Carbon;

/**
 * App\Model\FollowUP\FollowUP
 *
 * @property int $ID
 * @property int $openweathermap
 * @property string $description
 * @property int $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereOpenweathermap($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Weather\Weather whereUploadId($value)
 * @mixin \Eloquent
 */
class SalesTrack extends Model
{

    protected $table = 'apple_sales_track';
    protected $fillable = ['ID'];


    public function user() {
        return $this->hasOne('App\User', 'user_id', 'id');
    }
    public function findQuotation() {
        return $this->hasOne('App\Model\QuotationManage\Quotation', 'sales_tracking_id', 'sales_track_id');
    }

}
