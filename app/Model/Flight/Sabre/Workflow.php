<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SharedContext;
use Session;

class Workflow {
    //put your code here
    private $sharedContext;
    private $startActivity;

    /*public function Workflow(&$startActivity) {
        $this->startActivity = $startActivity;
    }*/

    public function create(&$startActivity) {
        $this->startActivity = $startActivity;
    }

    public function runWorkflow() {
        //var_dump(Session::get('sharedContext'));
        if(Session::get('sharedContext') == null) {
            $this->sharedContext = new SharedContext();
        } else {
            $this->sharedContext = Session::get('sharedContext');
        }

        $next = $this->startActivity;
        while($next) {
            $next = $next->run($this->sharedContext);
        }
        //var_dump($this->sharedContext);
        return $this->sharedContext;

    }
}
