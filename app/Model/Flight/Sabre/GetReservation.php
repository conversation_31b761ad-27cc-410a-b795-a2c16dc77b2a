<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use App\Model\Flight\Sabre\PassengerDetailsActivity;
use DOMDocument;
use App\Model\Flight\apple_pnr;

class GetReservation implements Activity {

    private $config;
    public $output;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("GetReservationRQ");
        $soapClient->setLastInFlow(false);

        if(isset($this->output['callType'])) {
            $soapClient->setCallType($this->output['callType']);
        }

        $xmlRequest = $this->getRequest();

        $soapClient->doCall($sharedContext, $xmlRequest);
    }

    private function getRequest() {
        if(isset($this->output)) {
            if(isset($this->output["pnrID"])) {
                $pnrID = $this->output["pnrID"];
            } else if(isset($this->output["bookingID"])) {
                $pnr_ref = apple_pnr::where('apple_ref','=',$this->output["bookingID"])->select('pnr_ref')->first();
                $pnrID = $pnr_ref->pnr_ref;
            }

            $requestArray = array(
                "GetReservationRQ" => array(
                    "_namespace" =>  "http://webservices.sabre.com/pnrbuilder/v1_19",
                    "_attributes" => array(
                        "Version" => "1.19.0"
                    ),
                    "Locator" => $pnrID,
                    "RequestType" => "Stateful",
                    "ReturnOptions" => array(
                        "SubjectAreas" => array(
                            "SubjectArea_36" => "PRICE_QUOTE",
                            "SubjectArea_0" => "FQTV",
                            "SubjectArea_1" => "ADDRESS",
                            "SubjectArea_2" => "DKNUMBER",
                            "SubjectArea_3" => "VCR",
                            "SubjectArea_4" => "PASSENGERDETAILS",
                            "SubjectArea_5" => "PRERESERVEDSEAT",
                            "SubjectArea_6" => "AFAX",
                            "SubjectArea_7" => "GFAX",
                            "SubjectArea_8" => "REMARKS",
                            "SubjectArea_9" => "MISC_TICKETING",
                            "SubjectArea_10" => "BSGPROCESSING",
                            "SubjectArea_11" => "TARGETMARKETING",
                            "SubjectArea_12" => "FARETYPE",
                            "SubjectArea_13" => "TICKETINGENTRIES",
                            "SubjectArea_14" => "BAS_EXTENSION",
                            "SubjectArea_16" => "CORPORATE_ID",
                            "SubjectArea_17" => "ACCOUNTING_LINE",
                            "SubjectArea_18" => "SUBSCRIPTION_CARD",
                            "SubjectArea_19" => "GROUP_PLANNER",
                            "SubjectArea_20" => "TRAVEL_POLICY",
                            "SubjectArea_21" => "CUST_INSIGHT_PROFILE",
                            "SubjectArea_22" => "TICKETING",
                            "SubjectArea_23" => "TKT_ON_DEMAND",
                            "SubjectArea_24" => "ITINERARY",
                            "SubjectArea_25" => "DESCRIPTIVE_BILLING_INFO",
                            "SubjectArea_26" => "EXT_FQTV",
                            "SubjectArea_27" => "HEADER, NAME",
                            "SubjectArea_28" => "RECORD_LOCATOR",
                            "SubjectArea_29" => "RECEIVED",
                            "SubjectArea_30" => "PHONE",
                            "SubjectArea_31" => "ANCILLARY",
                            "SubjectArea_34" => "DSS",
                            "SubjectArea_35" => "OPENRESDATA"
                        ),
                        "ViewName" => "Simple",
                        "ResponseFormat" => "STL"
                    )
                )
            );

            return XMLSerializer::generateValidXmlFromArray($requestArray);
        }
    }

}
