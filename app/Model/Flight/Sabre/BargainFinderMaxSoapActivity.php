<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use App\Model\Flight\Sabre\Activity;
use App\Model\Flight\Sabre\SACSSoapClient;
use App\Model\Flight\Sabre\XMLSerializer;
use DateTime;
use Session;

class BargainFinderMaxSoapActivity implements Activity {

    private $config;
    public $output;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }
    
    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("BargainFinderMaxRQ");
        $soapClient->setLastInFlow(false);
        $xmlRequest = $this->getRequest();
        if(isset($this->output)) {
            $type = $this->output['type']??'one-way';
        }
        if(isset($this->output['callType'])) {
            $soapClient->setCallType($this->output['callType']);
        }

        $soapClient->doCall($sharedContext, $xmlRequest, $type);

        Session::put('sharedContext', $sharedContext);
        Session::save();
    }

    private function getRequest() {
        $result = $this->output;

        if(isset($result)) {
            $request = $this->getArrayRequest($result, $result['type']??'one-way');

            return XMLSerializer::generateValidXmlFromArray($request);
        }
    }

    private function getArrayRequest($result, $type="return") {

        $class = isset($result['Class']) ? $result['Class'] : "Y";

        $seatCount = 0;
        if(isset($result['pax']['adult'])) {
            $adult = $result['pax']['adult'];
            $seatCount += (int)$adult;
        } else {
            $adult = 0;
        }

        if(isset($result['pax']['child'])) {
            $child = $result['pax']['child'];
            $seatCount += (int)$child;
        } else {
            $child = 0;
        }

        if(isset($result['pax']['cnb'])) {
            $cnb = $result['pax']['cnb'];
        } else {
            $cnb = 0;
        }

        $request = array("OTA_AirLowFareSearchRQ" => array(
            "_attributes" => array("Version" => $this->config->getSoapProperty("BargainFinderMaxRQVersion")),
            "_namespace" => "http://www.opentravel.org/OTA/2003/05",
            "POS" => array(
                "Source" => array(
                    "_attributes" => array("PseudoCityCode"=>"Z7B8"),
                    "RequestorID" => array(
                        "_attributes" => array("ID"=>"1", "Type"=>"1"),
                        "CompanyName" => array(
                            "_attributes" => array("Code"=>"TN")
                        )
                    )
                )
            ),
            "TravelPreferences" => array(
                "_attributes" => array("ValidInterlineTicket" => "true"),
                "CabinPref" => array("_attributes" => array("Cabin"=>$class, "PreferLevel"=>"Preferred")),
                "TPA_Extensions" => array(
                    "TripType" => array(
                        "_attributes" => array("Value" => "OneWay")
                    ),
                    "XOFares" => array(
                        "_attributes" => array("Value" => "true")
                    )
                )
            ),
            "TravelerInfoSummary" => array(
                "SeatsRequested" => $seatCount,
                "AirTravelerAvail" => array(),
                "PriceRequestInformation" => array(
                    "_attributes" => array("CurrencyCode" => "LKR"),
                    "TPA_Extensions" => array(
                        "PassengerStatus" => array(
                            "_attributes" => array("Type" => "R"),
                            "CountryCode" => "LK",
                        )
                    )
                )
            ),
            "TPA_Extensions" => array(
                "IntelliSellTransaction" => array(
                    "RequestType" => array("_attributes" => array("Name" => "50ITINS"))
                )
            )
        )
        );

        if(isset($adult) && $adult > 0) {
            $request['OTA_AirLowFareSearchRQ']["TravelerInfoSummary"]["AirTravelerAvail"]['PassengerTypeQuantity_1'] = array(
                "_attributes" => array("Code" => "ADT", "Quantity" => $adult),
                "TPA_Extensions" => array(
                    "VoluntaryChanges" => array(
                        "_attributes" => array("Match" => "Info")
                    )
                )
            );
        }

        if(isset($child) && $child > 0) {
            $request['OTA_AirLowFareSearchRQ']["TravelerInfoSummary"]["AirTravelerAvail"]['PassengerTypeQuantity_2'] = array(
                "_attributes" => array("Code" => "CNN", "Quantity" => $child),
                "TPA_Extensions" => array(
                    "VoluntaryChanges" => array(
                        "_attributes" => array("Match" => "Info")
                    )
                )
            );
        }

        if(isset($cnb) && $cnb > 0) {
            $request['OTA_AirLowFareSearchRQ']["TravelerInfoSummary"]["AirTravelerAvail"]['PassengerTypeQuantity_3'] = array(
                "_attributes" => array("Code" => "INF", "Quantity" => $cnb),
                "TPA_Extensions" => array(
                    "VoluntaryChanges" => array(
                        "_attributes" => array("Match" => "Info")
                    )
                )
            );
        }

        $tempDestArr = array();
        if($type=="return") {
            foreach($result['flight_departure_date'] as $key => $value) {
                $depDate = str_pad($result['flight_departure_date'][$key]['day'], 2, "0", STR_PAD_LEFT);
                $depMonth = str_pad($result['flight_departure_date'][$key]['month'], 2, "0", STR_PAD_LEFT);
                $depYear = $result['flight_departure_date'][$key]['year'];

                $dateDepString = $depYear . '-' . $depMonth . '-' .  $depDate;
                $departureDate = new \DateTime($dateDepString, new \DateTimeZone('UTC'));

                if($key == 0) {
                    $from = $result['destinations'][0];
                    $to = $result['destinations'][1];
                } else {
                    $from = $result['destinations'][1];
                    $to = $result['destinations'][0];
                }

                $tempDestArr["OriginDestinationInformation_".$key] = array(
                    "_attributes" => array("RPH"=>1),
                    "DepartureDateTime" => $dateDepString . "T11:00:00",
                    "OriginLocation" => array("_attributes" => array("LocationCode"=>$from)),
                    "DestinationLocation" => array("_attributes" => array("LocationCode"=>$to)),
                    "TPA_Extensions" => array(
                        "SegmentType" => array("_attributes" => array("Code" => "O"))
                    )
                );
            }
            $request['OTA_AirLowFareSearchRQ']["TravelPreferences"]['TPA_Extensions']["TripType"]["_attributes"]["Value"] = "Return";
        } else {
            foreach($result['flight_departure_date'] as $key => $value) {
                if($type=="multi-city" && $key == 0) {
                    continue;
                }
                $depDate = str_pad($result['flight_departure_date'][$key]['day'], 2, "0", STR_PAD_LEFT);
                $depMonth = str_pad($result['flight_departure_date'][$key]['month'], 2, "0", STR_PAD_LEFT);
                $depYear = $result['flight_departure_date'][$key]['year'];

                $dateDepString = $depYear . '-' . $depMonth . '-' .  $depDate;
                $departureDate = new \DateTime($dateDepString, new \DateTimeZone('UTC'));

                $tempDestArr["OriginDestinationInformation_".$key] = array(
                    "_attributes" => array("RPH"=>1),
                    "DepartureDateTime" => $dateDepString . "T11:00:00",
                    "OriginLocation" => array("_attributes" => array("LocationCode"=>$result['destinations'][$key])),
                    "DestinationLocation" => array("_attributes" => array("LocationCode"=>$result['destinations'][$key+1])),
                    "TPA_Extensions" => array(
                        "SegmentType" => array("_attributes" => array("Code" => "O"))
                    )
                );
            }

            if($type=="multi-city") {
                $request['OTA_AirLowFareSearchRQ']["TravelPreferences"]['TPA_Extensions']["TripType"]["_attributes"]["Value"] = "Circle";
            }
        }

        $request['OTA_AirLowFareSearchRQ'] = array_merge( array_slice( $request['OTA_AirLowFareSearchRQ'], 0, 3 ), $tempDestArr, array_slice( $request['OTA_AirLowFareSearchRQ'], 3 ) );

        return $request;
    }
}
