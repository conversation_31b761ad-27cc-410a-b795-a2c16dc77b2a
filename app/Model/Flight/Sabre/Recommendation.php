<?php

namespace App\Model\Flight\Sabre;

use App\Model\Place\Place;
use appletechlabs\flight\Client;
use appletechlabs\flight\Search\CalendarSearch;
use appletechlabs\flight\Search\FareMasterPricerTbSearch;
use Illuminate\Database\Eloquent\Model;
use Session;


/**
 * App\Model\Flight\Recommendation
 *
 * @mixin \Eloquent
 */
class Recommendation extends Model
{

    private $client;
    public $item;

    /**
     * @return Client
     */
    public function setup()
    {
        $this->client = new Client([
            'amadeus' => [
                'officeId' => env('AMADEUS_OFFICEID', false), //The Amadeus Office Id you want to sign in to - must be open on your WSAP.
                'userId' => env('AMADEUS_USERID', false),//Also known as 'Originator' for Soap Header 1 & 2 WSDL's
                'passwordData' => env('AMADEUS_PASSWORD_DETAIL', false),// **base 64 encoded** password
                'wsdl' => storage_path('wsdl2/1ASIWTPLSHP_PDT_20170804_120038.wsdl'),//Points to the location of the WSDL file for your WSAP. Make sure the associated XSD's are also available.
                'passwordLength' => env('AMADEUS_PASSWORD_LENGTH', false),
                'receivedFrom' => 'my test project',// The "Received From" string that will be visible in PNR History
            ]
        ]);

        $this->client->signIn();

        return $this->client;
    }

    public function signOut()
    {
        $this->client->signOut();
    }

    /**
     * @param $from
     * @param $to
     * @param $departureDate
     * @return mixed
     */
    public function fareSearchMain($from, $to, $departureDate)
    {
        $calendarSearchOpt = new CalendarSearch([
            'nrOfRequestedResults' => 2,
            'nrOfRequestedPassengers' => 1,
            'passengers' => [
                'ADT' => 1,
            ],
            'itineraries' => [
                array(
                    'departureLocation' => $from,
                    'arrivalLocation' => $to,
                    'date' => $departureDate,
                    'rangeMode' => 'RANGEMODE_MINUS_PLUS',
                    'range' => 3,
                ),
            ],
            'currencyOverride' => 'USD',
        ]);

        $Opt = new FareMasterPricerTbSearch([
            'nrOfRequestedResults' => 20,
            'nrOfRequestedPassengers' => 1,
            'passengers' => [
                'ADT' => 1,
            ],
            'itineraries' => [
                array(
                    'departureLocation' => $from,
                    'arrivalLocation' => $to,
                    'date' => $departureDate
                ),
            ],
            'currencyOverride' => 'USD',
        ]);
        ini_set('default_socket_timeout', 6000);

        //$result = $this->client->FareMasterPricerTravelboardSearch($Opt);
        Session::put('quotation.flight.Options.fare', $Opt);
        Session::put('quotation.flight.Options.calendar', $calendarSearchOpt);


        $result = $this->client->fareBoardAndCalendarSearchOptimzed($Opt,$calendarSearchOpt);


        return $result;

    }

    /**
     * @param $from
     * @param $to
     * @param $departureDate
     * @return mixed
     */
    public function fareSearch($from, $to, $departureDate)
    {
        $calendarSearchOpt = new CalendarSearch([
            'nrOfRequestedResults' => 1,
            'nrOfRequestedPassengers' => 1,
            'passengers' => [
                'ADT' => 1
            ],
            'itineraries' => [
                array(
                    'departureLocation' => $from,
                    'arrivalLocation' => $to,
                    'date' => $departureDate,
                    'rangeMode' => 'RANGEMODE_MINUS_PLUS',
                    'range' => 3,
                ),
            ],
            'currencyOverride' => 'LKR',
        ]);

        $fmptOpt = new FareMasterPricerTbSearch([
            'nrOfRequestedResults' => 20,
            'nrOfRequestedPassengers' => 1,
            'passengers' => [
                'ADT' => 1,
            ],
            'itineraries' => [
                array(
                    'departureLocation' => $from,
                    'arrivalLocation' => $to,
                    'date' => $departureDate
                ),
            ],
            'currencyOverride' => 'LKR',
        ]);
        ini_set('default_socket_timeout', 6000);

        $result = $this->client->fareBoardAndCalendarSearchOptimzed($fmptOpt,$calendarSearchOpt);
        //$result = $this->client->fareBoardSearchOptimzed($result);
        return $result;
    }

    /**
     * @param $from
     * @param $to
     * @param $departureDate
     * @return mixed
     */
    public function calenderSearch($from, $to, $departureDate)
    {
        $calendarSearchOpt = new CalendarSearch([
            'nrOfRequestedResults' => 2,
            'nrOfRequestedPassengers' => 1,
            'passengers' => [
                'ADT' => 1,
            ],
            'itineraries' => [
                array(
                    'departureLocation' => $from,
                    'arrivalLocation' => $to,
                    'date' => $departureDate,
                    'rangeMode' => 'RANGEMODE_MINUS_PLUS',
                    'range' => 3,
                ),
            ],
            'currencyOverride' => 'USD',
        ]);
        $deplist =$this->client->FareMasterPricerCalendarSort($calendarSearchOpt);

        return $deplist;
    }

    /**
     * @return Client
     */
    public function getOptions()
    {
        $this->client = new Client([
            'amadeus' => [
                'officeId' => env('AMADEUS_OFFICEID', false), //The Amadeus Office Id you want to sign in to - must be open on your WSAP.
                'userId' => env('AMADEUS_USERID', false),//Also known as 'Originator' for Soap Header 1 & 2 WSDL's
                'passwordData' => env('AMADEUS_PASSWORD_DETAIL', false),// **base 64 encoded** password
                'wsdl' => env('AMADEUS_WSDL', false),//Points to the location of the WSDL file for your WSAP. Make sure the associated XSD's are also available.
                'passwordLength' => env('AMADEUS_PASSWORD_LENGTH', false),
                'receivedFrom' => 'my test project',// The "Received From" string that will be visible in PNR History
            ]
        ]);

        return $this->client;
    }

    /**
     * @param $IATACode
     * @return null
     */
    public function getCountryCodeByIATA($IATACode)
    {
        $place = Place::where('A2', $IATACode)->get()->first();
        if (!empty($place)) {
            $country = Place::where('ID',$place->country)->get()->first();
            return $country->A2;
        }
        else {
            return null;
        }

        //dd($country['A2']);

    }

    /**
     * @param $CountryCode
     * @return mixed
     */
    public function getTimeZoneByCountry($CountryCode)
    {
        $timezone_identifiers = \DateTimeZone::listIdentifiers(\DateTimeZone::PER_COUNTRY, $CountryCode);
        return $timezone_identifiers[0];
    }

    /**
     * @param $flightInformation
     * @param $action
     * @return \DateTime
     */
    public function getDateTime($flightInformation, $action)
    {
        switch ($action) {
            case 'departure':
                $IATACode = $flightInformation->location[0]->locationId;
                $tz = $this->getTimeZoneByCountry($this->getCountryCodeByIATA($IATACode));
                $date = new \DateTime("now", new \DateTimeZone($tz));
                break;

            default:
                # code...
                break;
        }
        return $date;
    }


    /**
     * @param $flightDetails
     * @return \stdClass
     */
    public function getflightInformation($flightDetails)
    {
        $details = new \stdClass();
        if (isset($flightDetails)) {
            if (is_array($flightDetails)) {
                $details->departure = $this->getDateTime($flightDetails[0]->flightInformation,'departure');
            }
        }

        return $details;
    }

    /**
     * @param $airportIATACode
     * @return mixed|null|string
     */
    public function getAirportName($airportIATACode)
    {
        $a = Place::where('A2', $airportIATACode)->first();
        return $a->name ?? null;

    }

    /**
     * @param $flightDetails
     * @return array
     */
    public function getAirportsDetails($flightDetails){
        $airportName = [];
        if (isset($flightDetails)) {
            if (is_array($flightDetails)) {
                foreach ($flightDetails as $flight)
                {
                    foreach ($flight->flightInformation->location as $location)
                    {
                        $airportName[$location->locationId] = $this->getAirportName($location->locationId);
                    }
                }
            }
            else
            {
                foreach ($flightDetails->flightInformation->location as $location)
                {
                    $airportName[$location->locationId] = $this->getAirportName($location->locationId);
                }

            }
        }

        return $airportName;
    }

    /**
     * @param $recommendations
     * @return array
     */
    public function fillInfo($recommendations)
    {
        $recommendationList = [];
        foreach ($recommendations as $recommendationKey => $recommendationItem)
        {
            foreach ($recommendationItem->flightDetails as $flightDetailsItem)
            {
                $flightDetailsItem->departure['airport'] =  new Airport($flightDetailsItem->departure['airport']);
                $flightDetailsItem->arrival['airport'] =  new Airport($flightDetailsItem->arrival['airport']);
            }
            foreach ($recommendationItem->airports as $airportKey =>  $airportItem)
            {
                $recommendationItem->airports[$airportKey] = new Airport($airportItem);
            }
            $recommendationItem->MajAirlineImage = new \stdClass();
            $recommendationItem->MajAirlineImage = Airline::GetAirlineImage($recommendationItem->majAirline);

            $recommendationList[$recommendationItem->ref] = $recommendationItem;
        }
        return $recommendationList;

    }

    /**
     * @param $rawResults
     * @return mixed
     */
    public function getRecommendations($rawResults)
    {
        foreach ($rawResults as $resultKey => $result) {

            $newResultItem = new \stdClass();

            $newResultItem->ref = $result->ref;



           if (is_array($result->flightDetails))
           {

               foreach ($result->flightDetails as $flightKey => $flight)
               {
                   $newFlightInfo = new \stdClass();

                   $newFlightInfo->departure['airport'] = new Airport($flight->flightInformation->location[0]->locationId);
                   $newFlightInfo->departure['terminal'] = $flight->flightInformation->location[0]->terminal ?? null;



                   $depdate = $flight->flightInformation->productDateTime->dateOfDeparture;
                   $deptime = $flight->flightInformation->productDateTime->timeOfDeparture;
                   $dateOfDeparture  = date_create_from_format('dmyHi',$depdate.$deptime);

                   $newFlightInfo->departure['date'] = $dateOfDeparture;

                   $newFlightInfo->arrival['airport'] = new Airport($flight->flightInformation->location[1]->locationId);
                   $newFlightInfo->arrival['terminal'] = $flight->flightInformation->location[1]->terminal ?? null;

                   $arrdate = $flight->flightInformation->productDateTime->dateOfArrival;
                   $arrtime = $flight->flightInformation->productDateTime->timeOfArrival;
                   $dateOfArrival  = date_create_from_format('dmyHi',$arrdate.$arrtime);

                   $newFlightInfo->arrival['date'] = $dateOfArrival;

                   $newFlightInfo->marketingCarrier =  Airline::where('code', $flight->flightInformation->companyId->marketingCarrier)->first();


                   if ($flightKey != 0)
                   {
                       $beforeArrdate = $result->flightDetails[$flightKey-1]->flightInformation->productDateTime->dateOfArrival;
                       $beforeArrtime = $result->flightDetails[$flightKey-1]->flightInformation->productDateTime->timeOfArrival;
                       $beforeDateOfArrival  = date_create_from_format('dmyHi',$beforeArrdate.$beforeArrtime);

                       $beforeDate = $beforeDateOfArrival;
                       $afterDate = $dateOfDeparture;
                       $newFlightInfo->interval = $beforeDate->diff($afterDate);
                   }




                   if( $flight->flightInformation->attributeDetails->attributeType == 'EFT')
                   {
                       $newFlightInfo->flyingTime = $flight->flightInformation->attributeDetails->attributeDescription;

                   }


                   $newResultItem->flightInformation[$flightKey] = $newFlightInfo;
               }
           }
           else
           {


               $newFlightInfo = new \stdClass();
               $newFlightInfo->departure['airport'] = new Airport($result->flightDetails->flightInformation->location[0]->locationId);



               $newFlightInfo->departure['terminal'] =$result->flightDetails->flightInformation->location[0]->terminal ?? null;
               $newFlightInfo->departure['time'] =$result->flightDetails->flightInformation->productDateTime->timeOfDeparture;
               $newFlightInfo->departure['date'] =$result->flightDetails->flightInformation->productDateTime->dateOfDeparture;
               $newFlightInfo->arrival['airport'] = new Airport($result->flightDetails->flightInformation->location[1]->locationId);
               $newFlightInfo->arrival['terminal'] = $result->flightDetails->flightInformation->location[1]->terminal ?? null;
               $newFlightInfo->arrival['time'] = $result->flightDetails->flightInformation->productDateTime->timeOfArrival;
               $newFlightInfo->arrival['date'] = $result->flightDetails->flightInformation->productDateTime->dateOfArrival;



               $newFlightInfo->marketingCarrier =  Airline::where('code', $result->flightDetails->flightInformation->companyId->marketingCarrier)->first();

               if( $result->flightDetails->flightInformation->attributeDetails->attributeType == 'EFT')
               {
                   $newFlightInfo->flyingTime = $result->flightDetails->flightInformation->attributeDetails->attributeDescription;

               }
               $newResultItem->flightInformation = $newFlightInfo;
           }

            if (is_array($result->flightPrice->paxFareDetail->codeShareDetails)) {
                foreach ($result->flightPrice->paxFareDetail->codeShareDetails as $codeShareDetail) {
                    if (isset($codeShareDetail->transportStageQualifier) && $codeShareDetail->transportStageQualifier == "V") {
                        $IATACode = $codeShareDetail->company;
                        $newResultItem->MajAirline =   Airline::where('code', $IATACode)->first();
                    }
                }
            }
            else
            {
                $IATACode = $result->flightPrice->paxFareDetail->codeShareDetails->company;
                $newResultItem->MajAirline =  Airline::where('code', $IATACode)->first();
            }

            $stops = [];
            $airports = $result->airports;

            foreach ($airports as $airport) {
                $stop = $airport;
                $stops[] = $stop;
            }
            $newResultItem->stops = $stops;
            $newResultItem->stopInfo = $result->stopInfo;
            $newResultItem->majCabinDesc = $result->majCabinDesc;
            $newResultItem->seatstatus = $result->seatstatus;
            $newResultItem->flightPrice = $result->flightPrice;

            $optimized[$result->ref] = $newResultItem;


//            dd($newResultItem);
//            if( $newResultItem->stops[0] == '')
//            {
//                dd($newResultItem);
//            }

        }
        return $optimized;
    }

    /**
     * @param $Results
     * @return array
     * @throws \Exception
     */
    public function recommendationList($Results)
    {
        $recommendations = [];
        foreach ($Results as $key => $result) {
            $result->stops = [];
            foreach ($result->airports as $airport)
            {

               $stop = new \stdClass();
                $stop->name = $airport->IATA;
                $stop->lng = $airport->coordinates['longitude'];
                $stop->lat = $airport->coordinates['latitude'];
                $result->stops[] =  $stop;
            }
            $e = new \DateTime('00:00');
            $f = clone $e;

            foreach ($result->flightDetails as $flightDetail)
            {

                $flyingTimeArray = str_split($flightDetail->flyingTime, 2);
                $flyingTime = new \DateInterval('PT'.$flyingTimeArray[0].'H'.$flyingTimeArray[1].'M');
                $e->add($flyingTime);

                if (isset($flightDetail->stopOverTime))
                {
                    $e->add($flightDetail->stopOverTime);
                }


                $result->duration = $f->diff($e)->format("%H:%I");

            }

            $result->originDepartureTime = $result->flightDetails[0]->departure['dateTime']->format("H:i");;
            $result->DestinationArrivalTime = end($result->flightDetails)->arrival['dateTime']->format("H:i");;



            $recommendations[] = $result;
        }
        return $recommendations;

//        +ref: "2"
//    +flightDetails: array:2 [▼
//      0 => {#922 ▼
//        +"departure": array:3 [ …3]
//        +"arrival": array:3 [ …3]
//        +"flyingTime": "0230"
//        +"aircraft": "73H"
//        +"marketingCarrier": "9W"
//      }
//      1 => {#931 ▼
//        +"departure": array:3 [ …3]
//        +"arrival": array:3 [ …3]
//        +"flyingTime": "0325"
//        +"aircraft": "73H"
//        +"marketingCarrier": "9W"
//        +"stopOverTime": DateInterval {#929 …17}
//        }
//    ]
//    +majCabin: "Economic Standard"
//        +majAirline: "9W"
//        +stopInfo: "1 Stop"
//        +airports: array:3 [▶]
//        +seatAvailability: "few Seats Available"
//        +origin: array:3 [▼
//      "dateTime" => DateTime @1516915500 {#923 ▼
//            date: 2018-01-25 21:25:00.0 UTC (+00:00)
//      }
//      "airport" => "CMB"
//      "terminal" => ""
//    ]
//    +destination: array:3 [▶]
//        +fareSummary: fareSummary {#933 ▶}
//            +"MajAirlineImage": "//localhost:3000/assets/image/filght/airline_logos/jet-airways.jpg"

    }


}


?>