<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use DOMDocument;

class ContextChangeRQ implements Activity {

    private $config;
    public $output;

    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("ContextChangeLLSRQ");
        $soapClient->setLastInFlow(false);

        if(isset($this->output['callType'])) {
            $soapClient->setCallType($this->output['callType']);
        }

        $xmlRequest = $this->getRequest();

        $soapClient->doCall($sharedContext, $xmlRequest);
    }

    private function getRequest() {
        $requestArray = array(
            "ContextChangeRQ_0" => array(
                "_attributes" => array(
                    "Version" => "2.0.3",
                    "xmlns" => "http://webservices.sabre.com/sabreXML/2011/10",
                    "xmlns:xs" => "http://www.w3.org/2001/XMLSchema",
                    "xmlns:xsi" => "http://www.w3.org/2001/XMLSchema-instance"
                ),
                "ChangeAAA" => array(
                    "_attributes" => array(
                        "PseudoCityCode" => "23XD"
                    )
                )
            ),
            "ContextChangeRQ_1" => array(
                "_attributes" => array(
                    "Version" => "2.0.3",
                    "xmlns" => "http://webservices.sabre.com/sabreXML/2011/10",
                    "xmlns:xs" => "http://www.w3.org/2001/XMLSchema",
                    "xmlns:xsi" => "http://www.w3.org/2001/XMLSchema-instance"
                ),
                "ChangeDuty" => array(
                    "_attributes" => array(
                        "Code" => "9"
                    )
                )
            )
        );

        return XMLSerializer::generateValidXmlFromArray($requestArray);
    }
}
