<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use DOMDocument;

class CreatePassengerNameRecord implements Activity {

    private $config;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("CreatePassengerNameRecordRQ");
        $soapClient->setLastInFlow(false);

        // $bfmResult = $sharedContext->getResult("BargainFinderMaxRS");
        $bfmResult = $this->getStaticXML();
        $xmlRequest = $this->getRequest($bfmResult);
        $sharedContext->addResult("CreatePassengerNameRecordRQ", $xmlRequest);
        $sharedContext->addResult("CreatePassengerNameRecordRS", $soapClient->doCall($sharedContext, $xmlRequest));
        return new PassengerDetailsActivity();
    }


    private function getRequest($bfmResult) {

        return XMLSerializer::generateValidXmlFromArray($requestArray);
    }

    private function getStaticXML() {
        $xml = "<PricedItinerary SequenceNumber=\"2\">
               <AirItinerary DirectionInd=\"Return\">
                  <OriginDestinationOptions>
                     <OriginDestinationOption ElapsedTime=\"790\">
                        <FlightSegment DepartureDateTime=\"2018-11-30T12:35:00\" ArrivalDateTime=\"2018-11-30T17:00:00\" StopQuantity=\"0\" FlightNumber=\"7728\" ResBookDesigCode=\"K\" ElapsedTime=\"205\">
                           <DepartureAirport LocationCode=\"DFW\" TerminalID=\"E\" />
                           <ArrivalAirport LocationCode=\"YUL\" />
                           <OperatingAirline Code=\"AC\" FlightNumber=\"7728\" CompanyShortName=\"/AIR CANADA EXPRESS - SKY REGIONAL\" />
                           <Equipment AirEquipType=\"E75\" />
                           <MarketingAirline Code=\"AC\" />
                           <MarriageGrp>O</MarriageGrp>
                           <DepartureTimeZone GMTOffset=\"-6\" />
                           <ArrivalTimeZone GMTOffset=\"-5\" />
                           <TPA_Extensions>
                              <eTicket Ind=\"true\" />
                              <Mileage Amount=\"1513\" />
                           </TPA_Extensions>
                        </FlightSegment>
                        <FlightSegment DepartureDateTime=\"2018-11-30T19:55:00\" ArrivalDateTime=\"2018-12-01T08:45:00\" StopQuantity=\"0\" FlightNumber=\"870\" ResBookDesigCode=\"O\" ElapsedTime=\"410\">
                           <DepartureAirport LocationCode=\"YUL\" />
                           <ArrivalAirport LocationCode=\"CDG\" TerminalID=\"2A\" />
                           <OperatingAirline Code=\"AC\" FlightNumber=\"870\" />
                           <Equipment AirEquipType=\"77W\" />
                           <MarketingAirline Code=\"AC\" />
                           <MarriageGrp>I</MarriageGrp>
                           <DepartureTimeZone GMTOffset=\"-5\" />
                           <ArrivalTimeZone GMTOffset=\"1\" />
                           <TPA_Extensions>
                              <eTicket Ind=\"true\" />
                              <Mileage Amount=\"3442\" />
                           </TPA_Extensions>
                        </FlightSegment>
                     </OriginDestinationOption>
                  </OriginDestinationOptions>
               </AirItinerary>
               <AirItineraryPricingInfo PricingSource=\"ADVJR1\" PricingSubSource=\"MIP\" FareReturned=\"true\" LastTicketDate=\"2018-09-18\">
                  <ItinTotalFare>
                     <BaseFare Amount=\"2262.00\" CurrencyCode=\"USD\" DecimalPlaces=\"2\" />
                     <FareConstruction Amount=\"2261.63\" CurrencyCode=\"NUC\" DecimalPlaces=\"2\" />
                     <EquivFare Amount=\"6845000\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" />
                     <Taxes>
                        <Tax TaxCode=\"TOTALTAX\" Amount=\"72400\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" />
                     </Taxes>
                     <TotalFare Amount=\"6917400\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" />
                  </ItinTotalFare>
                  <PTC_FareBreakdowns>
                     <PTC_FareBreakdown>
                        <PassengerTypeQuantity Code=\"ADT\" Quantity=\"1\" />
                        <FareBasisCodes>
                           <FareBasisCode BookingCode=\"K\" DepartureAirportCode=\"DFW\" ArrivalAirportCode=\"YUL\" FareComponentBeginAirport=\"DFW\" FareComponentEndAirport=\"YUL\" FareComponentDirectionality=\"FROM\" FareComponentVendorCode=\"ATP\" GovCarrier=\"AC\">K21S0TG</FareBasisCode>
                           <FareBasisCode BookingCode=\"O\" AvailabilityBreak=\"true\" DepartureAirportCode=\"YUL\" ArrivalAirportCode=\"CDG\" FareComponentBeginAirport=\"YUL\" FareComponentEndAirport=\"CDG\" FareComponentDirectionality=\"FROM\" FareComponentVendorCode=\"ATP\" GovCarrier=\"AC\">OFFEO</FareBasisCode>
                        </FareBasisCodes>
                        <PassengerFare>
                           <BaseFare Amount=\"2262.00\" CurrencyCode=\"USD\" />
                           <FareConstruction Amount=\"2261.63\" CurrencyCode=\"NUC\" DecimalPlaces=\"2\" />
                           <EquivFare Amount=\"6845000\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" />
                           <Taxes>
                              <Tax TaxCode=\"AY\" Amount=\"17000\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" CountryCode=\"US\" />
                              <Tax TaxCode=\"US2\" Amount=\"55400\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" CountryCode=\"US\" />
                              <TaxSummary TaxCode=\"US2\" Amount=\"55400\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" CountryCode=\"US\" />
                              <TaxSummary TaxCode=\"AY\" Amount=\"17000\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" CountryCode=\"US\" />
                              <TotalTax Amount=\"72400\" CurrencyCode=\"COP\" DecimalPlaces=\"0\" />
                           </Taxes>
                           <TotalFare Amount=\"6917400\" CurrencyCode=\"COP\" />
                           <TPA_Extensions>
                              <Surcharges Ind=\"Q\" Type=\"MISCELLANEOUS/OTHER\">285.00</Surcharges>
                              <Surcharges Ind=\"Q\" Type=\"MISCELLANEOUS/OTHER\">11.52</Surcharges>
                              <Messages>
                                 <Message AirlineCode=\"AC\" Type=\"N\" FailCode=\"0\" Info=\"AC ONLY/NONREF/CHGFEE\" />
                                 <Message AirlineCode=\"AC\" Type=\"N\" FailCode=\"0\" Info=\"-REFUNDABLE-\" />
                                 <Message Type=\"W\" FailCode=\"0\" Info=\"VALIDATING CARRIER SPECIFIED - AA\" />
                              </Messages>
                           </TPA_Extensions>
                        </PassengerFare>
                        <Endorsements NonRefundableIndicator=\"true\" />
                        <TPA_Extensions>
                           <FareCalcLine Info=\"DFW AC YMQ211.00AC PAR Q285.00Q11.52 1754.11NUC2261.63END ROE1.00\" />
                        </TPA_Extensions>
                        <FareInfos>
                           <FareInfo>
                              <FareReference>K</FareReference>
                              <TPA_Extensions>
                                 <SeatsRemaining Number=\"7\" BelowMin=\"false\" />
                                 <Cabin Cabin=\"Y\" />
                                 <Meal Code=\"M\" />
                              </TPA_Extensions>
                           </FareInfo>
                           <FareInfo>
                              <FareReference>O</FareReference>
                              <TPA_Extensions>
                                 <SeatsRemaining Number=\"7\" BelowMin=\"false\" />
                                 <Cabin Cabin=\"S\" />
                                 <Meal Code=\"KM\" />
                              </TPA_Extensions>
                           </FareInfo>
                        </FareInfos>
                     </PTC_FareBreakdown>
                  </PTC_FareBreakdowns>
                  <FareInfos>
                     <FareInfo>
                        <FareReference>K</FareReference>
                        <TPA_Extensions>
                           <SeatsRemaining Number=\"7\" BelowMin=\"false\" />
                           <Cabin Cabin=\"Y\" />
                           <Meal Code=\"M\" />
                        </TPA_Extensions>
                     </FareInfo>
                     <FareInfo>
                        <FareReference>O</FareReference>
                        <TPA_Extensions>
                           <SeatsRemaining Number=\"7\" BelowMin=\"false\" />
                           <Cabin Cabin=\"S\" />
                           <Meal Code=\"KM\" />
                        </TPA_Extensions>
                     </FareInfo>
                  </FareInfos>
                  <TPA_Extensions>
                     <DivideInParty Indicator=\"false\" />
                  </TPA_Extensions>
               </AirItineraryPricingInfo>
               <TicketingInfo TicketType=\"eTicket\" ValidInterline=\"Yes\" />
               <TPA_Extensions>
                  <ValidatingCarrier Code=\"AA\" />
                  <DiversitySwapper WeighedPriceAmount=\"1.02316e+07\" />
               </TPA_Extensions>
            </PricedItinerary>";

        return $xml;
    }
}
