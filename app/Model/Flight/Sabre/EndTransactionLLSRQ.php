<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use DOMDocument;

class EndTransactionLLSRQ implements Activity {

    private $config;
    public $output;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("EndTransactionLLSRQ");
        $soapClient->setLastInFlow(false);

        if(isset($this->output['callType'])) {
            $soapClient->setCallType($this->output['callType']);
        }

        $xmlRequest = $this->getRequest();

        $soapClient->doCall($sharedContext, $xmlRequest);
    }

    private function getRequest() {

        $requestArray = array(
            "EndTransactionRQ" => array(
                "_namespace" => "http://webservices.sabre.com/sabreXML/2011/10",
                "_attributes" => array(
                    "Version" => "2.0.9",
                    "xmlns:xs" => "http://www.w3.org/2001/XMLSchema",
                    "xmlns:xsi" => "http://www.w3.org/2001/XMLSchema-instance"

                ),
                "EndTransaction" => array(
                    "_attributes" => array(
                        "Ind" => "true"
                    )
                ),
                "Source" => array(
                    "_attributes" => array(
                        "ReceivedFrom" => "SWS TEST"
                    )
                )
            )
        );

        return XMLSerializer::generateValidXmlFromArray($requestArray);
    }
}
