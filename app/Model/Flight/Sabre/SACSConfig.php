<?php

namespace App\Model\Flight\Sabre;

use Illuminate\Support\Facades\Storage;

class SACSConfig {
    
    private $restConfig;
    private $soapConfig;
    private static $instance = null;
    
    private function __construct() {
        $this->soapConfig = parse_ini_file("configuration/SACSSoapConfig.ini");
    }
    
    public static function getInstance() {
        if (SACSConfig::$instance === null) {
            SACSConfig::$instance = new SACSConfig();
        }
        return SACSConfig::$instance;
    }
    
    public function getRestProperty($propertyName) {
        return $this->restConfig[$propertyName];
    }
    
    public function getSoapProperty($propertyName) {
        return $this->soapConfig[$propertyName];
    }
}
