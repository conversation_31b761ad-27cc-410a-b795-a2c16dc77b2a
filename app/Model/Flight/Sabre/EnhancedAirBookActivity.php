<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use App\Model\Flight\Sabre\PassengerDetailsActivity;
use DOMDocument;

class EnhancedAirBookActivity implements Activity {

    private $config;
    public $output;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("EnhancedAirBookRQ");
        $soapClient->setLastInFlow(false);

        if(isset($this->output["callType"])) {
            $soapClient->setCallType($this->output["callType"]);
        }

        $xmlRequest = $this->getRequest();
        $soapClient->doCall($sharedContext, $xmlRequest);
    }

    private function getRequest() {

        $requestArray = array(
            "EnhancedAirBookRQ" => array(
                "_attributes" => array(
                    "HaltOnError" => "false",
                    "version" => $this->config->getSoapProperty("EnhancedAirBookRQVersion")
                ),
                "_namespace" => "http://services.sabre.com/sp/eab/v3_2",
                "OTA_AirBookRQ" => array(
                    /*"RetryRebook" => array(
                        "_attributes" => array("Option" => "true")
                    ),*/
                    "HaltOnStatus_0" => array(
                        "_attributes" => array("Code" => "UC")
                    ),
                    "HaltOnStatus_1" => array(
                        "_attributes" => array("Code" => "NN")
                    ),
                    "HaltOnStatus_2" => array(
                        "_attributes" => array("Code" => "LL")
                    ),
                    "HaltOnStatus_3" => array(
                        "_attributes" => array("Code" => "PN")
                    ),
                    "HaltOnStatus_4" => array(
                        "_attributes" => array("Code" => "NO")
                    ),
                    "HaltOnStatus_5" => array(
                        "_attributes" => array("Code" => "US")
                    ),
                    "HaltOnStatus_6" => array(
                        "_attributes" => array("Code" => "UU")
                    ),
                    "OriginDestinationInformation" => array()
                ),
                "OTA_AirPriceRQ" => array(
                    "PriceRequestInformation" => array(
                        "_attributes" => array("Retain" => "true"),
                        "OptionalQualifiers" => array(
                            "PricingQualifiers" => array()
                        )
                    )
                ),
                "PostProcessing" => array(
                    "RedisplayReservation" => array("_attributes" => array("WaitInterval" => "1000"))
                ),
                "PreProcessing" => array(
                    "UniqueID" => array("_attributes" => array("ID" => ""))
                )
            )
        );

        $tempSegArr = array();
        $i=0;

        foreach ($this->output["ways"] as $keyWays => $valueWays) {
            if(isset($valueWays["segment"])) {
                foreach ($valueWays["segment"] as $keySegment => $valueSegment) {
                    $tempSegArr["FlightSegment_" . $i] = array(
                        "_attributes" => array(
                            "DepartureDateTime" => $valueSegment["TecDepartureDateTime"],
                            "ArrivalDateTime" => $valueSegment["TecArrivalDateTime"],
                            "FlightNumber" => $valueSegment["FlightNumber"],
                            "NumberInParty" => $this->output["SeatCount"],
                            "ResBookDesigCode" => $valueSegment["ResBookDesigCode"],
                            "Status" => "NN"
                        ),
                        "DestinationLocation" => array("_attributes" => array("LocationCode" => $valueSegment["ArrivalLocationCode"])),
                        "Equipment" => array("_attributes" => array("AirEquipType" => $valueSegment["AirEquipType"])),
                        "MarketingAirline" => array("_attributes" => array("Code" => $valueSegment["MarketingAirline"], "FlightNumber" => $valueSegment["FlightNumber"])),
                        "OperatingAirline" => array("_attributes" => array("Code" => $valueSegment["AirlineCode"])),
                        "OriginLocation" => array("_attributes" => array("LocationCode" => $valueSegment["DepartureLocationCode"]))
                    );

                    $i++;
                }
            }
        }
        $requestArray['EnhancedAirBookRQ']['OTA_AirBookRQ']['OriginDestinationInformation']= $tempSegArr;

        $tempPaxArr = array();
        $j=0;
        $tempPaxArr["BargainFinder"] = array("_attributes" => array("Rebook" => "true"));
        foreach ($this->output["PTC_FareBreakdown"] as $keyPTC_FareBreakdown => $valuePTC_FareBreakdown) {
            $tempPaxArr["PassengerType_" . $j ] = array("_attributes" => array("Code" => $valuePTC_FareBreakdown["TypeID"], "Quantity" => $valuePTC_FareBreakdown["Quantity"]));
            $j++;

        }
        $requestArray['EnhancedAirBookRQ']['OTA_AirPriceRQ']['PriceRequestInformation']['OptionalQualifiers']['PricingQualifiers'] = $tempPaxArr;

        return XMLSerializer::generateValidXmlFromArray($requestArray);
    }

}
