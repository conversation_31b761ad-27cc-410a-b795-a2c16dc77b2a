<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use DOMDocument;
use Session;

class AirTicketRQ implements Activity {

    private $config;
    public $output;

    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("AirTicketRQ");
        $soapClient->setLastInFlow(false);

        if(isset($this->output['callType'])) {
            $soapClient->setCallType($this->output['callType']);
        }

        $xmlRequest = $this->getRequest();

        $soapClient->doCall($sharedContext, $xmlRequest);
    }

    private function getRequest() {
        $requestArray = array(
            "AirTicketRQ" => array(
                "_namespace" => "http://services.sabre.com/sp/air/ticket/v1",
                "_attributes" => array(
                    "version" => "1.2.0",
                    "targetCity" => "Z7B8"
                ),
                "DesignatePrinter" => array(
                    "Printers" => array(
                        "Hardcopy" => array(
                            "_attributes" => array(
                                "LNIATA" => "3D19f6"
                            )
                        ),
                        "InvoiceItinerary" => array(
                            "_attributes" => array(
                                "LNIATA" => "3D19f6"
                            )
                        ),
                        "Ticket" => array(
                            "_attributes" => array(
                                "CountryCode" => "LK"
                            )
                        ),
                    )
                ),
                "Itinerary" =>  array(
                    "_attributes" => array(
                        "ID" => Session::get('pnr_ref_id')
                    )
                ),
                "Ticketing" => array(
                    "FlightQualifiers" => array(
                        "VendorPrefs" => array(
                            "Airline" => array(
                                "_attributes" => array(
                                    "Code" => Session::get('ticketing_air_code')
                                )
                            )
                        )
                    ),
                    "FOP_Qualifiers" => array(
                        "BasicFOP" => array(
                            "CC_Info" => array(
                                "_attributes" => array(
                                    "Suppress" => "true"
                                ),
                                "PaymentCard" => array(
                                    "_attributes" => array(
                                        "Code" => "VI",
                                        "ExpireDate" => "2022-11",
                                        "Number" => "573912345621003"
                                    )
                                )
                            )
                        )
                    ),
                    "MiscQualifiers" => array(
                        "Commission" => array(
                            "_attributes" => array(
                                "Percent" => "5"
                            )
                        )
                    )
                ),
                "PostProcessing" => array(
                    "_attributes" => array(
                        "acceptPriceChanges" => "false",
                        "actionOnPQExpired" => "O"
                    ),
                    "EndTransaction" => array(
                        "Source" => array(
                            "_attributes" => array(
                                "ReceivedFrom" => "SPTEST"
                            )
                        )
                    )
                )
            )
        );

        return XMLSerializer::generateValidXmlFromArray($requestArray);
    }
}
