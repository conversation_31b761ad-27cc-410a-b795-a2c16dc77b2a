<OTA_AirLowFareSearchRQ xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.opentravel.org/OTA/2003/05" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" Target="Production" Version="4.2.0" ResponseType="OTA" ResponseVersion="4.2.0">
   <POS>
      <Source PseudoCityCode="PCC">
         <RequestorID ID="1" Type="1">
            <CompanyName Code="TN"/>
         </RequestorID>
      </Source>
   </POS>
   <OriginDestinationInformation RPH="[RPH_1]">
      <DepartureDateTime>[RPH_1_DepartureDateTime]</DepartureDateTime>
      <OriginLocation LocationCode="[RPH_1_OriginLocation_LocationCode]"/>
      <DestinationLocation LocationCode="[RPH_1_DestinationLocation_LocationCode]"/>
      <TPA_Extensions>
         <SegmentType Code="O"/>
      </TPA_Extensions>
   </OriginDestinationInformation>
   <OriginDestinationInformation RPH="[RPH_2]">
      <DepartureDateTime>[RPH_2_DepartureDateTime]</DepartureDateTime>
      <OriginLocation LocationCode="[RPH_2_OriginLocation_LocationCode]"/>
      <DestinationLocation LocationCode="[RPH_2_DestinationLocation_LocationCode]"/>
      <TPA_Extensions>
         <SegmentType Code="O"/>
      </TPA_Extensions>
   </OriginDestinationInformation>
   <TravelPreferences ValidInterlineTicket="[ValidInterlineTicket]">
      <CabinPref PreferLevel="[PreferLevel]" Cabin="[Cabin]"/>
      <Baggage RequestType="C" Description="true" RequestedPieces="4"/>
      <TPA_Extensions>
         <TripType Value="[TripType_Value]"/>
         <LongConnectTime Min="[LongConnectTime_Min]" Max="[LongConnectTime_Max]" Enable="[LongConnectTime_Enable]"/>
         <ExcludeCallDirectCarriers Enabled="[ExcludeCallDirectCarriers_Enabled]"/>
      </TPA_Extensions>
   </TravelPreferences>
   <TravelerInfoSummary>
      <SeatsRequested>1</SeatsRequested>
      <AirTravelerAvail>
         <PassengerTypeQuantity Code="ADT" Quantity="1"/>
      </AirTravelerAvail>
   </TravelerInfoSummary>
   <TPA_Extensions>
      <IntelliSellTransaction>
         <RequestType Name="50ITINS"/>
      </IntelliSellTransaction>
   </TPA_Extensions>
</OTA_AirLowFareSearchRQ>