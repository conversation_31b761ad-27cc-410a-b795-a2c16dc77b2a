<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use App\Model\Flight\Sabre\PassengerDetailsActivity;
use DOMDocument;

class EnhancedAirBookActivityPrice implements Activity {

    private $config;
    public $output;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("EnhancedAirBookRQ");
        $soapClient->setLastInFlow(false);

        if(isset($this->output["callType"])) {
            $soapClient->setCallType($this->output["callType"]);
        }

        $xmlRequest = $this->getRequest();
        $soapClient->doCall($sharedContext, $xmlRequest);
    }

    private function getRequest() {

        $requestArray = array(
            "EnhancedAirBookRQ" => array(
                "_attributes" => array(
                    "HaltOnError" => "false",
                    "version" => $this->config->getSoapProperty("EnhancedAirBookRQVersion")
                ),
                "_namespace" => "http://services.sabre.com/sp/eab/v3_2",
                "OTA_AirBookRQ" => array(
                    /*"RetryRebook" => array(
                        "_attributes" => array("Option" => "true")
                    ),*/
                    "HaltOnStatus_0" => array(
                        "_attributes" => array("Code" => "UC")
                    ),
                    "HaltOnStatus_1" => array(
                        "_attributes" => array("Code" => "NN")
                    ),
                    "HaltOnStatus_2" => array(
                        "_attributes" => array("Code" => "LL")
                    ),
                    "HaltOnStatus_3" => array(
                        "_attributes" => array("Code" => "PN")
                    ),
                    "HaltOnStatus_4" => array(
                        "_attributes" => array("Code" => "NO")
                    ),
                    "HaltOnStatus_5" => array(
                        "_attributes" => array("Code" => "US")
                    ),
                    "HaltOnStatus_6" => array(
                        "_attributes" => array("Code" => "UU")
                    ),
                    "OriginDestinationInformation" => array()
                ),
                "OTA_AirPriceRQ" => array(
                    "PriceRequestInformation" => array(
                        "_attributes" => array("Retain" => "true"),
                        "OptionalQualifiers" => array(
                            "PricingQualifiers" => array()
                        )
                    )
                ),
                "PostProcessing" => array(
                    "RedisplayReservation" => array("_attributes" => array("WaitInterval" => "1000"))
                ),
                "PreProcessing" => array(
                    "UniqueID" => array("_attributes" => array("ID" => ""))
                )
            )
        );

        $tempSegArr = array();
        $i=0;
        // var_dump($this->output);exit();
        foreach ($this->output["data"]->data->Segment as $keySegment => $valueSegment) {
            // var_dump($valueSegment);exit();
            $tempSegArr["FlightSegment_" . $i] = array(
                "_attributes" => array(
                    "DepartureDateTime" => $valueSegment->TecDepartureDateTime,
                    "ArrivalDateTime" => $valueSegment->TecArrivalDateTime,
                    "FlightNumber" => $valueSegment->FlightNumber,
                    "NumberInParty" => $this->output["data"]->data->seatcount,
                    "ResBookDesigCode" => $valueSegment->ResBookDesigCode,
                    "Status" => "NN"
                ),
                "DestinationLocation" => array("_attributes" => array("LocationCode" => $valueSegment->ArrivalAirport)),
                "Equipment" => array("_attributes" => array("AirEquipType" => $valueSegment->EquipmentType)),
                "MarketingAirline" => array("_attributes" => array("Code" => $valueSegment->MarketingAirlineCode, "FlightNumber" => $valueSegment->FlightNumber)),
                "OperatingAirline" => array("_attributes" => array("Code" => $valueSegment->OperatingAirlineCode)),
                "OriginLocation" => array("_attributes" => array("LocationCode" => $valueSegment->DepartureAirport))
            );
            $i++;

        }
        $requestArray['EnhancedAirBookRQ']['OTA_AirBookRQ']['OriginDestinationInformation']= $tempSegArr;

        $tempPaxArr = array();
        $j=0;
        $tempPaxArr["BargainFinder"] = array("_attributes" => array("Rebook" => "true"));
        foreach ($this->output["data"]->data->PassengerData as $keyPassenger => $valuePassenger) {
            $tempPaxArr["PassengerType_" . $j ] = array("_attributes" => array("Code" => $keyPassenger, "Quantity" => count($valuePassenger)));
            $j++;

        }
        $requestArray['EnhancedAirBookRQ']['OTA_AirPriceRQ']['PriceRequestInformation']['OptionalQualifiers']['PricingQualifiers'] = $tempPaxArr;

        return XMLSerializer::generateValidXmlFromArray($requestArray);
    }

}
