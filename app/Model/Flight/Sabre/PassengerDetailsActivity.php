<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use App\Model\Flight\Sabre\TravelItineraryReadActivity;
use DateTime;

class PassengerDetailsActivity implements Activity {
    
    private $config;
    public $output;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("PassengerDetailsRQ");
        $soapClient->setLastInFlow(false);

        if(isset($this->output["callType"])) {
            $soapClient->setCallType($this->output["callType"]);
        }

        $xmlRequest = $this->getRequest();
        $soapClient->doCall($sharedContext, $xmlRequest);
    }

    private function getRequest() {
        $result = $this->output;

        $requestArray = array(
            "PassengerDetailsRQ" => array(
                "_namespace" => "http://services.sabre.com/sp/pd/v3_2",
                "_attributes" => array(
                    "HaltOnError" => "true",
                    "version" => $this->config->getSoapProperty("PassengerDetailsRQVersion")
                ),
                "PostProcessing" => array(
                    "_namespace" => "http://services.sabre.com/sp/pd/v3_2",
                    "EndTransactionRQ" => array(
                        "EndTransaction" => array(
                            "_attributes" => array("Ind" => "true")
                        ),
                        "Source" => array(
                            "_attributes" => array("ReceivedFrom" => "KP")
                        )
                    )
                ),
                "PriceQuoteInfo" => array(),
                "SpecialReqDetails" => array(
                    "_attributes" => array(
                        "xmlns" => "http://services.sabre.com/sp/pd/v3_2"
                    ),
                    "AddRemarkRQ" => array(
                        "RemarkInfo" => array(
                            "FOP_Remark" => array(
                                "_attributes" => array("Type" => "CA"),
                            )
                        )
                    ),
                    "SpecialServiceRQ" => array(
                        "SpecialServiceInfo" => array()
                    )
                ),
                "TravelItineraryAddInfoRQ" => array(
                    "_attributes" => array("xmlns" => "http://services.sabre.com/sp/pd/v3_2"),
                    "AgencyInfo" => array(
                        "Address" => array(
                            "AddressLine" => "Sharmila Travels | Online Booking",
                            "CityName" => "",
                            "CountryCode" => "",
                            "PostalCode" => "",
                            "StreetNmbr" => ""
                        ),
                        "Ticketing" => array(
                            "_attributes" => array("TicketType" => "7TAW")
                        )
                    ),
                    "CustomerInfo" => array()
                )
            )
        );

        $tempLinkArr = array();

        $ADT = false;
        $CNN = false;
        $INF = false;
        $record = 0;
        foreach ($result["traveler_type"] as $keyType => $valueType) {
            if($valueType == "ADT") {
                if(!$ADT) {
                    $record++;
                }
                $tempLinkArr["Link_" . $keyType] = array(
                                            "_attributes" => array("NameNumber" => $result["traveler_name_number"][$keyType], "Record" => $record)
                                        );
                $ADT = true;
            }
            if($valueType == "CNN") {
                if(!$CNN) {
                    $record++;
                }
                $tempLinkArr["Link_" . $keyType] = array(
                    "_attributes" => array("NameNumber" => $result["traveler_name_number"][$keyType], "Record" => $record)
                );
                $CNN = true;
            }
            if($valueType == "INF") {
                if(!$INF) {
                    $record++;
                }
                $tempLinkArr["Link_" . $keyType] = array(
                    "_attributes" => array("NameNumber" => $result["traveler_name_number"][$keyType], "Record" => $record)
                );
                $INF = true;
            }
        };

        $requestArray["PassengerDetailsRQ"]["PriceQuoteInfo"] = $tempLinkArr;

        $tempPaxArr = array();
        $tempSerArr = array();
        $now = new DateTime();

        $tempPaxArr["ContactNumbers"]["ContactNumber_0"] = array(
            "_attributes" => array("NameNumber" => "1.1", "Phone" => "0112352400", "PhoneUseType" => "H")
        );
        $tempPaxArr["ContactNumbers"]["ContactNumber_1"] = array(
            "_attributes" => array("NameNumber" => "1.1", "Phone" => "0777881656", "PhoneUseType" => "M")
        );

        $tempPaxArr["Email"] = array(
            "_attributes" => array("NameNumber" => "1.1", "Address" => "<EMAIL>", "Type" => "CC")
        );

        $infantCount = 0;
        $nameNum = "1.1";
        $tempBirthDays = array();
        $adultAssgn = array();
        foreach ($result["traveler_fname"] as $key => $value) {
            if($result["traveler_type"][$key] == "ADT") {
                $adultAssgn[] = $result["traveler_name_number"][$key];
            }

            $tempPaxArr["PersonName_" . $key ] = array(
                                                    "_attributes" => array(
                                                        "NameNumber" => $result["traveler_name_number"][$key],
                                                        "PassengerType" => $result["traveler_type"][$key],
                                                        "Infant" => array(),
                                                        "NameReference" => array()
                                                    ),
                                                    "GivenName" => $result["traveler_fname"][$key] . " " . $result["traveler_title"][$key],
                                                    "Surname" => $result["traveler_lname"][$key]
                                                );

            if($result["traveler_type"][$key] == "INF") {
                $tempPaxArr["PersonName_" . $key ]["_attributes"]["Infant"] = "true";

                $infantAge = ((($now->diff(new DateTime($result["traveler_dob"][$key]))->y) * 12) + $now->diff(new DateTime($result["traveler_dob"][$key]))->m);
                $infantAge = ($infantAge == 0) ? 1 : $infantAge;
                $tempPaxArr["PersonName_" . $key ]["_attributes"]["NameReference"] = "I" . $infantAge;

                $nameNum = $adultAssgn[$infantCount];
                $infantCount++;

                $date = DateTime::createFromFormat('Y-m-d', $result["traveler_dob"][$key]);
                $tempBirthDays["Service_" . $key ] = array(
                    "_attributes" => array(
                        "SSR_Code" => "INFT",
                    ),
                    "PersonName" => array(
                        "_attributes" => array(
                            "NameNumber" => $nameNum
                        )
                    ),
                    "Text" => $result["traveler_fname"][$key] . "/" . $result["traveler_lname"][$key] . "/" . $date->format('dMy')
                );
            } else {
                unset($tempPaxArr["PersonName_" . $key ]["_attributes"]["Infant"]);
                unset($tempPaxArr["PersonName_" . $key ]["_attributes"]["NameReference"]);

                if($result["traveler_type"][$key] == "CNN") {
                    $tempPaxArr["PersonName_" . $key ]["_attributes"]["NameReference"] = "C" . $now->diff(new DateTime($result["traveler_dob"][$key]))->y;

                    $date = DateTime::createFromFormat('Y-m-d', $result["traveler_dob"][$key]);
                    $tempBirthDays["Service_" . $key ] = array(
                        "_attributes" => array(
                            "SSR_Code" => "CHLD",
                        ),
                        "PersonName" => array(
                            "_attributes" => array(
                                "NameNumber" => $result["traveler_name_number"][$key]
                            )
                        ),
                        "Text" => $date->format('dMy')
                    );
                } else {
                    unset($tempPaxArr["PersonName_" . $key ]["_attributes"]["NameReference"]);
                }
            }

            if(isset($result["traveler_passport_no"][$key]) && $result["traveler_passport_no"][$key] != "") {
                $tempSerArr["AdvancePassenger_" . $key ] = array(
                                                            "_attributes" => array("SegmentNumber" => "A"),
                                                            "Document" => array(
                                                                "_attributes" => array(
                                                                    "ExpirationDate" => date("Y-m-d", strtotime($result["traveler_passport_expire"][$key])),
                                                                    "Number" => $result["traveler_passport_no"][$key],
                                                                    "Type" => "P"
                                                                ),
                                                                "IssueCountry" => $result["traveler_passport_name"][$key],
                                                                "NationalityCountry" => $result["traveler_passport_name"][$key]
                                                            ),
                                                            "PersonName" => array(
                                                                "_attributes" => array("DateOfBirth" => date("Y-m-d", strtotime($result["traveler_dob"][$key])), "DocumentHolder" => "true", "Gender" => $result["traveler_gender"][$key], "NameNumber" => $nameNum),
                                                                "GivenName" => $result["traveler_fname"][$key],
                                                                "Surname" => $result["traveler_lname"][$key]
                                                            )
                                                        );
            }
        }

        $tempSMRArr = array();
        $serviceCount = 0;
        if(isset($result["smr_person_name"])) {
            foreach ($result["smr_person_name"] as $keyText => $valueText) {
                if($keyText == 0) {
                    continue;
                }

                $tempSMRArr["Service_" . $serviceCount ] = array(
                    "_attributes" => array(
                        "SSR_Code" => $result["smr_code"][$keyText],
                    ),
                    "PersonName" => array(
                        "_attributes" => array(
                            "NameNumber" => $result["smr_person_name"][$keyText]
                        )
                    )/*,
                    "Text" => $result["smr_text"][$keyText]*/
                );
                $serviceCount++;
            }
        }

        $tempSSRArr = array();
        if(isset($result["ssr_person_name"])) {
            foreach ($result["ssr_person_name"] as $keyText => $valueText) {
                if($keyText == 0) {
                    continue;
                }

                $tempSSRArr["Service_" . $serviceCount ] = array(
                    "_attributes" => array(
                        "SSR_Code" => $result["ssr_code"][$keyText],
                    ),
                    "PersonName" => array(
                        "_attributes" => array(
                            "NameNumber" => $result["ssr_person_name"][$keyText]
                        )
                    )/*,
                    "Text" => $result["ssr_text"][$keyText]*/
                );
                $serviceCount++;
            }
        }

        $tempSSIArr = array_merge($tempBirthDays, array_merge($tempSerArr, array_merge($tempSSRArr, $tempSMRArr)));

        $requestArray['PassengerDetailsRQ']['TravelItineraryAddInfoRQ']['CustomerInfo'] = $tempPaxArr;
        if(isset($tempSSIArr) && count($tempSSIArr) != 0) {
            $requestArray['PassengerDetailsRQ']['SpecialReqDetails']['SpecialServiceRQ']['SpecialServiceInfo'] = $tempSSIArr;
        } else {
            unset($requestArray['PassengerDetailsRQ']['SpecialReqDetails']);
        }

        return XMLSerializer::generateValidXmlFromArray($requestArray, null, "Remark");
    }
}
