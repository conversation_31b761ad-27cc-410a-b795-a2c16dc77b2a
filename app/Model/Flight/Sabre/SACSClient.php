<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SharedContext;
use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SessionCreateRequest;
use App\Model\Flight\Sabre\SessionCloseRequest;
use App\Model\Flight\Sabre\IgnoreTransactionRequest;
use App\Model\Flight\Sabre\XMLSerializer;
use App\Model\Flight\Sabre\FlightRefferences;
use App\Model\Flight\Sabre\Flight;
use App\Model\Flight\Sabre\SACSClient;
use SoapVar;
use SoapHeader;
use SimpleXMLElement;

class SACSClient {
    public $type;
    public $callType;

    public function setCallType($callType) {
        $this->callType = $callType;
    }

    public function doCall($headersXml, $body, $action) {
        //Data, connection, auth
        $config = SACSConfig::getInstance();
        $soapUrl = $config->getSoapProperty("environment");
        // xml post structure
        // var_dump($body);// exit();
        $xml_post_string = '<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">'
                . '<SOAP-ENV:Header>'
                . $headersXml
                . '</SOAP-ENV:Header>'
                . '<SOAP-ENV:Body>'
                . $body
                . '</SOAP-ENV:Body>'
                . '</SOAP-ENV:Envelope>';

        $headers = array(
            "Content-type: text/xml;charset=\"utf-8\"",
            "Accept: text/xml",
            "Cache-Control: no-cache",
            "Pragma: no-cache",
            "SOAPAction: " . $action,
            "Content-length: " . strlen($xml_post_string)
        );

        error_log($action);
        error_log($xml_post_string);
        error_log("------------------------------------------------");

        // PHP cURL  for https connection with auth
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_URL, $soapUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
//            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xml_post_string); // the SOAP request
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_VERBOSE, false);

        // converting
        $response = curl_exec($ch);

        $xml = new SimpleXMLElement($response);
        // var_dump($response);exit();
        $flightRefferences = new FlightRefferences();
        $flightRefferences->type = $this->type;

        if($this->callType == "BargainFinderMaxRQ") {
            if(!isset($xml->xpath('//SOAP-ENV:Body')[0]->OTA_AirLowFareSearchRS->Errors)) {
                $PricedItineraries = $xml->xpath('//SOAP-ENV:Body')[0]->OTA_AirLowFareSearchRS->PricedItineraries;
                echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_flight($PricedItineraries)));
            } else {
                $Errors = $xml->xpath('//SOAP-ENV:Body')[0]->OTA_AirLowFareSearchRS->Errors;
                echo json_encode(array("status"=>false, "data"=> addslashes((string)$flightRefferences->show_errors($Errors))));
            }
        } else if($this->callType == "EnhancedAirBookRQ") {
            // var_dump($xml_post_string);
            // var_dump($response); exit();

            if(isset($xml->xpath('//soap-env:Body')[0]->EnhancedAirBookRS->OTA_AirPriceRS)) { // ->TravelItinerary->ItineraryInfo /////// SOAP-ENV:Body
                $OTA_AirPriceRS = $xml->xpath('//soap-env:Body')[0]->EnhancedAirBookRS->OTA_AirPriceRS;
                echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_itinerary($OTA_AirPriceRS)));
            } else {
                echo json_encode(array("status"=>false, "data"=> "Selected flight is not booked properly. Please try again.."));
            }
        } else if($this->callType == "PassengerDetailsRQ") {
            // var_dump($xml_post_string);
            // var_dump($response); exit();

            if(isset($xml->xpath('//soap-env:Body')[0]->PassengerDetailsRS->ItineraryRef)) { // ->TravelItinerary->ItineraryInfo /////// SOAP-ENV:Body
                $PassengerDetailsRS = $xml->xpath('//soap-env:Body')[0]->PassengerDetailsRS->ItineraryRef;
                echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_PNR($PassengerDetailsRS)));
            } else {
                echo json_encode(array("status"=>false, "data"=> "PNR reference id is not booked properly. Please try again.."));
            }

        } else if($this->callType == "GetReservationRQ") {
            $xml->registerXPathNamespace('s','http://webservices.sabre.com/pnrbuilder/v1_19');
            $xml->registerXPathNamespace('o','http://services.sabre.com/res/or/v1_14');
            // var_dump($response); exit();
            if(isset($xml->xpath('//s:Reservation')[0])) { // ->TravelItinerary->ItineraryInfo /////// SOAP-ENV:Body

                if(isset($xml->xpath('//s:FlightsRange')[0]['Start'])) {
                    echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_PNRReview($xml)));
                } else {
                    echo json_encode(array("status"=>false, "data"=> "PNR is already deleted..."));
                }
            } else {
                echo json_encode(array("status"=>false, "data"=> addslashes((string)$flightRefferences->show_errors($xml, "GetReservationRQ"))));
            }
        } else if($this->callType == "TravelItineraryReadRQ") {
            if(isset($xml->xpath('//soap-env:Body')[0]->TravelItineraryReadRS->TravelItinerary)) { // ->TravelItinerary->ItineraryInfo /////// SOAP-ENV:Body
                $TravelItinerary = $xml->xpath('//soap-env:Body')[0]->TravelItineraryReadRS->TravelItinerary;
                echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_PNRReviewLoad($TravelItinerary)));
            } else {
                echo json_encode(array("status"=>false, "data"=> "PNR is not loading..."));
            }
        } else if($this->callType == "OTA_CancelRQ") {
            $xml->registerXPathNamespace('s',"http://services.sabre.com/STL/v01");

            if(isset($xml->xpath('//s:ApplicationResults')[0]["status"]) && (string)$xml->xpath('//s:ApplicationResults')[0]["status"] == "Complete") {
                echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_PNRReviewLoad()));
            } else {
                echo json_encode(array("status"=>false, "data"=> "PNR is not loading..."));
            }
        } else if($this->callType == "EndTransactionLLSRQ") {
            if(isset($xml->xpath('//soap-env:Body')[0]->EndTransactionRS->ItineraryRef)) { // ->TravelItinerary->ItineraryInfo /////// SOAP-ENV:Body
                $ItineraryRef = $xml->xpath('//soap-env:Body')[0]->EndTransactionRS->ItineraryRef;
                echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_EndTransaction($ItineraryRef)));
            } else {
                echo json_encode(array("status"=>false, "data"=> "PNR is not deleted..."));
            }
        } else if($this->callType == "AirTicketRQ") {
            var_dump($xml_post_string);
            var_dump($response); exit();

            if(isset($xml->xpath('//soap-env:Body')[0]->EndTransactionRS->ItineraryRef)) { // ->TravelItinerary->ItineraryInfo /////// SOAP-ENV:Body
                $ItineraryRef = $xml->xpath('//soap-env:Body')[0]->EndTransactionRS->ItineraryRef;
                echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_EndTransaction($ItineraryRef)));
            } else {
                echo json_encode(array("status"=>false, "data"=> "PNR is not deleted..."));
            }
        } else if($this->callType == "ContextChangeRQ") {
            var_dump($xml_post_string);
            var_dump($response); exit();

            /*if(isset($xml->xpath('//soap-env:Body')[0]->EndTransactionRS->ItineraryRef)) { // ->TravelItinerary->ItineraryInfo /////// SOAP-ENV:Body
                $ItineraryRef = $xml->xpath('//soap-env:Body')[0]->EndTransactionRS->ItineraryRef;
                echo json_encode(array("status"=>true, "data"=> $flightRefferences->show_EndTransaction($ItineraryRef)));
            } else {
                echo json_encode(array("status"=>false, "data"=> "PNR is not deleted..."));
            }*/
        } else {
            echo array("status"=>false, "data"=> "Something went wrong....");
        }
    }
}

