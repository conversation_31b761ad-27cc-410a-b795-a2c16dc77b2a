<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use App\Model\Flight\Sabre\Activity;
use App\Model\Flight\Sabre\SACSSoapClient;
use App\Model\Flight\Sabre\XMLSerializer;
use DateTime;
use Session;

class TravelItineraryReadActivity implements Activity {

    private $config;
    public $output;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("TravelItineraryReadRQ");

        if(isset($this->output['pnrID'])) {
            $soapClient->setCallType("TravelItineraryReadRQ");
        }

        $xmlRequest = $this->getRequest();
        $soapClient->doCall($sharedContext, $xmlRequest);

        Session::put('sharedContext', $sharedContext);
        Session::save();
    }

    private function getRequest() {
        $requestArray = array(
            "TravelItineraryReadRQ" => array(
                "_namespace" => "http://services.sabre.com/res/tir/v3_6",
                "_attributes" => array(
                    "Version" => $this->config->getSoapProperty("TravelItineraryReadRQVersion")
                ),
                "MessagingDetails" => array(
                    "SubjectAreas" => array(
                        "SubjectArea" => "PNR"
                    )
                ),
                "UniqueID" => array(
                    "_attributes" => array("ID" => $this->output["pnrID"])
                )
            )
        );
        return XMLSerializer::generateValidXmlFromArray($requestArray);
    }
}
