<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://webservices.sabre.com/sabreXML/2011/10" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:stl="http://services.sabre.com/STL/v01" targetNamespace="http://webservices.sabre.com/sabreXML/2011/10" elementFormDefault="qualified">
	<xs:import namespace="http://services.sabre.com/STL/v01" schemaLocation="STL_For_SabreProtocol_v.1.2.0.xsd"/>
	<xs:element name="IgnoreTransactionRS">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="stl:ApplicationResults"/>
			</xs:sequence>
			<xs:attribute name="Version" type="xs:string"  use="optional"/>
		</xs:complexType>
	</xs:element>
</xs:schema>