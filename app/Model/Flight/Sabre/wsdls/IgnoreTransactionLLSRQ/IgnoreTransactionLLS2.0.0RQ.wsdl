<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:sws_xsd="http://webservices.sabre.com/sabreXML/2011/10" xmlns:sws="https://webservices.sabre.com/websvc" xmlns:eb="http://www.ebxml.org/namespaces/messageHeader" xmlns:wsse="http://schemas.xmlsoap.org/ws/2002/12/secext" targetNamespace="https://webservices.sabre.com/websvc">
	<wsdl:types>
		<xsd:schema>
			<xsd:import namespace="http://webservices.sabre.com/sabreXML/2011/10" schemaLocation="IgnoreTransactionLLS2.0.0RQRS.xsd"/>
			<xsd:import namespace="http://www.ebxml.org/namespaces/messageHeader" schemaLocation="msg-header-2_0.xsd"/>
			<xsd:import namespace="http://schemas.xmlsoap.org/ws/2002/12/secext" schemaLocation="wsse.xsd"/>
			<xsd:import namespace="http://services.sabre.com/STL/v01" schemaLocation="STL_For_SabreProtocol_v.1.2.0.xsd"/>
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="IgnoreTransactionInput">
		<part name="header" element="eb:MessageHeader"/>
		<part name="header2" element="wsse:Security"/>
		<wsdl:part name="body" element="sws_xsd:IgnoreTransactionRQ"/>
	</wsdl:message>
	<wsdl:message name="IgnoreTransactionOutput">
		<part name="header" element="eb:MessageHeader"/>
		<part name="header2" element="wsse:Security"/>
		<wsdl:part name="body" element="sws_xsd:IgnoreTransactionRS"/>
	</wsdl:message>
	<wsdl:portType name="IgnoreTransactionPortType">
		<wsdl:operation name="IgnoreTransactionRQ">
			<wsdl:input message="sws:IgnoreTransactionInput"/>
			<wsdl:output message="sws:IgnoreTransactionOutput"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="IgnoreTransactionSoapBinding" type="sws:IgnoreTransactionPortType">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="IgnoreTransactionRQ">
			<soap:operation soapAction="IgnoreTransactionLLSRQ"/>
			<wsdl:input>
				<soap:header message="sws:IgnoreTransactionInput" part="header" use="literal"/>
				<soap:header message="sws:IgnoreTransactionInput" part="header2" use="literal"/>
				<soap:body parts="body" use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:header message="sws:IgnoreTransactionOutput" part="header" use="literal"/>
				<soap:header message="sws:IgnoreTransactionOutput" part="header2" use="literal"/>
				<soap:body parts="body" use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="IgnoreTransactionService">
		<wsdl:port name="IgnoreTransactionPortType" binding="sws:IgnoreTransactionSoapBinding">
			<soap:address location="https://webservices.sabre.com/websvc"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
