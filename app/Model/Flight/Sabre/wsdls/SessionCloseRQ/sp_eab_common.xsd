<xs:schema targetNamespace="http://services.sabre.com/sp/eab/v3" xmlns:xs="http://www.w3.org/2001/XMLSchema"  elementFormDefault="qualified">
	<xs:simpleType name="dateOrTime">
		<xs:annotation>
			<xs:documentation>Allows all combinations of date and time</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="((((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-9])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-30)))|(((19|20)(([02468][048])|([13579][26]))-02-29))|((20[0-9][0-9])|(19[0-9][0-9]))-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(([0-1][0-9]|[2][0-3])(:([0-5][0-9])){1,2}))|(((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-9])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-30)))|(((19|20)(([02468][048])|([13579][26]))-02-29))|((20[0-9][0-9])|(19[0-9][0-9]))-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))|(([0-1][0-9]|[2][0-3])(:([0-5][0-9])){1,2})"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="dateTime">
		<xs:annotation>
			<xs:documentation>A date time type that forces both date and time to be specified. A year and seconds are allowed to be omitted. Example formats: "yyyy-mm-ddThh:mm:ss", "mm-ddThh:mm:ss", "mm-ddThh:mm", "yyyy-mm-ddThh:mm"</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="(((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-9])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-30)))|(((19|20)(([02468][048])|([13579][26]))-02-29))|((20[0-9][0-9])|(19[0-9][0-9]))-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T([0-1][0-9]|[2][0-3])(:([0-5][0-9])){1,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="date">
		<xs:annotation>
			<xs:documentation>A simple date type. Allows specifying a date without a year. Accepted formats: "yyyy-mm-dd" or "mm-dd"</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="(((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-9])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-30)))|(((19|20)(([02468][048])|([13579][26]))-02-29))|((20[0-9][0-9])|(19[0-9][0-9]))-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="fullDate">
		<xs:annotation>
			<xs:documentation>A full date type. Accepted format: "yyyy-mm-dd"</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date">
			<xs:pattern value="\d{4}-\d{2}-\d{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="expireDate">
		<xs:annotation>
			<xs:documentation>A type representing credit card expiration date. Accepted format: "yyyy-mm"</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:gYearMonth">
			<xs:pattern value="\d{4}-\d{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="fullDateTime">
		<xs:annotation>
			<xs:documentation>A full dateTime type. Accepted format: "yyyy-mm-ddThh:mm:ss"</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime">
			<xs:pattern value="\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="time">
		<xs:annotation>
			<xs:documentation>A simple time type. Seconds can be omitted, since those values are not propagated to the Sabre backend systems. Accepted formats: "hh:mm:ss" or "hh:mm"</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="([0-1][0-9]|[2][0-3])(:([0-5][0-9])){1,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="shortTime">
		<xs:annotation>
			<xs:documentation>A short time type, that does not allow specifying seconds. Accepted format: "hh:mm"</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="([0-1][0-9]|[2][0-3]):([0-5][0-9])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="fullTime">
		<xs:annotation>
			<xs:documentation>A full time type. Accepted format: "hh:mm:ss"</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:time">
			<xs:pattern value="\d{2}:\d{2}:\d{2}"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
