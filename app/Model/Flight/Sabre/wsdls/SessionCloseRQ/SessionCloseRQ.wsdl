<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd1="http://www.opentravel.org/OTA/2002/11" xmlns:tns="https://webservices.sabre.com/websvc" xmlns:eb="http://www.ebxml.org/namespaces/messageHeader" xmlns:wsse="http://schemas.xmlsoap.org/ws/2002/12/secext" targetNamespace="https://webservices.sabre.com/websvc">
	<types>
		<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">
			<xsd:import namespace="http://www.opentravel.org/OTA/2002/11" schemaLocation="SessionCloseRQRS.xsd"/>
			<xsd:import namespace="http://www.ebxml.org/namespaces/messageHeader" schemaLocation="msg-header-2_0.xsd"/>
			<xsd:import namespace="http://schemas.xmlsoap.org/ws/2002/12/secext" schemaLocation="wsse.xsd"/>
		</xsd:schema>
	</types>
	<message name="SessionCloseInput">
		<part name="header" element="eb:MessageHeader"/>
		<part name="header2" element="wsse:Security"/>
		<part name="body" element="xsd1:SessionCloseRQ"/>
	</message>
	<message name="SessionCloseOutput">
		<part name="header" element="eb:MessageHeader"/>
		<part name="header2" element="wsse:Security"/>
		<part name="body" element="xsd1:SessionCloseRS"/>
	</message>
	<portType name="SessionClosePortType">
		<operation name="SessionCloseRQ">
			<input message="tns:SessionCloseInput"/>
			<output message="tns:SessionCloseOutput"/>
		</operation>
	</portType>
	<binding name="SessionCloseSoapBinding" type="tns:SessionClosePortType">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<operation name="SessionCloseRQ">
			<soap:operation soapAction="OTA"/>
			<input>
				<soap:header message="tns:SessionCloseInput" part="header" use="literal"/>
				<soap:header message="tns:SessionCloseInput" part="header2" use="literal"/>
				<soap:body parts="body" use="literal"/>
			</input>
			<output>
				<soap:header message="tns:SessionCloseOutput" part="header" use="literal"/>
				<soap:header message="tns:SessionCloseOutput" part="header2" use="literal"/>
				<soap:body parts="body" use="literal"/>
			</output>
		</operation>
	</binding>
	<service name="SessionCloseRQService">
		<port name="SessionClosePortType" binding="tns:SessionCloseSoapBinding">
			<soap:address location="https://sws-sts.cert.sabre.com"/>
		</port>
	</service>
</definitions>
