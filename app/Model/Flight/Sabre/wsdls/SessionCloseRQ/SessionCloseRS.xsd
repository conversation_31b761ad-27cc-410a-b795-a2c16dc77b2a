<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://www.opentravel.org/OTA/2002/11" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.opentravel.org/OTA/2002/11" elementFormDefault="qualified">
	<xs:element name="SessionCloseRS">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Success" minOccurs="0">
					<xs:complexType/>
				</xs:element>
				<xs:element name="Warnings" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Warning">
								<xs:complexType>
									<xs:attribute name="Language" type="xs:string" use="optional"/>
									<xs:attribute name="ShortText" type="xs:string" use="optional"/>
									<xs:attribute name="Type" type="xs:string" use="optional"/>
									<xs:attribute name="Code" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If present, this refers to a table of coded values exchanged between applications to identify errors or warnings.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:annotation>
												<xs:documentation xml:lang="en">Used for codes in the OTA code tables.</xs:documentation>
											</xs:annotation>
											<xs:restriction base="xs:string">
												<xs:pattern value="[0-9A-Z]{1,3}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="DocURL" type="xs:anyURI" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If present, this URL refers to an online description of the error that occurred.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="Status" type="xs:string" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If present, recommended values are those enumerated in the OTA_ErrorRS, (NotProcessed | Incomplete | Complete | Unknown) however, the data type is designated as string data, recognizing that trading partners may identify additional status conditions not included in the enumeration.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="Tag" type="xs:string" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If present, this attribute may identify an unknown or misspelled tag that caused an error in processing. It is recommended that the Tag attribute use XPath notation to identify the location of a tag in the event that more than one tag of the same name is present in the document. Alternatively, the tag name alone can be used to identify missing data [Type=ReqFieldMissing].</xs:documentation>
										</xs:annotation>
									</xs:attribute>
									<xs:attribute name="RecordId" type="xs:string" use="optional">
										<xs:annotation>
											<xs:documentation xml:lang="en">If present, this attribute allows for batch processing and the identification of the record that failed amongst a group of records.</xs:documentation>
										</xs:annotation>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="ConversationId" type="xs:string" minOccurs="0"/>
				<xs:element name="Errors" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Error">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="ErrorInfo">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="Message" type="xs:string"/>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
									<xs:attribute name="ErrorCode" type="xs:string" use="required"/>
									<xs:attribute name="Severity" type="xs:string" use="required"/>
									<xs:attribute name="ErrorMessage" type="xs:string" use="required"/>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="EchoToken" use="optional">
				<xs:annotation>
					<xs:documentation>A sequence number for additional message identification, assigned by the requesting host system. When a request 										message includes an echo token the corresponding response message MUST include an echo token with an identical value.											</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:annotation>
						<xs:documentation xml:lang="en">Used for Character Strings, length 1 to 64</xs:documentation>
					</xs:annotation>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="64"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:attribute>
			<xs:attribute name="TimeStamp" type="xs:string" use="optional">
				<xs:annotation>
					<xs:documentation>Indicates the creation date and time of the message in UTC using the following format specified by ISO 8601; YYYY-	MM-					DDThh:mm:ssZ with time values using the 24 hour clock (e.g. 20 November 2003, 1:59:38 pm UTC becomes 2003-11-	20T13:59:38Z).						</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="Target" use="optional" default="Production">
				<xs:annotation>
					<xs:documentation>Used to indicate whether the request is for the Test or Production system.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:NMTOKEN">
						<xs:enumeration value="Test"/>
						<xs:enumeration value="Production"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:attribute>
			<xs:attribute name="version" type="xs:string" use="optional">
				<xs:annotation>
					<xs:documentation>For all OTA versioned messages, the version of the message is indicated by a decimal value.</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="SequenceNmbr" type="xs:nonNegativeInteger" use="optional">
				<xs:annotation>
					<xs:documentation>Used to identify the sequence number of the transaction as assigned by the sending system; allows for an application 	to 					process messages in a certain order or to request a resynchronization of messages in the event that a system has been off-line and 	needs to 				retrieve messages that were missed. </xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="PrimaryLangID" type="xs:language" use="optional">
				<xs:annotation>
					<xs:documentation>Identifes the primary language preference for the form of travel represented in a collection.
					 The human language is identified by ISO 639 codes.</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="AltLangID" type="xs:language" use="optional">
				<xs:annotation>
					<xs:documentation>Identifes the primary language preference for the form of travel represented in a collection.
					 The human language is identified by ISO 639 codes.</xs:documentation>
				</xs:annotation>
			</xs:attribute>
			<xs:attribute name="status" type="xs:string" use="optional"/>
		</xs:complexType>
	</xs:element>
</xs:schema>
