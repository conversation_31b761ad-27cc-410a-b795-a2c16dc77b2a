<?php

namespace App\Model\Flight\Sabre;

use Session;
use App\Model\Flight\Flight;
use App\Http\Controllers\Flight\flightController;

class FlightRefferences {
    public $type;
    public $paxNames = array('ADT' => 'Adult', 'CNN' => 'Child', 'INF' => 'Infant');

    function show_flight($PricedItineraries) {
        $j = 1;
        $dataArr = array();
        $cabinNames = array('P' => 'Premium First', 'F' => 'First', 'J' => 'Premium Business', 'C' => 'Business', 'S' => 'Premium Economy', 'Y' => 'Economy');

        if(isset($PricedItineraries->PricedItinerary)) {
            foreach ($PricedItineraries->PricedItinerary as $keyPricedItinerary => $valuePricedItinerary) {

                $i = 0;
                $round = 0;
                foreach ($valuePricedItinerary->AirItinerary->OriginDestinationOptions->OriginDestinationOption as $keyOriginDestinationOption => $valueOriginDestinationOption) {
                    $k = 0;
                    $ElapsedTime = array();
                    foreach ($valueOriginDestinationOption->FlightSegment as $keyFlightSegment => $valueFlightSegment) {
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['DepartureLocationCode'] = (string)$valueFlightSegment->DepartureAirport['LocationCode'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['DepartureTerminalID'] = (string)$valueFlightSegment->DepartureAirport['TerminalID'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['TecDepartureDateTime'] = (string)$valueFlightSegment['DepartureDateTime'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ResBookDesigCode'] = (string)$valueFlightSegment['ResBookDesigCode'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['DepartureDateTime'] = aipDateToHumanDate((string)$valueFlightSegment['DepartureDateTime'], "datetime");
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['DepartureDate'] = aipDateToHumanDate((string)$valueFlightSegment['DepartureDateTime'], "date");
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['DepartureTime'] = aipDateToHumanDate((string)$valueFlightSegment['DepartureDateTime'], "time");
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalLocationCode'] = (string)$valueFlightSegment->ArrivalAirport['LocationCode'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalTerminalID'] = (string)$valueFlightSegment->ArrivalAirport['TerminalID'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['TecArrivalDateTime'] = (string)$valueFlightSegment['ArrivalDateTime'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalDateTime'] = aipDateToHumanDate((string)$valueFlightSegment['ArrivalDateTime'], "datetime");
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalDate'] = aipDateToHumanDate((string)$valueFlightSegment['ArrivalDateTime'], "date");
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalTime'] = aipDateToHumanDate((string)$valueFlightSegment['ArrivalDateTime'], "time");
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['StopQuantity'] = (string)$valueFlightSegment['StopQuantity'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ElapsedTime'] = date('H', mktime(0, (string)$valueFlightSegment['ElapsedTime'])) . "h " . date('i', mktime(0, (string)$valueFlightSegment['ElapsedTime'])) . "m";

                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['AirlineCode'] = (string)$valueFlightSegment->OperatingAirline['Code'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['MarketingAirline'] = (string)$valueFlightSegment->MarketingAirline['Code'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['FlightNumber'] = (string)$valueFlightSegment['FlightNumber'];

                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['AirEquipType'] = (string)$valueFlightSegment->Equipment['AirEquipType'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['AirEquipName'] = Flight::get_equipment((string)$valueFlightSegment->Equipment['AirEquipType']);
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['AirlineImgURL'] = Flight::get_logo($dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['AirlineCode']);
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['Mileage'] = (string)$valueFlightSegment->TPA_Extensions->Mileage['Amount'];

                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['SeatsRemaining'] = (string)$valuePricedItinerary->AirItineraryPricingInfo->FareInfos->FareInfo[$round]->TPA_Extensions->SeatsRemaining['Number'];
                        $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['Cabin'] = $cabinNames[(string)$valuePricedItinerary->AirItineraryPricingInfo->FareInfos->FareInfo[$round]->TPA_Extensions->Cabin['Cabin']];

                        if ($k == 0) {
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['DepartureLocationCode'] = (string)$valueFlightSegment->DepartureAirport['LocationCode'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['DepartureDateTime'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['DepartureDateTime'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['DepartureDate'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['DepartureDate'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['DepartureTime'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['DepartureTime'];

                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['ArrivalLocationCode'] = (string)$valueFlightSegment->ArrivalAirport['LocationCode'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['ArrivalDateTime'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalDateTime'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['ArrivalDate'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalDate'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['ArrivalTime'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalTime'];

                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['AirlineCode'] = (string)$valueFlightSegment->MarketingAirline['Code'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['SeatsRemaining'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['SeatsRemaining'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['AirlineImgURL'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['AirlineImgURL'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['Cabin'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['Cabin'];
                        } else {
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['ArrivalLocationCode'] = (string)$valueFlightSegment->ArrivalAirport['LocationCode'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['ArrivalDateTime'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalDateTime'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['ArrivalDate'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalDate'];
                            $dataArr['suggestion'][$j]['ways'][$i]['summary']['ArrivalTime'] = $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['ArrivalTime'];

                            $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['waiting'] = date('H', mktime(0, 0, strtotime(str_replace("T", " ", $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['TecDepartureDateTime'])) - strtotime(str_replace("T", " ", $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k-1]['TecArrivalDateTime'])))) . "h " .
                                date('i', mktime(0, 0, strtotime(str_replace("T", " ", $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k]['TecDepartureDateTime'])) - strtotime(str_replace("T", " ", $dataArr['suggestion'][$j]['ways'][$i]['segment'][$k-1]['TecArrivalDateTime'])))) . "m";
                        }

                        $dataArr['suggestion'][$j]['ways'][$i]['summary']['StopQuantity'] = $k;

                        $k++;
                        $round++;
                    }

                    $ElapsedTime["d"] = floor ((int)$valueOriginDestinationOption['ElapsedTime'] / 1440);
                    $ElapsedTime["H"] = floor (((int)$valueOriginDestinationOption['ElapsedTime'] - $ElapsedTime["d"] * 1440) / 60);
                    $ElapsedTime["i"] = (int)$valueOriginDestinationOption['ElapsedTime'] - ($ElapsedTime["d"] * 1440) - ($ElapsedTime["H"] * 60);

                    $ElapsedTimeStr = "";

                    if(isset($ElapsedTime["d"]) && $ElapsedTime["d"] != 0) {
                        $ElapsedTimeStr .= $ElapsedTime["d"] . "d ";
                    }
                    if(isset($ElapsedTime["H"])) {
                        $ElapsedTimeStr .= $ElapsedTime["H"] . "h ";
                    }
                    if(isset($ElapsedTime["i"])) {
                        $ElapsedTimeStr .= $ElapsedTime["i"] . "m ";
                    }

                    $dataArr['suggestion'][$j]['ways'][$i]['summary']['ElapsedTime'] = $ElapsedTimeStr;
                    $i++;
                }

                $dataArr['suggestion'][$j]['TotalPrice']['Amount'] = formatMoney((float)$valuePricedItinerary->AirItineraryPricingInfo->ItinTotalFare->TotalFare['Amount']);
                $dataArr['suggestion'][$j]['TotalPrice']['CurrencyCode'] = (string)$valuePricedItinerary->AirItineraryPricingInfo->ItinTotalFare->TotalFare['CurrencyCode'];
                $dataArr['suggestion'][$j]['Type'] = $this->type;
                $dataArr['suggestion'][$j]['RecommendationNO'] = $j;
                $dataArr['suggestion'][$j]['GDSType'] = "sabre";

                $p = 0;
                $paxType = "0";
                $seatCount = 0;
                foreach ($valuePricedItinerary->AirItineraryPricingInfo->PTC_FareBreakdowns->PTC_FareBreakdown as $PTC_FareBreakdown) {
                    if((string)$PTC_FareBreakdown->PassengerTypeQuantity['Code'] == "ADT") {
                        $paxType = "Adult";
                        $seatCount += (int)$PTC_FareBreakdown->PassengerTypeQuantity['Quantity'];
                    } else if ((string)$PTC_FareBreakdown->PassengerTypeQuantity['Code'] == "CNN") {
                        $paxType = "Child";
                        $seatCount += (int)$PTC_FareBreakdown->PassengerTypeQuantity['Quantity'];
                    } else {
                        $paxType = "Infant";
                    }

                    $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['Quantity'] = (string)$PTC_FareBreakdown->PassengerTypeQuantity['Quantity'];
                    $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['TypeID'] = (string)$PTC_FareBreakdown->PassengerTypeQuantity['Code'];
                    $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['Type'] = $paxType;
                    $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['TotalFare'] = (string)$PTC_FareBreakdown->PassengerFare->TotalFare['Amount'];
                    $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['TotalTax'] = (string)$PTC_FareBreakdown->PassengerFare->Taxes->TotalTax['Amount'];
                    $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['EquivFare'] = (string)$PTC_FareBreakdown->PassengerFare->EquivFare['Amount'];

                    $a = 0;

                    if(isset($PTC_FareBreakdown->PassengerFare->TPA_Extensions->BaggageInformationList->BaggageInformation)){
                        foreach ($PTC_FareBreakdown->PassengerFare->TPA_Extensions->BaggageInformationList->BaggageInformation as $BaggageInformation) {
                            if(isset($BaggageInformation->Allowance['Weight'])) {
                                $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['BaggageInformation'][$a]['Allowance'] = (string)$BaggageInformation->Allowance['Weight'];
                            } else if(isset($BaggageInformation->Allowance['Pieces'])) {
                                $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['BaggageInformation'][$a]['Pieces'] = (int)$BaggageInformation->Allowance['Pieces'];
                                $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['BaggageInformation'][$a]['PiecesWeight'] = 25;
                                $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['BaggageInformation'][$a]['Allowance'] = (int)$BaggageInformation->Allowance['Pieces'] * 25;
                            }

                            $b=0;
                            foreach ($BaggageInformation->Segment as $Segment) {
                                $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['BaggageInformation'][$a]['Segment'][$b]['From'] = $dataArr['suggestion'][$j]['ways'][$a]['segment'][$b]['DepartureLocationCode'] ?? "--";
                                $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['BaggageInformation'][$a]['Segment'][$b]['To'] = $dataArr['suggestion'][$j]['ways'][$a]['segment'][$b]['ArrivalLocationCode'] ?? "--";
                                $b++;
                            }
                            $a++;
                        }//end of foreach
                    }

                    $c=0;
                    $d=0;
                    $dataArrTemp = array();
                    foreach ($PTC_FareBreakdown->PassengerFare->PenaltiesInfo->Penalty as $Penalty) {
                        $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['Penalty'][$c]['Type'] = (string)$Penalty['Type'];
                        $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['Penalty'][$c]['Applicability'] = substr((string)$Penalty['Applicability'], 0, 1);
                        if(isset($Penalty['Amount'])) {
                            $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['Penalty'][$c]['Amount'] = (string)$Penalty['Amount'];
                        } else {
                            $dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['Penalty'][$c]['Amount'] = "--";
                        }

                        if((string)$Penalty['Type'] == "Refund") {
                            $dataArrTemp[$d]['Type'] = "Refundable";
                            $dataArrTemp[$d]['Applicability'] = substr((string)$Penalty['Applicability'], 0, 1);
                            if(isset($Penalty['NonApplicable'])) {
                                $dataArrTemp[$d]['Amount'] = "No";
                            } else {
                                $dataArrTemp[$d]['Amount'] = "Yes";
                            }
                            $d++;
                        }
                        $c++;
                    }
                    array_push($dataArr['suggestion'][$j]['PTC_FareBreakdown'][$p]['Penalty'], $dataArrTemp[0], $dataArrTemp[1]);
                    $p++;
                }

                $dataArr['suggestion'][$j]['SeatCount'] = $seatCount;
                $dataArr['suggestion'][$j]['AllData'] = json_encode($dataArr['suggestion'][$j]);

                $j++;
            }
        } else {
            var_dump($PricedItineraries);exit;
        }

        return $dataArr;
    }

    function show_itinerary($OTA_AirPriceRS) {
        $PricedItinerary = $OTA_AirPriceRS->PriceQuote->PricedItinerary;
        if(isset($PricedItinerary)) {
            $flight_data = Session::get('flight_data');
            $flight_data["TotalPrice"]["Amount"] = formatMoney((float)$PricedItinerary['TotalAmount']);
            Session::put('flight_data', $flight_data);

            return formatMoney((float)$PricedItinerary['TotalAmount']);
        }
        return false;
    }

    function show_PNR($PassengerDetailsRS) {
        Session::put('pnr_ref_id', (string)$PassengerDetailsRS["ID"]);
        $flightController= new flightController();
        if(env('APP_ENV') == "Devolopment") {
            // email need to setup
            $flightController->save_pnr();
        } else {
            $flightController->save_pnr();
        }

        return (string)$PassengerDetailsRS["ID"];
    }

    function show_PNRReview($xml) {
        $dataArr = array();

        $dataArr['RecordLocator'] = (string)$xml->xpath('//s:RecordLocator')[0];
        // Session::put('pnr_ref_id', $dataArr['RecordLocator']);
        $dataArr['CreationDateTime'] = aipDateToHumanDate((string)$xml->xpath('//s:CreationTimestamp')[0], "datetime");
        $dataArr['FlightsRangeStart'] = aipDateToHumanDate((string)$xml->xpath('//s:FlightsRange')[0]['Start'], "datetime");
        $dataArr['FlightsRangeEnd'] = aipDateToHumanDate((string)$xml->xpath('//s:FlightsRange')[0]['End'], "datetime");

        $PriceQuote = $xml->xpath('//o:PriceQuote');

        for ($i=0; $i < count($PriceQuote[0]->PriceQuoteInfo->Summary->NameAssociation); $i++) {
            $dataArr['Passenger'][$i]["passengerType"] = $this->paxNames[(string)$PriceQuote[0]->PriceQuoteInfo->Summary->NameAssociation[$i]->PriceQuote->Passenger["type"]];
            $dataArr['Passenger'][$i]["passengerTypeCode"] = (string)$PriceQuote[0]->PriceQuoteInfo->Summary->NameAssociation[$i]->PriceQuote->Passenger["type"];
            $dataArr['Passenger'][$i]["nameId"] = (string)$PriceQuote[0]->PriceQuoteInfo->Summary->NameAssociation[$i]['nameNumber'];
            $dataArr['Passenger'][$i]["LastName"] = (string)$PriceQuote[0]->PriceQuoteInfo->Summary->NameAssociation[$i]['lastName'];
            $dataArr['Passenger'][$i]["FirstName"] = (string)$PriceQuote[0]->PriceQuoteInfo->Summary->NameAssociation[$i]['firstName'];

            foreach ($PriceQuote[0]->PriceQuoteInfo->Summary->NameAssociation[$i]->PriceQuote as $keyPriceQuote => $valuePriceQuote) {
                $dataArr['Passenger'][$i]["Number"][] = (string)$valuePriceQuote['number'];
            }
            $dataArr['PassengerData'][$dataArr['Passenger'][$i]["passengerTypeCode"]][] = 1;
        }

        $ADTseatcount = isset($dataArr['PassengerData']['ADT']) ? count($dataArr['PassengerData']['ADT']) : 0;
        $CNNseatcount = isset($dataArr['PassengerData']['CNN']) ? count($dataArr['PassengerData']['CNN']) : 0;

        $dataArr['seatcount'] = $ADTseatcount + $CNNseatcount;

        foreach ($dataArr['Passenger'] as $keyPassenger => $valuePassenger) {
            $i = 0;
            foreach ($valuePassenger['Number'] as $keyNumber => $valueNumber) {
                $dataArr['Price'][$i]['pax'][$keyPassenger]["Type"] = $this->paxNames[(string)$PriceQuote[0]->PriceQuoteInfo->Details[$valueNumber-1]["passengerType"]];
                $dataArr['Price'][$i]['pax'][$keyPassenger]["Quantity"] = 1;
                $dataArr['Price'][$i]['pax'][$keyPassenger]["BaseFare"] = (string)$PriceQuote[0]->PriceQuoteInfo->Details[$valueNumber-1]->FareInfo->BaseFare;
                $dataArr['Price'][$i]['pax'][$keyPassenger]["TotalTax"] = (int)$PriceQuote[0]->PriceQuoteInfo->Details[$valueNumber-1]->FareInfo->TotalTax;
                $dataArr['Price'][$i]['pax'][$keyPassenger]["TotalFare"] = (int)$PriceQuote[0]->PriceQuoteInfo->Details[$valueNumber-1]->FareInfo->TotalFare;
                $dataArr['Price'][$i]['pax'][$keyPassenger]["TotalAmount"] = $dataArr['Price'][$i]['pax'][$keyPassenger]["TotalFare"] * $dataArr['Price'][$i]['pax'][$keyPassenger]["Quantity"];
                $dataArr['Price'][$i]['pax'][$keyPassenger]["CreateDateTime"] = aipDateToHumanDate((string)$PriceQuote[0]->PriceQuoteInfo->Details[$valueNumber-1]->TransactionInfo->CreateDateTime, "datetime");;
                $dataArr['Price'][$i]['pax'][$keyPassenger]["LocalCreateDateTime"] = aipDateToHumanDate((string)$PriceQuote[0]->PriceQuoteInfo->Details[$valueNumber-1]->TransactionInfo->LocalCreateDateTime, "datetime");;
                $i++;
            }
        }

        foreach ($dataArr['Price'] as $keyPrice => $keyValue) {
            $totalAmout = 0;
            foreach ($keyValue['pax'] as $keyPax => $valuePax) {
                $totalAmout = $totalAmout + $valuePax["TotalAmount"];
                $dataArr['Price'][$keyPrice]['TotalAmount'] = $totalAmout;
            }
        }

        foreach ($xml->xpath('//s:Passengers/s:Passenger/s:SpecialRequests/s:GenericSpecialRequest/s:Code') as $ssrKey => $ssrValue) {
            $dataArr['SSR'][$ssrKey]["nameId"] = (string)$xml->xpath('//s:Passengers/s:Passenger')['nameId'][$ssrKey];
            $dataArr['SSR'][$ssrKey]["FirstName"] = (string)$xml->xpath('//s:Passengers/s:Passenger/s:FirstName')[$ssrKey];
            $dataArr['SSR'][$ssrKey]["Code"] = (string)$xml->xpath('//s:Passengers/s:Passenger/s:SpecialRequests/s:GenericSpecialRequest/s:Code');
            $dataArr['SSR'][$ssrKey]["ActionCode"] = (string)$xml->xpath('//s:Passengers/s:Passenger/s:SpecialRequests/s:GenericSpecialRequest/s:ActionCode');
            $dataArr['SSR'][$ssrKey]["AirlineCode"] = (string)$xml->xpath('//s:Passengers/s:Passenger/s:SpecialRequests/s:GenericSpecialRequest/s:AirlineCode');
            $dataArr['SSR'][$ssrKey]["FullText"] = (string)$xml->xpath('//s:Passengers/s:Passenger/s:SpecialRequests/s:GenericSpecialRequest/s:FullText');
        }

        foreach ($xml->xpath('//s:Segments/s:Segment') as $segKey => $segValue) {
            $dataArr['Segment'][$segKey]["sequence"] = (string)$segValue["sequence"];
            $dataArr['Segment'][$segKey]["ResBookDesigCode"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air')[$segKey]['ResBookDesigCode'];
            $dataArr['Segment'][$segKey]["DepartureAirport"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:DepartureAirport')[$segKey];
            $dataArr['Segment'][$segKey]["ArrivalAirport"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:ArrivalAirport')[$segKey];
            $dataArr['Segment'][$segKey]["OperatingAirlineCode"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:OperatingAirlineCode')[$segKey];
            $dataArr['Segment'][$segKey]["OperatingAirlineURL"] = Flight::get_logo((string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:OperatingAirlineCode')[$segKey]);
            $dataArr['Segment'][$segKey]["OperatingAirlineName"] = Flight::getAirLineName((string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:OperatingAirlineCode')[$segKey]);
            $dataArr['Segment'][$segKey]["MarketingAirlineCode"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:MarketingAirlineCode')[$segKey];
            $dataArr['Segment'][$segKey]["MarketingAirlineURL"] = Flight::get_logo((string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:MarketingAirlineCode')[$segKey]);
            $dataArr['Segment'][$segKey]["MarketingAirlineName"] = Flight::getAirLineName((string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:MarketingAirlineCode')[$segKey]);
            $dataArr['Segment'][$segKey]["DepartureDateTime"] = aipDateToHumanDate((string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:DepartureDateTime')[$segKey], "datetime");
            $dataArr['Segment'][$segKey]["ArrivalDateTime"] = aipDateToHumanDate((string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:ArrivalDateTime')[$segKey], "datetime");
            $dataArr['Segment'][$segKey]["TecDepartureDateTime"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:DepartureDateTime')[$segKey];
            $dataArr['Segment'][$segKey]["TecArrivalDateTime"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:ArrivalDateTime')[$segKey];
            $dataArr['Segment'][$segKey]["FlightNumber"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:FlightNumber')[$segKey];
            $dataArr['Segment'][$segKey]["ActionCode"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:ActionCode')[$segKey];
            $dataArr['Segment'][$segKey]["EquipmentType"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:EquipmentType')[$segKey];
            $dataArr['Segment'][$segKey]["Banner"] = (string)$xml->xpath('//s:Segments/s:Segment/s:Air/s:Banner')[$segKey];
        }
        // var_dump((string)$xml->xpath('//s:AddressLines/s:AddressLine/s:Text')[0]);exit();
        $dataArr['agent']["address"] = (string)$xml->xpath('//s:AddressLines/s:AddressLine/s:Text')[0];
        $dataArr['agent']["tel1"] = (string)$xml->xpath('//s:PhoneNumbers/s:PhoneNumber/s:Number')[0];
        $dataArr['agent']["tel2"] = (string)$xml->xpath('//s:PhoneNumbers/s:PhoneNumber/s:Number')[1];

        foreach ($xml->xpath('//s:FullText') as $msgKey => $megValue) {
            $dataArr['Message'][$msgKey]["FullText"] = (string)$xml->xpath('//s:FullText')[$msgKey];
            // $dataArr['Message'][$msgKey]["ServiceRequest"] = (string)$xml->xpath('//s:ServiceRequest')['serviceType'][$msgKey];
        }

        Session::put('pnr_all_data', $dataArr);
        return $dataArr;
    }

    function show_PNRReviewLoad() {
        return true;
    }

    function show_EndTransaction() {
        return true;
    }

    function show_errors ($errors, $type = "") {
        $error = "";
        if($type == "GetReservationRQ") {
            foreach ($errors->xpath('//s:Errors/s:Error') as $key => $value) {
                $error = $error . "<li>" . (string)$errors->xpath('//s:Errors/s:Error/s:Message')[$key] . "</li>";
            }
        } else {
            foreach ($errors->Error as $keyError => $keyValue) {
                $error = $error . "<li>" . (string)$keyValue['ShortText'] . "</li>";
            }
        }

        return $error;
    }
}