<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SACSClient;
use DOMDocument;

class OTA_Cancel implements Activity {

    private $config;
    public $output;
    
    public function __construct() {
        $this->config = SACSConfig::getInstance();
    }

    public function filters($output) {
        $this->output = $output;
    }

    public function run(&$sharedContext) {
        $soapClient = new SACSSoapClient("OTA_CancelLLSRQ");
        $soapClient->setLastInFlow(false);

        if(isset($this->output['callType'])) {
            $soapClient->setCallType($this->output['callType']);
        }

        $xmlRequest = $this->getRequest();

        $soapClient->doCall($sharedContext, $xmlRequest);
    }

    private function getRequest() {

        $requestArray = array(
            "OTA_CancelRQ" => array(
                "_namespace" => "http://webservices.sabre.com/sabreXML/2011/10",
                "_attributes" => array(
                    "Version" => "2.0.2"
                ),
                "Segment" => array(
                    "_attributes" => array(
                        "Type" => "entire"
                    )
                )
            )
        );

        return XMLSerializer::generateValidXmlFromArray($requestArray);
    }
}
