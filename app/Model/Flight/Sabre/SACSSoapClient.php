<?php

namespace App\Model\Flight\Sabre;

use App\Model\Flight\Sabre\SharedContext;
use App\Model\Flight\Sabre\SACSConfig;
use App\Model\Flight\Sabre\SessionCreateRequest;
use App\Model\Flight\Sabre\SessionCloseRequest;
use App\Model\Flight\Sabre\IgnoreTransactionRequest;
use App\Model\Flight\Sabre\XMLSerializer;
use App\Model\Flight\Sabre\FlightRefferences;
use App\Model\Flight\Sabre\Flight;
use SoapVar;
use SoapHeader;
use SimpleXMLElement;

class SACSSoapClient {

    private $lastInFlow = false;
    private $actionName;
    public $callType;

    public function __construct($actionName) {
        $this->actionName = $actionName;
    }

    public function setCallType($callType) {
        $this->callType = $callType;
    }

    public function doCall($sharedContext, $request, $type="") {

        if ($sharedContext->getResult("SECURITY") == null) {

            error_log("SessionCreate");
            $securityCall = new SessionCreateRequest();
            $sharedContext->addResult("SECURITY", $securityCall->executeRequest());
        }

        $sacsClient = new SACSClient();
        $sacsClient->type = $type;
        $sacsClient->callType = $this->callType;

        $result = $sacsClient->doCall(SACSSoapClient::getMessageHeaderXml($this->actionName) . $this->createSecurityHeader($sharedContext), $request, $this->actionName);
        if ($this->lastInFlow) {
            error_log("Ignore and close");
            $this->ignoreAndCloseSession($sharedContext->getResult("SECURITY"));
            $sharedContext->addResult("SECURITY", null);
        }
        return $result;
    }

    private function ignoreAndCloseSession($security) {
        $it = new IgnoreTransactionRequest();
        $it->executeRequest($security);
        $sc = new SessionCloseRequest();
        $sc->executeRequest($security);
    }

    private function createSecurityHeader($sharedContext) {
        $security = array("Security" => array(
                "_namespace" => "http://schemas.xmlsoap.org/ws/2002/12/secext",
                "BinarySecurityToken" => array(
                    "_attributes" => array("EncodingType" => "Base64Binary", "valueType" => "String"),
                    "_value" => $sharedContext->getResult("SECURITY")->BinarySecurityToken
                )
            )
        );
        return XMLSerializer::generateValidXmlFromArray($security);
    }

    public static function createMessageHeader($actionString) {
        $messageHeaderXml = SACSSoapClient::getMessageHeaderXml($actionString);
        $soapVar = new SoapVar($messageHeaderXml, XSD_ANYXML, null, null, null);
        return new SoapHeader("http://www.ebxml.org/namespaces/messageHeader", "MessageHeader", $soapVar, 1);
    }

    private static function getMessageHeaderXml($actionString) {
        $messageHeader = array("MessageHeader" => array(
                "_namespace" => "http://www.ebxml.org/namespaces/messageHeader",
                "From" => array("PartyId" => "sample.url.of.sabre.client.com"),
                "To" => array("PartyId" => "webservices.sabre.com"),
                "CPAId" => "Z7B8",
                "ConversationId" => "convId",
                "Service" => $actionString,
                "Action" => $actionString,
                "MessageData" => array(
                    "MessageId" => "1000",
                    "Timestamp" => "2001-02-15T11:15:12Z",
                    "TimeToLive" => "2001-02-15T11:15:12Z"
                )
            )
        );
        return XMLSerializer::generateValidXmlFromArray($messageHeader);
    }

    public function setLastInFlow($lastInFlow) {
        $this->lastInFlow = $lastInFlow;
    }

}

