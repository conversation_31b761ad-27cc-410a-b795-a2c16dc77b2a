<?php
/**
 * Created by PhpStorm.
 * User: ka<PERSON>
 * Date: 10/31/18
 * Time: 10:25 AM
 */

namespace App\Model\Flight\Riya;

use App\Model\Flight\Riya\RiyaSoapResponse;
use App\Model\Flight\Riya\RiyaRetrieveRequest;

class RiyaRetrieveResponse

{
    public $retrive_response;
    public $retrive_request;
    public $obj_retrive_request;
    public $soap;

    public function __construct($inputs){

        $this->soap = new RiyaSoapResponse();
        $this->obj_retrive_request = new RiyaRetrieveRequest($inputs);
        $this->retrive_request = $this->obj_retrive_request->get_request();
        $this->retrive_response = $this->soap->get_response('riya_retrieve_pnr', $this->retrive_request);
       
    }

    public function get_response(){
        return $this->retrive_response;
    }
}