<?php

namespace App\Model\Flight;


use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Flight\Sabre\Airline
 *
 * @property int $id
 * @property string|null $name
 * @property string|null $code
 * @property string|null $image
 * @property string|null $rating
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Flight\Airline whereUploadId($value)
 * @mixin \Eloquent
 */
class Airline extends Model
{

	protected $table = 'apple_airlines';

//
//
//    public function __construct($code ,array $attributes = [])
//    {
//        parent::__construct($attributes);
//
//        return  $this->setAirline($code);
//
//    }
//
//    public function setAirline($code)
//    {
//
//
//        $airline = Airline::where('code',$code)->first();
//
//        return $airline;
//
//    }

    /**
     * @param $IATACode
     * @return null|string
     */
    public static function GetAirlineImage($IATACode)
    {
        $flights = Airline::where('code', $IATACode)->first();
        if ($flights){
            return asset('assets/image/filght/'.$flights->attributes['image']);
        }
        else
            return null;

    }


    /**
     * @param $IATACode
     * @return mixed
     */
    public static function GetAirlineName($IATACode)
    {
        $flights = Airline::where('code', $IATACode)->first();
        return $flights->attributes['name'];
    }
}

?>