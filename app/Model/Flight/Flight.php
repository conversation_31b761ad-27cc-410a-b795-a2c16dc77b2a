<?php

namespace App\Model\Flight;


use Illuminate\Database\Eloquent\Model;

use App\Model\Flight\Airline;
use App\Model\Place\Place;
use App\Model\Place\Stop;
use App\Model\Flight\Equipment;
use App\Model\Flight\MealCode;

use DB;
use Session;

use appletechlabs\flight\Client;
use appletechlabs\flight\Search\CalendarSearch;
use appletechlabs\flight\Search\FareMasterPricerTbSearch;

/**
 * App\Model\Flight\Sabre\Flight
 *
 * @mixin \Eloquent
 */
class Flight extends Model
{
    public $carrier;
    public $majCarrier;

    /**
     * @param $countries
     * @return array
     */
    public static function checkCountries($countries)
	{
		$airports =  $countries;

		$ourAirports = Place::where('type', 2)->get();
		
		$result2 = [];

		foreach ($airports as $airport) {
			$found = false;
			//$result[] = $place->A2;
				foreach ($ourAirports as $ourAirport) {

				//$result2[] = $place2->iso_country;
					if ($airport->ALPHA2 == $ourAirport->A2)
					{
						$found = true;		
						

					}

				}

				if ($found == false) 
				{
					$result2[] = $airport->ALPHA2;
				}				
		}		
		return $result2;
	}

    /**
     * @return null
     */
    public static function getAirports()
	{
		$result = null;
		$airports = Place::whereHas('Stop', function($q){
		    $q->where('type', '=', '1');
		})->orderBy('A2')->get();

		foreach ($airports as $key => $airport) {
			if ($airport->A2 != null)
				continue;
			$result[$key]['ID'] = $airport->ID;
			$result[$key]['name'] = $airport->name;
			$result[$key]['A2'] = $airport->A2;
		}
		return $result;
	}

    /**
     * @param $countries
     * @return string
     */
    public static function setCountires($countries)
	{
		//dd($countries);
		foreach ($countries as $country) {
			// $place = new Place();
			// $place->name =  $country->name;
			// $place->status =  1;
			// $place->popularity =  40;
			// $place->type =  2;
			// $place->A2 =  $country->ALPHA2;
			// $place->country =  62;

			// $place->save();

			// $upPlace = Place::where('A2', $country->ALPHA2);
			// $upPlace->country = $upPlace->ID;
			// $upPlace->save();

		}

		return "done";


	}

    public static function get_logo($airline_code)
    {
        $logo = Airline::where('code','=',$airline_code)->select('image')->first();
        if(isset($logo) && isset($logo->image)) {
            if(!file_exists(asset("assets/image/flight/" . $logo->image))) {
                return asset("assets/image/flight/" . $logo->image);
            }
        }
        return asset("assets/image/flight/default-airline.png");
    }

    public static function getAirLineName($airlineCode)
    {
        $name = Airline::where('code','=',$airlineCode)->select('name')->first();
        if(isset($name) && isset($name->name)) {
            return $name->name;
        }
    }

    public static function get_equipment($equipment_id) {
        $Equipment = Equipment::where('equipment_id','=',$equipment_id)->select('name')->first();
        if(isset($Equipment)) {
            return $Equipment->name;
        }
    }

    public static function get_pax_names() {
        $paxNames = ["adult"=>"Adult", "child"=>"Child", "cnb"=>"Infant"];

        $searchData = Session::get('search_data');
        $paxArr = array();
        $paxCount = 1;
        foreach ($searchData["pax"] as $paxKey => $paxValue) {
            for ($i=0; $i<$paxValue; $i++) {
                $paxArr[$paxCount . ".1"] = $paxNames[$paxKey]." ".($i+1)." (".$paxCount . ".1)";
                $paxCount++;
            }
        }

        return $paxArr;
    }

    public static function get_meal_codes() {
        $mealArr = array();
        $MealCodes = MealCode::select('meal_code', 'name')->get();
        if(isset($MealCodes)) {
            foreach ($MealCodes as $key => $value) {
                $mealArr[$value->meal_code] = $value->meal_code . " - " . $value->name;
            }
        }
        return $mealArr;
    }

    public static function get_ssr_codes() {
        $ssrArr = array();
        $SSRCodes = SSRCode::select('ssr_code', 'name')->get();
        if(isset($SSRCodes)) {
            foreach ($SSRCodes as $key => $value) {
                $ssrArr[$value->ssr_code] = $value->ssr_code;
            }
        }
        return $ssrArr;
    }
}

?>