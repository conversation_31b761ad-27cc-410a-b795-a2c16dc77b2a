<?php

namespace App\Model\Flight;


use Illuminate\Database\Eloquent\Model;

use App\Model\Flight\Airline;
use App\Model\Place\Place;
use App\Model\Place\Stop;

use DB;

use appletechlabs\flight\Client;
use appletechlabs\flight\Search\CalendarSearch;
use appletechlabs\flight\Search\FareMasterPricerTbSearch;

/**
 * Class Airport
 * @package App\Model\Flight\Sabre
 */
class Airport extends Model
{

    public $name;
    public $IATA;
    public $coordinates;
    public $country;
    public $countryName;
  


  
    /**
     * @param $IATACode
     * @return $this|null
     */
    public function setAirport($IATACode)
    {
        $this->IATA = $IATACode;
        $a = Place::where('A2', $IATACode)->first();
        if ($a){
            $this->name =  $a->name;
            $this->coordinates['longitude'] =  $a->longitude;
            $this->coordinates['latitude'] =  $a->latitude;
            $this->country =  $a->country;
            $this->countryName =  Place::find( $a->country)['name'];

            return $this;
        }
        else
        {
            return null;
        }
    }

    /**
     * Airport constructor.
     * @param $iataCode
     * @param array $attributes
     */
    public function __construct($iataCode , array $attributes = [])
    {
        parent::__construct($attributes);
        return  $this->setAirport($iataCode);

    }

    /**
     * @param $IATACode
     * @return mixed|null|string
     */
    public function getAirportName($IATACode)
    {
        $a = Place::where('A2', $IATACode)->first();
        return $a->name ?? NULL;

    }

    /**
     * @param $airportIATACode
     * @return mixed
     */
    public static function getCoordinates($airportIATACode)
    {

        $place = Place::where('A2', $airportIATACode)->first();
        if ($place  ===  null) {
            $coordinates['longitude'] = null;
            $coordinates['latitude'] = null;
        }
        else
        {
            //dd($place->longitude);
            $coordinates['longitude'] = $place->longitude;
            $coordinates['latitude'] = $place->latitude;


        }

        return $coordinates;


    }
    


}
