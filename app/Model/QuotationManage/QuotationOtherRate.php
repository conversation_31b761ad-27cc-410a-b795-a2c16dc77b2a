<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationOtherRate
 *
 * @property int $ID
 * @property int|null $reference_id
 * @property string|null $text
 * @property int|null $type
 * @property int|null $rate_type
 * @property float|null $rate
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \App\Model\QuotationManage\QuotationOtherRateSightseeing $sightseeing
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereRateType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationOtherRate whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationOtherRate extends Model
{
    protected $table = 'apple_quotation_other_rate';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function sightseeing()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationOtherRateSightseeing', 'other_rate_id', 'ID');
    }

}
