<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationPlace
 *
 * @property int $ID
 * @property int|null $reference_id
 * @property int|null $place
 * @property int|null $order
 * @property int|null $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \App\Model\QuotationManage\Quotation $quotation
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace wherePlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationPlace whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationPlaceVehicle extends Model
{
    protected $table = 'apple_quotation_place_vehicle';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function quotation(){
       return $this->belongsTo('App\Model\QuotationManage\Quotation',"ID",'reference_id');
     }

    public function findVehicle(){
        return $this->hasOne('App\Model\Vehicle\SubVehicle',"ID",'vehicle');
    }
}
