<?php
/**
 * Supun Praneeth 2017.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationCityTour
 *
 * @property int $ID
 * @property int|null $reference_id
 * @property int $city_tour
 * @property int|null $day
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationCityTourRate[] $Rate
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereCityTour($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationCityTour whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationCityTour extends Model
{
     protected $table = 'apple_quotation_city_tour';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationCityTourRate','city_tour','ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Details()
    {
        return $this->hasOne('App\Model\Place\CityTour', 'ID', 'city_tour');
    }
}
