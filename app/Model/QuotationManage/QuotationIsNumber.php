<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationConfirm
 *
 * @property int $ID
 * @property int $reference_id
 * @property int $client_title
 * @property string $client_name
 * @property int|null $agent
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationConfirmHotel[] $Hotel
 * @property-read \App\Model\QuotationManage\Quotation $quotation
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationConfirm whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationConfirm whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationConfirm whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationConfirm whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationConfirm whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationConfirm whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationConfirm whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationConfirm whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationIsNumber extends Model
{
    protected $table = 'apple_quotation_isnumber';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function quotation()
    {
        return $this->belongsTo('App\Model\QuotationManage\Quotation','reference_id','ID');
    }

}
