<?php
/**
 * Supun Praneeth 2017.  All rights reserved.
 * @link https://github.com/spmsupun
 */

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationExcursion
 *
 * @property int $ID
 * @property int|null $reference_id
 * @property int $excursion
 * @property int|null $day
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationExcursionRate[] $Rate
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereExcursion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationExcursion whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationExcursion extends Model
{
     protected $table = 'apple_quotation_excursion';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationExcursionRate','excursion','ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function Details()
    {
        return $this->hasOne('App\Model\Place\Excursion', 'ID', 'excursion');
    }
}
