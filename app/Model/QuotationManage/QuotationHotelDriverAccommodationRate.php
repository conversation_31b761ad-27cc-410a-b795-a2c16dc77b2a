<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationHotelRateChild
 *
 * @property int $ID
 * @property int|null $quotation_hotel_rate_id
 * @property int $year
 * @property int $month
 * @property int $day
 * @property int|null $age_from
 * @property int|null $age_to
 * @property float|null $rate
 * @property float|null $modified_rate
 * @property string|null $reason
 * @property int|null $is_modified
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereAgeFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereAgeTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereIsModified($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereModifiedRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereQuotationHotelRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRateChild whereYear($value)
 * @mixin \Eloquent
 */
class QuotationHotelDriverAccommodationRate extends Model
{
     protected $table = 'apple_quotation_hotel_driver_accommodation_rate';

}
