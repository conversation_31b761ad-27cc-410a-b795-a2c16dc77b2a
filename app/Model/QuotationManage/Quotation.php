<?php

namespace App\Model\QuotationManage;

use App\ClientEmail;
use App\Mail\HotelbedsInvoice;
use App\Mail\QuotationConfirmEmailClient;
use App\Model\Hotel\AllotmentUsed;
use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelBed;
use App\Model\Hotel\Meal;
use App\Model\Hotel\RoomCategory;
use App\Model\Image\Image;
use App\Model\Place\Attraction;
use App\Model\Place\CityTour;
use App\Model\Place\Distance;
use App\Model\Place\Excursion;
use App\Model\Place\Place;
use App\Model\Quotation\QuotationTask;
use App\Model\QuotationManage\Quotation as QuotationManage;
use App\Model\Tool\Honorific;
use App\Model\Vehicle\HotelTransportRate;
use App\Model\QuotationManage\QuotationDayUse;
use App\Model\Vehicle\TransportCost;
use App\Model\Vehicle\Vehicle;
use App\User;
use Auth;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Session;
use Mail;
use App\Model\DriverAccomodation\HotelDriverAccmomodation;
use App\Model\FdPackages\apple_fd_packages_quotation;
use App\Model\QuotationManage\QuotationHotelBedsCancel;
/**
 * App\Model\QuotationManage\Quotation
 *
 * @property int $ID
 * @property int $quotation_no
 * @property int $status
 * @property int $country
 * @property int $currency
 * @property int|null $tour_session
 * @property int|null $last_modified
 * @property \Carbon\Carbon|null $deleted_at
 * @property int $tour_type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property int|null $upload_id
 * @property-read \App\Model\QuotationManage\QuotationAirport $Airport
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationAttraction[] $Attraction
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationCityTour[] $CityTours
 * @property-read \App\Model\QuotationManage\QuotationConfirm $Confirm
 * @property-read \App\Model\QuotationManage\QuotationConfirmHotel $ConfirmHotel
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationExcursion[] $Excursion
 * @property-read \App\Model\QuotationManage\QuotationFlight $Flight
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationGuide[] $Guide
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationHotel[] $Hotel
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationHotelVoucher[] $HotelVoucher
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationItinerary[] $Itinerary
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationItineraryAttraction[] $ItineraryAttraction
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationItineraryCityTour[] $ItineraryCityTour
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationItineraryExcursion[] $ItineraryExcursion
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationLanguage[] $Language
 * @property-read \App\Model\QuotationManage\QuotationMain $Main
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationMeal[] $Meal
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationOtherRate[] $OtherRate
 * @property-read \App\Model\QuotationManage\QuotationPax $Pax
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationPlace[] $Place
 * @property-read \App\Model\QuotationManage\QuotationStatus $Status
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationTimeline[] $Timeline
 * @property-read \App\Model\QuotationManage\QuotationTransport $Transport
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Operation\VehicleAllocation[] $VehicleAllocation
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\QuotationManage\Quotation onlyTrashed()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereLastModified($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereQuotationNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereTourSession($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereTourType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\Quotation whereUploadId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\Model\QuotationManage\Quotation withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\QuotationManage\Quotation withoutTrashed()
 * @mixin \Eloquent
 */
class Quotation extends Model
{

    /**
     *
     */
    const CONFIRM = 'confirm';
    /**
     *
     */
    const CANCEL = 'cancel';
    /**
     *
     */
    const SAVE = 'save';

    /**
     *
     */
    const CWB = 'cwb';
    /**
     *
     */
    const CNB = 'cnb';

    const HOTEL_LOCAL = 'local';
    const HOTEL_BEDS = 'hotelbeds';
    const PROVIDER_LOCAL_SPECIAL = 'local_special';


    /**
     * @var string
     */
    public $error = "";

    /**
     * @var array
     */
    private $quotationArray = [];

    /**
     * @var string
     */
    protected $table = 'apple_quotation';

    use SoftDeletes;
    /**
     * @var array
     */
    protected $dates = ['deleted_at'];

    use SoftDeletes;
    /**
     * @var string
     */
    protected $primaryKey = 'ID';


    /**
     * @param $quotationArray
     * @return mixed
     * @throws \Exception
     * @throws \Throwable
     */
    function saveQuotation($quotationArray)
    {

        $this->quotationArray = $quotationArray;

        return \DB::transaction(function () {

            // Delete old quote
            if (isset($this->quotationArray['quotation_no']))
                Quotation::where('quotation_no', $this->quotationArray['quotation_no'])->delete();


            $Quotation = new Quotation();

            //set status
            if(!isset($this->quotationArray['save_type'])) {
                // $this->quotationArray['save_type'] = 'save';
            }
            switch ($this->quotationArray['save_type']) {
                case Quotation::CANCEL:
                    $Quotation->status = 3;
                    break;
                case  Quotation::CONFIRM:
                    $Quotation->status = 2;
                    break;
                case  Quotation::SAVE:
                    $Quotation->status = 1;
                    break;
                default:
                    return false;
            }

            #if quotation is cancellation provide quot must be confirmed one
            if ($this->quotationArray['status'] == 3) {
                $Quot = Quotation::withTrashed()
                    ->where('quotation_no', $this->quotationArray['quotation_no'])
                    ->orderBy('ID', 'desc')
                    ->first();


                /* Commented because need to set quotation cancel to panding status*/

                /*if ($Quot->status != 2) {
                    $this->error = "This tour is not a confirmed tour to cancel";
                    return false;
                }*/


            }

            if($this->quotationArray['save_type'] == "confirm") {
                $this->quotationArray = $this->TempToRateQuotationArray($this->quotationArray);
            }

            $quotation_no = Quotation::max('ID') + 1;
            if(!isset($this->quotationArray['quotation_no'])) {
                if (apple_fd_packages_quotation::where('quotation_no', '=', $quotation_no)->exists()) {
                    $Quotation->ID = apple_fd_packages_quotation::max('quotation_no') + 1;
                }
            }

            $Quotation->country = $this->quotationArray['country'];
            $Quotation->currency = $this->quotationArray['currency'] ?? null;
            $Quotation->ch_currency = $this->quotationArray['ch_currency'] ?? null;
            $Quotation->base_currency = $this->quotationArray['base_currency'] ?? null;
            $Quotation->tour_session = $this->quotationArray['tour_session'];
            $Quotation->last_modified = Carbon::now()->timestamp;
            $Quotation->tour_type = $this->quotationArray['tour_type'];
            $Quotation->nationality = $this->quotationArray['nationality'] ?? 62;
            $Quotation->quarantine_type = $this->quotationArray['quarantine_type'] ?? 0;
            $Quotation->sales_tracking_id = $this->quotationArray['sales_tracking_id'] ?? null; // sales tracking id
            $Quotation->save();

            $reference_id = $Quotation->ID;
            $quotation_no = $this->quotationArray['quotation_no'] ?? $reference_id;

            Quotation::where('ID', $reference_id)->update(['quotation_no' => $quotation_no]);

            //Saving
            $this->saveMain($reference_id);
            $this->savePax($reference_id);
            $this->saveChildAge($reference_id);
            $this->saveStatus($reference_id);
            $this->saveAttraction($reference_id);
            $this->saveCityTour($reference_id);
            $this->saveExcursion($reference_id);
            $this->saveHotel($reference_id);
            $this->saveItinerary($reference_id);
            $this->saveOtherRates($reference_id);
            $this->saveLanguages($reference_id);
            $this->saveMeal($reference_id);
            $this->savePlace($reference_id);
            $this->saveTransport($reference_id);
            $this->saveCostCut($reference_id);
            $this->saveDriverAccommodationDetails($reference_id);
            $this->saveTransportTimes($reference_id);
            $this->saveActivities($reference_id);
            
            $update_number = Quotation::withTrashed()->where('quotation_no', $quotation_no)->where('ID', '<=', $reference_id)->count();
            return ['reference_id' => $reference_id, 'quotation_no' => $quotation_no, 'update_number' => $update_number, 'status' => $Quotation->status];

        });

    }

    function TempToRateQuotationArray($quotationArray) {
        $TempRateQuotation = $quotationArray["temp"]??[];
        $RateQuotation = $quotationArray["rate"]??[];

        if(isset($TempRateQuotation) && isset($TempRateQuotation["hotel"])) {
            foreach ($TempRateQuotation["hotel"] as $index => $hotel) {
                if(!(isset($RateQuotation["hotel"]) && isset($RateQuotation["hotel"][$index]))) {
                    $quotationArray["rate"]["hotel"][$index] = $hotel;
                    continue;
                }

                /*if(isset($RateQuotation["hotel"][$index]) && isset($hotel) && is_array($RateQuotation["hotel"][$index]) && is_array($hotel)) {
                    $Hotel1 = array_shift($RateQuotation["hotel"][$index]);
                    $Hotel1 = array_shift($Hotel1);
                    $Hotel1 = array_shift($Hotel1);
                    $Hotel1 = $Hotel1["hotel"];

                    $newHotel = $hotel;
                    $Hotel2 = array_shift($hotel);
                    $Hotel2 = array_shift($Hotel2);
                    $Hotel2 = array_shift($Hotel2);
                    $Hotel2 = $Hotel2["hotel"];

                    if($Hotel1 != $Hotel2) {
                        $quotationArray["rate"]["hotel"][$index] = $newHotel;
                    }
                }*/
            }
        }

        return $quotationArray;
    }

    /**
     * @param $reference_id
     */
    private function saveMain($reference_id)
    {

        $QuotationMain = new QuotationMain;
        $QuotationMain->reference_id = $reference_id;
        // $QuotationMain->user = ($this->quotationArray['user'] ?? Auth::user()->id ?? 1);
        $QuotationMain->user = (Auth::user()->id ?? 1);
        $QuotationMain->arrival_year = $this->quotationArray['arrival_date']['year'];
        $QuotationMain->arrival_month = $this->quotationArray['arrival_date']['month'];
        $QuotationMain->arrival_day = $this->quotationArray['arrival_date']['day'];
        $QuotationMain->show_itinerary = $this->quotationArray['itinerary']['show_itinerary'] ?? "false";
        $QuotationMain->timestamp = Carbon::now()->timestamp;
        $QuotationMain->save();
    }

    /**
     * @param $reference_id
     */
    private function savePax($reference_id)
    {
        //Pax
        $QuotationPax = new QuotationPax;
        $QuotationPax->reference_id = $reference_id;
        $QuotationPax->adult = $this->quotationArray['pax']['adult'];
        $QuotationPax->cwb = $this->quotationArray['pax']['cwb'];
        $QuotationPax->cnb = $this->quotationArray['pax']['cnb'];
        $QuotationPax->infant = $this->quotationArray['pax']['infant'] ?? 0;
        $QuotationPax->market = $this->quotationArray['market'];
        $QuotationPax->visa = 0;
        $QuotationPax->save();

    }

    private function saveChildAge($reference_id)
    {
        if(isset($this->quotationArray['child_age']['cwb'])) {
            foreach ($this->quotationArray['child_age']['cwb'] as $index => $age) {
                $QuotationChildAge = new QuotationChildAge();
                $QuotationChildAge->reference_id = $reference_id;
                $QuotationChildAge->index = $index;
                $QuotationChildAge->type = "cwb";
                $QuotationChildAge->age = $age;
                $QuotationChildAge->save();
            }
        }
        if(isset($this->quotationArray['child_age']['cnb'])) {
            foreach ($this->quotationArray['child_age']['cnb'] as $index => $age) {
                $QuotationChildAge = new QuotationChildAge();
                $QuotationChildAge->reference_id = $reference_id;
                $QuotationChildAge->index = $index;
                $QuotationChildAge->type = "cnb";
                $QuotationChildAge->age = $age;
                $QuotationChildAge->save();
            }
        }
    }

    /**
     * @param $reference_id
     */
    private function saveStatus($reference_id)
    {
        //status
        $QuotationStatus = new QuotationStatus;
        $QuotationStatus->reference_id = $reference_id;
        $QuotationStatus->status = 1;
        $QuotationStatus->additional_email = ($this->quotationArray['additional_emails'] ?? null);
        $QuotationStatus->agent_email = ($this->quotationArray['agent_email'] ?? null);
        $QuotationStatus->agent = ($this->quotationArray['agent'] ?? null);
        $QuotationStatus->sales_person = ($this->quotationArray['sales_person'] ?? null);
        $QuotationStatus->follow_up_email_subject = ($this->quotationArray['follow_up_email_subject'] ?? null);
        $QuotationStatus->note = ($this->quotationArray['note'] ?? "");
        $QuotationStatus->markup_amount = $this->quotationArray['markup_amount'] ?? 0;
        $QuotationStatus->markup_type = $this->quotationArray['markup_type'] ?? 0;
        $QuotationStatus->markup_amount_child = $this->quotationArray['markup_amount_child'] ?? 0;
        $QuotationStatus->markup_type_child = $this->quotationArray['markup_type_child'];
        $QuotationStatus->send_email = ($this->quotationArray['send_email']??null);
        $QuotationStatus->hotel_filter_type = ($this->quotationArray['hotel_filter']['return_type']??null);
        $QuotationStatus->hotel_filter_star = ($this->quotationArray['hotel_filter']['return_star']??null);
        $QuotationStatus->save();
    }

    /**
     * @param $reference_id
     */
    private function saveAttraction($reference_id)
    {
        $i = 0;
        if (isset($this->quotationArray['attraction'])) {
//            $AttractionCost = Attraction::getAttraction($this->quotationArray, true);
            $AttractionCost = Attraction::getAttraction($this->quotationArray);

            foreach ($this->quotationArray['attraction'] as $Day => $AttractionList) {
                foreach ($AttractionList as $AttractionID) {
                    $QuotationAttraction = new QuotationAttraction;
                    $QuotationAttraction->reference_id = $reference_id;
                    $QuotationAttraction->attraction = $AttractionID;
                    $QuotationAttraction->day = $Day;
                    // $QuotationAttraction->time = $this->quotationArray['attraction_time'][$i]['time'] ?? null;
                    $QuotationAttraction->start_time = $this->quotationArray['time']['attraction']['attraction'][$AttractionID]['start'] ?? null;
                    $QuotationAttraction->end_time = $this->quotationArray['time']['attraction']['attraction'][$AttractionID]['end'] ?? null;
                    $QuotationAttraction->save();

                    //attraction  Rate
                    $AttractionRate = new QuotationAttractionRate();
                    $AttractionRate->attraction = $QuotationAttraction->id;
                    $AttractionRate->market = $this->quotationArray['market'];
                    $AttractionRate->adult = $AttractionCost[$AttractionID]['adult'] ?? 0;
                    $AttractionRate->child = (isset($AttractionCost[$AttractionID]['child']) ? $AttractionCost[$AttractionID]['child'] : null);
                    $AttractionRate->modified_adult = $AttractionCost[$AttractionID]['adult'] ?? 0;
                    $AttractionRate->modified_child = $AttractionCost[$AttractionID]['adult'] ?? 0;
                    $AttractionRate->reason = '';
                    $AttractionRate->is_modified = false;
                    $AttractionRate->save();
                    $i++;
                }
            }
        }
    }

    /**
     * @param $reference_id
     */
    private function saveCityTour($reference_id)
    {
        $i = 0;
        if (isset($this->quotationArray['city_tour'])) {
//            $CityTourCost = CityTour::getCityTour($this->quotationArray, true);
            $CityTourCost = CityTour::getCityTour($this->quotationArray);
            foreach ($this->quotationArray['city_tour'] as $Day => $CityTourList) {
                foreach ($CityTourList as $CityTourID) {
                    $QuotationCityTour = new QuotationCityTour();
                    $QuotationCityTour->reference_id = $reference_id;
                    $QuotationCityTour->city_tour = $CityTourID;
                    $QuotationCityTour->day = $Day;
                    // $QuotationCityTour->time = $this->quotationArray['city_tour_time'][$i]['time'] ?? null;
                    $QuotationCityTour->start_time = $this->quotationArray['time']['attraction']['city_tour'][$CityTourID]['start'] ?? null;
                    $QuotationCityTour->end_time = $this->quotationArray['time']['attraction']['city_tour'][$CityTourID]['end'] ?? null;
                    $QuotationCityTour->save();

                    //city_tour  Rate
                    $CityTourRate = new QuotationCityTourRate();
                    $CityTourRate->city_tour = $QuotationCityTour->id;
                    $CityTourRate->market = $this->quotationArray['market'];
                    $CityTourRate->adult = $CityTourCost[$CityTourID]['adult'] ?? 0;
                    $CityTourRate->child = (isset($CityTourCost[$CityTourID]['child']) ? $CityTourCost[$CityTourID]['child'] : null);
                    $CityTourRate->modified_adult = $CityTourCost[$CityTourID]['adult'] ?? 0;
                    $CityTourRate->modified_child = $CityTourCost[$CityTourID]['adult'] ?? 0;
                    $CityTourRate->reason = '';
                    $CityTourRate->is_modified = false;
                    $CityTourRate->save();

                }
            }
        }
    }

    /**
     * @param $reference_id
     */
    private function saveExcursion($reference_id)
    {
        $i = 0;
        if (isset($this->quotationArray['excursion'])) {
//            $ExcursionCost = Excursion::getExcursion($this->quotationArray, true);
            $ExcursionCost = Excursion::getExcursion($this->quotationArray);
            foreach ($this->quotationArray['excursion'] as $Day => $ExcursionList) {
                foreach ($ExcursionList as $ExcursionID) {
                    $QuotationExcursion = new QuotationExcursion;
                    $QuotationExcursion->reference_id = $reference_id;
                    $QuotationExcursion->Excursion = $ExcursionID;
                    $QuotationExcursion->day = $Day;
                    // $QuotationExcursion->time = $this->quotationArray['excursion_time'][$i]['time'] ?? null;
                    $QuotationExcursion->start_time = $this->quotationArray['time']['attraction']['excursion'][$ExcursionID]['start'] ?? null;
                    $QuotationExcursion->end_time = $this->quotationArray['time']['attraction']['excursion'][$ExcursionID]['end'] ?? null;
                    $QuotationExcursion->save();

                    //Excursion  Rate
                    $ExcursionRate = new QuotationExcursionRate();
                    $ExcursionRate->excursion = $QuotationExcursion->id;
                    $ExcursionRate->market = $this->quotationArray['market'];
                    $ExcursionRate->adult = $ExcursionCost[$ExcursionID]['adult'];
                    $ExcursionRate->child = (isset($ExcursionCost[$ExcursionID]['child']) ? $ExcursionCost[$ExcursionID]['child'] : null);
                    $ExcursionRate->modified_adult = $ExcursionCost[$ExcursionID]['adult'] ?? 0;
                    $ExcursionRate->modified_child = $ExcursionCost[$ExcursionID]['adult'] ?? 0;
                    $ExcursionRate->reason = '';
                    $ExcursionRate->is_modified = false;
                    $ExcursionRate->save();
                }
            }
        }
    }

    /**
     * @param $reference_id
     */
    private function saveHotel($reference_id)
    {
        if (isset($this->quotationArray['hotel'])) {
            foreach ($this->quotationArray['hotel'] as $Index => $HotelSetting) {
                if ($this->quotationArray['accommodation'][$Index] == 1) {
                    if ($HotelSetting['provider'] === Hotel::PROVIDER_LOCAL || $HotelSetting['provider'] === Hotel::PROVIDER_LOCAL_SPECIAL) {
                        $QuotationHotel = new QuotationHotel();
                        $QuotationHotel->reference_id = $reference_id;
                        $QuotationHotel->extra_bed = $HotelSetting['extrabed'];
                        $QuotationHotel->room_category = $HotelSetting['room_category'];
                        $QuotationHotel->hotel = $HotelSetting['hotel'];
                        $QuotationHotel->meal = $HotelSetting['meal_type'];
                        $QuotationHotel->index = $Index;
                        $QuotationHotel->check_in_year = $HotelSetting['check_in']['year'];
                        $QuotationHotel->check_in_month = $HotelSetting['check_in']['month'];
                        $QuotationHotel->check_in_day = $HotelSetting['check_in']['day'];
                        $QuotationHotel->check_out_year = $HotelSetting['check_out']['year'];
                        $QuotationHotel->check_out_month = $HotelSetting['check_out']['month'];
                        $QuotationHotel->check_out_day = $HotelSetting['check_out']['day'];
                        $QuotationHotel->provider = $HotelSetting['provider'];
                        $QuotationHotel->driver_accommodation = $HotelSetting['driver_accommodation'];
                        $QuotationHotel->save();
                        //Hotel rooms  counts
                        foreach ($HotelSetting['room_type'] as $RoomType => $RoomCount) {
                            if (empty($RoomCount))//no rates
                                continue;

                            $QuotationHotelRoom = new QuotationHotelRoom;
                            $QuotationHotelRoom->quotation_hotel = $QuotationHotel->id;
                            $QuotationHotelRoom->room_type = $RoomType;
                            $QuotationHotelRoom->room_count = $RoomCount;
                            $QuotationHotelRoom->save();
                        }

                        $this->saveHotelRatesAdult($Index, $QuotationHotel->id);
                        $this->saveHotelRatesTransfer($Index, $QuotationHotel->id);

                    } elseif ($HotelSetting['provider'] === Hotel::PROVIDER_HOTELBEDS) {

                        $QuotationHotelBeds = new QuotationHotelBeds();

                        $QuotationHotelBeds->reference_id = $reference_id;
                        $QuotationHotelBeds->extra_bed = $HotelSetting['extrabed'] ?? 0;
                        $QuotationHotelBeds->room_category = $HotelSetting['room_category'];
                        $QuotationHotelBeds->hotel = $HotelSetting['hotel'];
                        $QuotationHotelBeds->meal = $HotelSetting['meal_type'];
                        $QuotationHotelBeds->index = $Index;
                        $QuotationHotelBeds->check_in_year = $HotelSetting['check_in']['year'];
                        $QuotationHotelBeds->check_in_month = $HotelSetting['check_in']['month'];
                        $QuotationHotelBeds->check_in_day = $HotelSetting['check_in']['day'];
                        $QuotationHotelBeds->check_out_year = $HotelSetting['check_out']['year'];
                        $QuotationHotelBeds->check_out_month = $HotelSetting['check_out']['month'];
                        $QuotationHotelBeds->check_out_day = $HotelSetting['check_out']['day'];
                        $QuotationHotelBeds->save();
                        $this->saveHotelBedsRatesAdult($Index, $QuotationHotelBeds->id,$reference_id);
                    }
                } elseif ($this->quotationArray['accommodation'][$Index] == 2) {//if own arrangement
                    $QuotationHotel = new QuotationHotel();
                    $QuotationHotel->reference_id = $reference_id;
                    $QuotationHotel->extra_bed = null;
                    $QuotationHotel->room_category = null;
                    $QuotationHotel->hotel = null;
                    $QuotationHotel->meal = null;
                    $QuotationHotel->check_in_year = $HotelSetting['check_in']['year'];
                    $QuotationHotel->check_in_month = $HotelSetting['check_in']['month'];
                    $QuotationHotel->check_in_day = $HotelSetting['check_in']['day'];
                    $QuotationHotel->check_out_year = $HotelSetting['check_out']['year'];
                    $QuotationHotel->check_out_month = $HotelSetting['check_out']['month'];
                    $QuotationHotel->check_out_day = $HotelSetting['check_out']['day'];
                    $QuotationHotel->driver_accommodation = $HotelSetting['driver_accommodation'];
                    $QuotationHotel->save();
                    continue;
                }
            }
        }

        if (isset($this->quotationArray['cruise'])) {
            foreach ($this->quotationArray['cruise'] as $Index => $CruiseSetting) {
                if ($this->quotationArray['accommodation'][$Index] == 4) {
                    $QuotationCruise = new QuotationCruise();
                    $QuotationCruise->reference_id = $reference_id;
                    $QuotationCruise->extra_bed = $CruiseSetting['extrabed'];
                    $QuotationCruise->cabin_type = $CruiseSetting['cabin_type'];
                    $QuotationCruise->cruise = $CruiseSetting['cruise'];
                    $QuotationCruise->package = $CruiseSetting['package'];
                    $QuotationCruise->nights = $CruiseSetting['nights'];
                    $QuotationCruise->meal = $CruiseSetting['meal_type'];
                    $QuotationCruise->index = $Index;
                    $QuotationCruise->check_in_year = $CruiseSetting['check_in']['year'];
                    $QuotationCruise->check_in_month = $CruiseSetting['check_in']['month'];
                    $QuotationCruise->check_in_day = $CruiseSetting['check_in']['day'];
                    $QuotationCruise->check_out_year = $CruiseSetting['check_out']['year'];
                    $QuotationCruise->check_out_month = $CruiseSetting['check_out']['month'];
                    $QuotationCruise->check_out_day = $CruiseSetting['check_out']['day'];
                    $QuotationCruise->save();
                    //Hotel rooms  counts
                    foreach ($CruiseSetting['cabin_occupancy_type'] as $RoomType => $RoomCount) {
                        if (empty($RoomCount))//no rates
                            continue;

                        $QuotationCruiseRoom = new QuotationCruiseCabin();
                        $QuotationCruiseRoom->quotation_cruise = $QuotationCruise->id;
                        $QuotationCruiseRoom->cabin_occupancy_type = $RoomType;
                        $QuotationCruiseRoom->room_count = $RoomCount;
                        $QuotationCruiseRoom->save();
                    }
                    $this->saveCruiseRatesAdult($Index, $QuotationCruise->id);
                }
            }
        }
    }

    /**
     * @param $hotelIndex
     * @param $hotelSaveID
     */
    private function saveHotelBedsRatesAdult($hotelIndex, $hotelSaveID,$reference_id)
    {
        $RateArray = $this->quotationArray['api']['hotelbeds']['hotel'][$hotelIndex]['selected'];
        $HotelItem = $this->quotationArray['hotel'][$hotelIndex];
        $ArrayList = HotelBed::getHotelCost($HotelItem, collect($RateArray));

        foreach (($ArrayList['adult'] ?? []) as $RateItemArray) {
            foreach ($RateItemArray as $RateItem) {//one hotel can me
                $HotelBedsRateKeys = new QuotationHotelBedsRateKeys();
                $HotelBedsRateKeys->reference_id = $reference_id;
                $HotelBedsRateKeys->quotation_hotel = $hotelSaveID;
                $HotelBedsRateKeys->adult = $RateItem['adult'];
                $HotelBedsRateKeys->child = $RateItem['child'];
                $HotelBedsRateKeys->rooms = $RateItem['rooms'];
                $HotelBedsRateKeys->room_category = $RateItem['category'];
                $HotelBedsRateKeys->meal = $RateItem['meal'];
                $HotelBedsRateKeys->rate = $RateItem['net'];
                $HotelBedsRateKeys->bookday_index = $hotelIndex;
                $HotelBedsRateKeys->rate_key = $RateItem['rateKey'];
                $HotelBedsRateKeysObj =  QuotationHotelBedsRateKeys::where('rate_key',$RateItem['rateKey'])->orderby('ID','DESC')->first();
                if(($HotelBedsRateKeysObj) && $HotelBedsRateKeysObj->status == 1) {
                    $HotelBedsRateKeys->status = 1;
                    $quotation_hotel = QuotationHotelbedsVouchers::where('rate_key',$RateItem['rateKey'])->orderby('ID','DESC')->first();
                    if ($quotation_hotel) {
                        $QuotationHotelbedsVouchers = new QuotationHotelbedsVouchers();
                        $QuotationHotelbedsVouchers->quotation_hotel = $quotation_hotel->ID;
                        $QuotationHotelbedsVouchers->reference_id = $reference_id;
                        $QuotationHotelbedsVouchers->status = 1;
                        $QuotationHotelbedsVouchers->rate_key = $RateItem['rateKey'];
                        $QuotationHotelbedsVouchers->json = $quotation_hotel->json;
                        $QuotationHotelbedsVouchers->save();
                    }
                } else {
                    $HotelBedsRateKeys->status = 0;
                }
                $HotelBedsRateKeys->save();
            }
        }
    }


    /**
     * @param $hotelIndex
     * @param $hotelSaveID
     */
    private function saveHotelRatesAdult($hotelIndex, $hotelSaveID)
    {

        foreach (($this->quotationArray['rate']['hotel'][$hotelIndex]['adult'] ?? []) as $bookday_index => $RateArray) {

            if (empty($RateArray))//Rate Array is Empty
                continue;

            foreach ($RateArray as $RoomType => $RateRow) {

                if (isset($RateRow['is_modified']))#If rate is rate is change
                    $Bookdate = Carbon::create($RateRow['year'], $RateRow['month'], $RateRow['day']);
                else#If rate come from the database
                    $Bookdate = Carbon::create($RateRow['year'], $RateRow['month'], $RateRow['day']);

                $QuotationHotelRate = new QuotationHotelRate;

                $QuotationHotelRate->quotation_hotel = $hotelSaveID;
                $QuotationHotelRate->rate_id = isset($RateRow->id) ? $RateRow->id : false;
                $QuotationHotelRate->hotel_id = $RateRow['hotel'];
                $QuotationHotelRate->meal = $RateRow['meal'];
                $QuotationHotelRate->room_type = $RoomType;
                $QuotationHotelRate->room_category = $RateRow['room_category'];
                $QuotationHotelRate->rate = $RateRow['rate'];

                $QuotationHotelRate->year = $Bookdate->year;
                $QuotationHotelRate->month = $Bookdate->month;
                $QuotationHotelRate->day = $Bookdate->day;

                $QuotationHotelRate->bookday_index = $bookday_index;

                if (empty($RateRow['is_modified'])) {
                    $QuotationHotelRate->modified_rate = '';
                    $QuotationHotelRate->reason = '';
                    $QuotationHotelRate->is_modified = false;
                } else {
                    $QuotationHotelRate->modified_rate = $RateRow['modified_rate'];
                    $QuotationHotelRate->reason = $RateRow['reason'];
                    $QuotationHotelRate->is_modified = true;
                }

                $QuotationHotelRate->save();
            }
        }

        if (isset($QuotationHotelRate->id))
            $this->saveHotelRatesChild($hotelIndex, $QuotationHotelRate->id);

            if (isset($QuotationHotelRate->id))
            $this->saveDriverAccommodationRates($hotelIndex, $QuotationHotelRate->id);
    }

    /**
     * @param $hotelIndex
     * @param $hotelSaveID
     */
    private function saveCruiseRatesAdult($CruiseIndex, $CruiseSaveID)
    {
        foreach (($this->quotationArray['rate']['cruise'][$CruiseIndex]['adult'] ?? []) as $bookday_index => $RateArray) {

            if (empty($RateArray))//Rate Array is Empty
                continue;

            foreach ($RateArray as $RoomType => $RateRow) {

                if (isset($RateRow['is_modified']))#If rate is rate is change
                    $Bookdate = Carbon::create($RateRow['year'], $RateRow['month'], $RateRow['day']);
                else#If rate come from the database
                    $Bookdate = Carbon::create($RateRow['start_year'], $RateRow['start_month'], $RateRow['start_day']);

                $QuotationCruiseRate = new QuotationCruiseRate();
                $QuotationCruiseRate->quotation_cruise = $CruiseSaveID;
                $QuotationCruiseRate->rate_id = isset($RateRow->id) ? $RateRow->id : false;
                $QuotationCruiseRate->meal = $RateRow['meal'];
                $QuotationCruiseRate->cabin_occupancy_type = $RoomType;
                $QuotationCruiseRate->cabin_type = $RateRow['cabin_type'];
                $QuotationCruiseRate->rate = $RateRow['rate'];

                $QuotationCruiseRate->year = $Bookdate->year;
                $QuotationCruiseRate->month = $Bookdate->month;
                $QuotationCruiseRate->day = $Bookdate->day;

                $QuotationCruiseRate->bookday_index = $bookday_index;


                if (empty($RateRow['is_modified'])) {
                    $QuotationCruiseRate->modified_rate = '';
                    $QuotationCruiseRate->reason = '';
                    $QuotationCruiseRate->is_modified = false;
                } else {
                    $QuotationCruiseRate->modified_rate = $RateRow['modified_rate'];
                    $QuotationCruiseRate->reason = $RateRow['reason'];
                    $QuotationCruiseRate->is_modified = true;
                }

                $QuotationCruiseRate->save();
            }
        }

        if (isset($QuotationCruiseRate->id))
            $this->saveCruiseRatesChild($CruiseIndex, $QuotationCruiseRate->id);
    }

    /**
     * @param $hotelIndex
     * @param $hotelRateSaveID
     */
    private
    function saveHotelRatesChild($hotelIndex, $hotelRateSaveID)
    {
        foreach (($this->quotationArray['rate']['hotel'][$hotelIndex]['child'] ?? []) as $ChildType => $rateArray) {

            switch ($ChildType) {
                case Quotation::CWB:
                    foreach ($rateArray as $RateRow) {

                        if (empty($RateRow)) continue;

                        $Bookdate = Carbon::create($RateRow['year'] ?? $RateRow['year'], $RateRow['month'] ?? $RateRow['month'], $RateRow['day'] ?? $RateRow['day']);

                        $HotelRateChild = new QuotationHotelRateChild();
                        $HotelRateChild->quotation_hotel_rate_id = $hotelRateSaveID;
                        $HotelRateChild->rate = $RateRow['rate'];
                        $HotelRateChild->year = $Bookdate->year;
                        $HotelRateChild->month = $Bookdate->month;
                        $HotelRateChild->day = $Bookdate->day;
                        $HotelRateChild->age_from = 2;
                        $HotelRateChild->age_to = 12;

                        if (empty($RateRow['is_modified'])) {
                            $HotelRateChild->modified_rate = '';
                            $HotelRateChild->reason = '';
                            $HotelRateChild->is_modified = false;
                        } else {
                            $HotelRateChild->modified_rate = $RateRow['modified_rate'];
                            $HotelRateChild->reason = $RateRow['reason'];
                            $HotelRateChild->is_modified = true;
                        }

                        $HotelRateChild->save();
                    }
                    break;
                case Quotation::CNB:
                    foreach ($rateArray as $RateRow) {
                        if (empty($RateRow)) continue;

                        $Bookdate = Carbon::create($RateRow['year'] ?? $RateRow['year'], $RateRow['month'] ?? $RateRow['month'], $RateRow['day'] ?? $RateRow['day']);

                        $HotelRateChild = new QuotationHotelRateChild;
                        $HotelRateChild->quotation_hotel_rate_id = $hotelRateSaveID;
                        $HotelRateChild->rate = $RateRow['rate'];
                        $HotelRateChild->year = $Bookdate->year;
                        $HotelRateChild->month = $Bookdate->month;
                        $HotelRateChild->day = $Bookdate->day;
                        $HotelRateChild->age_from = 0;
                        $HotelRateChild->age_to = 2;

                        if (empty($RateRow['is_modified'])) {
                            $HotelRateChild->modified_rate = '';
                            $HotelRateChild->reason = '';
                            $HotelRateChild->is_modified = false;
                        } else {
                            $HotelRateChild->modified_rate = $RateRow['modified_rate'];
                            $HotelRateChild->reason = "";
                            $HotelRateChild->is_modified = true;
                        }

                        $HotelRateChild->save();
                    }
                    break;
            }

        }
    }

        /**
     * @param $hotelIndex
     * @param $hotelRateSaveID
     */
    private
    function saveDriverAccommodationRates($hotelIndex, $hotelRateSaveID)
    {
        foreach (($this->quotationArray['rate']['hotel'][$hotelIndex]['driver_accommodation'] ?? []) as $bookday_index => $RateRow) {

            if (empty($RateRow)) continue;

            $Bookdate = Carbon::create($RateRow['year'] ?? $RateRow['year'], $RateRow['month'] ?? $RateRow['month'], $RateRow['day'] ?? $RateRow['day']);

            $HotelDriverAccommodationRate = new QuotationHotelDriverAccommodationRate();
            $HotelDriverAccommodationRate->quotation_hotel_rate_id = $hotelRateSaveID;
            $HotelDriverAccommodationRate->rate = $RateRow['rate'];
            $HotelDriverAccommodationRate->year = $Bookdate->year;
            $HotelDriverAccommodationRate->month = $Bookdate->month;
            $HotelDriverAccommodationRate->day = $Bookdate->day;

            if (empty($RateRow['is_modified'])) {
                $HotelDriverAccommodationRate->modified_rate = '';
                $HotelDriverAccommodationRate->reason = '';
                $HotelDriverAccommodationRate->is_modified = false;
            } else {
                $HotelDriverAccommodationRate->modified_rate = $RateRow['modified_rate'];
                $HotelDriverAccommodationRate->reason = $RateRow['reason'];
                $HotelDriverAccommodationRate->is_modified = true;
            }

            $HotelDriverAccommodationRate->save();

        }
    }

    /**
     * @param $hotelIndex
     * @param $hotelRateSaveID
     */
    private
    function saveCruiseRatesChild($CruiseIndex, $CruiseRateSaveID)
    {
        foreach (($this->quotationArray['rate']['cruise'][$CruiseIndex]['child'] ?? []) as $ChildType => $rateArray) {

            switch ($ChildType) {
                case Quotation::CWB:
                    foreach ($rateArray as $RateRow) {

                        if (empty($RateRow)) continue;

                        $Bookdate = Carbon::create($RateRow['year'] ?? $RateRow['start_year'], $RateRow['month'] ?? $RateRow['start_month'], $RateRow['day'] ?? $RateRow['start_day']);

                        $CruiseRateChild = new QuotationCruiseRateChild();
                        $CruiseRateChild->quotation_cruise_rate_id = $CruiseRateSaveID;
                        $CruiseRateChild->rate = $RateRow['rateChild'];
                        $CruiseRateChild->year = $Bookdate->year;
                        $CruiseRateChild->month = $Bookdate->month;
                        $CruiseRateChild->day = $Bookdate->day;
                        $CruiseRateChild->age_from = 2;
                        $CruiseRateChild->age_to = 12;

                        if (empty($RateRow['is_modified'])) {
                            $CruiseRateChild->modified_rate = '';
                            $CruiseRateChild->reason = '';
                            $CruiseRateChild->is_modified = false;
                        } else {
                            $CruiseRateChild->modified_rate = $RateRow['modified_rate'];
                            $CruiseRateChild->reason = $RateRow['reason'];
                            $CruiseRateChild->is_modified = true;
                        }

                        $CruiseRateChild->save();
                    }
                    break;
                case Quotation::CNB:
                    foreach ($rateArray as $RateRow) {
                        if (empty($RateRow)) continue;

                        $Bookdate = Carbon::create($RateRow['year'] ?? $RateRow['start_year'], $RateRow['month'] ?? $RateRow['start_month'], $RateRow['day'] ?? $RateRow['start_day']);

                        $CruiseRateChild = new QuotationCruiseRateChild;
                        $CruiseRateChild->quotation_cruise_rate_id = $CruiseRateSaveID;
                        $CruiseRateChild->rate = $RateRow['rateChild'];
                        $CruiseRateChild->year = $Bookdate->year;
                        $CruiseRateChild->month = $Bookdate->month;
                        $CruiseRateChild->day = $Bookdate->day;
                        $CruiseRateChild->age_from = 0;
                        $CruiseRateChild->age_to = 2;

                        if (empty($RateRow['is_modified'])) {
                            $CruiseRateChild->modified_rate = '';
                            $CruiseRateChild->reason = '';
                            $CruiseRateChild->is_modified = false;
                        } else {
                            $CruiseRateChild->modified_rate = $RateRow['modified_rate'];
                            $CruiseRateChild->reason = "";
                            $CruiseRateChild->is_modified = true;
                        }

                        $CruiseRateChild->save();
                    }
                    break;
            }

        }
    }

    /**
     * @param $hotelIndex
     * @param $hotelRateSaveID
     */
    private
    function saveHotelRatesTransfer($hotelIndex, $hotelRateSaveID)
    {
        // dd($this->quotationArray);
        //hotel transfer rates
        $TransferCostList = HotelTransportRate::getHotelTransportRate($this->quotationArray);
        if(isset($TransferCostList['hotel'])) {
            if (($TransferCostList['hotel'][$hotelIndex]['to_hotel'] && $TransferCostList['hotel'][$hotelIndex]['from_hotel']) || isset($this->quotationArray['rate']['hotel_transfer'][$hotelIndex])) {
                $HotelTransfer = new QuotationHotelTransfer();
                $HotelTransfer->quotation_hotel = $hotelRateSaveID;
                $HotelTransfer->rate_id = null;
                $HotelTransfer->adult = $TransferCostList['hotel'][$hotelIndex]['to_hotel']->adult;
                $HotelTransfer->child = $TransferCostList['hotel'][$hotelIndex]['to_hotel']->child;
                $HotelTransfer->modified_adult_rate = $this->quotationArray['rate']['transport']['hotel_transfer'][$hotelIndex]['adult'] ?? 0;
                $HotelTransfer->modified_child_rate = $this->quotationArray['rate']['transport']['hotel_transfer'][$hotelIndex]['child'] ?? 0;
                $HotelTransfer->reason = isset($this->quotationArray['rate']['hotel_transfer'][$hotelIndex]['reason']) ?? "";
                $HotelTransfer->is_modified = isset($this->quotationArray['rate']['hotel_transfer'][$hotelIndex]) ? 1 : 0;
                $HotelTransfer->save();
            }
        }
    }

    /**
     * @param $reference_id
     */
    private function saveItinerary($reference_id)
    {
        foreach (($this->quotationArray['itinerary']['days'] ?? []) as $Day => $DayItem) {

            //get attraction
            foreach (($DayItem['attraction'] ?? []) as $AttractionID => $AttractionText) {
                $ItineraryAttraction = new QuotationItineraryAttraction();
                $ItineraryAttraction->reference_id = $reference_id;
                $ItineraryAttraction->attraction = $AttractionID;
                $ItineraryAttraction->day = $Day;
                $ItineraryAttraction->text = !empty($AttractionText) ? $AttractionText : null;
                $ItineraryAttraction->save();
            }

            //get city tour
            foreach (($DayItem['excursion'] ?? []) as $ExcursionID => $ExcursionText) {
                $ItineraryExcursion = new QuotationItineraryExcursion();
                $ItineraryExcursion->reference_id = $reference_id;
                $ItineraryExcursion->excursion = $ExcursionID;
                $ItineraryExcursion->day = $Day;
                $ItineraryExcursion->text = !empty($ExcursionText) ? $ExcursionText : null;
                $ItineraryExcursion->save();
            }

            //get excursion
            foreach (($DayItem['city_tour'] ?? []) as $CityTourID => $CityTourText) {
                $ItineraryCityTour = new QuotationItineraryCityTour();
                $ItineraryCityTour->reference_id = $reference_id;
                $ItineraryCityTour->city_tour = $CityTourID;
                $ItineraryCityTour->day = $Day;
                $ItineraryCityTour->text = !empty($CityTourText) ? $CityTourText : null;
                $ItineraryCityTour->save();
            }

            //day header
            $QuotationItinerary = new QuotationItinerary();
            $QuotationItinerary->reference_id = $reference_id;
            $QuotationItinerary->day = $Day;
            $QuotationItinerary->header = isset($DayItem['day_header']) ? $DayItem['day_header'] : null;
            $QuotationItinerary->day_text = isset($DayItem['day_text']) ? $DayItem['day_text'] : null;
            $QuotationItinerary->save();
        }
    }

    /**
     * @param $reference_id
     */
    private
    function saveOtherRates($reference_id)
    {
        foreach (($this->quotationArray['other_rate'] ?? []) as $Item) {

            $QuotationOtherRate = new QuotationOtherRate();
            $QuotationOtherRate->reference_id = $reference_id;
            $QuotationOtherRate->text = $Item['text'];
            $QuotationOtherRate->type = $Item['type'];
            $QuotationOtherRate->rate = $Item['rate'];
            $QuotationOtherRate->cwb_rate = $Item['cwb_rate'];
            $QuotationOtherRate->cnb_rate = $Item['cnb_rate'];
            $QuotationOtherRate->rate_type = $Item['rate_type'];
            $QuotationOtherRate->save();

            if ($QuotationOtherRate->type == 1) {//attraction

                $QuotationOtherRateSightseeing = new QuotationOtherRateSightseeing();
                $QuotationOtherRateSightseeing->other_rate_id = $QuotationOtherRate->id;
                $QuotationOtherRateSightseeing->day = $Item['details']['attraction_day'];
                $QuotationOtherRateSightseeing->description = $Item['details']['attraction_description'];
                $QuotationOtherRateSightseeing->save();
            }

            if ($Item['type'] === '3' && isset($Item['details']['hotel_id'])) {//day use

                $QuotationDayUse= new QuotationDayUse();
                $QuotationDayUse->reference_id = $reference_id;
                $QuotationDayUse->hotel_id = $Item['details']['hotel_id'];
                $QuotationDayUse->meal_preference = $Item['details']['meal_preference'];
                $QuotationDayUse->date = $Item['details']['date'];
                $QuotationDayUse->single_bed_rate = $Item['details']['single_bed_rate'];
                $QuotationDayUse->double_bed_rate = $Item['details']['double_bed_rate'];
                $QuotationDayUse->triple_bed_rate = $Item['details']['triple_bed_rate'];
                $QuotationDayUse->single_bed_room_count = $Item['details']['single_bed_room_count'];
                $QuotationDayUse->double_bed_room_count = $Item['details']['double_bed_room_count'];
                $QuotationDayUse->triple_bed_room_count = $Item['details']['triple_bed_room_count'];
                $QuotationDayUse->save();

            }


        }
    }

    /**
     * @param $reference_id
     */
    private
    function saveLanguages($reference_id)
    {
        foreach ((empty($this->quotationArray['languages']) ? [] : $this->quotationArray['languages']) as $LanguageID) {
            $QuotationLanguage = new QuotationLanguage;
            $QuotationLanguage->reference_id = $reference_id;
            $QuotationLanguage->language = $LanguageID;
            $QuotationLanguage->save();
        }
    }

    /**
     * @param $reference_id
     */
    private
    function saveMeal($reference_id)
    {
        foreach (($this->quotationArray['meal'] ?? []) as $Day => $MealTime) {

            foreach ($MealTime as $FoodTime => $FoodArray) {

                $QuotationMeal = new QuotationMeal;

                $QuotationMeal->reference_id = $reference_id;
                $QuotationMeal->day = $Day;
                $QuotationMeal->food_time = $FoodTime;
                $QuotationMeal->meal = $FoodArray['meal_where'];
                $QuotationMeal->transport = $FoodArray['transport'] ?? 0;
                $QuotationMeal->restaurant = $FoodArray['restaurant'] ?? null;
                $QuotationMeal->preference = $FoodArray['meal_preference'] ?? null;
                $QuotationMeal->save();

                //meal Rate
                if (isset($this->quotationArray['rate']['meal'][$Day][$FoodTime])) {

                    $QuotationMealRate = new QuotationMealRate;
                    $QuotationMealRate->meal = $QuotationMeal->id;
                    $QuotationMealRate->rate = $this->quotationArray['rate']['meal'][$Day][$FoodTime];
                    $QuotationMealRate->child_rate = $this->quotationArray['rate']['meal_child'][$Day][$FoodTime];
                    $QuotationMealRate->reason = '';
                    $QuotationMealRate->is_modified = false;

                    $QuotationMealRate->save();

                }

            }

        }
    }

    /**
     * @param $reference_id
     */
    private
    function savePlace($reference_id)
    {
        foreach ($this->quotationArray['place_full'] as $Order => $PlaceID) {
            $QuotationPlace = new QuotationPlace;

            $QuotationPlace->reference_id = $reference_id;
            $QuotationPlace->place = $PlaceID;
            $QuotationPlace->order = $Order;
            $QuotationPlace->type = $this->quotationArray['place_type'][$Order];

            $QuotationPlace->save();

        }

        if(isset($this->quotationArray['place_vehicle'])) {
            foreach ($this->quotationArray['place_vehicle'] as $Order => $VehicleID) {
                $QuotationPlaceVehicle = new QuotationPlaceVehicle();

                $QuotationPlaceVehicle->reference_id = $reference_id;
                $QuotationPlaceVehicle->vehicle = $VehicleID;
                $QuotationPlaceVehicle->order = $Order;

                $QuotationPlaceVehicle->save();

            }
        }

        foreach (($this->quotationArray['night'] ?? []) as $NoNight) {
            $QuotationNight = new QuotationNight();

            $QuotationNight->reference_id = $reference_id;
            $QuotationNight->night = $NoNight;
            $QuotationNight->save();

        }
    }

    /**
     * @param $reference_id
     */
    private
    function saveTransport($reference_id)
    {

        if (isset($this->quotationArray['rate']['transport'])) {

            $QuotationTransport = new QuotationTransport;
            $QuotationTransport->reference_id = $reference_id;
            $QuotationTransport->vehicle_type = $this->quotationArray['rate']['transport']['vehicle']['vehicle_type'];
            $QuotationTransport->rate = $this->quotationArray['rate']['transport']['vehicle']['rate'] ?? 0;
            $QuotationTransport->total_rate = $this->quotationArray['rate']['transport']['rates']['total'] ?? 0;
            $QuotationTransport->adult_rate = $this->quotationArray['rate']['transport']['rates']['adult_rate'] ?? 0;
            $QuotationTransport->child_rate = $this->quotationArray['rate']['transport']['rates']['child_rate'] ?? 0;
            $QuotationTransport->bata = $this->quotationArray['rate']['transport']['vehicle']['bata'] ?? 0;
            $QuotationTransport->paging = $this->quotationArray['rate']['transport']['vehicle']['paging'] ?? 0;
            $QuotationTransport->highway_charges = $this->quotationArray['rate']['transport']['vehicle']['highway_charges'] ?? 0;
            $QuotationTransport->driver_accommodation = $this->quotationArray['rate']['transport']['vehicle']['driver_accommodation'] ?? 0;
            $QuotationTransport->actual_distance = $this->quotationArray['rate']['transport']['mileage']['actual_distance'] ?? 0;
            $QuotationTransport->additional_distance = $this->quotationArray['rate']['transport']['mileage']['additional_distance'] ?? 0;
            $QuotationTransport->per_water_bottle = $this->quotationArray['rate']['transport']['per_water_bottle'] ?? 0;
            $QuotationTransport->reason = '';
            $QuotationTransport->is_modified = false;
            $QuotationTransport->save();
        }

        foreach (($this->quotationArray['guide'] ?? []) as $GuideItem) {

            $QuotationGuide = new QuotationGuide();
            $QuotationGuide->reference_id = $reference_id;
            $QuotationGuide->type = $GuideItem['type'];
            $QuotationGuide->rate = $GuideItem['rate'];
            $QuotationGuide->accommodation = $GuideItem['accommodation'];
            $QuotationGuide->tip = $GuideItem['tip'];
            $QuotationGuide->save();
        }
    }

    private
    function saveCostCut($reference_id) {
        if (isset($this->quotationArray['cost_cutting'])) {
            foreach ($this->quotationArray['cost_cutting'] as $day => $costData) {
                if(isset($costData)) {
                    foreach ($costData as $ID => $apply) {
                        $QuotationCostCut = new QuotationCostCut();
                        $QuotationCostCut->reference_id = $reference_id;
                        $QuotationCostCut->day = $day;
                        $QuotationCostCut->costcut = $ID;
                        $QuotationCostCut->apply = $apply;
                        $QuotationCostCut->rate = $this->quotationArray['cost_cut_rate'][$day][$ID];
                        $QuotationCostCut->save();
                    }
                }
            }
        }
    }

    function saveDriverAccommodationDetails($reference_id){

        if (isset($this->quotationArray['hotel'])) {
            foreach ($this->quotationArray['hotel'] as $Index => $HotelSetting) {
                if(isset($HotelSetting['hotel'])) {
                    $HotelDriverAccmomodation = new HotelDriverAccmomodation();
                    $HotelDriverAccmomodation->reference_id = $reference_id;
                    $HotelDriverAccmomodation->hotel_index = $Index;
                    $HotelDriverAccmomodation->hotel_id = $HotelSetting['hotel'];
                    if (isset($this->quotationArray['accommodation-charges'][$Index])) {
                        $HotelDriverAccmomodation->rate = $this->quotationArray['accommodation-charges'][$Index];
                    } else {
                        $HotelDriverAccmomodation->rate = 0;
                    }


                    $HotelDriverAccmomodation->save();

                    }
                }
            }


    }

    /**
     * @param $reference_id
     */
    private
    function saveTransportTimes($reference_id)
    {
        foreach ((empty($this->quotationArray['transport_time']['transport']) ? [] : $this->quotationArray['transport_time']['transport']) as $key => $time) {
            $QuotationTransportTime = new QuotationTransportTime;
            $QuotationTransportTime->reference_id = $reference_id;
            $QuotationTransportTime->type = 'transport';
            $QuotationTransportTime->time = $time;
            $QuotationTransportTime->index = $key;
            $QuotationTransportTime->save();
        }
        foreach ((empty($this->quotationArray['transport_time']['meal_transfer']) ? [] : $this->quotationArray['transport_time']['meal_transfer']) as $key => $time) {
            $QuotationTransportTime = new QuotationTransportTime;
            $QuotationTransportTime->reference_id = $reference_id;
            $QuotationTransportTime->type = 'meal_transfer';
            $QuotationTransportTime->time = $time;
            $QuotationTransportTime->index = $key;
            $QuotationTransportTime->save();
        }
        foreach ((empty($this->quotationArray['transport_time']['meal_transfer']) ? [] : $this->quotationArray['transport_time']['meal_transfer']) as $key => $time) {
            $QuotationTransportTimeDetails = new QuotationTransportTimeDetails;
            $QuotationTransportTimeDetails->reference_id = $reference_id;
            $QuotationTransportTimeDetails->year = $this->quotationArray['transport_time']['meal_transfer_date']['year'][$key] ?? null;
            $QuotationTransportTimeDetails->month = $this->quotationArray['transport_time']['meal_transfer_date']['month'][$key] ?? null;
            $QuotationTransportTimeDetails->day = $this->quotationArray['transport_time']['meal_transfer_date']['day'][$key] ?? null;
            $QuotationTransportTimeDetails->meal_time = $this->quotationArray['transport_time']['meal_transfer_time'][$key] ?? null;
            $QuotationTransportTimeDetails->time = $time ?? null;
            $QuotationTransportTimeDetails->save();
        }
    }


    /**
     * @param $reference_id
     */
    private
    function saveActivities($reference_id)
    {
        foreach ((empty($this->quotationArray['activity']['type']) ? [] : $this->quotationArray['activity']['type']) as $key => $type) {
            $QuotationActivity = new QuotationActivity;
            $QuotationActivity->reference_id = $reference_id;
            $QuotationActivity->type = $type;
            $QuotationActivity->index = $key;
            $QuotationActivity->item_id = $this->quotationArray['activity']['id'][$key] ?? null;
            $QuotationActivity->departure_time = $this->quotationArray['activity']['departure_time'][$key] ?? null;
            $QuotationActivity->arrival_time = $this->quotationArray['activity']['arrival_time'][$key] ?? null;
            $QuotationActivity->year = $this->quotationArray['activity']['date']['year'][$key] ?? null;
            $QuotationActivity->month = $this->quotationArray['activity']['date']['month'][$key] ?? null;
            $QuotationActivity->day = $this->quotationArray['activity']['date']['day'][$key] ?? null;
            $QuotationActivity->save();
        }
    }

    /**
     * @param $reference_id
     * @param $quotation_no
     * @throws \Exception
     */
    function confirmQuotation($reference_id, $quotation_no)
    {
        if (!empty($this->quotationArray['confirm'])) {
            $roleId = Auth::user()->roles->toArray()[0]['pivot']['role_id'];
            // $clientEmailForm =  $this->quotationArray['confirm']['client_email'];

            /*if (ClientEmail::where('email', $clientEmailForm)->get()->count() > 0)
            {
                $clientEmail  = ClientEmail::where('email', $clientEmailForm)->get();

            } else {

                $clientEmail = new ClientEmail();

                $clientEmail->email = $clientEmailForm;

                $clientEmail->saveOrFail();
            }

            $recentClientId = $clientEmail->id;
            */

            $QuotationConfirm = new QuotationConfirm;
            $QuotationConfirm->reference_id = $reference_id;
            $QuotationConfirm->client_title = $this->quotationArray['confirm']['client_title'];
            $QuotationConfirm->client_name = $this->quotationArray['confirm']['client_name'];
            $QuotationConfirm->client_email = $this->quotationArray['confirm']['client_email'];
            $QuotationConfirm->save();

            if(isset($this->quotationArray['cancel']["cancel_remark"]) && !empty($this->quotationArray['cancel']["cancel_remark"])) {
                foreach (($this->quotationArray['cancel']["cancel_remark"] ?? []) as $CancelIndex => $HotelCancelItem) {
                    $QuotationcCancelHotel = new QuotationCancelHotel();

                    $QuotationcCancelHotel->quotation_confirm = $QuotationConfirm->id;
                    $QuotationcCancelHotel->cancel_note = $this->quotationArray['cancel']['cancel_note'][$CancelIndex];
                    $QuotationcCancelHotel->hotel = $this->quotationArray['cancel']['cancel_hotel'][$CancelIndex];
                    $QuotationcCancelHotel->index = $CancelIndex;
                    $QuotationcCancelHotel->cancel_remark = $this->quotationArray['cancel']['cancel_remark'][$CancelIndex];

                    $QuotationcCancelHotel->save();
                }
            }

            if(isset($this->quotationArray['dayused']["dayused_remark"]) && !empty($this->quotationArray['dayused']["dayused_remark"])) {
                foreach (($this->quotationArray['dayused']["dayused_remark"] ?? []) as $DayUsedIndex => $HotelDayUsedItem) {
                    $QuotationDayUsedHotel = new QuotationDayUsedHotel();

                    $QuotationDayUsedHotel->quotation_confirm = $QuotationConfirm->id;
                    $QuotationDayUsedHotel->dayused_note = $this->quotationArray['dayused']['dayused_note'][$DayUsedIndex];
                    $QuotationDayUsedHotel->index = $DayUsedIndex;
                    $QuotationDayUsedHotel->dayused_remark = $this->quotationArray['dayused']['dayused_remark'][$DayUsedIndex];

                    $QuotationDayUsedHotel->save();
                }
            }

            foreach (($this->quotationArray['hotel'] ?? []) as $Index => $HotelItem) {

                if ($this->quotationArray['accommodation'][$Index] == 2)//own arrangement
                    continue;

                if ($HotelItem['provider'] == Quotation::HOTEL_LOCAL) {

                    #Set used allotment
                    $AllotmentUsed = new AllotmentUsed();

                    $AllotmentUsed->quatation = $reference_id;
                    $AllotmentUsed->hotel = $HotelItem['hotel'];
                    $AllotmentUsed->use_room = array_sum($HotelItem['room_type']);
                    $AllotmentUsed->year = $HotelItem['check_in']['year'];
                    $AllotmentUsed->month = $HotelItem['check_in']['month'];
                    $AllotmentUsed->day = $HotelItem['check_in']['day'];

                    $AllotmentUsed->save();

                    $QuotationConfirmHotel = new QuotationConfirmHotel();

                    $QuotationConfirmHotel->quotation_confirm = $QuotationConfirm->id;
                    $QuotationConfirmHotel->confirm_note = $this->quotationArray['confirm']['confirm_note'][$Index];
                    $QuotationConfirmHotel->index = $Index;
                    $QuotationConfirmHotel->remark = $this->quotationArray['confirm']['remark'][$Index];

                    $QuotationConfirmHotel->save();

                } elseif ($HotelItem['provider'] == Quotation::HOTEL_BEDS) {

                    $RateKeyArray = [];
                    $PaxArray = [];

                    foreach ($this->quotationArray['api']['hotelbeds']['hotel'][$Index]['selected'] as $Room => $RateItem) {

                        $PaxArray[$Room] = [
                            "adult" => $RateItem['adult'],
                            "child" => $RateItem['child'],
                            "rooms" => $RateItem['rooms'],
                        ];

                        $RateKeyArray[$Room] = ["rateKey" => $RateItem['rateKey']];
                    }

                        $HotelBed = new HotelBed();
                        $Status = $HotelBed->CheckRate($RateKeyArray);
                        if (!$Status) {
                            throw new \Exception($HotelBed::find($HotelItem['hotel'])->name . ' is not available!');
                        }


                    foreach ($PaxArray as $Room => $PaxItem) {//book each one by one cos this shit doesn't work

                        $HotelBedsRateKeys =  QuotationHotelBedsRateKeys::where('reference_id',$reference_id)
                            ->where('rate_key',$RateKeyArray[$Room])->orderby('reference_id','DESC')->first();

                        if ($HotelBedsRateKeys->status == 0) {

                            $title = Honorific::where("ID", $this->quotationArray['confirm']['client_title'])->first()->honorific;
                            $ClientName = explode(" ", $this->quotationArray['confirm']['client_name']);

                            $BookingStatus = $HotelBed->BookingConfirm($PaxItem, $RateKeyArray[$Room], (string)$quotation_no, 'ENG', $title . '.' . $ClientName[0] ?? "Name", $ClientName[1] ?? " ");

                            if (!$BookingStatus) {

                                $error_msg = 'Sorry, we were unable to book <i>' . $HotelBed::find($HotelItem['hotel'])->name . '</i> due to a system failure!';

                                $error_msg .= "({$HotelBed->ErrorMsg})";

                                throw new \Exception($error_msg);
                            } else {

                                if (env("APP_DEBUG"))
                                    $CSS = file_get_contents(public_path('assets/css/apple.css'));
                                else
                                    $CSS = file_get_contents(public_path('assets/css/apple.min.css'));

                                $BookingItem = $BookingStatus->booking->toArray();

                                $Data = $HotelBed->getBookDetails($BookingItem['reference']);
                                $voucher = \View::make("email.body-start")->render();
                                $voucher .= \View::make("element.hotel.hotelbed.hotel-booking-voucher", ['Data' => $Data, 'Comment' => ""])->render();
                                $voucher .= \View::make("email.body-end")->render();


                                //save vouchers in database
                                $quotation_hotel = QuotationHotelBeds::where('reference_id', $reference_id)
                                    ->where('hotel', $Data['hotel']['code'])
                                    ->orderBy('updated_at', 'desc')->first();

                                if ($quotation_hotel) {
                                    $QuotationHotelbedsVouchers = new QuotationHotelbedsVouchers();
                                    $QuotationHotelbedsVouchers->quotation_hotel = $quotation_hotel->ID;
                                    $QuotationHotelbedsVouchers->reference_id = $reference_id;
                                    $QuotationHotelbedsVouchers->status = 1;
                                    $QuotationHotelbedsVouchers->rate_key = $RateKeyArray[$Room]['rateKey'];
                                    $QuotationHotelbedsVouchers->bookday_index = $Index;
                                    $QuotationHotelbedsVouchers->json = json_encode($BookingItem);
                                    $QuotationHotelbedsVouchers->save();
                                }

                                $subject = "Hotel Voucher : ".$Data['hotel']['name']."#".$quotation_no;
                                $emailArray = array();
                                array_push($emailArray, "<EMAIL>");
                                array_push($emailArray, "<EMAIL>");
                                array_push($emailArray, "<EMAIL>");
                                array_push($emailArray, Auth::user()->email);
                                Mail::to($emailArray)
                                    ->send(new HotelbedsInvoice($voucher, $subject, $CSS));
                                QuotationHotelBedsRateKeys::where('reference_id',$reference_id)
                                    ->where('rate_key',$RateKeyArray[$Room])->update(['status' => 1]);

                                if (!($roleId == 12 || $roleId == 19 || $roleId == 20 || $roleId == 21 || $roleId == 22)) {
                                    $proforma = \View::make("email.body-start")->render();
                                    $proforma.= \View::make("element.hotel.hotelbed.proforma-invoice", ['Data' => $Data, 'Comment' => ""])->render();
                                    $proforma.= \View::make("email.body-end")->render();

                                    $subject = "Proforma Invoice : ".$Data['hotel']['name']."#".$quotation_no;
                                    Mail::to(Auth::user()->email)
                                        ->send(new HotelbedsInvoice($proforma, $subject, $CSS));
                                    QuotationHotelBedsRateKeys::where('reference_id',$reference_id)
                                        ->where('rate_key',$RateKeyArray[$Room])->update(['status' => 1]);
                                }

                            }
                        }
                    }

                    QuotationHotelBedsRateKeys::where('reference_id', $reference_id)
                        ->where('rate_key', $RateKeyArray[$Room])->update(['status' => 1]);

                    if ($quotation_no !== $reference_id) {
                        // Detect Change
                        $quotationObj = Quotation::where('quotation_no', $quotation_no)->withTrashed()->get();
                        if (!empty($quotationObj)) {
                            foreach ($quotationObj as $keyOb => $quotation) {
                                $lastRefNo = $quotation->ID;
                                $lastKeyObj = QuotationHotelBedsRateKeys::where('reference_id', $lastRefNo)
                                    ->where('bookday_index', $Index)->get();
                                $currentKey = $RateKeyArray[$Room]['rateKey'];
                                foreach ($lastKeyObj as $lastKey => $lastKeyValue) {
                                    // check current key is equal to last booking key
                                    if ($lastKeyValue->rate_key !== $currentKey) {

                                        $refIdObj = QuotationHotelbedsVouchers::where('reference_id', $lastRefNo)->where('bookday_index', $Index)->first();
                                        if (!empty($refIdObj)) {
                                            $jsonResponse = $refIdObj->json;
                                            $voucherId = $refIdObj->ID;
                                            $jsonResponse = json_decode($jsonResponse, true);
                                            $bookingRef = $jsonResponse['reference'];
                                            $res = $HotelBed->BookingCancellation($bookingRef);
                                            $InvoiceHtml = "";
                                            if ($res) {
                                                if (env("APP_DEBUG"))
                                                    $CSS = file_get_contents(public_path('assets/css/apple.css'));
                                                else
                                                    $CSS = file_get_contents(public_path('assets/css/apple.min.css'));

                                                $InvoiceHtml .= \View::make("element.hotel.hotelbed.hotel-cancel-voucher", ['Data' => $res, 'Comment' => ""])->render();
                                                $subject = "Cancellation Voucher:#" . $quotation_no;
                                                Mail::to(Auth::user()->email)
                                                    ->send(new HotelbedsInvoice($InvoiceHtml, $subject, $CSS));
                                                QuotationHotelbedsVouchers::where('ID', $voucherId)->update(['status' => 0]);
                                            }
                                        }

                                    }
                                }
                            }
                        }
                    }
                }
            }

            if(true) {
                $lastNumberRow = QuotationIsNumber::where('country', $this->quotationArray['country'])
                    ->orderBy('is_number', 'desc')
                    ->first();
                
                if(!isset($this->quotationArray['is_number'])) {
                    $lastNumber = $lastNumberRow->is_number;
                    $parts = preg_split('/\s+/', $lastNumber);
    
                    foreach ($parts as &$part) {
                        if (is_numeric($part)) {
                            $part++;
                        }
                    }
                    $result = implode(' ', $parts);
                } else {
                    $result = $this->quotationArray['is_number'];
                }
                
                $QuotationIsNumber = new QuotationIsNumber;
                $QuotationIsNumber->reference_id = $reference_id;
                $QuotationIsNumber->quotation_no = $quotation_no;
                $QuotationIsNumber->country = $this->quotationArray['country'];
                $QuotationIsNumber->is_number = $result;
                $QuotationIsNumber->save();
            }

            //set status
            Quotation::where('ID', $reference_id)->update(['status' => 2]);

        }
    }

    /**
     * @param $reference_id
     * @param $quotation_no
     * @throws \Exception
     */
    function cancelQuotation($reference_id, $quotation_no)
    {
        if (!empty($this->quotationArray['confirm'])) {

            $QuotationConfirm = new QuotationConfirm;
            $QuotationConfirm->reference_id = $reference_id;
            $QuotationConfirm->client_title = $this->quotationArray['confirm']['client_title'];
            $QuotationConfirm->client_name = $this->quotationArray['confirm']['client_name'];
            $QuotationConfirm->client_email = $this->quotationArray['confirm']['client_email'];
            $QuotationConfirm->agent = $this->quotationArray['confirm']['agent']??$this->quotationArray['agent'];
            $QuotationConfirm->save();

            foreach (($this->quotationArray['hotel'] ?? []) as $Index => $HotelItem) {

                if ($this->quotationArray['accommodation'][$Index] == 2)//own arrangement
                    continue;

                if ($HotelItem['provider'] == Quotation::HOTEL_LOCAL) {

                    $QuotationConfirmHotel = new QuotationConfirmHotel();

                    $QuotationConfirmHotel->quotation_confirm = $QuotationConfirm->id;
                    $QuotationConfirmHotel->confirm_note = $this->quotationArray['confirm']['confirm_note'][$Index];
                    $QuotationConfirmHotel->index = $Index;
                    $QuotationConfirmHotel->remark = $this->quotationArray['confirm']['remark'][$Index];

                    $QuotationConfirmHotel->save();
                }
            }
        if(isset($this->quotationArray['api']['hotelbeds'])) {
            $QuotationEdit = QuotationManage::withTrashed()->where('quotation_no', $quotation_no)->get();
            if ($QuotationEdit) {
                foreach ($QuotationEdit as $edit) {
                    $ref = $edit->ID;
                    $vouchers = QuotationHotelbedsVouchers::where('reference_id', $ref)->where('status', 1)->get();
                        if($vouchers) {
                            foreach ($vouchers as $key => $voucher) {
                                $json = $voucher->json;
                                $json = json_decode($json);
                                $reference = $json->reference;
                                $HotelBed = new HotelBed();
                                $res = $HotelBed->BookingCancellation($reference);
                                $InvoiceHtml = "";
                                if($res) {
                                    if (env("APP_DEBUG"))
                                        $CSS = file_get_contents(public_path('assets/css/apple.css'));
                                    else
                                        $CSS = file_get_contents(public_path('assets/css/apple.min.css'));

                                    $InvoiceHtml .= \View::make("element.hotel.hotelbed.hotel-cancel-voucher", ['Data' => $res, 'Comment' => ""])->render();
                                    $subject = "Cancellation Voucher:#".$quotation_no;
                                    Mail::to(Auth::user()->email)
                                        ->send(new HotelbedsInvoice($InvoiceHtml, $subject, $CSS));
                                    QuotationHotelbedsVouchers::where('ID', $voucher->ID)->update(['status' => 0]);

                                    $QuotationHotelBedsCancel = new QuotationHotelBedsCancel();
                                    $QuotationHotelBedsCancel->reference_id = $reference_id;
                                    $QuotationHotelBedsCancel->quotation_hotel = $res['hotel']['code'];
                                    $QuotationHotelBedsCancel->json = json_encode($res);
                                    $QuotationHotelBedsCancel->save();

                                }
                            }
                        }
                    }
                }
            }


            //set status
            Quotation::where('ID', $reference_id)->update(['status' => 3]);

        }

    }

    /**
     * @param $reference_id
     * @param bool $quotation_no
     * @param string $ReturnType
     * @param bool $latestConfirmed
     *
     * @return array
     */
    static function getQuotation($reference_id, $quotation_no = false, $ReturnType = 'array')
    {

        if (!$reference_id) {
            $reference_id = QuotationManage::withTrashed()->where('quotation_no', $quotation_no)->orderBy('ID', 'desc')->first()->ID ?? false;
        }
        
        if (!$quotation_no) {
            $quotation_no = QuotationManage::withTrashed()->find($reference_id)->quotation_no;
        }
        
        $update_number = QuotationManage::withTrashed()->where('quotation_no', $quotation_no)->where('ID', '<=', $reference_id)->count();
        $QuotationEdit = QuotationManage::withTrashed()->where('quotation_no', $quotation_no)->where('ID', $reference_id)->first();
        
        if (!$QuotationEdit || !$reference_id || !$quotation_no) return false;
        
        
        if ($ReturnType == 'object')
            return $QuotationEdit;

            
        $QuotationTask = new QuotationTask();
        $Image = new Image();


        $SessionArray = [];
        #####################################################################
        #main
        $SessionArray['id_list'] = Quotation::getReferenceID($QuotationEdit->quotation_no, $QuotationEdit->ID);
        $SessionArray['quotation_no'] = $QuotationEdit->quotation_no;
        $SessionArray['ID'] = $QuotationEdit->ID;
        $SessionArray['r'] = $update_number;
        $SessionArray['status'] = $QuotationEdit->status;
        $SessionArray['tour_session'] = $QuotationEdit->tour_session;
        $SessionArray['timestamp'] = $QuotationEdit->last_modified;
        $SessionArray['country'] = $QuotationEdit->country;
        $SessionArray['currency'] = $QuotationEdit->currency ?? 142;//usd
        $SessionArray['nationality'] = $QuotationEdit->nationality ?? 62;
        $SessionArray['quarantine_type'] = $QuotationEdit->quarantine_type ?? 0;
        $SessionArray['ch_currency'] = $QuotationEdit->ch_currency ?? 142;//usd
        $SessionArray['base_currency'] = $QuotationEdit->base_currency ?? 142;//usd
        $SessionArray['tour_type'] = $QuotationEdit->tour_type;
        $SessionArray['slide_array'] = $QuotationTask->getTourSlideArray($SessionArray['tour_type']);
        $SessionArray['slide'] = array_fill_keys(array_flip($QuotationTask->getTourSlideArray($SessionArray['tour_type'])), true);

        #####################################################################
        #slide 2
        //Arrival date
        $SessionArray['arrival_date']['year'] = $QuotationEdit->main->arrival_year;
        $SessionArray['arrival_date']['month'] = $QuotationEdit->main->arrival_month;
        $SessionArray['arrival_date']['day'] = $QuotationEdit->main->arrival_day;

        $SessionArray['itinerary']['show_itinerary'] = $QuotationEdit->main->show_itinerary;
        //pax
        $SessionArray['pax']['adult'] = $QuotationEdit->pax->adult;
        $SessionArray['pax']['cwb'] = $QuotationEdit->pax->cwb;
        $SessionArray['pax']['cnb'] = $QuotationEdit->pax->cnb;
        $SessionArray['pax']['infant'] = $QuotationEdit->pax->infant ?? 0;

        //child_age
        foreach ($QuotationEdit->ChildAge as $index => $Child) {
            $SessionArray['child_age'][$Child->type][$Child->index] = $Child->age;
        }

        //market
        $SessionArray['market'] = $QuotationEdit->pax->market;

        #####################################################################
        #Slide3


        $SessionArray['languages'] = $QuotationEdit->language->pluck('language')->toArray();
        $SessionArray['place_full'] = $QuotationEdit->place->pluck('place', 'order')->toArray();
        $SessionArray['place_type'] = $QuotationEdit->place->pluck('type', 'order')->toArray();


        //pick dropoff for old quotation
        if (!empty($QuotationEdit->airport->airport_pick_up)) {
            array_unshift($SessionArray['place_type'], 2);
            array_unshift($SessionArray['place_full'], 1);
        }

        if (!empty($QuotationEdit->airport->airport_drop_off)) {

            array_push($SessionArray['place_type'], 3);
            array_push($SessionArray['place_full'], 1);
        }


        foreach ($SessionArray['place_full'] as $index => $PlaceID) {
            if ($SessionArray['place_type'][$index] == 1)
                $SessionArray['place'][] = $PlaceID;
        }


        //child_age
        foreach ($QuotationEdit->PlaceVehicle as $index => $PlaceVehicle) {
            $SessionArray['place_vehicle'][$PlaceVehicle->order] = $PlaceVehicle->vehicle;
        }

        #####################################################################
        #Slide17  nights
        foreach ($QuotationEdit->Night()->get() as $index => $Night) {
            $SessionArray['night'][$index] = $Night->night;
        }


        #####################################################################
        #Slide4 hotel

        //Local Hotel
        foreach ($QuotationEdit->hotel as $Index => $HotelSettings) {

            $Index = intval($HotelSettings['index'] ?? $Index);

            $SessionArray['hotel'][$Index]['hotel'] = $HotelSettings->hotel;
            $SessionArray['hotel'][$Index]['place'] = $SessionArray['place'][$Index];

            $SessionArray['hotel'][$Index]['check_in']['year'] = $HotelSettings->check_in_year;
            $SessionArray['hotel'][$Index]['check_in']['month'] = $HotelSettings->check_in_month;
            $SessionArray['hotel'][$Index]['check_in']['day'] = $HotelSettings->check_in_day;

            $SessionArray['hotel'][$Index]['check_out']['year'] = $HotelSettings->check_out_year;
            $SessionArray['hotel'][$Index]['check_out']['month'] = $HotelSettings->check_out_month;
            $SessionArray['hotel'][$Index]['check_out']['day'] = $HotelSettings->check_out_day;

            $CheckInDate = Carbon::create($SessionArray['hotel'][$Index]['check_in']['year'], $SessionArray['hotel'][$Index]['check_in']['month'], $SessionArray['hotel'][$Index]['check_in']['day'], 0);
            $CheckOutDate = Carbon::create($SessionArray['hotel'][$Index]['check_out']['year'], $SessionArray['hotel'][$Index]['check_out']['month'], $SessionArray['hotel'][$Index]['check_out']['day'], 0);

            $SessionArray['hotel'][$Index]['night'] = $CheckInDate->diffInDays($CheckOutDate);
            $SessionArray['hotel'][$Index]['provider'] = $HotelSettings->provider ?? "local";

            $SessionArray['hotel'][$Index]['room_category'] = $HotelSettings->room_category;
            $SessionArray['hotel'][$Index]['meal_type'] = $HotelSettings->meal;
            $SessionArray['hotel'][$Index]['room_type'] = $HotelSettings->room->pluck('room_count', 'room_type')->toArray();

            $SessionArray['hotel'][$Index]['extrabed'] = $HotelSettings->extra_bed;
            $SessionArray['hotel'][$Index]['driver_accommodation'] = $HotelSettings->driver_accommodation;


            $SessionArray['accommodation'][$Index] = empty($HotelSettings->hotel) ? 2 : 1;


            #####################################################################
            #Rate
            ##adult

            foreach ($HotelSettings->rate as $RateDay => $RateItem) {

                $AdultRate = $RateItem->toArray();
                $AdultRate['hotel'] = $AdultRate['hotel_id'];

                $SessionArray['rate']['hotel'][$Index]['adult'][$RateItem['bookday_index']][$RateItem['room_type']] = $AdultRate;

                foreach ($RateItem->child->where('age_from', 0)->toArray() as $c => $RateChildItem) {#child
                    $RateChildItem['hotel'] = $HotelSettings->hotel;
                    $SessionArray['rate']['hotel'][$Index]['child']['cnb'][] = $RateChildItem;
                }
                foreach ($RateItem->child->where('age_from', 2)->toArray() as $c => $RateChildItem) {#child
                    $RateChildItem['hotel'] = $HotelSettings->hotel;
                    $SessionArray['rate']['hotel'][$Index]['child']['cwb'][] = $RateChildItem;
                }

                foreach ($RateItem->driver_accommodation->toArray() as $c => $driverAccommodation) {#child
                    $driverAccommodation['hotel'] = $HotelSettings->hotel;
                    $SessionArray['rate']['hotel'][$Index]['driver_accommodation'][] = $driverAccommodation;
                }
            }

            foreach ($HotelSettings->hotelTransfer as $RateItem) {
                $SessionArray['rate']['hotel_transfer'][$Index] = [
                    'adult' => $RateItem->adult,
                    'child' => $RateItem->child
                ];
            }

        }

        //Local Cruise
        foreach ($QuotationEdit->cruise as $Index => $CruiseSettings) {

            $Index = intval($CruiseSettings['index'] ?? $Index);

            $SessionArray['cruise'][$Index]['cruise'] = $CruiseSettings->cruise;
            $SessionArray['cruise'][$Index]['place'] = $SessionArray['place'][$Index];

            $SessionArray['cruise'][$Index]['check_in']['year'] = $CruiseSettings->check_in_year;
            $SessionArray['cruise'][$Index]['check_in']['month'] = $CruiseSettings->check_in_month;
            $SessionArray['cruise'][$Index]['check_in']['day'] = $CruiseSettings->check_in_day;

            $SessionArray['cruise'][$Index]['check_out']['year'] = $CruiseSettings->check_out_year;
            $SessionArray['cruise'][$Index]['check_out']['month'] = $CruiseSettings->check_out_month;
            $SessionArray['cruise'][$Index]['check_out']['day'] = $CruiseSettings->check_out_day;

            $CheckInDate = Carbon::create($SessionArray['cruise'][$Index]['check_in']['year'], $SessionArray['cruise'][$Index]['check_in']['month'], $SessionArray['cruise'][$Index]['check_in']['day'], 0);
            $CheckOutDate = Carbon::create($SessionArray['cruise'][$Index]['check_out']['year'], $SessionArray['cruise'][$Index]['check_out']['month'], $SessionArray['cruise'][$Index]['check_out']['day'], 0);

            $SessionArray['cruise'][$Index]['nights'] = $CheckInDate->diffInDays($CheckOutDate);

            $SessionArray['cruise'][$Index]['package'] = $CruiseSettings->package;
            $SessionArray['cruise'][$Index]['cabin_type'] = $CruiseSettings->cabin_type;
            $SessionArray['cruise'][$Index]['meal_type'] = $CruiseSettings->meal;
            $SessionArray['cruise'][$Index]['cabin_occupancy_type'] = $CruiseSettings->cabin->pluck('room_count', 'cabin_occupancy_type')->toArray();

            $SessionArray['cruise'][$Index]['extrabed'] = $CruiseSettings->extra_bed;


            $SessionArray['accommodation'][$Index] = !empty($CruiseSettings->cruise) ? 4 : 1;

            #####################################################################
            #Rate
            ##adult

            foreach ($CruiseSettings->rate as $RateDay => $RateItem) {

                $AdultRate = $RateItem->toArray();

                $SessionArray['rate']['cruise'][$Index]['adult'][$RateItem['bookday_index']][$RateItem['cabin_occupancy_type']] = $AdultRate;

                foreach ($RateItem->child->where('age_from', 0)->toArray() as $c => $RateChildItem) {#child
                    $RateChildItem['cruise'] = $CruiseSettings->cruise;
                    $RateChildItem['rateChild'] = $CruiseSettings->rate;
                    $SessionArray['rate']['cruise'][$Index]['child']['cnb'][] = $RateChildItem;
                }
                foreach ($RateItem->child->where('age_from', 2)->toArray() as $c => $RateChildItem) {#child
                    $RateChildItem['cruise'] = $CruiseSettings->cruise;
                    $RateChildItem['rateChild'] = $CruiseSettings->rate;
                    $SessionArray['rate']['cruise'][$Index]['child']['cwb'][] = $RateChildItem;
                }
            }
        }

        //hotelbeds
        foreach ($QuotationEdit->HotelBeds as $Index => $HotelSettings) {

            $Index = intval($HotelSettings['index'] ?? $Index);

            $SessionArray['hotel'][$Index]['hotel'] = $HotelSettings->hotel;
            $SessionArray['hotel'][$Index]['place'] = $SessionArray['place'][$Index];

            $SessionArray['hotel'][$Index]['check_in']['year'] = $HotelSettings->check_in_year;
            $SessionArray['hotel'][$Index]['check_in']['month'] = $HotelSettings->check_in_month;
            $SessionArray['hotel'][$Index]['check_in']['day'] = $HotelSettings->check_in_day;

            $SessionArray['hotel'][$Index]['check_out']['year'] = $HotelSettings->check_out_year;
            $SessionArray['hotel'][$Index]['check_out']['month'] = $HotelSettings->check_out_month;
            $SessionArray['hotel'][$Index]['check_out']['day'] = $HotelSettings->check_out_day;

            $CheckInDate = Carbon::create($SessionArray['hotel'][$Index]['check_in']['year'], $SessionArray['hotel'][$Index]['check_in']['month'], $SessionArray['hotel'][$Index]['check_in']['day'], 0);
            $CheckOutDate = Carbon::create($SessionArray['hotel'][$Index]['check_out']['year'], $SessionArray['hotel'][$Index]['check_out']['month'], $SessionArray['hotel'][$Index]['check_out']['day'], 0);

            $SessionArray['hotel'][$Index]['night'] = $CheckInDate->diffInDays($CheckOutDate);
            $SessionArray['hotel'][$Index]['provider'] = "hotelbeds";

            $SessionArray['hotel'][$Index]['room_category'] = $HotelSettings->room_category;
            $SessionArray['hotel'][$Index]['meal_type'] = $HotelSettings->meal;
            $SessionArray['hotel'][$Index]['extrabed'] = $HotelSettings->extra_bed;


            $SessionArray['accommodation'][$Index] = empty($HotelSettings->hotel) ? 2 : 1;

            $SelectedRateList=[];

            foreach ($HotelSettings->rate as $k => $RateItem) {
                $SelectedRateListItem = [
                    'meal' => $RateItem->meal,
                    'adult' => $RateItem->adult,
                    'child' => $RateItem->child,
                    'rooms' => $RateItem->rooms,
                    'category' => $RateItem->room_category,
                    'rateKey' => $RateItem->rate_key,
                    'rateClass' => $RateItem->rate_class,
                    'net' => $RateItem->rate,
                    'rate' => HotelBed::getRateExtract(['net' => $RateItem->rate], $SessionArray['pax']),
                ];


                $SelectedRateList[] = $SelectedRateListItem;

                //full
                $CombinedRateList[$RateItem->room_category][$RateItem->meal] = $SelectedRateListItem;
                $MealTypes[$RateItem->meal] = Meal::find($RateItem->meal)->plan;
                $room_category[$RateItem->room_category] = RoomCategory::find($RateItem->room_category)->name;
            }

            $SessionArray['api']['hotelbeds']['hotel'][$Index]['selected'] = $SelectedRateList ?? [];

            //full
            $SessionArray['api']['hotelbeds']['hotel'][$Index]['full']['id'] = $HotelSettings->hotel;
            $SessionArray['api']['hotelbeds']['hotel'][$Index]['full']['room_category'] = $room_category ?? [];
            $SessionArray['api']['hotelbeds']['hotel'][$Index]['full']['meal_types'] = $MealTypes ?? [];
            $SessionArray['api']['hotelbeds']['hotel'][$Index]['full']['combine'] = $CombinedRateList ?? [];

            $SessionArray['hotel'][$Index]['room_type'] = HotelBed::getRoomTypesFromRate($SelectedRateList ?? []);

        }
        #driver Accomodation
         $HotelDriverAccmomodation = HotelDriverAccmomodation::where('reference_id',$reference_id)->get();

         if($HotelDriverAccmomodation){
             foreach ($HotelDriverAccmomodation as  $key => $acc){
                 if($acc->rate == "0.00"){
                     $SessionArray['accommodation-charges'][$acc->hotel_index] = null;
                 } else {
                     $SessionArray['accommodation-charges'][$acc->hotel_index] = $acc->rate;
                 }

             }

         }

        if (isset($SessionArray['hotel']))
            ksort($SessionArray['hotel']);
        #####################################################################
        #Slide6
        //attraction;
        foreach ($QuotationEdit->attraction as $key => $AttractionItem) {
            $SessionArray['attraction'][$AttractionItem->day][] = $AttractionItem->attraction;
            $SessionArray['rate']['attraction']['attraction'][$AttractionItem->attraction]['adult'] = getActualDataType($AttractionItem->rate[0]->adult ?? 0);
            $SessionArray['rate']['attraction']['attraction'][$AttractionItem->attraction]['child'] = getActualDataType($AttractionItem->rate[0]->child ?? 0);
            $SessionArray['time']['attraction']['attraction'][$AttractionItem->attraction]['start'] = $AttractionItem->start_time;
            $SessionArray['time']['attraction']['attraction'][$AttractionItem->attraction]['end'] = $AttractionItem->end_time;
            //$SessionArray['attraction_time'][$key] = array("id" => $AttractionItem->attraction, "time" => $AttractionItem->time, "day" => $AttractionItem->day);
        }
        //city tour;
        if (isset($QuotationEdit->CityTours)) {
            foreach ($QuotationEdit->CityTours as $key => $CityTourItem) {
                $SessionArray['city_tour'][$CityTourItem->day][] = $CityTourItem->city_tour;
                $SessionArray['rate']['attraction']['city_tour'][$CityTourItem->city_tour]['adult'] = getActualDataType($CityTourItem->rate[0]->adult ?? 0);
                $SessionArray['rate']['attraction']['city_tour'][$CityTourItem->city_tour]['child'] = getActualDataType($CityTourItem->rate[0]->child ?? 0);
                $SessionArray['time']['attraction']['city_tour'][$CityTourItem->city_tour]['start'] = $CityTourItem->start_time;
                $SessionArray['time']['attraction']['city_tour'][$CityTourItem->city_tour]['end'] = $CityTourItem->end_time;
                //$SessionArray['city_tour_time'][$key] = array("id" => $CityTourItem->city_tour, "time" => $CityTourItem->time, "day" => $CityTourItem->day);
            }
        }
        //excursion;
        if (isset($QuotationEdit->excursion)) {
            foreach ($QuotationEdit->excursion as $key => $ExcursionItem) {
                $SessionArray['excursion'][$ExcursionItem->day][] = $ExcursionItem->excursion;
                $SessionArray['rate']['attraction']['excursion'][$ExcursionItem->excursion]['adult'] = getActualDataType($ExcursionItem->rate[0]->adult ?? 0);
                $SessionArray['rate']['attraction']['excursion'][$ExcursionItem->excursion]['child'] = getActualDataType($ExcursionItem->rate[0]->child ?? 0);
                $SessionArray['time']['attraction']['excursion'][$ExcursionItem->excursion]['start'] = $ExcursionItem->start_time;
                $SessionArray['time']['attraction']['excursion'][$ExcursionItem->excursion]['end'] = $ExcursionItem->end_time;
                //$SessionArray['excursion_time'][$key] = array("id" => $ExcursionItem->excursion, "time" => $ExcursionItem->time, "day" => $ExcursionItem->day);
            }
        }
        #####################################################################
        #Slide6 meal

        foreach ($QuotationEdit->meal as $MealSettings) {

            $SessionArray['meal'][$MealSettings->day][$MealSettings->food_time] = [
                'transport' => $MealSettings->transport,
                'meal_where' => $MealSettings->meal,
                'restaurant' => $MealSettings->restaurant,
                'meal_preference' => $MealSettings->preference
            ];
            
            ///////////////////////////////////////
            /// meal not specified custom price
            foreach ($MealSettings->rate as $rate) {
                $SessionArray['rate']['meal'][$MealSettings->day][$MealSettings->food_time] = $rate->rate;
                $SessionArray['rate']['meal_child'][$MealSettings->day][$MealSettings->food_time] = $rate->child_rate;
            }

            ////////////////////////////////
        }
        #####################################################################
        #Slide8 itinerary


        #headers and day text
        foreach ($QuotationEdit->itinerary as $MealSettings) {
            $SessionArray['itinerary']['days'][$MealSettings->day]['day_text'] = $MealSettings['day_text'];#day headers
            $SessionArray['itinerary']['days'][$MealSettings->day]['day_header'] = $MealSettings['header'];
        }
        #Attraction
        foreach ($QuotationEdit->ItineraryAttraction as $ItineraryAttractionItem) {
            $SessionArray['itinerary']['days'][$ItineraryAttractionItem->day]['attraction'][$ItineraryAttractionItem->attraction] = $ItineraryAttractionItem->text;
        }

        #Excersion
        foreach ($QuotationEdit->ItineraryExcursion as $ItineraryExcursionItem) {
            $SessionArray['itinerary']['days'][$ItineraryExcursionItem->day]['excursion'][$ItineraryExcursionItem->excursion] = $ItineraryExcursionItem->text;
        }
        
        #City tours
        foreach ($QuotationEdit->ItineraryCityTour as $ItineraryCityTourItem) {
            $SessionArray['itinerary']['days'][$ItineraryCityTourItem->day]['city_tour'][$ItineraryCityTourItem->city_tour] = $ItineraryCityTourItem->text;
        }
        
        #transport
        if (!empty($QuotationEdit->transport)) {
            $SessionArray['rate']['transport']['vehicle']['vehicle_type'] = $QuotationEdit->transport->vehicle_type;
            $SessionArray['rate']['transport']['vehicle']['rate'] = $QuotationEdit->transport->rate;
            $SessionArray['rate']['transport']['rates']['total'] = $QuotationEdit->transport->total_rate;
            $SessionArray['rate']['transport']['rates']['adult_rate'] = $QuotationEdit->transport->adult_rate;
            $SessionArray['rate']['transport']['rates']['child_rate'] = $QuotationEdit->transport->child_rate;
            $SessionArray['rate']['transport']['vehicle']['bata'] = $QuotationEdit->transport->bata;
            $SessionArray['rate']['transport']['vehicle']['paging'] = $QuotationEdit->transport->paging;
            $SessionArray['rate']['transport']['vehicle']['highway_charges'] = $QuotationEdit->transport->highway_charges;

            $SessionArray['rate']['transport']['vehicle']['driver_accommodation'] = $QuotationEdit->transport->driver_accommodation;

            $SessionArray['rate']['transport']['mileage']['actual_distance'] = $QuotationEdit->transport->actual_distance;
            $SessionArray['rate']['transport']['mileage']['additional_distance'] = $QuotationEdit->transport->additional_distance;
            $SessionArray['rate']['transport']['per_water_bottle'] = $QuotationEdit->transport->per_water_bottle;

        }
        
        // TransportTime
        if (!empty($QuotationEdit->TransportTime())) {
            foreach ($QuotationEdit->TransportTime as $Time) {
                $SessionArray['transport_time'][$Time->type][$Time->index] = (isset($Time->time) && $Time->time != "00:00:00") ? substr($Time->time, 0, 5) : 0;
            }
        }

        if (!empty($QuotationEdit->Activities())) {
            foreach ($QuotationEdit->Activities as $Activity) {
                $SessionArray['activity']["type"][$Activity->index] = (isset($Activity->type)) ? $Activity->type : null;
                $SessionArray['activity']["id"][$Activity->index] = (isset($Activity->item_id)) ? $Activity->item_id : null;
                $SessionArray['activity']["arrival_time"][$Activity->index] = (isset($Activity->arrival_time) && $Activity->arrival_time != "00:00:00") ? substr($Activity->arrival_time, 0, 5) : 0;
                $SessionArray['activity']["departure_time"][$Activity->index] = (isset($Activity->departure_time) && $Activity->departure_time != "00:00:00") ? substr($Activity->departure_time, 0, 5) : 0;
                $SessionArray['activity']['date']['year'][$Activity->index] = (isset($Activity->year)) ? $Activity->year : null;
                $SessionArray['activity']['date']['month'][$Activity->index] = (isset($Activity->month)) ? $Activity->month : null;
                $SessionArray['activity']['date']['day'][$Activity->index] = (isset($Activity->day)) ? $Activity->day : null;
            }
        }

        /**if (!empty($QuotationEdit->TransportTimeDetails())) {
            foreach ($QuotationEdit->TransportTimeDetails as $key => $Time) {
                $Single['date']['year'] = $Time->year;
                $Single['date']['month'] = $Time->month;
                $Single['date']['day'] = $Time->day;
                $Single['meal_time'] = $Time->meal_time;
                $Single['time'] = (isset($Time->time) && $Time->time != "00:00:00") ? substr($Time->time, 0, 5) : 0;
                $SessionArray['transport_time_details'] = $Single;
            }
        }**/

        //guide
        if (!empty($QuotationEdit->Guide())) {

            foreach ($QuotationEdit->guide as $GuideItem) {
                $SessionArray['guide'][] = [
                    'type' => $GuideItem->type,
                    'rate' => $GuideItem->rate,
                    'accommodation' => $GuideItem->accommodation,
                    'tip' => $GuideItem->tip
                ];
            }
        }

        if (!empty($QuotationEdit->Flight())) {
            $SessionArray['flight'] = $QuotationEdit->Flight()->first();
        }

        //other Rates
        if (!empty($QuotationEdit->CostCut()->first())) {
            $DataArray = [];
            $inc = 0;

            foreach ($QuotationEdit->CostCut as $CostCut) {
                $SessionArray['cost_cutting'][$CostCut["day"]][$CostCut["costcut"]] = $CostCut["apply"];
                $SessionArray['cost_cut_rate'][$CostCut["day"]][$CostCut["costcut"]] = $CostCut["rate"];
            }
        }

        //other Rates
        if (!empty($QuotationEdit->OtherRate()->first())) {

            $DataArray = [];
            $inc = 0;

            foreach ($QuotationEdit->OtherRate as $OtherRateItem) {

                $DataArrayItem['text'] = $OtherRateItem->text;
                $DataArrayItem['type'] = $OtherRateItem->type;
                $DataArrayItem['rate_type'] = $OtherRateItem->rate_type;
                $DataArrayItem['rate'] = $OtherRateItem->rate;
                $DataArrayItem['cwb_rate'] = $OtherRateItem->cwb_rate;
                $DataArrayItem['cnb_rate'] = $OtherRateItem->cnb_rate;
                $DataArrayItem['details'] = [];


                if ($OtherRateItem->sightseeing) {
                    $DataArrayItem['details']['attraction_day'] = $OtherRateItem->sightseeing->day;
                    $DataArrayItem['details']['attraction_description'] = $OtherRateItem->sightseeing->description;
                }

                if (!empty($QuotationEdit->DayUseHotel()->first())) {

                    foreach ($QuotationEdit->DayUseHotel as $key => $DayUsedItem) {
                        if($DataArrayItem['type'] == 3) {
                            if($inc == $key) {
                                $DataArrayItem['details']['hotel_id'] = $DayUsedItem->hotel_id;
                                $DataArrayItem['details']['meal_preference'] = $DayUsedItem->meal_preference;
                                $DataArrayItem['details']['date'] = $DayUsedItem->date;
                                $DataArrayItem['details']['single_bed_rate'] = $DayUsedItem->single_bed_rate;
                                $DataArrayItem['details']['double_bed_rate'] = $DayUsedItem->double_bed_rate;
                                $DataArrayItem['details']['triple_bed_rate'] = $DayUsedItem->triple_bed_rate;
                                $DataArrayItem['details']['single_bed_room_count'] = $DayUsedItem->single_bed_room_count;
                                $DataArrayItem['details']['double_bed_room_count'] = $DayUsedItem->double_bed_room_count;
                                $DataArrayItem['details']['triple_bed_room_count'] = $DayUsedItem->triple_bed_room_count;

                                $inc++;
                            }
                        }
                    }
                }
                $DataArray[] = $DataArrayItem;

            }

            $SessionArray['other_rate'] = $DataArray;

        }


        #Slide9 itinerary
        $SessionArray['markup_amount'] = $QuotationEdit->Status->markup_amount;
        $SessionArray['markup_type'] = $QuotationEdit->Status->markup_type;
        $SessionArray['markup_amount_child'] = $QuotationEdit->Status->markup_amount_child;
        $SessionArray['markup_type_child'] = $QuotationEdit->Status->markup_type_child;
        $SessionArray['additional_emails'] = $QuotationEdit->Status->additional_email;
        $SessionArray['agent_email'] = $QuotationEdit->Status->agent_email;
        $SessionArray['agent'] = $QuotationEdit->Confirm->agent ?? $QuotationEdit->Status->agent; // neeeeeeeeeeeeeeeeeeeeeeeeeeeeed to change
        $SessionArray['sales_person'] = $QuotationEdit->Confirm->sales_person ?? $QuotationEdit->Status->sales_person;
        $SessionArray['follow_up_email_subject'] = $QuotationEdit->Status->follow_up_email_subject;
        $SessionArray['sales_tracking_id'] = $QuotationEdit->sales_tracking_id;
        $SessionArray['send_email'] = $QuotationEdit->Status->send_email;
        $SessionArray['note'] = $QuotationEdit->Status->note;
        $SessionArray['hotel_filter']['return_type'] = $QuotationEdit->Status->hotel_filter_type;
        $SessionArray['hotel_filter']['return_star'] = $QuotationEdit->Status->hotel_filter_star;


        #Confirmation Details
        if ($QuotationEdit->Confirm()->first()) {
            if($SessionArray['status'] == 3) {
                $SessionArray['save_type'] = 'save';
            } else {
                $SessionArray['save_type'] = 'confirm';
            }

            $SessionArray['confirm']['client_title'] = $QuotationEdit->Confirm->client_title;
            $SessionArray['confirm']['client_name'] = $QuotationEdit->Confirm->client_name;
            $SessionArray['confirm']['client_email'] = $QuotationEdit->Confirm->client_email;
            // $SessionArray['confirm']['agent'] = $QuotationEdit->Confirm->agent;


            foreach ($QuotationEdit->Confirm->hotel as $HotelItem) {

                $SessionArray['confirm']['confirm_note'][$HotelItem->index] = $HotelItem->confirm_note;
                $SessionArray['confirm']['remark'][$HotelItem->index] = $HotelItem->remark;
            }

            foreach ($QuotationEdit->Confirm->CancelOneHotel as $key => $CancelHotelItem) {
                $SessionArray['cancel']['cancel_hotel'][$key] = $CancelHotelItem->hotel;
                $SessionArray['cancel']['cancel_note'][$key] = $CancelHotelItem->cancel_note;
                $SessionArray['cancel']['cancel_remark'][$key] = $CancelHotelItem->cancel_remark;
            }

            foreach ($QuotationEdit->Confirm->DayUsedHotel as $key => $DUsedItem) {
                $SessionArray['dayused']['dayused_note'][$key] = $DUsedItem->dayused_note;
                $SessionArray['dayused']['dayused_remark'][$key] = $DUsedItem->dayused_remark;
            }

            if ($QuotationEdit->IsNumber()->first()) {
                $SessionArray['is_number'] = $QuotationEdit->IsNumber->is_number;
            }

        } else {
            $SessionArray['save_type'] = 'save';
        }
        $SessionArray['vehicle_allocation'] = $QuotationEdit->VehicleAllocation()->first() ?? false;


        #set user
        $SessionArray['user'] = $QuotationEdit->main->user;
        $SessionArray['user_details'] = User::find($QuotationEdit->main->user);
        $SessionArray['image'] = $Image->getGoogleGenDirectionPathStatic($QuotationEdit->place, 5, "80x80");


            $Vehicle = new Vehicle();
            $Place = new Place();
            $PathPair = $Place->getPathPair($SessionArray['place_full'], $SessionArray['place_type']);
            $transportType = 2;
            $transportTypes = array();
            foreach ($PathPair as $index => $PathPairItem) {
                $Distance = Distance::where("from", $PathPairItem['from'])->where('to', $PathPairItem['to'])->first();
                if ($Distance) {
                    $DistanceID = $Distance->ID;
                    $VehicleType = $Vehicle->getPaxToVehicle($SessionArray);
                    if (!empty($VehicleType)) {
                        $VehicleID = $VehicleType->ID;
                    } else {
                        $VehicleID = '-';
                    }
                    $transportType = TransportCost::where("distance_id", $DistanceID)
                            ->where('vehicle_type', $VehicleID)
                            ->first()->type ?? 2;
                    $transportTypes[$index]['from'] = $PathPairItem['from'];
                    $transportTypes[$index]['to'] = $PathPairItem['to'];
                    $transportTypes[$index]['transport_type'] = $transportType;
                } else {
                    $transportTypes[$index]['from'] = $PathPairItem['from'];
                    $transportTypes[$index]['to'] = $PathPairItem['to'];
                    $transportTypes[$index]['transport_type'] = 2;
                }
            }
            $SessionArray['transportType']['transport']['transportType'] = $transportType;
            $SessionArray['transportType']['transport']['transportTypes'] = $transportTypes;


        return $SessionArray;
    }


    /**
     * @param $ReferenceID
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    static function getLastQuotation($ReferenceID)
    {
        return Quotation::where("quotation_no", $ReferenceID)->orderBy('last_modified', 'desc')->get();
    }

    /**
     * @param bool $ReferenceID
     * @param bool $quotationID
     * @return array
     */
    static function getReferenceID($ReferenceID = false, $quotationID = false)
    {

        if (!$ReferenceID && $quotationID) {
            $Quote = Quotation::find($quotationID)->orderBy('last_modified', 'desc')->first();
            $ReferenceID = $Quote->reference_ID;
        }

        return [
            $ReferenceID,
            sprintf("%05s", $ReferenceID),
            $ReferenceID . "CNTL",
            "TMP-PO" . $ReferenceID
        ];
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function Airport()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationAirport', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Attraction()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationAttraction', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function CityTours()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationCityTour', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Excursion()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationExcursion', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Hotel()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationHotel', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Cruise()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationCruise', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function HotelBeds()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationHotelBeds', 'reference_id', 'ID');
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Itinerary()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationItinerary', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function ItineraryAttraction()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationItineraryAttraction', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function ItineraryExcursion()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationItineraryExcursion', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function ItineraryCityTour()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationItineraryCityTour', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Language()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationLanguage', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function Main()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationMain', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Meal()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationMeal', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function Pax()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationPax', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function ChildAge()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationChildAge', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Place()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationPlace', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function PlaceVehicle()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationPlaceVehicle', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Night()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationNight', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function Status()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationStatus', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Timeline()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationTimeline', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function Transport()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationTransport', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function TransportTime()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationTransportTime', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Activities()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationActivity', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function TransportTimeDetails()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationTransportTimeDetails', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function Confirm()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationConfirm', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function IsNumber()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationIsNumber', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function ConfirmHotel()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationConfirmHotel', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function CancelHotel()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationCancelHotel', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function DayUsedHotel()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationDayUsedHotel', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function DayUseHotel()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationDayUse', 'reference_id', 'ID');
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function VehicleAllocation()
    {
        return $this->hasMany('App\Model\Operation\VehicleAllocation', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public
    function Flight()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationFlight', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function Guide()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationGuide', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function OtherRate()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationOtherRate', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    function HotelVoucher()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationHotelVoucher', 'reference_id', 'ID');
    }

	public function findCountry() {
		return $this->hasOne('App\Model\Place\Place',"ID",'country');
	}

	public function CostCut() {
        return $this->hasMany('App\Model\QuotationManage\QuotationCostCut',"reference_id",'ID');
    }

    public function findSalesTrack() {
        return $this->hasOne('App\Model\Admin\SalesTrack', 'sales_track_id', 'sales_tracking_id');
    }

    public function HotelBedsCancelVouchers()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationHotelBedsCancel', 'reference_id', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public
    function ReConfirm()
    {
        return $this->hasMany('App\Model\Operation\Reconfirm', 'tour_id', 'quotation_no');
    }

    public
    function OutBoundDriverAllocation()
    {
        return $this->hasMany('App\Model\Transport\TransportOutBoundDriverAllocation', 'quotation_id', 'quotation_no');
    }
}
