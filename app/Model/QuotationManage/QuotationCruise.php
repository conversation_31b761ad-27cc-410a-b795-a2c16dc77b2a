<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationHotel
 *
 * @property int $ID
 * @property int $reference_id
 * @property int|null $extra_bed
 * @property int|null $room_category
 * @property int|null $hotel
 * @property int|null $meal
 * @property int $check_in_year
 * @property int $check_in_month
 * @property int $check_in_day
 * @property int $check_out_year
 * @property int $check_out_month
 * @property int $check_out_day
 * @property int $provider
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \App\Model\QuotationManage\Quotation $quotation
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationHotelRate[] $rate
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationHotelRoom[] $room
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereCheckInDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereCheckInMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereCheckInYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereCheckOutDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereCheckOutMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereCheckOutYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereExtraBed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereRoomCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotel whereUploadId($value)
 * @mixin \Eloquent
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationHotelTransfer[] $hotelTransfer
 */
class QuotationCruise extends Model
{
    protected $table = 'apple_quotation_cruise';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cabin()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationCruiseCabin', 'quotation_cruise', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function rate()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationCruiseRate', 'quotation_cruise', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function quotation()
    {
        return $this->belongsTo('App\Model\QuotationManage\Quotation', 'reference_id', 'ID');
    }

}
