<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * Class QuotationHotelBeds
 * @package App\Model\QuotationManage
 */
class QuotationHotelBeds extends Model
{
    protected $table = "apple_quotation_hotel_hotelbeds";

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function rate()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationHotelBedsRateKeys', 'quotation_hotel', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function hotelbedsVouchers()
    {
        return $this->hasOne('App\Model\QuotationManage\QuotationHotelbedsVouchers', 'quotation_hotel', 'ID');
    }

}
