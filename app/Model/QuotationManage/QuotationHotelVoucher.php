<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationHotelVoucher
 *
 * @property int $ID
 * @property int $quotation_no
 * @property int $hotel
 * @property string|null $details
 * @property string|null $status
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelVoucher whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelVoucher whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelVoucher whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelVoucher whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelVoucher whereQuotationNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelVoucher whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelVoucher whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class QuotationHotelVoucher extends Model
{
    protected $table = "apple_quotation_hotel_voucher";
}
