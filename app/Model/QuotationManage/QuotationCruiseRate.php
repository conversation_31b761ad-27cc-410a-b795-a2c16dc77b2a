<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationHotelRate
 *
 * @property int $ID
 * @property int|null $quotation_hotel
 * @property int|null $rate_id
 * @property int $year
 * @property int $month
 * @property int $day
 * @property int|null $meal
 * @property int|null $room_type
 * @property int|null $room_category
 * @property float|null $rate
 * @property float|null $modified_rate
 * @property string|null $reason
 * @property int|null $is_modified
 * @property int $bookday_index
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationHotelRateChild[] $child
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereBookdayIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereIsModified($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereModifiedRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereQuotationHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereRoomCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereRoomType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationHotelRate whereYear($value)
 * @mixin \Eloquent
 */
class QuotationCruiseRate extends Model
{
    protected $table = 'apple_quotation_cruise_rate';
    protected $appends = ['cruise'];

    /**
     * @return int|mixed|null
     */
    public function getCruiseAttribute()
    {
        return \App\Model\Quotation\QuotationCruise::where("ID","=", $this->quotation_cruise)->first()->cruise;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function child()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationCruiseRateChild', 'quotation_cruise_rate_id', 'ID');
    }
}
