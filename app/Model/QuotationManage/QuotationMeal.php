<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationMeal
 *
 * @property int $ID
 * @property int|null $reference_id
 * @property int $day
 * @property int $food_time
 * @property int|null $meal
 * @property int|null $restaurant
 * @property int|null $preference
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\QuotationManage\QuotationMealRate[] $rate
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereFoodTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal wherePreference($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereRestaurant($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationMeal whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationMeal extends Model
{
    protected $table = 'apple_quotation_meal';


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function rate()
    {
        return $this->hasMany('App\Model\QuotationManage\QuotationMealRate','meal','ID');
    }
}
