<?php

namespace App\Model\QuotationManage;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\QuotationManage\QuotationLanguage
 *
 * @property int $ID
 * @property int|null $reference_id
 * @property int $language
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\QuotationManage\QuotationLanguage whereUploadId($value)
 * @mixin \Eloquent
 */
class QuotationLanguage extends Model
{
    protected $table = 'apple_quotation_language';

}
