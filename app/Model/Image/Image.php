<?php

namespace App\Model\Image;

use App\Model\Cruise\Cruise;
use App\Model\Place\Place;
use App\User;
use Carbon\Carbon;
use File;
use Illuminate\Database\Eloquent\Model;
use ImageGenerator;
use YoHang88\LetterAvatar\LetterAvatar;

/**
 * App\Model\Image\Image
 *
 * @mixin \Eloquent
 */
class Image extends Model
{

    /**
     * @var string
     */
    protected $ImageFolder = "assets/image/";
    /**
     * @var array
     */
    protected $FolderSizeNames = ['1x' => '1x',
        '2x' => '2x',
        '3x' => '3x',
        '4x' => '4x',
        '5x' => '5x',
        '300' => '300'];

    /**
     * @var array
     */
    protected $imageSize = ['1x' => ['width' => 30, 'height' => 30],
        '2x' => ['width' => 70, 'height' => 70],
        '3x' => ['width' => 130, 'height' => 130],
        '4x' => ['width' => 200, 'height' => 200],
        '5x' => ['width' => 310, 'height' => 310],
        '300' => ['width' => 300, 'height' => 300]];


    /**
     * @param $ID
     * @param $Size
     * @param $FolderName
     * @param int $Limit
     * @param string $Name
     * @param bool $ForceConvert
     * @return array
     */
    static function getImageDirect($ID, $Size, $FolderName, $Limit = 1, $Name = "N", $ForceConvert = false)
    {

        $Image = new self;

        if ($FolderName == 'user') {
            $cacheID = 'cache=' . User::find($ID)->updated_at->timestamp;
        } else if ($FolderName == 'cruise') {
            $cacheID = 'cache=' . Cruise::find($ID)->updated_at->timestamp;
        } else {
            $cacheID = 'cache=' . Carbon::now()->day . Carbon::now()->month;
        }

        return $Image->getImage($ID, $Size, $FolderName, $Limit, $Name, $ForceConvert, $cacheID);
    }

    /**
     * @param $ID
     * @param $Size
     * @param $FolderName
     * @param int $Limit
     * @param string $Name
     * @param bool $ForceConvert
     * @param string $cacheID
     * @return array
     */
    function getImage($ID, $Size, $FolderName, $Limit = 1, $Name = "N", $ForceConvert = false, $cacheID = "")
    {

//        dump($ID, $Size, $FolderName, $Limit, $Name);
        if (!isset($this->FolderSizeNames[$Size]))
            return $this->getNotFoundImage($Size, $Name);
//dd(public_path("$this->ImageFolder$FolderName/$ID"));

        if (File::exists(public_path("$this->ImageFolder$FolderName/$ID")) && File::allFiles(public_path("$this->ImageFolder$FolderName/$ID"))) {

            $files = File::files(public_path("$this->ImageFolder$FolderName/$ID"));

            //if no folder for sizes
            if (!File::exists(public_path("$this->ImageFolder$FolderName/$ID/{$this->FolderSizeNames[$Size]}"))) {
                File::makeDirectory(public_path("$this->ImageFolder$FolderName/$ID/{$this->FolderSizeNames[$Size]}"));
            }
            //if no images for sizes
            if (!count(File::allFiles(public_path("$this->ImageFolder$FolderName/$ID/{$this->FolderSizeNames[$Size]}"))) || $ForceConvert) {

                //divide to image size
                foreach ($files as $imgID => $fileToRezizse) {
                    $pathinfo = pathinfo($fileToRezizse);
                    $img = ImageGenerator::make($fileToRezizse)->fit($this->imageSize[$Size]['width'], $this->imageSize[$Size]['height']);
                    $img->interlace();
                    $img->save(public_path("$this->ImageFolder$FolderName/$ID/{$this->FolderSizeNames[$Size]}/$imgID.{$pathinfo['extension']}"));
                }
            }

            $filesSizes = File::files(public_path("$this->ImageFolder$FolderName/$ID/{$this->FolderSizeNames[$Size]}"));

            $ImageCount = 1;
            $ReturnImageList = array();
            foreach ($filesSizes as $file) {
                $pathinfo = pathinfo($file);
                $ReturnImageList[] = asset("$this->ImageFolder$FolderName/$ID/{$this->FolderSizeNames[$Size]}/{$pathinfo['basename']}?$cacheID");
                if ($ImageCount >= $Limit)
                    break;

                $ImageCount++;
            }

            return (empty($ReturnImageList) ? $this->getNotFoundImage($Size, $Name) : $ReturnImageList);
        } else
            return $this->getNotFoundImage($Size, $Name);

    }

    /**
     * @param string $Size
     * @param $Name
     * @return array
     */
    function getNotFoundImage($Size = '1x', $Name)
    {

        #short the name
        $Name = preg_replace('/[^\p{L}\p{N}\s]/u', '', $Name);
        preg_match_all("/(\S)\S*/i", $Name, $Letters, PREG_PATTERN_ORDER);

        $Name = strtoupper(implode("", array_slice($Letters[1], 0, 2)));


        if (!File::exists(public_path('assets/image/no_image/' . $Name . "_$Size.jpg"))) {


            #$Size = '3x';

            $W = $this->imageSize[$Size]['width'];
            $H = $this->imageSize[$Size]['height'];

            $FontSize = ($W / 2 + $H / 2) / 2.3;


            $img = ImageGenerator::canvas($W, $H, "#a5a7a7");
            $img->text($Name, $W / 2.0, $W / 2.0, function ($font) use ($FontSize) {


                $font->file(public_path("/assets/fonts/OpenSans-Regular.ttf"));
                $font->size($FontSize);
                $font->color('#fff');
                $font->align('center');
                $font->valign('middle');
            });

            $img->save(public_path('assets/image/no_image/' . $Name . "_$Size.jpg"));

        }

        return [(asset('assets/image/no_image/' . $Name . "_$Size.jpg"))];
    }


    /**
     * @param $PlaceList
     * @param int $zoom
     * @param string $size
     * @return string
     */
    function getGoogleGenDirectionPathStatic($PlaceList, $zoom = 7, $size = "250x432")
    {

        $con_list = array();
        foreach ($PlaceList as $PlaceItem) {

            $PlaceDetails = Place::where('ID', $PlaceItem['place'] ?? $PlaceItem['place_id'])->get()->first();

            $con_name = $PlaceDetails->country()->first()->name;
            $con_list[] = $PlaceDetails->name . '+' . $con_name;
        }

        $way_point = implode("|", $con_list);


        $path = "//maps.googleapis.com/maps/api/staticmap?&zoom=$zoom&center=$con_name&size=$size&style=element:labels|visibility:on&style=element:geometry.stroke|visibility:off&style=feature:landscape|element:geometry|saturation:-100&style=feature:water|saturation:-100|invert_lightness:true&key=AIzaSyAnA1a95aQr50mkRYEAxdQuYx71ek470Tc&markers=color:green|$way_point";
        return $path;
    }

    /**
     * @param $Path
     * @return string
     */
    function imageToBase64($Path)
    {
        $type = pathinfo($Path, PATHINFO_EXTENSION);
        $data = file_get_contents($Path);
        return 'data:image/' . $type . ';base64,' . base64_encode($data);
    }


}
