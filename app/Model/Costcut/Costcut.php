<?php

namespace App\Model\Costcut;

use App\CountryCurrency;
use App\Model\Place\CityTour;
use App\Model\Place\Place;
use App\Model\Quotation\Quotation;
use App\Model\Transport\Transport;
use App\Model\Vehicle\TransportType;
use App\Model\Vehicle\Vehicle;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Costcut\Costcut
 *
 * @mixin \Eloquent
 */
class Costcut extends Model
{
    protected $table = 'apple_costcut';
    public $mealTimeArr = ["B"=>1, "L"=>2, "D"=>3];

    public function getCostCutPoints($QuotationArray) {
        $mainCostCut = $this->getCostCut($QuotationArray);
        // dd($mainCostCut);
        // dd($mainCostCut);

        if(isset($mainCostCut)) {
            foreach ($mainCostCut as $day => $CostCutItem) {
                if(isset($CostCutItem["cost-cuts"]) && !empty($CostCutItem["cost-cuts"])) {
                    foreach ($CostCutItem["cost-cuts"] as $key => $cost_cuts) {
                        $mainCostCut[$day]["cost-cuts"][$key]["description"] = $this->getDescription($cost_cuts["template"]);

                        $CostCutRate = $this->getCurrentCost($cost_cuts->ID, $QuotationArray);
                        $mainCostCut[$day]["cost-cuts"][$key]["cost-break"] = $this->formatCostCut($CostCutRate, $QuotationArray);
                    }
                }
            }
        }

        return $mainCostCut;
    }

    public function getDescription($template) {
        $temArr = explode(",", $template);
        $description =[];
        foreach ($temArr as $key => $val) {
            preg_match('/\[(.*)\]/', $val, $matches);
            $item = $matches[1];
            $description[] = $this->singalDisc($item);
        }
        return $description;
    }

    public function singalDisc($item) {
        $items = explode("-", $item);

        if($items[0] == "PP") {
            $disc = $this->PPDisc($items[1]);
        }
        if($items[0] == "C") {
            $disc = $this->CDisc($items[1]);
        }
        if($items[0] == "MT") {
            $disc = $this->MTDisc($items[1]);
        }

        return $disc;
    }

    public function PPDisc($item) {
        $items = explode("/", $item);

        $from = Place::find($items[0])->name;
        $to = Place::find($items[1])->name;

        return "(".$from." -> ".$to.") point to point transfer";
    }

    public function CDisc($item) {
        $citytour = CityTour::find($item)->name;

        return "(".$citytour.") attraction";
    }

    public function MTDisc($item) {
        if($item == "L") {
            $name = "Lunch";
        } else if($item == "D") {
            $name = "Dinner";
        } else if($item == "B") {
            $name = "Break first";
        }

        return "Meal transfer for (".$name.")";
    }

    public function getCostCut($QuotationArray) {
        $Vehicle = new Vehicle();
        $VehiclePax = $Vehicle->getPaxToVehicle($QuotationArray);//get vehicle vehicle

        $vehicleType = isset($VehiclePax->vehicleType->ID) ? $VehiclePax->vehicleType->ID : 0;

        $mainCostCut = $this->getCostCutArrCityTour($QuotationArray);
        $mainCostArr = $this->getCostCutText($mainCostCut, $vehicleType);
        $mainCostArr = $this->getCostPackage($mainCostArr, $QuotationArray);

        return $mainCostArr;
    }

    public function getCostCutArrCityTour($QuotationArray) {
        $DaysDetail = Quotation::getTourDaysDetail($QuotationArray);
        $DaysDetail[0] = [];
        if(isset($QuotationArray["place_type"][0]) && $QuotationArray["place_type"][0] != 1) {
            $DaysDetail[0]["place"] = $QuotationArray["place_full"][0];
        }
        ksort($DaysDetail);
        $Previous = false;
        $CostCuttingApp = [];
        // dd($DaysDetail);
        if(isset($DaysDetail)) {
            foreach ($DaysDetail as $Day=>$DayValue) {
                if($Previous && (isset($DayValue['place']) && $DayValue['place'] != $Previous)) {
                    $CostCuttingApp[$Day]['PP'] = $Previous."/".$DayValue['place'];
                    if(isset($QuotationArray["meal"][$Day][2]["transport"]) && $QuotationArray["meal"][$Day][2]["transport"]==1)
                        $CostCuttingApp[$Day]['MT'][] = "[MT-L]";
                    if(isset($QuotationArray["meal"][$Day][3]["transport"]) && $QuotationArray["meal"][$Day][3]["transport"]==1)
                        $CostCuttingApp[$Day]['MT'][] = "[MT-D]";
                }
                $Previous = $DayValue['place']??"";
            }
        }
        if(end($QuotationArray["place_type"]) == 3) {
            $CostCuttingApp[]["PP"] = $Previous . "/" . end($QuotationArray["place_full"]);
        }

        $mainCostCut = [];
        $i = 0;
        $PPtemplate = "";
        $Key = 0;
        foreach ($CostCuttingApp as $Key => $CostDetails) {
            if(isset($QuotationArray["city_tour"])) {
                if(isset($QuotationArray["city_tour"][$Key])) {
                    $mainCostCut[$i]["type"] = "City Tour Transport";
                    $mainCostCut[$i]["short"] = "CT";
                    $mainCostCut[$i]["index"] = $Key;
                    foreach($QuotationArray["city_tour"][$Key] as $City_tour) {
                        $template = "";
                        if(isset($CostDetails['PP'])) {
                            if($template != "") {
                                $template .= ",";
                            }
                            $template .= "[PP-".$CostDetails['PP']."]";
                        }
                        if(isset($City_tour)) {
                            if($template != "") {
                                $template .= ",";
                            }
                            $template .= "[C-".$City_tour."]";
                        }
                        if(isset($CostDetails['MT'])) {
                            if($template != "") {
                                $template .= ",";
                            }
                            $template .= implode(",", $CostDetails['MT']);
                        }
                        $mainCostCut[$i]["template"] = $template;
                    }
                    $i++;
                }
            }

            if(isset($CostDetails['PP'])) {
                if($PPtemplate != "") {
                    $PPtemplate .= ",";
                }
                $PPtemplate .= "[PP-".$CostDetails['PP']."]";
            }
        }
        $mainCostCut[$i]["type"] = "Point to point Transport";
        $mainCostCut[$i]["short"] = "PP";
        $mainCostCut[$i]["index"] = $Key;
        $mainCostCut[$i]["template"] = $PPtemplate;

        foreach ($mainCostCut as $day =>$CostCutVal) {
            if($CostCutVal["short"] == "CT") {
                foreach ($this->pc_array_power_set(explode(",", $CostCutVal["template"])) as $key => $value) {
                    if(isset($value) && count($value)>1) {
                        $value = array_reverse($value);
                        $mainCostCut[$day]["templates"][] = implode(",", $value);
                    }
                }
            } else if($CostCutVal["short"] == "PP") {
                $PPTemplateArray = $this->getPPTemplateArray(explode(",", $CostCutVal["template"]));
                $mainCostCut[$day]["templates"] = $PPTemplateArray;
            }
        }

        return $mainCostCut;
    }

    public function getPPTemplateArray($TemplateArray) {
        $templates = [];
        for ($i=0; $i<count($TemplateArray)-1; $i++) {
            $temp = "";
            for ($j=$i; $j<count($TemplateArray); $j++) {
                $temp .= $TemplateArray[$j] . ",";
            }
            $templates[] = rtrim($temp, ",");
        }

        for ($i=1; $i<count($TemplateArray)-1; $i++) {
            $temp = "";
            for ($j=0; $j<count($TemplateArray)-$i; $j++) {
                $temp .= $TemplateArray[$j] . ",";
            }
            $templates[] = rtrim($temp, ",");
        }

        return $templates;
    }

    public function getCostCutText($mainCostCut, $vehicleType=0) {
        foreach ($mainCostCut as $day=>$CostCuts) {
            foreach ($CostCuts["templates"] as $CostCut) {

                $CostCutObjs = Costcut::whereHas('Rate', function ($query) use ($vehicleType){
                    $query->where('vehicle_type', '=', $vehicleType);
                })->with("Rate")->where("template","=", $CostCut)->first();

                if(isset($CostCutObjs) && !empty($CostCutObjs)) {
                    $mainCostCut[$day]["cost-cuts"][] = $CostCutObjs;
                }
            }
        }

        return $mainCostCut;
    }

    public function getCostPackage($mainCostArr, $QuotationArray) {
        foreach ($mainCostArr as $day=>$CostCuts) {
            if(isset($CostCuts["cost-cuts"]) && !empty($CostCuts["cost-cuts"])) {
                foreach ($CostCuts["cost-cuts"] as $item => $CostCut) {
                    $total = $CostCut->Rate[0]["vehicle_rate"];
                    if(isset($CostCut["city_tour"]) && !empty($CostCut["city_tour"])) {
                        $total += ((float)($CostCut->Rate[0]["adult_ticket"])*$QuotationArray["pax"]["adult"]) +
                        ((float)$CostCut->Rate[0]["child_ticket"]*($QuotationArray["pax"]["cwb"] + $QuotationArray["pax"]["cnb"]));
                    }

                    $packgeArray = array(
                        "vehicle" => $CostCut->Rate[0]["vehicle_rate"],
                        "adult_ticket" => $CostCut->Rate[0]["adult_ticket"],
                        "child_ticket" => $CostCut->Rate[0]["child_ticket"],
                        "total" => $total
                    );

                    $mainCostArr[$day]["cost-cuts"][$item]["package"] = json_encode($packgeArray);
                }
            }
        }

        return $mainCostArr;
    }

    function pc_array_power_set($array) {
        // initialize by adding the empty set
        $results = array(array( ));

        foreach ($array as $element)
            foreach ($results as $combination)
                array_push($results, array_merge(array($element), $combination));

        return $results;
    }

    public function getCostCutRate($Quotation) {
        $CostCutRate = [];
        foreach ($Quotation["cost_cutting"]??[] as $Day => $CostCut) {
            foreach ($CostCut as $ID => $apply) {
                $CostCutRate = $this->getCurrentCost($ID, $Quotation);
            }
        }

        return $this->formatCostCut($CostCutRate, $Quotation);
    }

    public function getCurrentCost($ID, $Quotation) {
        $CostCutRate = [];
        $Place = new Place();
        $Transport = new Transport();

        if(isset(Costcut::find($ID)->template)) {
            $Template = Costcut::find($ID)->template;
            $PlaceArrayPP = $this->getValueTemplate("PP", $Template);
            if(isset($PlaceArrayPP) && !empty($PlaceArrayPP)) {
                foreach ($PlaceArrayPP as $PlacePP) {
                    $PlacePPArray = explode("/", $PlacePP);

                    if(TransportType::getType($Quotation["country"]) == 1) {
                        $distance = $Place->getPathDistance($PlacePPArray);
                        $CostCutRate["PP"][] = $this->getKMRate($distance, $Quotation);
                    } else if(TransportType::getType($Quotation["country"]) == 2) {
                        $CostCutRate["PP"][] = $Transport->getPPRate($PlacePPArray, $Quotation);
                    }
                }
            }

            $PlaceArrayC = $this->getValueTemplate("C", $Template);
            if(isset($PlaceArrayC) && !empty($PlaceArrayC)) {
                $PlaceArrayC = explode("/", $PlaceArrayC[0]);
                $FinalCurrency = CountryCurrency::getFinalCurrency($Quotation);
                $CostCutRate["C"][] = CityTour::getCityTourCost($PlaceArrayC, $Quotation['market'], $Quotation['pax']['cwb'] + $Quotation['pax']['cnb'], $FinalCurrency, $Quotation);;
            }

            $PlaceArrayMT = $this->getValueTemplate("MT", $Template);

            if(isset($PlacePPArray[1])) {
                $place = $PlacePPArray[1];
            } else {
                $place = CityTour::find($PlaceArrayC)->first()->place;
            }

            if(isset($PlaceArrayMT) && !empty($PlaceArrayMT)) {
                foreach ($PlaceArrayMT as $ID) {

                    $CostCutRate["MT"][$ID][] = Transport::getMealTRate($this->mealTimeArr[$ID], $place, $Quotation);
                }
            }
        }

        return $CostCutRate;
    }

    public function getKMRate($distance, $QuotationArray) {
        $Vehicle = new Vehicle();
        $FinalCurrency = CountryCurrency::getFinalCurrency($QuotationArray);
        $TransportCurrency = CountryCurrency::getPlaceCurrency($QuotationArray['country'], 'transport');

        #Vehicle
        $VehicleType = $Vehicle->getPaxToVehicle($QuotationArray);//get vehicle vehicle
        $rate = currency($VehicleType->rate, $TransportCurrency->code, $FinalCurrency->code, false);

        $total = ($distance["disatnce"] / 1000) * $rate;

        return $total;
    }

    public function formatCostCut($CostCutRate, $Quotation) {
        $CostCutRateFormatted = [];

        $total = 0;
        if(isset($CostCutRate["PP"]) && !empty($CostCutRate["PP"])) {
            $subTot = 0;
            foreach ($CostCutRate["PP"] as $item) {
                $subTot += $item;
            }
            $total += $subTot;
            $CostCutRateFormatted["PP"]["total"] = $subTot;
            $CostCutRateFormatted["PP"]["pp"] = $subTot/$Quotation['pax']['adult'];
        }

        if(isset($CostCutRate["C"]) && !empty($CostCutRate["C"])) {
            $subTot = 0;
            $subChildTot = 0;
            foreach ($CostCutRate["C"] as $item) {
                foreach ($item as $innerItem) {
                    $subTot += $innerItem["adult"];
                    $subChildTot += $innerItem["child"];
                }
            }

            $CostCutRateFormatted["C"]["total"] = ($subTot * $Quotation['pax']['adult']) + ($subChildTot * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']));
            $CostCutRateFormatted["C"]["child"] = $subChildTot * ($Quotation['pax']['cwb'] + $Quotation['pax']['cnb']);
            $CostCutRateFormatted["C"]["pp"] = $subTot;
            $total += $CostCutRateFormatted["C"]["total"];
        }

        if(isset($CostCutRate["MT"]) && !empty($CostCutRate["MT"])) {
            $subTot = 0;
            foreach ($CostCutRate["MT"] as $item) {
                foreach ($item as $innerItem) {
                    $subTot += $innerItem["cost"];
                }
            }

            $total += $subTot;
            $CostCutRateFormatted["MT"]["total"] = $subTot;
            $CostCutRateFormatted["MT"]["pp"] = $subTot/$Quotation['pax']['adult'];
        }

        return ["break_down" => $CostCutRateFormatted, "total" => $total];
    }

    public function getCostCutPkgRate($Quotation) {
        $pkg_total = "";

        if(isset($Quotation['cost_cut_rate'])) {
            foreach ($Quotation["cost_cut_rate"]??[] as $Day => $CostCut) {
                foreach ($CostCut as $ID => $apply) {
                    $pkg_total = $apply;
                }
            }
        }
        return $pkg_total;
    }

    public function getValueTemplate($key, $template) {
        preg_match_all('/\['.$key.'-(.*?)\]/', $template, $output_array);

        return $output_array[1];
    }

    public function Rate() {
        return $this->hasMany("App\Model\Costcut\CostcutRate", "costcut", "ID");
    }
}
?>