<?php

namespace App\Model\Notice;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Notice\NoticeRead
 *
 * @property int $id
 * @property int $user
 * @property int $notice_id
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeRead whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeRead whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeRead whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeRead whereNoticeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeRead whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeRead whereUser($value)
 * @mixin \Eloquent
 */
class NoticeRead extends Model
{
    protected $table = 'notice_read';

}
