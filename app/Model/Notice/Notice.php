<?php

namespace App\Model\Notice;

use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Wildside\Userstamps\Userstamps;

/**
 * App\Model\Notice\Notice
 *
 * @property int $id
 * @property string $note
 * @property string $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read \App\User $by
 * @property-read \App\Model\Notice\NoticeRead $read
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Notice\NoticeUser[] $user
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\Notice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\Notice whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\Notice whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\Notice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\Notice whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\Notice whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\Notice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\Notice whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class Notice extends Model
{
    use Userstamps;

    protected $table = 'notice';


    const TYPE_WARNING = 'TYPE_WARNING';
    const TYPE_INFO = 'TYPE_INFO';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function user()
    {
        return $this->hasMany('App\Model\Notice\NoticeUser', 'notice_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function by()
    {
        return $this->hasOne('App\User', 'id', 'created_by');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function read()
    {
        return $this->hasOne('App\Model\Notice\NoticeRead', 'notice_id', 'id');
    }

    /**
     * @param User $user
     * @return Collection
     */
    static public function getUnread(User $user)
    {


        return Notice::where(function($fQuery) use ($user) {
            $fQuery->whereHas('user', function ($q) use ($user) {
                $q->where('user', $user->id);
            })->orDoesntHave('user');

            })->whereDoesntHave('read',function($q) use ($user) {
            $q->where('user',  $user->id);
        })->limit(10)->get();

    }
}
