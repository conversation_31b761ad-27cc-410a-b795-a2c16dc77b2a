<?php

namespace App\Model\Notice;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Notice\NoticeUser
 *
 * @property int $id
 * @property int $user
 * @property int $notice_id
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeUser whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeUser whereNoticeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeUser whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Notice\NoticeUser whereUser($value)
 * @mixin \Eloquent
 */
class NoticeUser extends Model
{
    protected $table = 'notice_users';

}
