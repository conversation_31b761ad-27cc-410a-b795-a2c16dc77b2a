<?php

namespace App\Model\Text;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Text\Text
 *
 * @property int $ID
 * @property string $code
 * @property string $code_2t
 * @property string $name
 * @property string $native_name
 * @property int $prefer
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereCode2t($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereNativeName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text wherePrefer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Text\Text whereUploadId($value)
 * @mixin \Eloquent
 */
class Text extends Model
{
    protected $table = 'apple_languages'; 
	
	
	
	
	
	
	
}
