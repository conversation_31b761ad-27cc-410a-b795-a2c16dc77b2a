<?php

namespace App\Model\StaticPackages;

use App\Model\Image\Image;
use Illuminate\Database\Eloquent\Model;

/**
 * Class StaticPackages
 * @package App\Model\StaticPackages
 */
class StaticPackages extends Model
{
    protected $table = "apple_static_packages";


    protected $appends = ['image'];


    /**
     * @return mixed
     */
    public function getImageAttribute()
    {
        $imageVer = Image::getImageDirect($this->id, '4x', 'static_packages', 1, $this->name)[0];
        $imageVer = str_replace("/4x","",$imageVer);
        return $imageVer;
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    function place(){
        return $this->hasMany(StaticPackagesPlaces::class,'package_id','id');
    }



    function hotel(){
        return $this->hasMany(StaticPackagesHotels::class,'package_id','id');
    }
}
