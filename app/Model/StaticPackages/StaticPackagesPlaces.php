<?php

namespace App\Model\StaticPackages;

use App\Model\Place\Place;
use Illuminate\Database\Eloquent\Model;

/**
 * Class StaticPackagesPlaces
 * @package App\Model\StaticPackages
 */
class StaticPackagesPlaces extends Model
{
    protected $table = "apple_static_packages_places";

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    function place(){
        return $this->hasOne(Place::class,'ID','place');

    }
}
