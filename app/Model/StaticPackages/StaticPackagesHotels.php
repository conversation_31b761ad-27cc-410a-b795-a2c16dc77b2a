<?php

namespace App\Model\StaticPackages;

use App\Model\Hotel\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * Class StaticPackagesPlaces
 * @package App\Model\StaticPackages
 */
class StaticPackagesHotels extends Model
{
    protected $table = "apple_static_packages_hotels";

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    function hotel(){
        return $this->hasOne(Hotel::class,'ID','hotel');

    }
}
