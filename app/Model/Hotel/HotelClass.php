<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\HotelClass
 *
 * @property int $ID
 * @property string|null $class
 * @property int|null $star
 * @property string|null $category_code
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereCategoryCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereClass($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereStar($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelClass whereUploadId($value)
 * @mixin \Eloquent
 */
class HotelClass extends Model
{
    protected $primaryKey = "ID";

    protected $table = 'apple_hotel_class';

}
