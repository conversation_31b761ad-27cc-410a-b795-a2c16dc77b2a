<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\Contact
 *
 * @property int $ID
 * @property int $hotel
 * @property string $contact_id
 * @property int $type
 * @property string|null $note
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereContactId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Contact whereUploadId($value)
 * @mixin \Eloquent
 */
class Contact extends Model
{
    protected $table = 'apple_hotel_contacts';

    protected $guarded = 'ID';
    protected $primaryKey = "ID";


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    function type(){
        return $this->hasOne(ContactType::class, 'id', 'type')
            ->select('id','type');
    }
}
