<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\RoomCategory
 *
 * @property int $ID
 * @property string|null $name
 * @property string|null $category_code
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereCategoryCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomCategory whereUploadId($value)
 * @mixin \Eloquent
 */
class RoomCategory extends Model
{
    protected $table = 'apple_hotel_room_category';


}
