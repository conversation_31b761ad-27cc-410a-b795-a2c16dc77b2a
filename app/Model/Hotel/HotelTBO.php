<?php

namespace App\Model\Hotel;

use File;
use Illuminate\Database\Eloquent\Model;
use Sabre\Xml\Reader as XMLParser;
use SimpleXMLElement;
use View;

/**
 * App\Model\Hotel\HotelTBO
 *
 * @mixin \Eloquent
 */
class HotelTBO extends Model
{


    //Request actions
    var $HotelSearchAction = "http://TekTravel/HotelBookingApi/HotelSearch";
    var $AvailableHotelRoomsAction = "http://TekTravel/HotelBookingApi/AvailableHotelRooms";
    var $HotelCancellationPolicyAction = "http://TekTravel/HotelBookingApi/HotelCancellationPolicy";
    var $HotelCancellationPolicyForAllRoomsAction = "http://TekTravel/HotelBookingApi/HotelCancellationPolicyForAllRooms";
    var $HotelBookAction = "http://TekTravel/HotelBookingApi/HotelBook";
    var $AvailabilityAndPricingAction = "http://TekTravel/HotelBookingApi/AvailabilityAndPricing";
    var $HotelBookingDetailAction = "http://TekTravel/HotelBookingApi/HotelBookingDetail";
    var $GenerateInvoiceAction = "http://TekTravel/HotelBookingApi/GenerateInvoice";
    var $AmendmentAction = "http://TekTravel/HotelBookingApi/Amendment";
    var $HotelCancelAction = "http://TekTravel/HotelBookingApi/HotelCancel";
    var $CountryListAction = "http://TekTravel/HotelBookingApi/CountryList";
    var $DestinationCityListAction = "http://TekTravel/HotelBookingApi/DestinationCityList";
    var $TopDestinationsAction = "http://TekTravel/HotelBookingApi/TopDestinations";
    var $HotelDetailsAction = "http://TekTravel/HotelBookingApi/HotelDetails";
    var $HotelCodeListAction = "http://TekTravel/HotelBookingApi/HotelCodes";
    var $HotelSearchWithRoomsAction = "http://TekTravel/HotelBookingApi/HotelSearchWithRooms";
    var $HotelBookingDetailBasedOnDateAction = "http://TekTravel/HotelBookingApi/HotelBookingDetailBasedOnDate";
    var $GiataHotelCodeListAction = "http://TekTravel/HotelBookingApi/GiataHotelCodes";
    var $TagInfoAction = "http://TekTravel/HotelBookingApi/TagInfos";

    var $url = "http://api.tbotechnology.in/HotelAPI_V7/HotelService.svc?";


    //Credentials
    var $Credentials = ['UserName' => '', 'Password' => ''];


    function sendCustomSoap()
    {

        $wsdlContent = File::get(public_path('\assets\file\TBO.wsdl'));


        $parser = new XMLParser();

        $parser->xml($wsdlContent);
        $result = $parser->parse();


        foreach ($result['value'][3]['value'] as $Key => $operationArray) {

            if ($operationArray['value'][0]) {

                echo 'var $';
                echo($operationArray['attributes']['name']), 'Action = ';
                echo '"', ($operationArray['value'][0]['attributes']['soapAction']), '"';
                echo ";<br>";


            }


        }


// The URL to POST to
        $url = "http://api.tbotechnology.in/HotelAPI_V7/HotelService.svc?";
// The value for the SOAPAction: header
        $action = "http://TekTravel/HotelBookingApi/CountryListRequest";

        $mySOAP = <<<XML
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:hot="http://TekTravel/HotelBookingApi">
   <soap:Header xmlns:wsa="http://www.w3.org/2005/08/addressing">
      <hot:Credentials UserName="" Password=""/>
      <wsa:Action>http://TekTravel/HotelBookingApi/CountryList</wsa:Action>
      <wsa:To>http://api.tbotechnology.in/HotelAPI_V7/HotelService.svc</wsa:To>
   </soap:Header>
   <soap:Body>
      <hot:CountryListRequest/>
   </soap:Body>
</soap:Envelope>
XML;


        // The HTTP headers for the request (based on image above)
        $headers = array(
            'Content-Type: application/soap+xml; charset=utf-8',
            'Content-Length: ' . strlen($mySOAP),
            'SOAPAction: ' . $action
        );

        // Build the cURL session
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $mySOAP);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);

        // Send the request and check the response
        if (($result = curl_exec($ch)) === FALSE) {
            die('cURL error: ' . curl_error($ch) . "<br />\n");
        }
        curl_close($ch);

        // Handle the response from a successful request
        $soap = simplexml_load_string($result);
        //$response = $soap->children('http://www.w3.org/2003/05/soap-envelope')->Body->children()->CountryListResponse;


        sd($result);
    }

    /**
     * @param $action
     * @param $xml
     * @return bool|mixed
     */
    function sendRequest($action, $xml)
    {



        $headers = array(
            'Content-Type: application/soap+xml; charset=utf-8',
            'Content-Length: ' . strlen($xml),
            'SOAPAction: ' . $action
        );




        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        $result = curl_exec($ch);



        if ($result) {
            $XMLObject = new SimpleXMLElement($result);
            return objectToArray($XMLObject->children('http://www.w3.org/2003/05/soap-envelope')->Body->children());
        } else
            return false;


    }


    /**
     * @param $Data
     * @return mixed
     */
    function getLowestHotel($Data)
    {
        $HotelSearchXML = View::make("element.hotel.TBO.hotel-search-xml", ['Credentials' => $this->Credentials, 'Action' => $this->HotelSearchAction, 'Data' => $Data])->render();
        return $this->sendRequest($this->HotelSearchAction, $HotelSearchXML)['HotelSearchResponse'];
    }

    /**
     * @param $Data
     * @return mixed
     */
    function getHotelDetails($Data)
    {

        $HotelDetails = View::make("element.hotel.TBO.hotel-details-xml", ['Credentials' => $this->Credentials, 'Action' => $this->HotelDetailsAction, 'Data' => $Data])->render();
        return $this->sendRequest($this->HotelDetailsAction, $HotelDetails)['HotelDetailsResponse'];
    }

    /**
     * @param $Data
     * @return mixed
     */
    function AvailableHotelRooms($Data)
    {
        $AvailableHotelRooms = View::make("element.hotel.TBO.hotel-available-room-xml", ['Credentials' => $this->Credentials, 'Action' => $this->AvailableHotelRoomsAction, 'Data' => $Data])->render();
        return $this->sendRequest($this->AvailableHotelRoomsAction, $AvailableHotelRooms)['HotelRoomAvailabilityResponse'];
    }

    /**
     * @param $Data
     * @return mixed
     */
    function getHotelCancellationPolicy($Data)
    {

        $HotelCancellationPolicy = View::make("element.hotel.TBO.hotel-room-cancellation-policy-xml", ['Credentials' => $this->Credentials, 'Action' => $this->HotelCancellationPolicyAction, 'Data' => $Data])->render();
        return $this->sendRequest($this->HotelCancellationPolicyAction, $HotelCancellationPolicy)['HotelCancellationPolicyResponse'];
    }

    /**
     * @param $Data
     * @return mixed
     */
    function getAvailablePriceHotelRooms($Data)
    {

        $AvailablePriceHotelRooms = View::make("element.hotel.TBO.hotel-available-price-room-xml", ['Credentials' => $this->Credentials, 'Action' => $this->AvailabilityAndPricingAction, 'Data' => $Data])->render();
        return $this->sendRequest($this->AvailabilityAndPricingAction, $AvailablePriceHotelRooms)['AvailabilityAndPricingResponse'];
    }

    /**
     * @param $FirstName
     * @param $LastName
     * @return string
     */
    function getClientRef($FirstName, $LastName)
    {
        $cus_1 = "";
        if (strlen($LastName) >= 4)
            $cus_1 = strtoupper(substr($LastName, 0, 4));
        else if (strlen($LastName) == 3)
            $cus_1 = strtoupper(substr($LastName, 0, 3)) . strtoupper(substr($LastName, 0, 1));
        else if (strlen($LastName) == 2)
            $cus_1 = strtoupper(substr($LastName, 0, 2)) . strtoupper(substr($LastName, 0, 2));
        else if (strlen($LastName) == 1 && strlen($FirstName) > 1)
            $cus_1 = strtoupper(substr($LastName, 0, 1)) . strtoupper(substr($FirstName, 0, 1)) . strtoupper(substr($LastName, 0, 1)) . strtoupper(substr($FirstName, 0, 1));

        $today = date("Y-m-d H:i:s");

        $ref = substr($today, 8, 2) . substr($today, 5, 2) . substr($today, 2, 2) . substr($today, 11, 2) . substr($today, 14, 2) . substr($today, 17, 2) . "000#" . $cus_1;

        return $ref;
    }


}
