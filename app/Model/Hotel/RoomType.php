<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\RoomType
 *
 * @property int $ID
 * @property string|null $type
 * @property string $short_name
 * @property string $other_name
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereOtherName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereShortName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RoomType whereUploadId($value)
 * @mixin \Eloquent
 */
class RoomType extends Model
{
    protected $table = 'apple_hotel_room_type';

    protected $guarded = 'ID';
}
