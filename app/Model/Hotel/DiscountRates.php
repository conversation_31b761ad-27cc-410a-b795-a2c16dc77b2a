<?php

namespace App\Model\Hotel;


use App\Model\Place\Place;
use App\Model\Quotation\QuotationHotel;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Hotel\Rates
 *
 * @property int $ID
 * @property int $start_year
 * @property int $start_month
 * @property int $start_day
 * @property int $end_year
 * @property int $end_month
 * @property int $end_day
 * @property string|null $rate_key
 * @property int $meal
 * @property int $hotel
 * @property float $rate
 * @property int $room_type
 * @property int $room_category
 * @property int $market
 * @property int $child_count
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property int|null $tour_session
 * @property \Carbon\Carbon|null $deleted_at
 * @property int|null $upload_id
 * @property string $special
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\Rates onlyTrashed()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereChildCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereEndDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereEndMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereEndYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereMarket($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereRateKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereRoomCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereRoomType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereSpecial($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereStartDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereStartMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereStartYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereTourSession($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereUploadId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\Rates withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\Rates withoutTrashed()
 * @mixin \Eloquent
 */
class DiscountRates extends Model
{

    var $BookDate;
    protected $table = 'apple_hotel_discount_room_rates';
    protected $table_child = 'apple_hotel_discount_room_rates_child';

    var $Error;
    var $RateShow = false;
    protected $dates = ['deleted_at'];

    use SoftDeletes;

    public function hotel()
    {
        return $this->belongsTo('App\Hotel', 'ID', 'hotel');
    }

    /**
     * @param $Hotel
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param int $Limit
     */
    function getDiscountHotel($Hotel, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false, $Nights, $Limit = 1) {

        $SelectedHotelRateValue = 0;

        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        // match book date first
        $Rates = DB::table(DB::raw($this->table . " rate"))
            ->select(DB::raw("rate.*, rate.rate, hotel.*, rate.hotel , hotel.city as CityH, hotel.class as ClassH"));

        $Rates->join(DB::raw('apple_hotels hotel'), 'hotel.ID', '=', 'rate.hotel');//join hotel table for search city

        $Rates->where('rate.hotel', '=', $Hotel); // hotel
        $Rates->where('hotel.availability', '=', 1); // availability

        $Rates->whereNull("rate.deleted_at");

        #If they can change rate  give them any hotel
        // if (!\Auth::user()->can("change_hotel_rates")) {

            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat);
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

            #####filters room######	###################
            //if Set room type
            if ($RoomType) {
                $Rates->where(function ($query) use ($RoomType) {
                    $query->whereIn("rate.room_type", array_keys(array_filter($RoomType)));
                    $query->orWhereIn("rate.room_type", [1, 2, 3]);
                });
            } else {
                $Rates->whereIn("rate.room_type", [1, 2, 3]);
            }

            //if Set Meal
            if ($Meal)
                $Rates->where("rate.meal", "=", $Meal);

            //if Set RoomCategory
            if ($RoomCategory)
                $Rates->where("rate.room_category", "=", $RoomCategory);

            //if Set Market
            if ($Market) {
                $Rates->where(function ($query) use ($Market) {
                    $query->where("market", "=", $Market)
                        ->orWhere("market", "=", 1);
                });
            }

            //if Set Nights

            if ($Nights) {
                $Rates->where("rate.nights", "=", $Nights);
            }

        // } else
        //     $Rates->where("rate.room_type", '=', 2);//by default select double for lowest rate calculate


        //limit
        if ($Limit)
            $Rates->limit($Limit);
        #######end of filters ######################


        $Rates->groupBy('rate.hotel');


        $Rates->orderBy('hotel.preferred', 'desc');

        $Rates->orderBy('rate.rate', 'asc');


        $RateList = $Rates->get();

        if ($RateList->isEmpty())
            return false;
        else
            return $RateList;

    }

    /**
     * @param $Hotel
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param int $Limit
     */
    function getDiscountPlace($Place, $Hotel, $BookDate, $RoomType, $Meal, $RoomCategory = false, $Market, $Nights, $Limit = 1) {

        $SelectedHotelRateValue = 0;

        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $PlaceObj = Place::where('ID', $Place)->first();

        // match book date first
        $Rates = DB::table(DB::raw($this->table . " rate"))
            ->select(DB::raw("rate.*, rate.rate, hotel.*, rate.hotel , hotel.city as CityH, hotel.class as ClassH"));

        $Rates->join(DB::raw('apple_hotels hotel'), 'hotel.ID', '=', 'rate.hotel');//join hotel table for search city

        $Rates->where('rate.hotel', '<>', $Hotel); // hotel
        $Rates->Having("CityH", "=", $Place); // hotel
        $Rates->where('hotel.availability', '=', 1); // availability

        $Rates->whereNull("rate.deleted_at");

        #If they can change rate  give them any hotel
        // if (!\Auth::user()->can("change_hotel_rates")) {

        $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat);
        $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        #####filters room######	###################
        //if Set room type
        if ($RoomType) {
            $Rates->where(function ($query) use ($RoomType) {
                $query->whereIn("rate.room_type", array_keys(array_filter($RoomType)));
                $query->orWhereIn("rate.room_type", [1, 2, 3]);
            });
        } else {
            $Rates->whereIn("rate.room_type", [1, 2, 3]);
        }

        //if Set Meal
        if ($Meal)
            $Rates->where("rate.meal", "=", $Meal);

        //if Set RoomCategory
        /*if ($RoomCategory)
            $Rates->where("rate.room_category", "=", $RoomCategory);*/

        //if Set Market
        if ($Market) {
            $Rates->where(function ($query) use ($Market) {
                $query->where("market", "=", $Market)
                    ->orWhere("market", "=", 1);
            });
        }

        //if Set Nights

        if ($Nights) {
            $Rates->where("rate.nights", "=", $Nights);
        }

        // } else
        //     $Rates->where("rate.room_type", '=', 2);//by default select double for lowest rate calculate


        //limit
        if ($Limit)
            $Rates->limit($Limit);
        #######end of filters ######################


        $Rates->groupBy('rate.hotel');


        $Rates->orderBy('hotel.preferred', 'desc');

        $Rates->orderBy('rate.rate', 'asc');


        $RateList = $Rates->get();

        if ($RateList->isEmpty())
            return false;
        else
            return $RateList;

    }

    /**
     * @param $Hotel
     * @param bool $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param bool $GroupBy
     * @param bool $OnlyHotelBeds
     * @param int $limit
     * @return array|\Illuminate\Support\Collection
     */
    function getRate($Hotel, $BookDate = false, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false, $GroupBy = false, $OnlyHotelBeds = false, $limit = 500, $Nights = false)
    {


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rates = DB::table($this->table . ' AS parent')
            ->select(DB::Raw("*
            
            ,CONCAT(start_year,start_month,start_day) as start_date
            ,CONCAT(start_year,start_month,start_day,end_year,end_month,end_day) as start_end_date
            ,(SELECT ID
                FROM apple_hotel_room_rates
                WHERE
                     ID!=  parent.ID and
                     hotel = parent.hotel and 
                     meal = parent.meal and 
                     room_category = parent.room_category and 
                     market = parent.market and 
                     room_type = parent.room_type and
                     
                     
                     STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) = STR_TO_DATE(CONCAT(  parent.start_day, '/',  parent.start_month, '/',  parent.start_year ) ,'%d/%m/%Y' ) and
                     STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) = STR_TO_DATE(CONCAT(  parent.end_day, '/',  parent.end_month, '/',  parent.end_year ) ,'%d/%m/%Y' )
                     ORDER BY `ID` DESC
                     limit 1
                     
                     
                ) AS duplicate"));



        if ($BookDate) {
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
                ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        }


        $Rates->where("hotel", $Hotel);
        $Rates->whereNull('deleted_at');

        //if Set room type
        if ($RoomType) {
            if (is_array($RoomType))
                $Rates->whereIn("room_type", $RoomType);
            else
                $Rates->where("room_type", '=', $RoomType);
        }

        //if Set Meal
        if ($Meal) {
            if (is_array($Meal))
                $Rates->whereIn("meal", $Meal);
            else
                $Rates->where("meal", '=', $Meal);
        }

        //if Set Meal
        if ($Nights) {
            $Rates->where("nights", '=', $Nights);
        }

        //if Set RoomCategory
        if ($RoomCategory) {

            if (is_array($RoomCategory))
                $Rates->whereIn("room_category", $RoomCategory);
            elseif ($RoomCategory == 'first')
                $Rates->where("room_category", '=', DB::Raw("(select room_category from {$this->table} where hotel = {$Hotel} limit 1)"));
            else {
                $Rates->where(function ($query) use ($RoomCategory) {
                    $query->where("room_category", "=", $RoomCategory);
                    #->orWhere(DB::Raw(1), '=', 1);
                });


            }
        }
        if ($OnlyHotelBeds)
            $Rates->whereNotNull('rate_key');

        //if Set Market
        if ($Market) {

            if (is_array($Market))
                $Rates->whereIn("market", $Market);
            elseif ($this->RateShow) {

                $Rates->where("market", $Market);
            } else {
                $Rates->where(function ($query) use ($Market) {
                    $query->where("market", "=", $Market)
                        ->orWhere("market", '=', 1);
                });
            }

        }


        if ($GroupBy) {
            $Rates->GroupBy($GroupBy);

        }

        $Rates->OrderBy('rate', 'asc');

        $Rates->limit($limit);

        return $Rates->get();

    }


    /**
     * @param $Hotel
     * @param $BookDate
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param bool $AgeFrom
     * @param bool $AgeTo
     * @param bool $RoomType
     * @return \Illuminate\Support\Collection
     */
    function getRateChild($Hotel, $BookDate, $Meal = false, $RoomCategory = false, $Market = false, $AgeFrom = false, $AgeTo = false, $RoomType = false, $Nights = false)
    {


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rates = DB::table($this->table_child)
            ->select(DB::Raw("*,CONCAT(start_year,start_month,start_day) as start_date,CONCAT(start_year,start_month,start_day,end_year,end_month,end_day) as start_end_date"));
        if ($BookDate) {
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
                ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        }
        $Rates->where("hotel", "=", $Hotel);
        $Rates->whereNull('deleted_at');


        //Age
        if ($AgeFrom !== false && $AgeTo !== false) {
            $Rates->where("age_from", "=", $AgeFrom);
            $Rates->where("age_to", "=", $AgeTo);
        }

        //room type
        if ($RoomType) {
            if (is_array($RoomType))
                $Rates->whereIn("room_type", $RoomType);
            else
                $Rates->where("room_type", '=', $RoomType);
        }

        //if Set Meal
        if ($Meal)
            $Rates->where("meal", "=", $Meal);

        //if Set Meal
        if ($Nights) {
            $Rates->where("nights", '=', $Nights);
        }

        //if Set RoomCategory
        if ($RoomCategory)
            $Rates->where("room_category", "=", $RoomCategory);

        //if Set Market
        if ($Market) {

            if (is_array($Market))
                $Rates->whereIn("market", $Market);
            elseif ($this->RateShow)
                $Rates->where("market", $Market);
            else
                $Rates->whereIn("market", [$Market, 1]);
        }

        $Rates->OrderBy('special', 'asc');
        $Rates->OrderBy('rate', 'asc');

        $Rates->limit(1000);

        return $Rates->get();

    }

}
