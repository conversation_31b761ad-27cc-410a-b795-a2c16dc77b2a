<?php

namespace App\Model\Hotel;


use App\Model\Place\Place;
use App\Model\Quotation\QuotationHotel;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Model\Hotel\Rates
 *
 * @property int $ID
 * @property int $start_year
 * @property int $start_month
 * @property int $start_day
 * @property int $end_year
 * @property int $end_month
 * @property int $end_day
 * @property string|null $rate_key
 * @property int $meal
 * @property int $hotel
 * @property float $rate
 * @property int $room_type
 * @property int $room_category
 * @property int $market
 * @property int $child_count
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property int|null $tour_session
 * @property \Carbon\Carbon|null $deleted_at
 * @property int|null $upload_id
 * @property string $special
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\Rates onlyTrashed()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereChildCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereEndDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereEndMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereEndYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereMarket($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereRateKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereRoomCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereRoomType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereSpecial($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereStartDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereStartMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereStartYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereTourSession($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Rates whereUploadId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\Rates withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\Rates withoutTrashed()
 * @mixin \Eloquent
 */
class Rates extends Model
{

    var $BookDate;
    protected $table = 'apple_hotel_room_rates';
    protected $table_child = 'apple_hotel_room_rates_child';

    var $Error;
    var $RateShow = false;
    protected $dates = ['deleted_at'];

    use SoftDeletes;

    public function hotel()
    {
        return $this->belongsTo('App\Hotel', 'ID', 'hotel');
    }


    /**
     * @param Carbon $Date
     * @param $RateArray
     * @return array
     */
    function hasRateArray(Carbon $Date, $RateArray)
    {

        $Data = [];

        foreach ($RateArray as $RateItem) {

            $StartDate = Carbon::parse($RateItem->start_year . '/' . $RateItem->start_month . '/' . $RateItem->start_day);
            $EndDate = Carbon::parse($RateItem->end_year . '/' . $RateItem->end_month . '/' . $RateItem->end_day);


            if ($Date >= $StartDate && $Date <= $EndDate) {

                return $RateItem;

            }
        }


        return $Data;

    }


    function getAvailabilityHotelPlace($Place, $BookDate) {
        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rates = DB::table(DB::raw($this->table . " rate"));
        $Rates->join(DB::raw('apple_hotels hotel'), 'hotel.ID', '=', 'rate.hotel');//join hotel table for search city
        $Rates->where(DB::raw("STR_TO_DATE(CONCAT( rate.start_day, '/', rate.start_month, '/', rate.start_year ) ,'%d/%m/%Y' )"),  '>=', $BookDateFormat);
        $Rates->where(DB::raw("STR_TO_DATE(CONCAT( rate.end_day, '/', rate.end_month, '/', rate.end_year ) ,'%d/%m/%Y' )"), '<=', $BookDateFormat);
        $Rates->where('hotel.availability', '=', 1); // availability
        $Rates->where('hotel.city', '=', $Place); // availability
        $Rates->whereNull("rate.deleted_at");
        $Rates->groupBy('rate.hotel');

        if($Rates->get()->count() > 0) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * @param $Place
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param int $Limit
     * @param bool $HotelSettingsModified
     * @param bool $dateRange ---------------------> only show hotels available for a specific range
     * @return bool|\Illuminate\Support\Collection
     * @throws \ReflectionException
     */
    function getLowestHotelPlace($Place, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false,
                                 $Limit = 1, $HotelSettingsModified = false, $dateRange = false, $ratesAvailable = true)
    {

        //Filters
        $Filters = isset($HotelSettingsModified['filter']) ? $HotelSettingsModified['filter'] : false;


        #get room type required
        $SelectedHotelRateValue = 0;
        if (!$RoomType && $HotelSettingsModified && !empty($HotelSettingsModified['hotel'])) {

            $HotelSettingsModified = arrayMapMulti('getActualDataType', $HotelSettingsModified);

            if (!empty($HotelSettingsModified['room_type'][2])) {
                $RoomType = 2;
            } elseif (!empty($HotelSettingsModified['room_type'][1])) {
                $RoomType = 1;
            } elseif (!empty($HotelSettingsModified['room_type'][3])) {
                $RoomType = 3;
            } else {
                $RoomType = 2;
            }

            $HotelID = $HotelSettingsModified['hotel'];
            $RateSelectedHotel = $this->getRate($HotelID, $HotelSettingsModified['check_in']??false, $RoomType,
                                         $HotelSettingsModified['meal_type']??false, $HotelSettingsModified['room_category']??false);

            if (!empty($RateSelectedHotel[0])) {
                $SelectedHotelRateValue = $RateSelectedHotel[0]->rate;
            } else
                $SelectedHotelRateValue = 0;


        }


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $PlaceObj = Place::where('ID', $Place)->first();

        //match book date first
        $Rates = DB::table(DB::raw($this->table . " rate"))
            // ->select(DB::raw("rate.*, rate.hotel , hotel.start_date, hotel.end_date, hotel.preferred, hotel.city as CityH, hotel.class as ClassH,
            ->select(DB::raw("rate.*, rate.rate, hotel.*, rate.hotel , hotel.city as CityH, hotel.class as ClassH, rate.ID as rateID, 
            
            COALESCE((
            SELECT 1 from apple_hotel_room_allotment WHERE hotel = rate.hotel and
            
            STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) >= '$BookDateFormat' and
            STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) <='$BookDateFormat'
        
            
            limit 1 
            
            ), 0) AS allotment,
            
            round(( 111.111 * DEGREES(acos( cos( radians($PlaceObj->latitude) ) * cos( radians( latitude) ) 
										   * cos( radians(longitude) - radians($PlaceObj->longitude)) + sin(radians($PlaceObj->latitude)) 
										   * sin( radians(latitude))))),2) as distance_from_city,
            
            (rate.rate-$SelectedHotelRateValue) as rate_difference
            
            "));
        #->Having("CityH", "=", $Place)
        #->OrHaving("distance_from_city", "<", 20);

        $Rates->join(DB::raw('apple_hotels hotel'), 'hotel.ID', '=', 'rate.hotel');//join hotel table for search city
        
            //join hotel table for search city

        //DB::raw("sum(rate.rate) as rate_tot"), DB::raw("count(rate.rate) as rate_count"), DB::raw("sum(rate.rate) /count(rate.rate) as avg_rate")


        $Rates->where('hotel.availability', '=', 1); // availability

        if ($dateRange){
            $Rates->where('hotel.preferred', '>=', 1); // preferred is validated from view

            $Rates->where('hotel.start_date', '<=', $BookDateFormat->format('Y-m-d')); // start date
            $Rates->where('hotel.end_date', '>=', $BookDateFormat->format('Y-m-d')); // end date

//        $Rates->whereRaw('date(hotel.start_date) <= ?', $BookDateFormat->format('Y-m-d')); // start date
//        $Rates->whereRaw('date(hotel.end_date) >= ?', $BookDateFormat->format('Y-m-d')); // end date
//        $Rates->where('hotel.end_date', '<=', $BookDateFormat->addDays(20)->format('Y-m-d')); // end date

        }

        $Rates->whereNull("rate.deleted_at");

        if($ratesAvailable) {
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat);
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);
        }

        #If they can change rate  give them any hotel
        if (!\Auth::user()->can("change_hotel_rates")) {
            if(!$ratesAvailable) {
                $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat);
                $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);
            }
            #####filters room######	###################
            //if Set room type
            if ($RoomType) {
                $Rates->where(function ($query) use ($RoomType) {
                    $query->where("rate.room_type", "=", $RoomType);
                    $query->orWhereIn("rate.room_type", [1, 2, 3]);
                });
            } else {
                $Rates->whereIn("rate.room_type", [1, 2, 3]);
            }

            //if Set Meal
            if ($Meal)
                $Rates->where("rate.meal", "=", $Meal);

            //if Set RoomCategory
            if ($RoomCategory)
                $Rates->where("rate.room_category", "=", $RoomCategory);

            //if Set Market
            if ($Market) {
                $Rates->where(function ($query) use ($Market) {
                    $query->where("market", "=", $Market)
                        ->orWhere("market", "=", 1);
                });
            }
        } else
            $Rates->where("rate.room_type", '=', 2);//by default select double for lowest rate calculate

        if ($Filters) {
            if(isset($Filters['name'])) {
                $Rates->where("hotel.name", "LIKE", "%{$Filters['name']}%");
            }

            if (isset($Filters['city']))
                $Rates->whereIn("hotel.city", $Filters['city']);
            else
                $Rates->Having("CityH", "=", $Place);

            if (isset($Filters['class']))
                $Rates->whereIn("hotel.class", $Filters['class']);


        } else {
            $Rates->Having("CityH", "=", $Place);
            $Rates->OrHaving("distance_from_city", "<", 20);
        }

        //limit
        if ($Limit)
            $Rates->limit($Limit);
        #######end of filters ######################

        $Rates->orderBy('rate.rate', 'asc');
        $Rates->orderBy('hotel.preferred', 'desc');
        $Rates->orderBy('allotment', 'desc');

        $Rates->groupBy('rate.hotel');

        $RateList = $Rates->get();
        
        if ($RateList->isEmpty())
            return false;
        else 
            return $RateList;

    }


    function ApiGetLowestHotelPlace($Place, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false, $Limit = 1, $HotelSettingsModified = false, $dateRange = false)
    {

        //Filters
        $Filters = isset($HotelSettingsModified['filter']) ? $HotelSettingsModified['filter'] : false;

        #get room type required
        $SelectedHotelRateValue = 0;
        if (!$RoomType && $HotelSettingsModified && !empty($HotelSettingsModified['hotel'])) {

            $HotelSettingsModified = arrayMapMulti('getActualDataType', $HotelSettingsModified);

            if (!empty($HotelSettingsModified['room_type'][2])) {
                $RoomType = 2;
            } elseif (!empty($HotelSettingsModified['room_type'][1])) {
                $RoomType = 1;
            } elseif (!empty($HotelSettingsModified['room_type'][3])) {
                $RoomType = 3;
            } else {
                $RoomType = 2;
            }

            $HotelID = $HotelSettingsModified['hotel'];
            $RateSelectedHotel = $this->getRate($HotelID, $HotelSettingsModified['check_in']??false, $RoomType, $HotelSettingsModified['meal_type']??false, $HotelSettingsModified['room_category']??false);

            if (!empty($RateSelectedHotel[0])) {
                $SelectedHotelRateValue = $RateSelectedHotel[0]->rate;
            } else {
                $SelectedHotelRateValue = 0;
            }
        }


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $PlaceObj = Place::where('ID', $Place)->first();

        //match book date first
        $Rates = DB::table(DB::raw($this->table . " rate"))
            ->select(DB::raw("rate.*, rate.rate, hotel.*, rate.hotel , hotel.city as CityH, hotel.class as ClassH,                            
            COALESCE((
            SELECT 1 from apple_hotel_room_allotment WHERE hotel = rate.hotel and
            
            STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) <= '$BookDateFormat' and
            STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) >= '$BookDateFormat'
        
            
            limit 1 
            
            ), 0) AS allotment,
            
            round(( 111.111 * DEGREES(acos( cos( radians($PlaceObj->latitude) ) * cos( radians( latitude) ) 
										   * cos( radians(longitude) - radians($PlaceObj->longitude)) + sin(radians($PlaceObj->latitude)) 
										   * sin( radians(latitude))))),2) as distance_from_city,
            
            (rate.rate-$SelectedHotelRateValue) as rate_difference
            
            "));
        #->Having("CityH", "=", $Place)
        #->OrHaving("distance_from_city", "<", 20);

        $Rates->join(DB::raw('apple_hotels hotel'), 'hotel.ID', '=', 'rate.hotel');//join hotel table for search city

        $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

            //join hotel table for search city

        //DB::raw("sum(rate.rate) as rate_tot"), DB::raw("count(rate.rate) as rate_count"), DB::raw("sum(rate.rate) /count(rate.rate) as avg_rate")
        if ($dateRange){
            $Rates->where('hotel.preferred', '>=', 1); // preferred is validated from view

            $Rates->where('hotel.start_date', '<=', $BookDateFormat->format('Y-m-d')); // start date
            $Rates->where('hotel.end_date', '>=', $BookDateFormat->format('Y-m-d')); // end date

//        $Rates->whereRaw('date(hotel.start_date) <= ?', $BookDateFormat->format('Y-m-d')); // start date
//        $Rates->whereRaw('date(hotel.end_date) >= ?', $BookDateFormat->format('Y-m-d')); // end date
//        $Rates->where('hotel.end_date', '<=', $BookDateFormat->addDays(20)->format('Y-m-d')); // end date

        }

        // Start Checking for deleted hotels and hotels rate by priyantha.

        $Rates->whereNull("rate.deleted_at");

        // End Checking for deleted hotels and hotels rate by priyantha.

        #If they can change rate  give them any hotel
        if (!\Auth::user()->can("change_hotel_rates")) {

            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat);
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

            #####filters room######	###################
            //if Set room type
            if ($RoomType) {
                $Rates->where(function ($query) use ($RoomType) {
                    $query->where("rate.room_type", "=", $RoomType);
                    $query->orWhereIn("rate.room_type", [1, 2, 3]);
                });
            } else {
                $Rates->whereIn("rate.room_type", [1, 2, 3]);
            }

            //if Set Meal
            if ($Meal)
                $Rates->where("rate.meal", "=", $Meal);

            //if Set RoomCategory
            if ($RoomCategory)
                $Rates->where("rate.room_category", "=", $RoomCategory);

            //if Set Market
            if ($Market) {
                $Rates->where(function ($query) use ($Market) {
                    $query->where("market", "=", $Market)
                        ->orWhere("market", "=", 1);
                });
            }

        } else {

            if ($RoomType) {
                // $Rates->whereIn("rate.room_type", [1, 2, 3]);
                $Rates->where("rate.room_type", '=', $RoomType);//by default select double for lowest rate calculate
            } else {
                $Rates->where("rate.room_type", '=', 2);
            }
        }

        if ($Filters) {
            $Rates->where("hotel.name", "LIKE", "%{$Filters['name']}%");

            if (isset($Filters['city']))
                $Rates->whereIn("hotel.city", $Filters['city']);
            else
                $Rates->Having("CityH", "=", $Place);

            if (isset($Filters['class']))
                $Rates->whereIn("hotel.class", $Filters['class']);


        } else {
            $Rates->Having("CityH", "=", $Place);
            $Rates->OrHaving("distance_from_city", "<", 20);
        }


        //limit
        if ($Limit)
            $Rates->limit($Limit);
        #######end of filters ######################


        $Rates->groupBy('rate.hotel');

        $Rates->orderBy('hotel.preferred', 'desc');

        $Rates->orderBy('allotment', 'desc');
        $Rates->orderBy('rate.rate', 'asc');

        $RateList = $Rates->get();


        if ($RateList->isEmpty())
            return false;
        else
            return $RateList;

    }

    /**
     * @param $Hotel
     * @param bool $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param bool $GroupBy
     * @param bool $OnlyHotelBeds
     * @param int $limit
     * @return array|\Illuminate\Support\Collection
     */
    function getRate($Hotel, $BookDate = false, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false, $GroupBy = false, $OnlyHotelBeds = false, $limit = 500)
    {


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rates = DB::table($this->table . ' AS parent')
            ->select(DB::Raw("*
            
            ,CONCAT(start_year,start_month,start_day) as start_date
            ,CONCAT(start_year,start_month,start_day,end_year,end_month,end_day) as start_end_date
            ,(SELECT ID
                FROM apple_hotel_room_rates
                WHERE
                     ID!=  parent.ID and
                     hotel = parent.hotel and 
                     meal = parent.meal and 
                     room_category = parent.room_category and 
                     market = parent.market and 
                     room_type = parent.room_type and
                     
                     
                     STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) = STR_TO_DATE(CONCAT(  parent.start_day, '/',  parent.start_month, '/',  parent.start_year ) ,'%d/%m/%Y' ) and
                     STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) = STR_TO_DATE(CONCAT(  parent.end_day, '/',  parent.end_month, '/',  parent.end_year ) ,'%d/%m/%Y' )
                     ORDER BY `ID` DESC
                     limit 1
                     
                     
                ) AS duplicate"));



        if ($BookDate) {
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
                ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        }


        $Rates->where("hotel", $Hotel);
        $Rates->whereNull('deleted_at');

        //if Set room type
        if ($RoomType) {
            if (is_array($RoomType))
                $Rates->whereIn("room_type", $RoomType);
            else
                $Rates->where("room_type", '=', $RoomType);
        }

        //if Set Meal
        if ($Meal) {
            if (is_array($Meal))
                $Rates->whereIn("meal", $Meal);
            else
                $Rates->where("meal", '=', $Meal);
        }


        //if Set RoomCategory
        if ($RoomCategory) {

            if (is_array($RoomCategory))
                $Rates->whereIn("room_category", $RoomCategory);
            elseif ($RoomCategory == 'first')
                $Rates->where("room_category", '=', DB::Raw("(select room_category from {$this->table} where hotel = {$Hotel} limit 1)"));
            else {
                $Rates->where(function ($query) use ($RoomCategory) {
                    $query->where("room_category", "=", $RoomCategory);
                    #->orWhere(DB::Raw(1), '=', 1);
                });


            }
        }
        if ($OnlyHotelBeds)
            $Rates->whereNotNull('rate_key');

        //if Set Market
        if ($Market) {


            if (is_array($Market))
                $Rates->whereIn("market", $Market);
            elseif ($this->RateShow) {

                $Rates->where("market", $Market);
            } else {
                $Rates->where(function ($query) use ($Market) {
                    $query->where("market", "=", $Market)
                        ->orWhere("market", '=', 1);
                });
            }

        }

        if (!$Rates->get() && $Meal) { //if doesn't find the rate


            $TempRate = $this->getRate($Hotel, $BookDate, $RoomType, false, $RoomCategory, $Market);//
            $TempRateArray = [];


            foreach ($TempRate as $k => $RateItem) {

                $TempRateArray[$k] = clone $RateItem;

                $Lunch = (!empty(MealRates::where('hotel', $Hotel)->where('meal_type', $RateItem->meal)->first()->lunch) ? MealRates::where('hotel', $Hotel)->where('meal_type', $RateItem->meal)->first()->lunch : 0);
                $Dinner = (!empty(MealRates::where('hotel', $Hotel)->where('meal_type', $RateItem->meal)->first()->dinner) ? MealRates::where('hotel', $Hotel)->where('meal_type', $RateItem->meal)->first()->dinner : 0);


                if ($Meal == 1) {//requested meal type is BB

                    if ($RateItem->meal == 2) {//hb available

                        if (!empty($Dinner))//if dinner not zero add given meal rate
                            $TempRateArray[$k]->rate = $TempRateArray[$k]->rate - ($Dinner * $TempRateArray[$k]->room_type);//remove dinner and multply buy room typ for pp

                        $TempRateArray[$k]->meal = $Meal;
                    } elseif ($RateItem->meal == 3) {//fb available

                        $TempRateArray[$k]->rate = $TempRateArray[$k]->rate - (
                                ($Lunch * $TempRateArray[$k]->room_type) +
                                ($Dinner * $TempRateArray[$k]->room_type));//remove dinner lunch and multply buy room typ for pp
                        $TempRateArray[$k]->meal = $Meal;
                    }

                }
                if ($Meal == 2) {//requested meal type is HB
                    if ($RateItem->meal == 1) {//bb available

                        $TempRateArray[$k]->rate = $TempRateArray[$k]->rate + ($Dinner * $TempRateArray[$k]->room_type);//add dinner and multply buy room typ for pp
                        $TempRateArray[$k]->meal = $Meal;
                    } elseif ($RateItem->meal == 3) {//hb available

                        $TempRateArray[$k]->rate = $TempRateArray[$k]->rate - ($Lunch * $TempRateArray[$k]->room_type);
                        $TempRateArray[$k]->meal = $Meal;
                    }
                }
                if ($Meal == 3) {//requested meal type is FB

                    if ($RateItem->meal == 2) {//hb available

                        $TempRateArray[$k]->rate = $TempRateArray[$k]->rate + ($Lunch * $TempRateArray[$k]->room_type);
                        $TempRateArray[$k]->meal = $Meal;
                    } elseif ($RateItem->meal == 1) {//bb available

                        $TempRateArray[$k]->rate = $TempRateArray[$k]->rate + (
                                ($Lunch * $TempRateArray[$k]->room_type) +
                                ($Dinner * $TempRateArray[$k]->room_type));//add dinner lunch and multply buy room typ for pp
                        $TempRateArray[$k]->meal = $Meal;
                    }
                }

            }


            return $TempRateArray;
        }


        if ($GroupBy) {
            $Rates->GroupBy($GroupBy);

        }

        $Rates->OrderBy('special', 'asc');
        $Rates->OrderBy('rate', 'asc');

        $Rates->limit($limit);

        return $Rates->get();

    }


    /**
     * @param $Hotel
     * @param $BookDate
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param bool $AgeFrom
     * @param bool $AgeTo
     * @param bool $RoomType
     * @return \Illuminate\Support\Collection
     */
    function getRateChild($Hotel, $BookDate, $Meal = false, $RoomCategory = false, $Market = false, $AgeFrom = false, $AgeTo = false, $RoomType = false)
    {


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rates = DB::table($this->table_child)
            ->select(DB::Raw("*,CONCAT(start_year,start_month,start_day) as start_date,CONCAT(start_year,start_month,start_day,end_year,end_month,end_day) as start_end_date"));
        if ($BookDate) {
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
                ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        }
        $Rates->where("hotel", "=", $Hotel);
        $Rates->whereNull('deleted_at');


        //Age
        if ($AgeFrom !== false && $AgeTo !== false) {
            $Rates->where("age_from", "=", $AgeFrom);
            $Rates->where("age_to", "=", $AgeTo);
        }

        //room type
        if ($RoomType) {
            if (is_array($RoomType))
                $Rates->whereIn("room_type", $RoomType);
            else
                $Rates->where("room_type", '=', $RoomType);
        }

        //if Set Meal
        if ($Meal)
            $Rates->where("meal", "=", $Meal);

        //if Set RoomCategory
        if ($RoomCategory)
            $Rates->where("room_category", "=", $RoomCategory);

        //if Set Market
        if ($Market) {

            if (is_array($Market))
                $Rates->whereIn("market", $Market);
            elseif ($this->RateShow)
                $Rates->where("market", $Market);
            else
                $Rates->whereIn("market", [$Market, 1]);
        }

        $Rates->OrderBy('special', 'asc');
        $Rates->OrderBy('rate', 'asc');

        $Rates->limit(1000);

        return $Rates->get();

    }

    function ApiGetRateChild($Hotel, $BookDate, $Meal = false, $RoomCategory = false, $Market = false, $AgeFrom = false, $AgeTo = false, $RoomType = false) {
        $childRates = $this->getRateChild($Hotel, $BookDate, $Meal = false, $RoomCategory = false, $Market = false, $AgeFrom = false, $AgeTo = false, $RoomType = false);

        return $childRates[0]->rate ?? 0;
    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availableCategory($ID)
    {
        return Category::WhereIn('ID', Rates::where('hotel', $ID)->select('room_category')->groupBy('room_category')->pluck('room_category'))->pluck('name', 'ID');
    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availableMarket($ID)
    {
        return Market::WhereIn('ID', Rates::where('hotel', $ID)->select('market')->groupBy('market')->pluck('market'))->pluck('market', 'ID');

    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availableCategoryChild($ID)
    {
        return Category::WhereIn('ID', RatesChild::where('hotel', $ID)->select('room_category')->groupBy('room_category')->pluck('room_category'))->pluck('name', 'ID');
    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availableMarketChild($ID)
    {
        return Market::WhereIn('ID', RatesChild::where('hotel', $ID)->select('market')->groupBy('market')->pluck('market'))->pluck('market', 'ID');

    }

    /**
     * @param $ID
     * @return bool|Carbon
     */
    function getAdultRateMaxDate($ID)
    {
        try {
            return new Carbon(@Rates::select(DB::Raw("STR_TO_DATE(CONCAT( start_day,'/', start_month, '/', start_year ) ,'%d/%m/%Y') as start_date"))
                ->where('hotel', $ID)
                ->orderBy('start_date', 'desc')->first()->start_date);
        } catch (\Exception $e) {
            return false;
        }

    }

    /**
     * @param $ID
     * @return bool|Carbon
     */
    function getAdultRateMinDate($ID)
    {
        try {
            return new Carbon(Rates::select(DB::Raw("STR_TO_DATE(CONCAT( start_day,'/', start_month, '/', start_year ) ,'%d/%m/%Y') as start_date"))
                ->where('hotel', $ID)
                ->orderBy('start_date', 'asc')->first()->start_date);
        } catch (\Exception $e) {
            return false;
        }


    }

    /**
     * @param $Hotel
     * @return array
     */
    function getMaxMinBookDates($Hotel)
    {

        $MinBookDate = false;
        $MaxBookDate = false;

        $BookMax = DB::table($this->table)
            ->where("hotel", "=", $Hotel)
            ->orderBy('start_year', 'desc')
            ->orderBy('start_month', 'desc')
            ->orderBy('start_day', 'desc')
            ->limit(1);


        $BookMin = DB::table($this->table)
            ->where("hotel", "=", $Hotel)
            ->orderBy('start_year', 'ASC')
            ->orderBy('start_month', 'ASC')
            ->orderBy('start_day', 'ASC')
            ->limit(1);

        $BookMax = $BookMax->get();
        $BookMin = $BookMin->get();

        if (isset($BookMin[0]))
            $MinBookDate = array('year' => $BookMin[0]->start_year, 'month' => $BookMin[0]->start_month, 'day' => $BookMin[0]->start_day);
        if (isset($BookMax[0]))
            $MaxBookDate = array('year' => $BookMax[0]->start_year, 'month' => $BookMax[0]->start_month, 'day' => $BookMax[0]->start_day);

        return array($MaxBookDate, $MinBookDate);

    }

    /**
     * @param $Hotel
     * @param $BookDateCheckIn
     * @param $BookDateCheckOut
     * @param $Market
     * @return array|\Illuminate\Support\Collection
     */
    function getAvailableRoomCategory($Hotel, $BookDateCheckIn, $BookDateCheckOut, $Market)
    {

        $BookDateCheckInFormat = Carbon::create($BookDateCheckIn['year'], $BookDateCheckIn['month'], $BookDateCheckIn['day'], 0);
        $BookDateCheckOutFormat = Carbon::create($BookDateCheckOut['year'], $BookDateCheckOut['month'], $BookDateCheckOut['day'], 0);

        $AvailableRoomCategory = DB::table(DB::raw($this->table . " rate"))
            ->select("*")
            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateCheckInFormat)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateCheckInFormat)
            ->where("hotel", "=", $Hotel)
            ->whereNull("rate.deleted_at")
            ->join(DB::raw('apple_hotel_room_category category'), 'category.ID', '=', 'rate.room_category')
            ->groupBy("room_category")->pluck("category.name", "rate.room_category");

        //$Rates->where("market","=", $Market);


        if ($AvailableRoomCategory)
            return $AvailableRoomCategory;
        return
            array();

    }

        /**
     * @param $Hotel
     * @param $BookDateCheckIn
     * @param $BookDateCheckOut
     * @param $Market
     * @return array|\Illuminate\Support\Collection
     */
    function getAvailableMealType($Hotel, $BookDateCheckIn, $BookDateCheckOut, $Market)
    {

        $BookDateCheckInFormat = Carbon::create($BookDateCheckIn['year'], $BookDateCheckIn['month'], $BookDateCheckIn['day'], 0);
        $BookDateCheckOutFormat = Carbon::create($BookDateCheckOut['year'], $BookDateCheckOut['month'], $BookDateCheckOut['day'], 0);

        $AvailableRoomCategory = DB::table(DB::raw($this->table . " rate"))
            ->select("*")
            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateCheckInFormat)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateCheckInFormat)
            ->where("hotel", "=", $Hotel)
            ->whereNull("rate.deleted_at")
            ->join(DB::raw('apple_meal_plan meal'), 'meal.ID', '=', 'rate.meal')
            ->groupBy("meal")->pluck("meal.plan", "rate.meal");

        //$Rates->where("market","=", $Market);


        if ($AvailableRoomCategory)
            return $AvailableRoomCategory;
        return
            array();

    }

    /**
     * @param $RateArray
     * @param $HotelSettings
     * @param string $PaxType
     * @return bool
     */
    function searchInRateArrayHotelSettings($RateArray, $HotelSettings, $PaxType = 'adult')
    {

        $QuotationHotel = new QuotationHotel();
        $Bookdates = $QuotationHotel->getBookdatesNightCheckIn($HotelSettings['check_in'], $HotelSettings['check_out']);


        foreach ($Bookdates as $Day => $Bookdate) {

            if (empty($HotelSettings['room_type']))
                continue;

            foreach ($HotelSettings['room_type'] as $RoomType => $RoomCount) {
                if (!$RoomCount) continue;


                if (isset($RateArray[$PaxType][$Day][$RoomType]) && isset($RateArray[$PaxType][$Day][$RoomType]['hotel'])) {

                    $RateItem = $RateArray[$PaxType][$Day][$RoomType];


                    if ($HotelSettings['hotel'] != $RateItem['hotel']) {
                        $this->Error = "Hotel Not Mach!";
                        return false;
                    }

                    if ($HotelSettings['room_category'] != $RateItem['room_category']) {
                        $this->Error = "Category Not Mach!";
                        return false;
                    }
                    if ($HotelSettings['meal_type'] != $RateItem['meal']) {
                        $this->Error = "Meal Not Mach!";
                        return false;
                    }


                    //validate date
                    $BookdateUnix = strtotime($Bookdate['year'] . '-' . $Bookdate['month'] . '-' . $Bookdate['day']);
                    $StartDateUnix = strtotime($RateItem['year'] . '-' . $RateItem['month'] . '-' . $RateItem['day']);
                    #$EndDateUnix = strtotime($RateItem['end_year'] . '-' . $RateItem['end_month'] . '-' . $RateItem['end_day']);


//                    if ($BookdateUnix < $StartDateUnix || $BookdateUnix > $EndDateUnix) {
                    if ($BookdateUnix != $StartDateUnix) {

                        $this->Error = "Date Not Match!";
                        return false;
                    }

                }
                else {

                    //sd($RateArray[$PaxType][$Day]);

                    $this->Error = "Room Type Not Mach! {$HotelSettings['hotel']} ";
                    return false;//no room type
                }


            }

        }

        return true;
    }
}
