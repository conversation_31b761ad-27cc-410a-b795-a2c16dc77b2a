<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;

/**
 * App\Model\Hotel\Allotment
 *
 * @property int $ID
 * @property int $hotel
 * @property int $room
 * @property int $start_year
 * @property int $start_month
 * @property int $start_day
 * @property int $end_year
 * @property int $end_month
 * @property int $end_day
 * @property int $release_period
 * @property int $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereEndDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereEndMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereEndYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereReleasePeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereRoom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereStartDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereStartMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereStartYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Allotment whereUploadId($value)
 * @mixin \Eloquent
 */
class Allotment extends Model
{

    protected $table = 'apple_hotel_room_allotment';


    /**
     * @param $Hotel
     * @param bool $BookDate
     * @return \Illuminate\Support\Collection
     */
    function getAllotments($Hotel, $BookDate = false){

        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);
        $Allotment = Allotment::select(DB::Raw("*"));

        $Allotment->where('hotel',$Hotel);

        if ($BookDate) {
            $Allotment->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
                ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        }

        return $Allotment->get();
    }

}
