<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\MealRates
 *
 * @property int $ID
 * @property int $hotel
 * @property int|null $meal_type
 * @property float|null $lunch
 * @property float|null $dinner
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereDinner($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereLunch($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereMealType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\MealRates whereUploadId($value)
 * @mixin \Eloquent
 */
class MealRates extends Model
{
    protected $table = "apple_hotel_meal_rate";
}
