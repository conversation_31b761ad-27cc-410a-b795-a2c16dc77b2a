<?php

namespace App\Model\Hotel;

use App\CountryCurrency;
use App\Currency;
use App\Model\Cruise\Cruise;
use App\Model\Cruise\CruiseMeal;
use App\Model\Cruise\CruiseOccupancyType;
use App\Model\Image\Image;
use App\Model\Place\Place;
use App\Model\Quotation\QuotationCruise;
use App\Model\Quotation\QuotationHotel;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Session;

/**
 * App\Model\Hotel\Hotel
 *
 * @property int $ID
 * @property int $city
 * @property int $class
 * @property string|null $name
 * @property string|null $description
 * @property float|null $latitude
 * @property float|null $longitude
 * @property int|null $provider 1 apple 2 appleholidays
 * @property int|null $provider_id
 * @property int|null $cancellation_days
 * @property int $preferred
 * @property string|null $address
 * @property string|null $tripadvisor
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Hotel\Allotment[] $Allotment
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Hotel\AllotmentUsed[] $AllotmentUsed
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Hotel\Contact[] $Contact
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Hotel\Rates[] $Rate
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Hotel\RatesChild[] $RateChild
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Hotel\StopSale[] $StopSales
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereCancellationDays($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereClass($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel wherePreferred($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereTripadvisor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Hotel whereUploadId($value)
 * @mixin \Eloquent
 * @property-read mixed $image
 */
class Hotel extends Model
{
    use SoftDeletes;

    protected $primaryKey = "ID";

    /**
     * @var string
     */
    protected $table = 'apple_hotels';
    /**
     * @var array
     */
    protected $appends = ['image'];

    /**
     *
     */
    const PROVIDER_LOCAL = 'local';
    /**
     *
     */
    const PROVIDER_HOTELBEDS = 'hotelbeds';

    const PROVIDER_LOCAL_SPECIAL = 'local_special';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Allotment()
    {
        return $this->hasMany('App\Model\Hotel\Allotment', 'hotel', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function StopSales()
    {
        return $this->hasMany('App\Model\Hotel\StopSale', 'hotel', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function AllotmentUsed()
    {
        return $this->hasMany('App\Model\Hotel\AllotmentUsed', 'hotel', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Contact()
    {
        return $this->hasMany('App\Model\Hotel\Contact', 'hotel', 'ID');
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function class()
    {
        return $this->hasOne(HotelClass::class, 'id', 'class')
            ->select('id','class AS hotel_stars','star');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\Hotel\Rates', 'hotel', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function City()
    {
        return $this->hasOne(Place::class, 'id', 'city')
            ->select('id','name','popularity','longitude','latitude');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function RateChild()
    {
        return $this->hasMany('App\Model\Hotel\RatesChild', 'hotel', 'ID');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Messages()
    {
        return $this->hasMany('App\Model\Hotel\Messages', 'hotel', 'ID');
    }

    /**
     * @param $ID
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @return array
     * @throws \ReflectionException
     */
    public static function getStatus($ID, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false)
    {

        $Hotel = new self();
        if ($Hotel->hasStopSales($ID, $BookDate, $RoomCategory))
            return [4];//not available

        $RateStatus = $Hotel->hasRate($ID, $BookDate, $RoomType, $Meal, $RoomCategory, $Market);

        if (isset($RateStatus['available']) && !empty($RateStatus['available'])) {
            if ($AllotmentCount = $Hotel->hasAllotment($ID, $BookDate)) {
                if($AllotmentCount > 0) {
                    return [1, $AllotmentCount, 'rate' => $RateStatus];//available
                } else {
                    return [2, 'rate' => $RateStatus];//on request
                }
            }
            return [2, 'rate' => $RateStatus];//on request
        } else {
            if(isset($RateStatus['available_listing']) && !empty($RateStatus['available_listing'])) {
                return [2, 'rate' => $RateStatus];//on request
            }
            return [3];//not available
        }
    }

    /**
     * @return array
     */
    public function getImageAttribute()
    {
        return Image::getImageDirect($this->ID, '2x', 'hotel', 1, $this->name);
    }

    /**
     * @param $HotelID
     * @return bool|Model|mixed|null|object|static
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    public static function getHotelFromAll($HotelID)
    {

        $data = Hotel::find($HotelID);

        if (!$data) {
            $HotelBeds = new HotelBed();
            $data = $HotelBeds->getHotelDetails($HotelID);
        }
        return $data;
    }


    /**
     * @param $ID
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param int $limit
     * @return bool|mixed
     * @throws \ReflectionException
     */
    public static function hasRate($ID, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false, $limit = 1)
    {

        $Rate = new Rates();

        $RoomTypeKey = false;
        if (is_array($RoomType))
            $RoomTypeKey = array_keys(filter_array_false(arrayMapMulti("intval", $RoomType)));

        $RateList = $Rate->getRate($ID, $BookDate, $RoomTypeKey, $Meal, $RoomCategory, $Market, false, false, $limit);

        $RatesAvailableRooms = [];
        $RatesAvailableRoomsListing = [];
        if(isset($RateList) && !empty($RateList)) {
            foreach ($RateList as $Rate) {
                if($RoomTypeKey) {
                    if(in_array($Rate->room_type , $RoomTypeKey)) {
                        $RatesAvailableRooms[] = $Rate->room_type;
                    }
                }
                $RatesAvailableRoomsListing[] = $Rate->room_type;
            }
        }

        if(!$RoomTypeKey) {
            $RoomTypeKey = [];
        }

        $notRates = array_diff($RoomTypeKey, $RatesAvailableRooms);

        if (!$RateList) {
            return false;
        } else {
            // return objectToArray($RateList->first());
            return array("available" => $RatesAvailableRooms, "not_available" => $notRates, "available_listing" => $RatesAvailableRoomsListing);
        }

    }

    /**
     * @param $ID
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @return bool|mixed
     * @throws \ReflectionException
     */
    public static function hasRateChild($ID, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false)
    {

        $Rate = new Rates();

        $RoomTypeKey = false;
        if (is_array($RoomType))
            $RoomTypeKey = array_keys(filter_array_false(arrayMapMulti("intval", $RoomType)));

        $RateList = $Rate->getRateChild($ID, $BookDate, $Meal, $RoomCategory, $Market, false, false, $RoomTypeKey);


        if (!$RateList) {
            return false;
        } else {
            return objectToArray($RateList->first());
        }

    }

    /**
     * @param $Index
     * @param $Hotel
     * @param bool $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @return bool
     * @throws \ReflectionException
     */
    static function hasRateSession($Index, $Hotel, $BookDate = false, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false)
    {

        $HotelRate = Session::get("quotation.rate.hotel." . $Index);
        $RoomTypeKey = array_keys(filter_array_false(arrayMapMulti("intval", $RoomType)));


        if ($HotelRate) {

            if (!empty($HotelRate['adult'])) {
                foreach ($RoomTypeKey as $RoomTypeID) {

                    if (isset($HotelRate['adult'][0][$RoomTypeID])) {

                        if ($HotelRate['adult'][0][$RoomTypeID]['hotel'] == $Hotel &&
                            $HotelRate['adult'][0][$RoomTypeID]['year'] == $BookDate['year'] &&
                            $HotelRate['adult'][0][$RoomTypeID]['month'] == $BookDate['month'] &&
                            $HotelRate['adult'][0][$RoomTypeID]['day'] == $BookDate['day'] &&
                            $HotelRate['adult'][0][$RoomTypeID]['room_category'] == $RoomCategory &&
                            $HotelRate['adult'][0][$RoomTypeID]['meal'] == $Meal &&
                            $HotelRate['adult'][0][$RoomTypeID]['room_type'] == $RoomTypeID) {
                            return true;
                        } else
                            return false;


                    } else
                        return false;

                }

            } else
                return false;


        } else
            return false;


    }

    /**
     * @param $Index
     * @param $Hotel
     * @param bool $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @return bool
     */
    static function hasRateChildSession($Index, $Hotel, $BookDate = false, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false)
    {

        $HotelRate = Session::get("quotation.rate.hotel." . $Index);
        $return = true;

        if ($HotelRate) {

            if (!empty($HotelRate['child'])) {

                if (isset($HotelRate['child']['cwb']) && $HotelRate['child']['cwb'][0]['hotel'] == $Hotel &&
                    $HotelRate['child']['cwb'][0]['year'] == $BookDate['year'] &&
                    $HotelRate['child']['cwb'][0]['month'] == $BookDate['month'] &&
                    $HotelRate['child']['cwb'][0]['day'] == $BookDate['day']) {

                    if ($HotelRate['child']['cwb'][0]['is_modified'] && $HotelRate['child']['cwb'][0]['modified_rate'] > 0)
                        $return = true;
                    elseif (!$HotelRate['child']['cwb'][0]['is_modified'] && $HotelRate['child']['cwb'][0]['rate'] > 0)
                        $return = true;
                    else
                        return false;

                }

                if (isset($HotelRate['child']['cnb']) && $HotelRate['child']['cnb'][0]['hotel'] == $Hotel &&
                    $HotelRate['child']['cnb'][0]['year'] == $BookDate['year'] &&
                    $HotelRate['child']['cnb'][0]['month'] == $BookDate['month'] &&
                    $HotelRate['child']['cnb'][0]['day'] == $BookDate['day']) {

                    if ($HotelRate['child']['cnb'][0]['is_modified'] && $HotelRate['child']['cnb'][0]['modified_rate'] > 0)
                        $return = true;
                    elseif (!$HotelRate['child']['cnb'][0]['is_modified'] && $HotelRate['child']['cnb'][0]['rate'] > 0)
                        $return = true;
                    else
                        return false;

                }

            } else
                return false;


        }
        else
            return false;


        return $return;

    }

    /**
     * @param $ID
     * @param $BookDate
     * @return bool|int|mixed
     */
    public static function hasAllotment($ID, $BookDate)
    {


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);


        $Hotel = new self();
        $hasRate = $Hotel->find($ID)->Allotment()
            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);


        if ($hasRate->get()->isEmpty())
            return false;
        else {
            #check used allotment
            $hasUsedAllotment = $Hotel->find($ID)->AllotmentUsed()->whereHas('Quotation')
                ->where(DB::Raw("STR_TO_DATE(CONCAT( day, '/', month, '/', year ) ,'%d/%m/%Y' )"), "=", $BookDateFormat);

            $UsedAlltoment = 0;
            if (!$hasUsedAllotment->get()->isEmpty()) {
                $UsedAlltomentArr = $hasUsedAllotment->get(['use_room'])->toArray();
                foreach ($UsedAlltomentArr as $UsedAlltomentItem) {
                    $UsedAlltoment += (int)$UsedAlltomentItem["use_room"];
                }
            }

            if(($BookDateFormat->diffInDays(Carbon::now()) + 1) >= (int)$hasRate->first()->release_period) {
                return $hasRate->first()->room - $UsedAlltoment;
            } else {
                return false;
            }
        }

    }

    /**
     * @param $ID
     * @param $BookDate
     * @return bool
     */
    public static function hasStopSales($ID, $BookDate, $roomCategory=false)
    {

        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Hotel = new Hotel();

        if(isset($ID) && $ID != 0) {
            /*$hasRate = $Hotel::find($ID);
            if(isset($hasRate)) {
                $hasRate->StopSales();
                    ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
                    ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);
            }*/

            $hasRate = $Hotel::whereHas('StopSales', function ($query) use ($BookDateFormat, $roomCategory) {
                $query->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat);
                $query->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);
                if($roomCategory) {
                    $query->where('room_category', $roomCategory);
                }
            })->where("ID", "=", $ID)->get();

            if ($hasRate->isEmpty())
                return false;
            else
                return true;
        } else {
            return false;
        }
    }


    /**
     * @param $ID
     * @param $BookDate
     * @return array
     */
    function getAvailableSettings($ID, $BookDate)
    {


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rate = $this::find($ID)->Rate()
            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        $RoomTypeList = $Rate->groupBy('room_type')->select('room_type')->get()->toArray();
        $RoomTypeList = array_column($RoomTypeList, 'room_type');

        return ['room_type' => $RoomTypeList];


    }


    /**
     * @param $longitude
     * @param $latitude
     * @param float $AreaDistance
     * @return bool|\Illuminate\Support\Collection
     */
    function getAreaHotels($longitude, $latitude, $AreaDistance = 0.3)
    {//area between hotels

        $Places = DB::table($this->table)
            ->select("ID", "name", "longitude", "latitude")
            ->where("longitude", "<", $longitude + $AreaDistance)
            ->where("latitude", "<", $latitude + $AreaDistance)
            ->where("longitude", ">", $longitude - $AreaDistance)
            ->where("latitude", ">", $latitude - $AreaDistance)
            ->limit(10)
            ->get();

        $Places = $Places->map(function($d){
            $d->image = Image::getImageDirect($d->ID, '2x', 'hotel', 1, $d->name);
            return $d;
        });

        if (!empty($Places))
            return $Places;
        else
            return false;

    }

    /**
     * @param $Place
     * @return bool|\Illuminate\Support\Collection
     */
    function getHotelsInPlaces($Place)
    {//get place hotels

        $Hotels = DB::table($this->table)
            ->select("$this->table.*", "apple_places.name as city_name", "apple_places.ID as city_id")
            ->join('apple_places', "$this->table.city", '=', 'apple_places.ID')
            ->where("city", "=", $Place)
            ->get();

        if (!empty($Hotels))
            return $Hotels;
        else
            return false;


    }

    /**
     * @param $Place
     * @param $BookDateFormat
     * @return \Illuminate\Support\Collection
     */
    function getHotelList($Place, $BookDateFormat)
    {

        $Hotels = DB::table($this->table . ' AS parent')
            ->select("$this->table.*,
            
            (
                SELECT * from  apple_hotel_room_allotment            
                
                WHERE
                
                STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) = STR_TO_DATE(CONCAT(  parent.start_day, '/',  parent.start_month, '/',  parent.start_year ) ,'%d/%m/%Y' ) and
                STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) = STR_TO_DATE(CONCAT(  parent.end_day, '/',  parent.end_month, '/',  parent.end_year ) ,'%d/%m/%Y' ) and
                
                hotel = parent.ID
                
                LIMIT 1
            ) as sd
            
            ")
            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);


        return $Hotels->get();


    }

    /**
     * @param $Place
     * @param int $Limit
     * @return bool|mixed
     */
    function getLowestHotelInPlaces($Place, $Limit = 1)
    {//get place hotels lowest Rate

        $Hotels = DB::table($this->table)
            ->select("$this->table.*", "apple_places.name as city_name", "apple_places.ID as city_id")
            ->join('apple_places', "$this->table.city", '=', 'apple_places.ID')
            ->where("city", "=", $Place)
            ->take($Limit)
            ->get();

        if (!empty($Hotels))
            return $Hotels[0];
        else
            return false;


    }

    /**
     * @param string $size
     * @param int $limit
     * @return array
     */
    function image($size = '1x', $limit = 1)
    {
        $Image = new Image();
        return $Image->getImage($this->ID, $size, 'hotel', $limit);
    }

    /**
     * @param $HotelSettingsArray
     * @param $Type
     * @param $ArrivalDate
     * @return array
     * @throws \ReflectionException
     */
    function getHotelSettingsBookDatesChange($HotelSettingsArray, $Type, $ArrivalDate)
    {

        $ArrivalDate = Carbon::create($ArrivalDate['year'], $ArrivalDate['month'], $ArrivalDate['day'], 0);

        $QuotationHotel = new QuotationHotel();
        $QuotationCruise = new QuotationCruise();
        $BookDateArray = [];
        $HotelSettingsQueryArray = [];

        foreach($HotelSettingsArray['accommodation'] as $mainIndex => $accommodation) {
            if($accommodation == 1 || $accommodation == 2) {
                $HotelSettingsQueryArray[$mainIndex] = $HotelSettingsArray['hotel_setting'][$mainIndex];
            } else if($accommodation == 4) {
                $HotelSettingsQueryArray[$mainIndex] = $HotelSettingsArray['cruise_setting'][$mainIndex];
            }
        }

        unset($HotelSettingsArray['hotel_setting']);//remove encoded query
        unset($HotelSettingsArray['cruise_setting']);//remove encoded query
        $HotelModifySettings = arrayMapMulti('getActualDataType', $HotelSettingsArray);//set hotel modified settings

        foreach ($HotelSettingsQueryArray as $CurrentHotelIndex => $HotelSettings) {
            $acc_type = $HotelSettingsArray['accommodation'][$CurrentHotelIndex];

            if($acc_type == 4) {
                $CurrentHotelSettings = arrayMapMulti('getActualDataType', $QuotationHotel->getDecodeArray($HotelSettings));
            } else {
                $CurrentHotelSettings = arrayMapMulti('getActualDataType', $QuotationCruise->getDecodeArray($HotelSettings));
            }
            
            $BookDateArray[$CurrentHotelIndex]['place'] = $CurrentHotelSettings['place'];
            if($acc_type == 1 || $acc_type == 2) {
                $BookDateArray[$CurrentHotelIndex]['provider'] = $CurrentHotelSettings['provider'];
            }
            

            if (isset($HotelModifySettings['index']) && intval($CurrentHotelIndex) == intval($HotelModifySettings['index'])) {//if modify hotel is this one...

                if($acc_type == 4) {
                    $BookDateArray[$CurrentHotelIndex]['cruise'] = $HotelModifySettings['cruise'] ?? null;
                } else {
                    $BookDateArray[$CurrentHotelIndex]['hotel'] = $HotelModifySettings['hotel'] ?? null;
                }

                if($acc_type == 4) {
                    $BookDateArray[$CurrentHotelIndex]['package'] = $HotelModifySettings['package'] ?? null;
                }

                #set night
                $BookDateArray[$CurrentHotelIndex]['nights'] = $HotelModifySettings['nights'];

                #first set the days to check in
                $BookDateArray[$CurrentHotelIndex]['check_in'] = ['year' => $ArrivalDate->year, 'month' => $ArrivalDate->month, 'day' => $ArrivalDate->day];
                $BookDateArray[$CurrentHotelIndex]['check_in_format'] = $ArrivalDate->toFormattedDateString();

                #add night certain hotel
                $ArrivalDate->addDays($HotelModifySettings['nights']);//add modified nights

                #add again add check out day
                $BookDateArray[$CurrentHotelIndex]['check_out'] = ['year' => $ArrivalDate->year, 'month' => $ArrivalDate->month, 'day' => $ArrivalDate->day];
                $BookDateArray[$CurrentHotelIndex]['check_out_format'] = $ArrivalDate->toFormattedDateString();

                if($acc_type == 1 || $acc_type == 2) {
                    $BookDateArray[$CurrentHotelIndex]['driver_accommodation'] = isset($HotelModifySettings['driver_accommodation']) ? 1 : 0;
                }
            } else {//this is for all other hotels
                if($acc_type == 4) {
                    if(isset($CurrentHotelSettings['cruise'])) {
                        $BookDateArray[$CurrentHotelIndex]['cruise'] = $CurrentHotelSettings['cruise'];
                    }
                } else {
                    if(isset($CurrentHotelSettings['hotel'])) {
                        $BookDateArray[$CurrentHotelIndex]['hotel'] = $CurrentHotelSettings['hotel'];
                    }
                }

                #set night
                $BookDateArray[$CurrentHotelIndex]['nights'] = $CurrentHotelSettings['nights'];

                #first set the days to check in
                $BookDateArray[$CurrentHotelIndex]['check_in'] = ['year' => $ArrivalDate->year, 'month' => $ArrivalDate->month, 'day' => $ArrivalDate->day];
                $BookDateArray[$CurrentHotelIndex]['check_in_format'] = $ArrivalDate->toFormattedDateString();

                #add night certain hotel
                $ArrivalDate->addDays($CurrentHotelSettings['nights']);//add current nights

                #add again add check out day
                $BookDateArray[$CurrentHotelIndex]['check_out'] = ['year' => $ArrivalDate->year, 'month' => $ArrivalDate->month, 'day' => $ArrivalDate->day];
                $BookDateArray[$CurrentHotelIndex]['check_out_format'] = $ArrivalDate->toFormattedDateString();

                if($acc_type == 1 || $acc_type == 2) {
                    $BookDateArray[$CurrentHotelIndex]['driver_accommodation'] = isset($CurrentHotelSettings['driver_accommodation']) ? 1 : 0;
                    if($CurrentHotelSettings['driver_accommodation'] == 0) {
                        $BookDateArray[$CurrentHotelIndex]['driver_accommodation'] = 0;
                    }
                }
            }

            #Set other data
            if (/*!empty($HotelSettingsArray['hotel']) &&*/
            !($HotelSettingsArray['index'] == $CurrentHotelIndex && isset($HotelSettingsArray['transportation']))) {//first check if there's a hotel


                if ($Type == 'all') {//if it apply for all

                    if($acc_type == 1 && isset($HotelModifySettings['hotel'])) {
                        $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($HotelModifySettings['meal_type'])->plan; //get meal plan text
                        $BookDateArray[$CurrentHotelIndex]['meal'] = $HotelModifySettings['meal_type'];//get meal id
                        $BookDateArray[$CurrentHotelIndex]['room_category'] = $CurrentHotelSettings['room_category'];//don't set catogory for all hotels
                        $BookDateArray[$CurrentHotelIndex]['room_type'] = $HotelModifySettings['room_type']; //room type count set for all
                        $BookDateArray[$CurrentHotelIndex]['driver_accommodation'] = isset($HotelModifySettings['driver_accommodation']) ? 1 : 0; // driver accommodation


                        if ($CurrentHotelIndex == $HotelModifySettings['index']) {//if modify hotel is this one apply for only this one for modified data
                            $BookDateArray[$CurrentHotelIndex]['room_category'] = $HotelModifySettings['room_category'];//set catogory for this hotels
                        }


                        foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                            if (intval($RoomCount))//only if room count requested
                                $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                        }
                        $BookDateArray[$CurrentHotelIndex]['driver_accommodation'] = isset($HotelModifySettings['driver_accommodation']) ? 1 : 0;
                    }

                } else {//only for one
                    if ($CurrentHotelIndex == $HotelModifySettings['index']) {//if modify hotel is this one apply for only this one for modified data

                        $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($HotelModifySettings['meal_type'])->plan; //get meal plan text
                        $BookDateArray[$CurrentHotelIndex]['meal'] = $HotelModifySettings['meal_type'];//get meal id

                        if($acc_type == 4) {
                            $BookDateArray[$CurrentHotelIndex]['cabin_type'] = $HotelModifySettings['cabin_type'];//set catogory for this hotels
                        } else {
                            $BookDateArray[$CurrentHotelIndex]['room_category'] = $HotelModifySettings['room_category'];//set catogory for this hotels
                        }

                        if($acc_type == 4) {
                            $BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type'] = $HotelModifySettings['cabin_occupancy_type'];//set catogory for this hotels
                            foreach ($BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                                if (intval($RoomCount))//only if room count requested
                                    $BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . CruiseOccupancyType::find($RoomTypeID)->short_name; //get the text type
                            }
                        } else {
                            $BookDateArray[$CurrentHotelIndex]['room_type'] = $HotelModifySettings['room_type']; //room type count set for all
                            foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                                if (intval($RoomCount))//only if room count requested
                                    $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                            }
                        }
                        
                        if($acc_type == 1) {
                            $BookDateArray[$CurrentHotelIndex]['driver_accommodation'] = isset($HotelModifySettings['driver_accommodation']) ? 1 : 0;
                        }

                    } else {

                        if(!empty($CurrentHotelSettings['hotel']) || !empty($CurrentHotelSettings['cruise'])) {
                            if ($acc_type == 4) {
                                $BookDateArray[$CurrentHotelIndex]['meal_text'] = CruiseMeal::find($CurrentHotelSettings['meal_type'])->plan; //get meal plan text
                            } else {
                                $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($CurrentHotelSettings['meal_type'])->plan; //get meal plan text
                            }

                            $BookDateArray[$CurrentHotelIndex]['meal'] = $CurrentHotelSettings['meal_type'];//get meal id


                            if ($acc_type == 4) {
                                $BookDateArray[$CurrentHotelIndex]['cabin_type'] = $CurrentHotelSettings['cabin_type'];//set catogory for this hotels
                            } else {
                                $BookDateArray[$CurrentHotelIndex]['room_category'] = $CurrentHotelSettings['room_category'];//set catogory for this hotels
                            }

                            if ($acc_type == 4) {
                                $BookDateArray[$CurrentHotelIndex]['package'] = $CurrentHotelSettings['package'];//set catogory for this hotels
                            }

                            if ($acc_type == 4) {
                                $BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type'] = $CurrentHotelSettings['cabin_occupancy_type'];//set catogory for this hotels
                                foreach ($BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                                    if (intval($RoomCount))//only if room count requested
                                        $BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . CruiseOccupancyType::find($RoomTypeID)->short_name; //get the text type
                                }
                            } else {
                                $BookDateArray[$CurrentHotelIndex]['room_type'] = $CurrentHotelSettings['room_type']; //room type count set for all
                                foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                                    if (intval($RoomCount))//only if room count requested
                                        $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                                }
                            }
                            if($acc_type == 1) {
                                $BookDateArray[$CurrentHotelIndex]['driver_accommodation'] = (isset($CurrentHotelSettings['driver_accommodation']) && $CurrentHotelSettings['driver_accommodation'] == 1) ? 1 : 0;
                            }
                        } else { #own arrangement

                        }
                    }
                }

            } elseif (empty($HotelModifySettings)) {//only date  want to change date


                $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($CurrentHotelSettings['meal_type'])->plan; //get meal plan text
                $BookDateArray[$CurrentHotelIndex]['meal'] = $CurrentHotelSettings['meal_type'];//get meal id
                $BookDateArray[$CurrentHotelIndex]['room_category'] = $CurrentHotelSettings['room_category'];//don't set catogory for all hotels
                $BookDateArray[$CurrentHotelIndex]['room_type'] = $CurrentHotelSettings['room_type']; //room type count set for all
                if($acc_type == 1) {
                    $BookDateArray[$CurrentHotelIndex]['driver_accommodation'] = isset($CurrentHotelSettings['driver_accommodation']) ? 1 : 0;
                }

                foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                    if (intval($RoomCount))//only if room count requested
                        $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                }

                if ($acc_type == 4) {
                    $BookDateArray[$CurrentHotelIndex]['meal_text'] = CruiseMeal::find($CurrentHotelSettings['meal_type'])->plan; //get meal plan text
                } else {
                    $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($CurrentHotelSettings['meal_type'])->plan; //get meal plan text
                }

                $BookDateArray[$CurrentHotelIndex]['meal'] = $CurrentHotelSettings['meal_type'];//get meal id

                if ($acc_type == 4) {
                    $BookDateArray[$CurrentHotelIndex]['cabin_type'] = $CurrentHotelSettings['cabin_type'];//set catogory for this hotels
                } else {
                    $BookDateArray[$CurrentHotelIndex]['room_category'] = $CurrentHotelSettings['room_category'];//set catogory for this hotels
                }

                if ($acc_type == 4) {
                    $BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type'] = $CurrentHotelSettings['cabin_occupancy_type'];//set catogory for this hotels
                    foreach ($BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                        if (intval($RoomCount))//only if room count requested
                            $BookDateArray[$CurrentHotelIndex]['cabin_occupancy_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . CruiseOccupancyType::find($RoomTypeID)->short_name; //get the text type
                    }
                } else {
                    $BookDateArray[$CurrentHotelIndex]['room_type'] = $CurrentHotelSettings['room_type']; //room type count set for all
                    foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                        if (intval($RoomCount))//only if room count requested
                            $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                    }
                }
            }
        }
        
        return $BookDateArray;

    }


    /**
     * @param $NewSetting
     * @param $BookDates
     * @return array
     * @throws \ReflectionException
     */
    function getHotelSettingsQuery($NewSetting, $BookDates)
    {

        $QuotationHotel = new QuotationHotel();
        $Hotel = new Hotel();
        $CompanyID = \Auth::user()->Profile()->first()->company;

        $QueryArray = [];
        $HotelStatusArray = [];
        $CanApply = true;

        $Market = false;

        if (Session::has('quotation.market')) {
            $Market = Session::get('quotation.market');
        }

        foreach ($BookDates as $HotelIndex => $CurrentDate) {

            $NewSetting['check_in'] = $CurrentDate['check_in'];
            $NewSetting['check_out'] = $CurrentDate['check_out'];
            $NewSetting['nights'] = $CurrentDate['nights'];
            $NewSetting['place'] = $CurrentDate['place'];
            if(isset($CurrentDate['cruise'])) {
                $NewSetting['cruise'] = $CurrentDate['cruise'] ?? null;
                $NewSetting['provider'] = $CurrentDate['provider']??"local";
            } else {
                $NewSetting['hotel'] = $CurrentDate['hotel'] ?? null;
                $NewSetting['driver_accommodation'] = $CurrentDate['driver_accommodation'];
            }

            if(isset($CurrentDate['hotel']) && $CurrentDate['hotel'] != 0) {
                $NewSetting['provider'] = $CurrentDate['provider']??"local";
            }
            
            $NewSetting['extrabed'] = $CurrentDate['extrabed'] ?? 0;
            $NewSetting['index'] = $HotelIndex;


            if (!empty($CurrentDate['hotel'])) {

                $NewSetting['room_type'] = $CurrentDate['room_type'];
                $NewSetting['meal_type'] = $CurrentDate['meal'];
                $NewSetting['room_category'] = $CurrentDate['room_category'];


                $RequestedRoomCount = array_sum($NewSetting['room_type']);

                if (!empty($CurrentDate['hotel']) && $CurrentDate['provider'] === 'local') {

                    $HotelDateStatus = "(" . Hotel::find($NewSetting['hotel'])->name . " {$NewSetting['check_in']['year']}-{$NewSetting['check_in']['month']}-{$NewSetting['check_in']['day']})";

                    #Check Allotment and type and okayyayay mother fuckerrree
                    if ($Allotment = Hotel::hasAllotment($NewSetting['hotel'], $NewSetting['check_in'])) {


                        if ($RequestedRoomCount > $Allotment) {
                            $Text = "You have requested $RequestedRoomCount rooms but we have only $Allotment rooms!";
                            $Satus = false;

                            #so if no allotment they cant apply
                            $CanApply = true;

                        } else {
                            $Satus = true;
                            $Text = ($Allotment - $RequestedRoomCount) . " more rooms are remaining!";
                        }
                        $HotelStatusArray[$HotelIndex][] = ['signal' => 'available', 'status' => $Satus, 'text' => $Text, 'hotel_name' => Hotel::find($NewSetting['hotel'])->name];

                    } else {

                        // $Text = "There are no any allotments!";
                        $Text = "Rooms are on request!";
                        $HotelStatusArray[$HotelIndex][] = ['signal' => 'on_request', 'status' => false, 'text' => $Text, 'hotel_date' => $HotelDateStatus, 'hotel_name' => Hotel::find($NewSetting['hotel'])->name];

                    }

                    if ($Hotel->hasStopSales($NewSetting['hotel'], $NewSetting['check_in'], $NewSetting['room_category'])) {

                        $Text = "Rooms are not available!";
                        $HotelStatusArray[$HotelIndex][] = ['signal' => 'stop_sale', 'status' => false, 'text' => $Text, 'hotel_date' => $HotelDateStatus, 'hotel_name' => Hotel::find($NewSetting['hotel'])->name];

                    }

                    #Check rates
                    if ($RateAvailable = Hotel::hasRate($NewSetting['hotel'], $NewSetting['check_in'], $NewSetting['room_type'], $NewSetting['meal_type'], $NewSetting['room_category'], $Market, 100)) {

                        if(isset($RateAvailable)) {
                            if(isset($RateAvailable['available'])) {
                                $Text = "Rates are available!";

                                $HotelStatusArray[$HotelIndex][] = ['status' => true, 'text' => $Text, 'hotel_date' => $HotelDateStatus];
                            }
                            if(isset($RateAvailable['not_available'])) {
                                foreach ($RateAvailable['not_available'] as $room) {
                                    $Text = "Rates are not available for " . RoomType::find($room)->type;

                                    $HotelStatusArray[$HotelIndex][] = ['status' => false, 'text' => $Text, 'hotel_date' => $HotelDateStatus];
                                }
                            }
                        }

                    } else {
                        $Text = "There are no any rates!";
                        if (Hotel::hasRate($NewSetting['hotel'], $NewSetting['check_in'], $NewSetting['room_type'], $NewSetting['meal_type'], $NewSetting['room_category'])) {
                            $Text .= ' (for ' . Market::find($Market)->market . ")";
                        }
                        $HotelStatusArray[$HotelIndex][] = ['status' => false, 'text' => $Text, 'hotel_date' => $HotelDateStatus, 'hotel_name' => Hotel::find($NewSetting['hotel'])->name];

                        $CanApply = false;
                    }
                    
                    if(empty(array_filter($NewSetting['room_type']))) {
                        $HotelStatusArray[$HotelIndex][] = ['status' => false, 'error' => true, 'text' => 'Room types are not selected', 'hotel_date' => $HotelDateStatus];
                    }
                }


                if (isset($NewSetting['selected_rooms'])) {
                    foreach ($NewSetting['selected_rooms'] as $RoomCode => $On) {
                        $NewSetting['rate_keys'][] = $NewSetting['room'][$RoomCode];
                    }
                }
            }

            if (!empty($CurrentDate['cruise'])) {
                $CurrentHotelSettings = [];
                if(isset($NewSetting["cruise_setting"][$HotelIndex])) {
                    $CurrentHotelSettings = arrayMapMulti('getActualDataType', $QuotationHotel->getDecodeArray($NewSetting["cruise_setting"][$HotelIndex]));
                }

                $NewSetting['cabin_occupancy_type'] = $CurrentDate['cabin_occupancy_type'] ?? $CurrentHotelSettings["cabin_occupancy_type"];
                $NewSetting['meal_type'] = $CurrentDate['meal'] ?? $CurrentHotelSettings["meal_type"];
                $NewSetting['cabin_type'] = $CurrentDate['cabin_type'] ?? $CurrentHotelSettings["cabin_type"];
                $NewSetting['package'] = $CurrentDate['package'] ?? $CurrentHotelSettings["package"];


                $RequestedRoomCount = array_sum($NewSetting['cabin_occupancy_type']);

                if (!empty($CurrentDate['cruise'])) {

                    $HotelDateStatus = "(" . Cruise::find($NewSetting['cruise'])->name . " {$NewSetting['check_in']['year']}-{$NewSetting['check_in']['month']}-{$NewSetting['check_in']['day']})";
                    $Text = "System can see the that cabins are available";
                    $HotelStatusArray[$HotelIndex][] = ['status' => true, 'text' => $Text, 'hotel_date' => $HotelDateStatus, 'hotel_name' => Cruise::find($NewSetting['cruise'])->name];

                    if ($RateAvailable = Cruise::hasRate($NewSetting['cruise'], $NewSetting['check_in'], $NewSetting['cabin_occupancy_type'], $NewSetting['meal_type'], $NewSetting['cabin_type'], $Market)) {
                        $Text = "Rates are available!";

                        $HotelStatusArray[$HotelIndex][] = ['status' => true, 'text' => $Text, 'hotel_date' => $HotelDateStatus];

                    } else {
                        $Text = "There are no any rates!";
                        if (Hotel::hasRate($NewSetting['cruise'], $NewSetting['check_in'], $NewSetting['cabin_occupancy_type'], $NewSetting['meal_type'], $NewSetting['cabin_type'])) {
                            $Text .= ' (for ' . Market::find($Market)->market . ")";
                        }
                        $HotelStatusArray[$HotelIndex][] = ['status' => false, 'text' => $Text, 'hotel_date' => $HotelDateStatus, 'hotel_name' => Cruise::find($NewSetting['cruise'])->name];

                        $CanApply = false;
                    }
                }


                if (isset($NewSetting['selected_rooms'])) {
                    foreach ($NewSetting['selected_rooms'] as $RoomCode => $On) {
                        $NewSetting['rate_keys'][] = $NewSetting['room'][$RoomCode];
                    }
                }
            }

            unset($NewSetting['room']);

            unset($NewSetting['hotel_setting']);
            unset($NewSetting['cruise_setting']);
            $QueryArray[$HotelIndex] = http_build_query($NewSetting);
        }

        if (\Auth::user()->can("change_hotel_rates")) {
            $CanApply = true;
        }



        return ['Query' => $QueryArray, 'HotelStatusArray' => $HotelStatusArray, 'CanApply' => $CanApply];
    }

    /**
     * @param $Pax
     * @return array
     */
    static function getHotelRoomCountWithPax($Pax)
    {

        $Adult = $Pax['adult'];
        $CWB = $Pax['cwb'];
        $CNB = $Pax['cnb'];

        $single = 0;
        $double = 0;
        $tripple = 0;

        if ($CWB == 0) {
            $tripple = floor($Adult / 3);
            if ($tripple > 0) $Adult -= $tripple * 3;
        }

        $double = floor($Adult / 2);
        if ($double > 0)
            $Adult -= $double * 2;
        $single = floor($Adult);

        return ['1' => $single, '2' => $double, '3' => $tripple, '4' => 0, '5' => 0];

    }


    /**
     * @param $HotelID
     * @return Model|\Illuminate\Database\Eloquent\Relations\HasOne|null|object
     */
    static function getHotelCurrency($HotelID){
        $Country = Place::find(Hotel::find($HotelID)->city)->country()->first()->ID;
        return Currency::find(CountryCurrency::getCurrency($Country)->hotel);
    }
}
