<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\SoftDeletes;
/**
 * App\Model\Hotel\RatesChild
 *
 * @property int $ID
 * @property int $start_year
 * @property int $start_month
 * @property int $start_day
 * @property int $end_year
 * @property int $end_month
 * @property int $end_day
 * @property int $age_from
 * @property int $age_to
 * @property int $meal
 * @property int $hotel
 * @property float $rate
 * @property int $room_type
 * @property int $room_category
 * @property int $market
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property int|null $upload_id
 * @property string $special
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\RatesChild onlyTrashed()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereAgeFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereAgeTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereEndDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereEndMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereEndYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereMarket($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereRoomCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereRoomType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereSpecial($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereStartDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereStartMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereStartYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereUploadId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\RatesChild withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\RatesChild withoutTrashed()
 * @mixin \Eloquent
 */
class DiscountRatesChild extends Model
{

    protected $table = 'apple_hotel_discount_room_rates_child';
    protected $guarded = 'ID';
    use SoftDeletes;
    protected $dates = ['deleted_at'];

}
