<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;
use DB;

/**
 * App\Model\Hotel\Meal
 *
 * @property int $ID
 * @property string|null $plan
 * @property string $long_name
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal whereLongName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal wherePlan($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Meal whereUploadId($value)
 * @mixin \Eloquent
 */
class Meal extends Model
{
    protected $table = 'apple_meal_plan';
	
}
