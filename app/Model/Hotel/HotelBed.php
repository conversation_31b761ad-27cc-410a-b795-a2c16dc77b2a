<?php

namespace App\Model\Hotel;

use App\Model\Hotel\Hotel as HotelModel;
use App\Model\Image\Image;
use App\Model\Meal\MealPlan;
use App\Model\Place\Place;
use App\Model\Place\PlaceHotelbed;
use App\Model\Quotation\Quotation;
use App\Model\Quotation\QuotationHotel;
use Carbon\Carbon;
use DateTime;
use DB;
use hotelbeds\hotel_api_sdk\helpers\Booking;
use hotelbeds\hotel_api_sdk\HotelApiClient;
use hotelbeds\hotel_api_sdk\model\BookingRoom;
use hotelbeds\hotel_api_sdk\model\Destination;
use hotelbeds\hotel_api_sdk\model\Filter;
use hotelbeds\hotel_api_sdk\model\Holder;
use hotelbeds\hotel_api_sdk\model\Hotels;
use hotelbeds\hotel_api_sdk\model\Occupancy;
use hotelbeds\hotel_api_sdk\model\Pax;
use hotelbeds\hotel_api_sdk\model\Stay;
use hotelbeds\hotel_api_sdk\types\ApiVersion;
use hotelbeds\hotel_api_sdk\types\ApiVersions;
use Httpful\Handlers\JsonHandler;
use Httpful\Httpful;
use Httpful\Request;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use mysql_xdevapi\Exception;
use Session;

use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Illuminate\Support\Facades\Storage;
use Faker;

use App\Model\Hotel\apple_hotelbeds_destination_market;


/**
 * App\Model\Hotel\HotelBed
 *
 * @property int $ID
 * @property string|null $country_code
 * @property string|null $destination_code
 * @property string|null $name
 * @property string|null $description
 * @property string|null $address
 * @property string|null $city
 * @property string|null $email
 * @property string $timestamp
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Model\Hotel\HotelBedImage[] $HotelBedImage
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereCountryCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereDestinationCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereTimestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBed whereUploadId($value)
 * @mixin \Eloquent
 * @property-read mixed $image
 */
class HotelBed extends Model
{

    /**
     * @var string
     */
    protected $table = 'apple_hotel_hotelbeds';
    /**
     * @var array
     */
    protected $appends = ['image', 'city'];

    /**
     * @var mixed|string
     */
    var $url_search = "";
    /**
     * @var mixed|string
     */
    var $url_details = "";
    /**
     * @var mixed|string
     */
    var $url_country_list = "";
    /**
     * @var mixed|string
     */
    var $url_destinations_list = "";
    /**
     * @var mixed|string
     */
    var $url_ratecomment = "";
    /**
     * @var mixed|string
     */
    var $url_book_details = "";
    var $url_book_list = "";

    /**
     * @var mixed|string
     */
    var $apiKEY = "";
    /**
     * @var mixed|string
     */
    var $apiSECRET = "";


    /**
     * @var bool|HotelApiClient
     */
    var $apiClient = false;
    /**
     * @var bool
     */
    var $ReturnStaus = true;
    /**
     * @var string
     */
    var $ErrorMsg = "";

    /**
     * @var string
     */
    var $Status = '';

    /**
     * @var string
     */
    var $LastRequest = "";
    /**
     * @var bool
     */
    var $availRS = false;

    var  $storagePath = "";
    var $log = "";

    /**
     * HotelBed constructor.
     */
    function __construct()
    {
        $Status = env('HOTELBEDS_STATUS','test');

        $this->apiKEY = env('HOTELBEDS_KEY_TEST');
        $this->apiSECRET = env('HOTELBEDS_SECRET_TEST');

        $this->url_search = env('HOTELBEDS_SEARCH_URL');
        $this->url_details = env('HOTELBEDS_DETAILS_URL');
        $this->url_country_list = env('HOTELBEDS_COUNTRY_LIST_URL');
        $this->url_destinations_list = env('HOTELBEDS_DESTINATION_URL');
        $this->url_ratecomment = env('HOTELBEDS_RATECOMMENT_URL');
        $this->url_book_details = env('HOTELBEDS_BOOKDETAILS_URL');
        $this->url_book_list = env('HOTELBEDS_BOOKDETAILS_URL');
        $this->storagePath =  Storage::disk('mono_logs')->getDriver()->getAdapter()->getPathPrefix();
        $this->log = new Logger('Hotel Beds SDK Logs');

        #IF its the live version
        if ($this->Status == 'live') {

            $this->apiKEY = env('HOTELBEDS_KEY');
            $this->apiSECRET = env('HOTELBEDS_SECRET');

            $this->url_search = str_replace('.test', '', $this->url_search);
            $this->url_details = str_replace('.test', '', $this->url_details);
            $this->url_country_list = str_replace('.test', '', $this->url_country_list);
            $this->url_destinations_list = str_replace('.test', '', $this->url_destinations_list);
            $this->url_ratecomment = str_replace('.test', '', $this->url_ratecomment);
            $this->url_book_details = str_replace('.test', '', $this->url_book_details);
            $this->url_book_list = str_replace('.test', '', $this->url_book_list);

        }


        $this->apiClient = new HotelApiClient($this->url_search,
            $this->apiKEY,
            $this->apiSECRET,
            new ApiVersion(ApiVersions::V1_0),
            env('HOTELBEDS_TIMEOUT'));
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function HotelBedImage()
    {
        return $this->hasMany('App\Model\Hotel\HotelBedImage', 'id', 'hotel_id');
    }


    /**
     * @return string
     */
    function getSignature()
    {
        return hash("sha256", $this->apiKEY . $this->apiSECRET . time());
    }

    /**
     * @return array
     */
    function getHeader()
    {


        $Header = [];

        $Header['Accept'] = 'application/json';
        $Header['Api-key'] = $this->apiKEY;
        $Header['X-Signature'] = $this->getSignature();
        $Header['Content-Type'] = 'application/json';

        return $Header;

    }

    /**
     * @param $Data
     * @return string
     */
    function getHotelSearchBody($Data)
    {


        $MainArray = [];

        //dates
        $check_in_format = Carbon::create($Data['hotel_check_in']['year'], $Data['hotel_check_in']['month'], $Data['hotel_check_in']['day'], 0);
        $check_out_format = Carbon::create($Data['hotel_check_out']['year'], $Data['hotel_check_out']['month'], $Data['hotel_check_out']['day'], 0);

        $MainArray['stay']['checkIn'] = $check_in_format->toDateString();
        $MainArray['stay']['checkOut'] = $check_out_format->toDateString();


        //pax and room
        for ($c = 1; $c <= $Data['hotel_num_room']; $c++) {

            $MainArray['occupancies'][$c - 1]['rooms'] = 1;
            $MainArray['occupancies'][$c - 1]['adults'] = $Data['pax']['adult'][$c];
            $MainArray['occupancies'][$c - 1]['children'] = $Data['pax']['child'][$c];


            if ($Data['pax']['child'][$c] > 0) {//check room has any children

                for ($cc = 0; $cc < $Data['pax']['child'][$c]; $cc++) {

                    $MainArray['occupancies'][$c - 1]['paxes'][] = ['type' => 'CH', 'age' => $Data['pax']['child_age'][$c][$cc]];
                }

            }

        }

        //location
        $MainArray['destination']['code'] = 'MAD';

//        spd($MainArray);


        return json_encode($MainArray);

    }

    /**
     * @param $Data
     * @return bool
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    function getSearchHotels($Data)
    {

        $Header = $this->getHeader();
        $Body = $this->getHotelSearchBody($Data);


        $response = Request::post($this->url_search)
            ->addHeaders($Header)
            ->body($Body)
            ->send();

        if (!empty($response->body->hotels) && $response->body->hotels->total > 0) {
            return $response->body->hotels;
        } else
            return false;

    }


    /**
     * @param $a2
     * @throws \Exception
     * @throws \Throwable
     */
    static function updateDB($a2)
    {
        $HotelBeds = new HotelBed();
        $go = true;
        $from = 0;
        $to = 500;

        while ($go) {

            $HotelList = $HotelBeds->getHotelLists(["name", "address", "coordinates", "description", "images", 'destinationCode', 'countryCode', 'categoryCode'], $a2, false, false, false, false, $from, $to);

            foreach ($HotelList->body['hotels'] ?? [] as $k => $hotelItem) {
                /*if (HotelBed::find($hotelItem['code']))
                    continue;*/
                HotelBed::updateOrAddHotel($hotelItem);
            }

            if (count($HotelList->body['hotels'] ?? []) < $to) {
                $go = false;
            } else {
                $from = $to;
                $to = $to + 500;
            }

        }

    }

    /**
     * @param $hotelItem
     * @throws \Throwable
     */
    static function updateOrAddHotel($hotelItem)
    {

        ini_set('memory_limit', '2048M');
        try {
            DB::transaction(function () use ($hotelItem) {
                $HotelBed = HotelBed::find($hotelItem['code']) ?? new HotelBed();

                $HotelBed->id = $hotelItem['code'];
                $HotelBed->name = $hotelItem['name']['content'];
                $HotelBed->description = $hotelItem['description']['content'] ?? "";
                $HotelBed->country_code = $hotelItem['destination']['countryCode'] ?? $hotelItem['countryCode'];
                $HotelBed->destination_code = $hotelItem['destination']['code'] ?? $hotelItem['destinationCode'];
                $HotelBed->coordinates_longitude = $hotelItem['coordinates']['longitude'] ?? null;
                $HotelBed->coordinates_latitude = $hotelItem['coordinates']['latitude'] ?? null;
                $HotelBed->address = $hotelItem['address']['content'];
                $HotelBed->class = $hotelItem['categoryCode'] ?? null;
                $HotelBed->save();

                foreach ($hotelItem['images'] ?? [] as $imageItem) {


                if (empty($imageItem['path']))
                    continue;
                    // if(!(HotelBedImage::where('path',$imageItem['path'])->exists())) {
                        $HotelBedImage = new HotelBedImage();
                        $HotelBedImage->hotel_id = $hotelItem['code'];
                        $HotelBedImage->image_type_code = $imageItem['type']['code'] ?? null;
                        $HotelBedImage->path = $imageItem['path'];
                        $HotelBedImage->order = $imageItem['order'];
                        $HotelBedImage->save();
                    // }
                }


            });
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * @param bool $fields
     * @param bool $destinationCode
     * @param bool $codes
     * @param bool $countryCode
     * @param bool $lastUpdateTime
     * @param bool $language
     * @param int $from
     * @param int $to
     * @param bool $useSecondaryLanguage
     * @return bool|\Httpful\Response
     */
    function getHotelLists($fields = false, $destinationCode = false, $codes = false, $countryCode = false, $lastUpdateTime = false, $language = false, $from = 1, $to = 10, $useSecondaryLanguage = false)
    {
        ini_set('max_execution_time', 0);

        $url = $this->url_details;
        $Header = $this->getHeader();

        $URL_Fields = [];


        if ($codes) {
            $URL_Fields['codes'] = $codes;
        }
        if ($destinationCode) {
            $URL_Fields['destinationCode'] = $destinationCode;
        }
        if ($countryCode) {
            $URL_Fields['countryCode'] = ($countryCode);
        }
        if ($lastUpdateTime) {
            $URL_Fields['lastUpdateTime'] = $lastUpdateTime;
        }

        if ($language) {
            $URL_Fields['language'] = ($language);
        }

        if ($from) {
            $URL_Fields['from'] = ($from);
        }

        if ($to) {
            $URL_Fields['to'] = ($to);
        }

        if ($useSecondaryLanguage) {
            $URL_Fields['useSecondaryLanguage'] = $useSecondaryLanguage;
        }


        $url .= http_build_query($URL_Fields);

        if ($fields) {
            $url .= "&fields=" . implode('%2C', $fields);
        } else {
            $url .= "&fields=all";
        }

        //to array
        $json_handler = new JsonHandler(['decode_as_array' => true]);
        Httpful::register('application/json', $json_handler);

        try {

            $response = Request::get($url)
                ->addHeaders($Header)
                ->send();

            if (empty($response->body['hotels'])) {
                $this->LastRequest = $url;
                $this->ErrorMsg = $response->body;
                return false;
            }
            return $response;

        } catch (\Exception $e) {
            $this->ErrorMsg = $e->getMessage();
            return false;
        }
    }


    /**
     * @param int $limit
     * @return array|Collection
     */
    public function getImageAttribute($limit = 5)
    {
        $imageList = HotelBedImage::where('hotel_id', $this->id)->limit($limit)->get();

        if ($imageList->isNotEmpty())
            return $imageList->pluck("path")->map(function ($path) {
                return "//photos.hotelbeds.com/giata/" . $path;
            });
        else
            return collect(Image::getImageDirect($this->id, "4x", "hotelbeds"));
    }


    /**
     * @return int|mixed|null
     */
    public function getCityAttribute()
    {
        $City = Place::where('A2', $this->destination_code)->where("type", 1)->where("status", 1)->first();
        if ($City)
            return $City->ID;
        else
            return null;

    }

    /**
     * @param $ID
     * @param bool $AppleData
     * @return bool|Model|null|object|static
     * @throws \Httpful\Exception\ConnectionErrorException
     * @throws \Throwable
     */
    function getHotelDetails($ID, $AppleData = true)
    {
        $HotelBedsImageAvailable = HotelBedImage::where('hotel_id', $ID)->first();

        if (!$HotelBedsImageAvailable || !$AppleData) {

            $url = str_replace("?", "/", $this->url_details . $ID);
            $Header = $this->getHeader();


            //to array
            $json_handler = new JsonHandler(array('decode_as_array' => true));
            Httpful::register('application/json', $json_handler);


            $response = Request::get($url)
                ->addHeaders($Header)
                ->expects('application/json')
                ->send();


            if (!empty($response->body['hotel'])) {

                if (!$AppleData) {
                    return $response->body['hotel'];
                }

                HotelBed::updateOrAddHotel($response->body['hotel']);


            } else
                return false;

        }

        return HotelBed::where("ID", $ID)->first();

    }


    /**
     * @param $Reference
     * @return array|bool
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    function getBookDetails($Reference)
    {
        #check in the api
        $Header = $this->getHeader();

        $json_handler = new JsonHandler(array('decode_as_array' => true));
        Httpful::register('application/json', $json_handler);

        $response = Request::get($this->url_book_details . $Reference)
            ->addHeaders($Header)
            ->expects('application/json')
            ->send();

        if (!empty($response->body['booking'])) {
            return $response->body['booking'];
        } else
            return array('status' => false,'result' => $response->body );


    }

    /**
     * @param $Code
     * @param $RateClass
     * @param $Meal
     * @param bool $Priority
     * @param $Filters
     * @param $RoomCount
     * @return bool
     */
    function getAvailableRates($Code, $RateClass, $Meal, $Priority = false, $Filters, $RoomCount)
    {

        $Rooms = Session::get("quotation.api_hotel.hotelbed.rooms");

        $ReturnRate = false;
        $LastRate = false;

        //pax
        $Adult = intval($Filters['pax']['adult'][$RoomCount]);
        $Child = intval($Filters['pax']['child'][$RoomCount]);
        $ChildAge = implode(',', array_filter($Filters['pax']['child_age'][$RoomCount]));


//        s($Adult,$Child,$ChildAge);

        foreach ($Rooms as $RoomItem) {


            $CatCode = $RoomItem->code;
            $CatName = $RoomItem->name;


            //check catogory
            if ($RoomItem->code != $Code)
                continue;


            foreach ($RoomItem->rates as $RateItem) {

                $LastRate = $RateItem;

                //pax check
                if ($RateItem->adults != $Adult || $RateItem->children != $Child || (isset($RateItem->childrenAges) && $RateItem->childrenAges != $ChildAge)) {
                    continue;
                }


                if ($Priority != 'cat') {
                    //check rate class
                    if ($RateItem->rateClass != $RateClass)
                        continue;

                    //check meal
                    if ($RateItem->boardCode != $Meal)
                        continue;
                }

                $RateItem->name = $CatName;
                $RateItem->code = $CatCode;

                $ReturnRate = $RateItem;


                return $ReturnRate;


            }

        }
        return $LastRate;


    }

    /**
     * @param $Pax
     * @return array
     */
    function paxToFilters($Pax)
    {

        $Hotel = new Hotel();
        $Filters = [];

        $PaxRoomCount = $Hotel->getHotelRoomCountWithPax($Pax);
        $Filters['hotel_num_room'] = array_sum($PaxRoomCount);

        foreach ($PaxRoomCount as $RoomType => $RoomCount) {
            for ($c = 1; $c <= $RoomCount; $c++) {

                $Filters['pax']['adult'][$c] = $RoomType;
                $Filters['pax']['child'][$c] = [];
                $Filters['pax']['child_age'][$c] = [];

            }
        }


        return $Filters;

    }


    /**
     * @param $Rooms
     * @param $Filters
     * @return array
     */
    static function getAvailableFilters($Rooms, $Filters)
    {

        $Rooms = objectToArray($Rooms);

        $MainArray = [];
        $CurrentRoomRate = [];

        $RoomCatList = array_unique(array_column($Rooms, 'name', 'code'));


        for ($RoomCount = 1; $RoomCount <= $Filters['hotel_num_room']; $RoomCount++) {//room count loop

            $Adult = intval($Filters['pax']['adult'][$RoomCount]);
            $Child = intval($Filters['pax']['child'][$RoomCount]);
            $ChildAge = implode(',', array_filter($Filters['pax']['child_age'][$RoomCount]));

            $MainArray[$RoomCount]['pax'] = [$Adult, $Child, $ChildAge];


            foreach ($Rooms as $Key => $RateItemArray) {


                $RoomColumn = $RateItemArray['rates'];
                $CatCode = $RateItemArray['code'];
                $CatName = $RateItemArray['name'];

                foreach ($RoomColumn as $RateIndex => $RateColumn) {

                    //check adult
                    if ($RateColumn['adults'] == $Adult && $RateColumn['children'] == $Child && ($Child && $RateColumn['childrenAges'] == $ChildAge || !$Child)) {

                        $RateColumn['category'] = $CatName;
                        $RateColumn['code'] = $CatCode;
                        $CurrentRoomRate[$RoomCount][$CatCode][] = $RateColumn;
                    }
                }

            }

            foreach ($RoomCatList as $catCode => $catName) {


                if (isset($CurrentRoomRate[$RoomCount][$catCode])) {

                    $MainArray[$RoomCount][$catCode]['category'] = $catName;
                    $MainArray[$RoomCount][$catCode]['code'] = $catCode;


                    $MainArray[$RoomCount][$catCode]['rateType'] = array_unique(array_column($CurrentRoomRate[$RoomCount][$catCode], 'rateType', 'rateType'));
                    $MainArray[$RoomCount][$catCode]['rateClass'] = array_unique(array_column($CurrentRoomRate[$RoomCount][$catCode], 'rateClass', 'rateClass'));
                    $MainArray[$RoomCount][$catCode]['meal'] = array_unique(array_column($CurrentRoomRate[$RoomCount][$catCode], 'boardCode', 'boardCode'));


                }
            }

        }

        return $MainArray;
    }


    /**
     * @param $Data
     * @param int $Limit
     * @return bool|Hotels
     * @throws \ReflectionException
     */
    function Availability($Data, $Limit = 10)
    {


        $rqData = new \hotelbeds\hotel_api_sdk\helpers\Availability();


        $check_in = $Data['hotel_check_in']['year'] . "-" . $Data['hotel_check_in']['month'] . "-" . $Data['hotel_check_in']['day'];
        $check_out = $Data['hotel_check_out']['year'] . "-" . $Data['hotel_check_out']['month'] . "-" . $Data['hotel_check_out']['day'];

        $rqData->stay = new Stay(DateTime::createFromFormat("Y-m-d", $check_in), DateTime::createFromFormat("Y-m-d", $check_out));


        if (!empty($Data['hotels']))
            $rqData->hotels = ["hotel" => arrayMapMulti('getActualDataType', $Data['hotels'])];

        else {
            $PlaceCode = PlaceHotelbed::where('ID', $Data['place'])->first();
            $rqData->destination = new Destination($PlaceCode->code);//By default this search type is disabled
        }
        //$rqData->destination = new Destination("PMI");

        /*      $geolocation = new Geolocation();
                $geolocation->latitude = $PlaceCode->latitude;
                $geolocation->longitude = $PlaceCode->longitude;
                $geolocation->radius = 5.0;
                $geolocation->unit = Geolocation::KM;
                $rqData->geolocation = $geolocation;*/


        $occupancyAR = [];


        for ($c = 1; $c <= $Data['hotel_num_room']; $c++) {


            $occupancy = new Occupancy();
            $occupancy->adults = intval($Data['pax']['adult'][$c]);
            $occupancy->children = intval($Data['pax']['child'][$c]);
            $occupancy->rooms = 1;


            if ($Data['pax']['child'][$c] > 0) {//check room has any children

                $occupancy_pax = [];
                for ($cc = 0; $cc < $Data['pax']['child'][$c]; $cc++) {
                    $occupancy_pax[] = new Pax(Pax::CH, intval($Data['pax']['child_age'][$c][$cc]));

                }
                $occupancy->paxes = $occupancy_pax;

            }

            $occupancyAR[] = $occupancy;

        }

        $rqData->occupancies = $occupancyAR;

        //filters
        $rqData->filter = new Filter();
        $rqData->filter->maxHotels = $Limit;


        try {
            $availRS = $this->apiClient->Availability($rqData);

        } catch (\hotelbeds\hotel_api_sdk\types\HotelSDKException $e) {
            $this->LastRequest = $this->apiClient->getLastRequest()->getContent();
            $auditData = $e->getAuditData();
            $this->ErrorMsg = $e->getMessage() . "(" . json_encode($auditData) . ")";
            $this->ReturnStaus = false;
            return false;
        } catch (\Exception $e) {
            $this->ReturnStaus = false;
            $this->ErrorMsg = $e->getMessage();
            return false;
        }

        // Check availability is empty or not!
        if (!$availRS->isEmpty()) {
            $this->ReturnStaus = true;
            return $availRS->hotels;
        } else {


            $this->ErrorMsg = "No Hotels!";
            $this->ReturnStaus = false;
            return false;
        }
    }

    /**
     * @param $RateKeyArray
     * @return bool|\hotelbeds\hotel_api_sdk\messages\CheckRateRS
     */
    function CheckRate($RateKeyArray)
    {
        $now = Carbon::now()->toDateTimeString();
        $this->log->pushHandler(new StreamHandler($this->storagePath.'/'.$now.'-checkRate.json', Logger::DEBUG));

        $rqCheck = new \hotelbeds\hotel_api_sdk\helpers\CheckRate();
        $rqCheck->rooms = $RateKeyArray;

        try {
            $this->ReturnStaus = false;
            $checkRate =  $this->apiClient->CheckRate($rqCheck);

            $this->log->info($this->apiClient->getLastRequest());
            $this->log->info('---------------------------------------------------------------------');
            $this->log->info('Response' , $checkRate->hotel->toArray());
            return $checkRate;



        } catch (\Exception $e) {

            $this->ErrorMsg = $e->getMessage();
            $this->ReturnStaus = false;
            $this->log->info($this->apiClient->getLastRequest() .  $e->getMessage());
            return false;
        }

    }

    /**
     * @return array
     */
    function getRateKeys()
    {

        $RoomSettings = Session::get("quotation.api_hotel.hotelbed.room_settings");
        $SavedFilters = Session::get("quotation.api_hotel.filters");

        $RateKeyArray = [];

        for ($c = 1; $c <= $SavedFilters['hotel_num_room']; $c++) {
            $RateKeyArray[]['rateKey'] = $this->getAvailableRates($RoomSettings['room_category'][$c], $RoomSettings['rate'][$c], $RoomSettings['meal'][$c], false, $SavedFilters, $c)->rateKey;

        }

        return $RateKeyArray;
    }


    /**
     * @param $From
     * @param $To
     * @return bool
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    function getCountryList($From, $To)
    {

        $Header = $this->getHeader();

        $Query['language'] = "ENG";
        $Query['from'] = $From;
        $Query['to'] = $To;
        $Query['fields'] = "all";


        //to array
        $json_handler = new JsonHandler(array('decode_as_array' => true));
        Httpful::register('application/json', $json_handler);

        $response = Request::get($this->url_country_list . http_build_query($Query))
            ->addHeaders($Header)
            ->expects('application/json')
            ->send();

        if (isset($response->body['countries'])) {
            return $response->body['countries'];
        } else
            return false;
    }


    /**
     * @param $From
     * @param $To
     * @param $countryCodes
     * @return bool
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    function getDestinationList($From, $To, $countryCodes)
    {

        $Header = $this->getHeader();
        $Query['fields'] = "all";
        #$Query['codes'] = "";
        $Query['countryCodes'] = $countryCodes;
        #$Query['lastUpdateTime'] = "";
        $Query['language'] = "ENG";
        $Query['from'] = $From;
        $Query['to'] = $To;
        #$Query['useSecondaryLanguage'] = "";


        //to array
        $json_handler = new JsonHandler(array('decode_as_array' => true));
        Httpful::register('application/json', $json_handler);

        $response = Request::get($this->url_destinations_list . http_build_query($Query))
            ->addHeaders($Header)
            ->expects('application/json')
            ->send();


        if (isset($response->body['destinations'])) {
            return $response->body['destinations'];
        } else
            return false;
    }

    /**
     * @param $PaxList
     * @return array
     */
    function getPaxes($PaxList)
    {

        $Paxes = [];
        $SavedFilters = Session::get("quotation.api_hotel.filters.pax");


        foreach ($PaxList as $RoomCount => $PaxArray) {

            foreach ($PaxArray as $GuestCount => $PaxItem) {

                foreach ($PaxItem as $PaxType => $PaxDetails) {
                    if ($PaxType == 'adult')
                        $Paxes[$RoomCount][] = new Pax(Pax::AD, 30, $PaxDetails['fn'], $PaxDetails['ln'], 1);
                    else {

                        $ChAge = intval($SavedFilters['child_age'][$RoomCount][$GuestCount - 1]);
                        $Paxes[$RoomCount][] = new Pax(Pax::CH, $ChAge, $PaxDetails['fn'], $PaxDetails['ln'], 1);

                    }
                }
            }

        }

        return $Paxes;


    }


    /**
     * @param $PaxArray
     * @param $rateKey
     * @param string $ClientReference
     * @param string $language
     * @param string $HolderName
     * @param string $HolderSurname
     * @return bool|\hotelbeds\hotel_api_sdk\messages\BookingConfirmRS
     */
    function BookingConfirm($PaxArray, $rateKey, $ClientReference = "Apple_Holidays", $language = "ENG", $HolderName = "Apple Holidays", $HolderSurname = "Apple Holidays")
    {

        $PaxObject = HotelBed::paxGenerator($PaxArray);

        $rqBookingConfirm = new Booking();
        $rqBookingConfirm->holder = new Holder($HolderName, $HolderSurname);


        $bookingRoom = new BookingRoom($rateKey['rateKey']);
        $bookingRoom->paxes = $PaxObject;
        $bookRooms[] = $bookingRoom;


        $rqBookingConfirm->language = $language;
        $rqBookingConfirm->rooms = $bookRooms ?? [];
        $rqBookingConfirm->clientReference = $ClientReference;

        $now = Carbon::now()->toDateTimeString();
        $this->log->pushHandler(new StreamHandler($this->storagePath.'/'.$now.'-Booking.json', Logger::DEBUG));


        try {
            $confirmRS = $this->apiClient->BookingConfirm($rqBookingConfirm);
            $this->ReturnStaus = true;

            $this->log->info($this->apiClient->getLastRequest());
            $this->log->info('---------------------------------------------------------------------');
            $this->log->info('Response' , $confirmRS->booking->toArray());

            \Log::info($this->apiClient->getLastRequest());
            return $confirmRS;
        } catch (\Exception $e) {
            $this->ReturnStaus = false;
            \Log::error($this->apiClient->getLastRequest() . $e->getMessage());
            $this->log->info($this->apiClient->getLastRequest() .  $e->getMessage());
            $this->ErrorMsg = $e->getMessage();
            return false;
        }

    }

    /**
     *
     */
    function BookingCancellation($bookingRef)
    {

//        $flag = "SIMULATION";
        $flag = "CANCELLATION";

        $Header = $this->getHeader();
        $dataWithUrl = $this->url_book_details .$bookingRef."?cancellationFlag=".$flag;

        $now = Carbon::now()->toDateTimeString();
        $this->log->pushHandler(new StreamHandler($this->storagePath.'/'.$now.'-cancel.json', Logger::DEBUG));

        //to array
        $json_handler = new JsonHandler(array('decode_as_array' => true));
        Httpful::register('application/json', $json_handler);

        try{
            $response = \Httpful\Request::delete($dataWithUrl)
                ->addHeaders($Header)
                ->expects('application/json')
                ->send();
//            $response = json_decode($response);
            $this->log->info('Request' , [$dataWithUrl]);
            $this->log->info('---------------------------------------------------------------------');
            $this->log->info('Response' , [json_decode($response,true)]);
            if(isset($response->body['booking'])) {
                return $response->body['booking'];
            } else {
                return false;
            }


        } catch (Exception $e){
            $this->log->info('Response' , $e->getMessage());
            return false;
        }
    }


    /**
     * @param $From
     * @param $To
     * @param $filterType
     * @param $clientReference
     * @return bool
     */
    function BookingList($From, $To, $filterType, $clientReference)
    {
        $urlFields = [];
        $Header = $this->getHeader();

        $urlFields['start'] = Carbon::parse($From)->toDateString();
        $urlFields['end'] = Carbon::parse($To)->toDateString();
        $urlFields['from'] = 1;
        $urlFields['to'] = 1;
        $urlFields['includeCancelled'] = true;
        $urlFields['filterType'] = $filterType;

        if ($clientReference) {
            $urlFields['clientReference'] = $clientReference;
        }

        $url = http_build_query($urlFields);

        //to array
        $json_handler = new JsonHandler(['decode_as_array' => true]);
        Httpful::register('application/json', $json_handler);

        try {

            $response = Request::get($this->url_book_list ."?". $url)
                ->addHeaders($Header)
                ->send();

            if (empty($response->body['bookings'])) {
                $this->LastRequest = $url;
                $this->ErrorMsg = $response->body;
                return false;
            }
            return $response->body['bookings'];

        } catch (\Exception $e) {
            $this->ErrorMsg = $e->getMessage();
            return false;
        }

    }


    /**
     *
     */
    function Status()
    {

    }


    /**
     * @param $Date
     * @param $Code
     * @return bool
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    function rateCommentDetails($Date, $Code)
    {


        $Header = $this->getHeader();
        $Query['fields'] = "all";
        $Query['code'] = $Code;
        $Query['date'] = $Date->toDateString();

        $Query['language'] = "ENG";
        $Query['from'] = 1;
        $Query['to'] = 100;


        //to array
        $json_handler = new JsonHandler(array('decode_as_array' => true));
        Httpful::register('application/json', $json_handler);

        $response = Request::get($this->url_ratecomment . urldecode(http_build_query($Query)))
            ->addHeaders($Header)
            ->expects('application/json')
            ->send();

        if (isset($response->body['rateComments'])) {
            return $response->body['rateComments'];
        } else
            return false;
    }


    /**
     * @param $Data
     * @param $QuotationArray
     * @param bool $Hotels
     * @param int $Limit
     * @param bool $MaxRatesPerRoom
     * @param bool $MaxCategory
     * @return bool|Hotels
     * @throws \ReflectionException
     */
    function AvailabilityCombine($Data, $QuotationArray, $Hotels = false, $Limit = 5, $MaxRatesPerRoom = false, $MaxCategory = false)
    {

        ini_set('max_execution_time', 0);

        $Hotel = new Hotel();
        $PaxRoomCount = $Hotel->getHotelRoomCountWithPax($QuotationArray['pax']);
        //$MealPlan = Meal::find($Data['meal_type'])->plan;
        //$RoomTypes = RoomType::whereIn('ID', array_keys(array_filter($Data['room_type'])))->pluck('short_name')->toArray();

        $rqData = new \hotelbeds\hotel_api_sdk\helpers\Availability();

        $check_in = $Data['check_in']['year'] . "-" . $Data['check_in']['month'] . "-" . $Data['check_in']['day'];
        $check_out = $Data['check_out']['year'] . "-" . $Data['check_out']['month'] . "-" . $Data['check_out']['day'];

        $rqData->stay = new Stay(DateTime::createFromFormat("Y-m-d", $check_in), DateTime::createFromFormat("Y-m-d", $check_out));

        $PlaceCode = Place::where('ID', $Data['place'])->first();

        #Search by desitination code
        if ($Hotels) {
            $rqData->hotels = ["hotel" => arrayMapMulti('getActualDataType', $Hotels)];
        } else {
            $rqData->destination = new Destination($PlaceCode->A2);//By default this search type is disabled
        }

        $TotalChildren = $QuotationArray['pax']['cwb'] + $QuotationArray['pax']['cnb'];
        $currentHotelRoomTypes = $Data['room_type'];
        $currentHotelRoomTypes =  arrayMapMulti('getActualDataType', $currentHotelRoomTypes);

        $TotalRooms = array_sum($PaxRoomCount);
        $ChildCountArray = $this->childrenToRoom($TotalChildren, $TotalRooms, $PaxRoomCount);
        $RoomArray = HotelBed::duplicateRoomGenerator($PaxRoomCount, $ChildCountArray);
        $occupancies = HotelBed::occupanciesGenerator($RoomArray,$currentHotelRoomTypes);
        $rqData->occupancies = $occupancies;


        //filters



        $rqData->filter = new Filter();
        $rqData->filter->maxHotels = $Limit;
        if ($MaxRatesPerRoom)
            $rqData->filter->maxRatesPerRoom = $MaxRatesPerRoom;
        if ($MaxCategory)
            $rqData->filter->maxCategory = $MaxCategory;

        $rqData->filter->hotelPackage = "NO";
        $rqData->filter->paymentType = 'AT_WEB';

        $nationality = $QuotationArray['nationality'];
        $place  = Place::where('ID',$nationality)->first();
        if($place) {
            $sourceMarket = $place->A2;
            $rqData->sourceMarket = $sourceMarket;
        }

        $now = Carbon::now()->toDateTimeString();

        $this->log->pushHandler(new StreamHandler($this->storagePath.'/'.$now.'-Availability.json', Logger::DEBUG));

        try {
            $this->availRS = $this->apiClient->Availability($rqData);
            $this->LastRequest = $this->apiClient->getLastRequest()->getContent();
            \Log::info($this->LastRequest);


            $this->log->info($this->apiClient->getLastRequest());
            $this->log->info('---------------------------------------------------------------------');
            $this->log->info('Response' , $this->availRS->hotels->toArray());

        } catch (\Exception $e) {
            $this->LastRequest = $this->apiClient->getLastRequest()->getContent();
            $this->ReturnStaus = false;
            $this->ErrorMsg = $e->getMessage();
            \Log::error($this->ErrorMsg);
            \Log::info($this->LastRequest);
            $this->log->info($this->apiClient->getLastRequest() .  $e->getMessage());
            return false;
        }

        // Check availability is empty or not!
        if (!$this->availRS->isEmpty()) {
            $this->ReturnStaus = true;
            return $this->availRS->hotels;
        } else {
            $this->ErrorMsg = "No Hotels!";
            $this->ReturnStaus = false;
            return false;
        }
    }

    /**
     * @param $PaxRoomCount
     * @param $ChildCountArray
     * @return array
     */
    static function duplicateRoomGenerator($PaxRoomCount, $ChildCountArray)
    {
        $OccupancyObject = [];
        foreach ($PaxRoomCount as $RoomType => $RoomCount) {
            for ($c = 1; $c <= $RoomCount; $c++) {

                $ChildNum = intval($ChildCountArray[$RoomType][$c]);
                $OccupancyObject['a' . $RoomType . 'c' . $ChildNum][] = [
                    'adult' => $RoomType,
                    'child' => $ChildNum
                ];
            }
        }

        return array_values($OccupancyObject);
    }

    /**
     * @param $RoomArray
     * @return array
     */
    static function paxGenerator($RoomArray)
    {

        $faker = Faker\Factory::create();
        $Paxes = [];
        $k = 1;
        $j = 1;
        for ($a_count = 0; $RoomArray['adult'] * $RoomArray['rooms'] > $a_count; $a_count++) {
            $Paxes[] = new Pax(Pax::AD, 30, $faker->firstName, $faker->lastName, $k);
            if($j % $RoomArray['adult'] == 0){
                $k++;
            }
            $j++;
        }
        $k = 1;
        $j = 1;
        for ($c_count = 0; $RoomArray['child'] > $c_count; $c_count++) {
            $Paxes[] = new Pax(Pax::CH, 6, $faker->firstName, $faker->lastName, $k);
            if($j % $RoomArray['adult'] == 0){
                $k++;
            }
            $j++;
        }

        return $Paxes;
    }

    /**
     * @param $RoomArray
     * @return array
     */
    static function occupanciesGenerator($RoomArray,$RoomType)
    {
        $occupancyAR = [];
        $childCountArray = array();
        foreach ($RoomArray as $Combines) {
            foreach ($Combines as $CombineItem ) {
                if($CombineItem['child']) {
                    array_push($childCountArray, $CombineItem['child']);
                }
            }
        }
        sort($childCountArray);
        $index = 0;
        $indices = 0;
        foreach ($RoomType as $RoomTypeID => $RoomCount) {
            if ($RoomCount) {
                $indices++;
            }
        }
//                dd($RoomArray,$childCountArray,$indices);
        foreach ($RoomType as $RoomTypeID => $RoomCount) {
            if ($RoomCount) {
                $occupancy = new Occupancy();
                $occupancy->adults = intval($RoomTypeID) * intval($RoomCount);//maximum count for room ex:room-type DBL==2adult
                if(isset($childCountArray[$index])){
                    $childrenSum = array_sum($childCountArray);
                    if($indices == 1){
                        $occupancy->children = $childrenSum;
                        $occupancy_pax = [];
                        for ($cc = 0; $cc < $childrenSum; $cc++) {
                            $occupancy_pax[] = new Pax(Pax::CH, 6);
                        }
                        $occupancy->paxes = $occupancy_pax;

                    } else {
                        $occupancy->children = $childCountArray[$index];
                        $occupancy_pax = [];
                        for ($cc = 0; $cc < $childCountArray[$index]; $cc++) {
                            $occupancy_pax[] = new Pax(Pax::CH, 6);
                        }
                        $occupancy->paxes = $occupancy_pax;
                    }



                } else {
                    $occupancy->children = 0;
                }

                $occupancy->rooms = $RoomCount;
                $occupancyAR[] = $occupancy;
                $index++;
            }
        }

//        foreach ($RoomArray as $Combine) {
//
//            $Rooms = count($Combine);
//            $CombineItem = $Combine[0];
//
//
//            $occupancy = new Occupancy();
//            $occupancy->adults = intval($CombineItem['adult']);//maximum count for room ex:room-type DBL==2adult
//            $occupancy->children = intval($CombineItem['child']);
//            $occupancy->rooms = $Rooms;
//
//
//            if ($CombineItem['child'] > 0) {//check room has any children
//
//                $occupancy_pax = [];
//                for ($cc = 0; $cc < $CombineItem['child']; $cc++) {
//                    $occupancy_pax[] = new Pax(Pax::CH, 6);
//                }
//                $occupancy->paxes = $occupancy_pax;
//
//            }
//            $occupancyAR[] = $occupancy;
//
//        }

        return $occupancyAR;


    }

    /**
     * @param $availRS
     * @param array $meals
     * @return mixed
     */
    function filterMeal($availRS, Array $meals)
    {
        $Hotels = $availRS->hotels->hotels;

        foreach ($Hotels as $HotelItemKey => $HotelItem) {
            foreach ($HotelItem['rooms'] as $RoomKey => $RoomItem) {
                foreach ($RoomItem['rates'] as $RateKey => $RatesItem) {
                    if (!in_array($RatesItem['boardCode'], $meals)) {
                        unset($Hotels[$HotelItemKey]['rooms'][$RoomKey]['rates'][$RateKey]);
                    }
                };
            };

        };

        $availRS->hotels->hotels = $Hotels;

        return $availRS;
    }

    /**
     * @param $availRS
     * @param array $rooms
     * @return mixed
     */
    function filterRooms($availRS, Array $rooms)
    {
        $Hotels = $availRS->hotels->hotels;

        foreach ($Hotels as $HotelItemKey => $HotelItem) {
            foreach ($HotelItem['rooms'] as $RoomKey => $RoomItem) {


                if (!str_contains($RoomItem['code'], $rooms))
                    unset($Hotels[$HotelItemKey]['rooms'][$RoomKey]);

            };

        };

        $availRS->hotels->hotels = $Hotels;

        return $availRS;
    }

    /**
     * @param $TotalChildren
     * @param $TotalRooms
     * @param $PaxRoomCount
     * @return array
     */
    static function childrenToRoom($TotalChildren, $TotalRooms, $PaxRoomCount)
    {

        $Array = [];

        $avg = floor($TotalChildren / $TotalRooms);
        $Extra = $TotalChildren % $TotalRooms;

        foreach ($PaxRoomCount as $RoomType => $RoomCount) {

            for ($c = 1; $c <= $RoomCount; $c++) {

                $Array[$RoomType][$c] = $avg + $Extra;
                $Extra = 0;

            }
        }

        return $Array;

    }

    /**
     * @param $RateArray
     * @return array
     */
    function rateArrayExtract($RateArray)
    {

        $List = [];

        foreach ($RateArray as $item) {

            $RateKey = explode("|", $item);

            $List['meal_type'][$RateKey[7]] = true;
            $List['category'][$RateKey[5]] = true;

        }
        return $List;

    }


    /**
     * @param Hotels $result
     * @param $Quotation
     */
    function cacheRates(Hotels $result, $Quotation)
    {


        ini_set('max_execution_time', 0);
        $CheckIn = Carbon::parse($result->checkIn);
        $CheckOut = Carbon::parse($result->checkOut);


        foreach ($result->hotels as $HotelItem) {


            $Code = $HotelItem['code'];
            $Name = $HotelItem['name'];
            $categoryCode = $HotelItem['categoryCode'];
            $categoryName = $HotelItem['categoryName'];
            $destinationCode = $HotelItem['destinationCode'];

            $latitude = $HotelItem['latitude'];
            $longitude = $HotelItem['longitude'];


            if (!Place::where('A2', $destinationCode)->first())
                continue;

            $PlaceID = Place::where('A2', $destinationCode)->first()->ID;


            //add class if not exist
            if (!$HotelClassRow = HotelClass::where('category_code', '=', $categoryCode)->first()) {

                $HotelClass = new HotelClass();
                $HotelClass->class = $categoryName;
                $HotelClass->category_code = $categoryCode;
                $HotelClass->star = intval($categoryCode);
                $HotelClass->save();
                $ClassID = $HotelClass->id;

            } else {
                $ClassID = $HotelClassRow->ID;
            }


            //add hotel if not exist
            if (!$HotelRow = HotelModel::where('provider_id', '=', $Code)->where('provider', '=', 2)->first()) {


                $Hotel = new HotelModel();
                $Hotel->city = $PlaceID;
                $Hotel->class = $ClassID;
                $Hotel->name = $Name;
                $Hotel->description = $Name;
                $Hotel->latitude = $latitude;
                $Hotel->longitude = $longitude;
                $Hotel->provider = 2;
                $Hotel->provider_id = $Code;
                $Hotel->save();

                $HotelID = $Hotel->id;

            } else {
                $HotelID = $HotelRow->ID;
            }

            #first remover old rates
            Rates::where('tour_session', $Quotation['tour_session'])->where('hotel', $HotelID)->forceDelete();
            #remove allotment
            Allotment::where('hotel', $HotelID)->forceDelete();


            foreach ($HotelItem['rooms'] as $RoomItem) {//room list


                $RoomCode = $RoomItem['code'];
                $RoomName = $RoomItem['name'];
                $RoomCodeArray = explode('.', $RoomCode);


                //add catogory if not exist
                if (!$RoomCategoryRow = RoomCategory::where('category_code', '=', $RoomCode)->first()) {

                    $HotelCategory = new RoomCategory();
                    $HotelCategory->name = $RoomName;
                    $HotelCategory->category_code = $RoomCode;
                    $HotelCategory->save();
                    $CategoryID = $HotelCategory->id;

                } else {
                    $CategoryID = $RoomCategoryRow->ID;
                }

                #set saved room types meal types rate item
                $SavedMeals = [];


                foreach ($RoomItem['rates'] as $RateItem) {//Rate List

                    #check meal type already saved
                    if (in_array($RateItem['boardCode'], $SavedMeals))
                        continue;

                    if (!RoomType::where("ID", $RateItem['adults'])->first())//if room type not in our system then don't fucking add it
                        continue;

                    if (!MealPlan::where('plan', $RateItem['boardCode'])->first())
                        continue;


                    $RoomType = RoomType::where("ID", $RateItem['adults'])->first()->ID;//if its 3 adult get it as tripple like that

                    #set Rats
                    $CloneCheckIn = clone $CheckIn;
                    foreach ($RateItem['dailyRates'] as $DailyRateItem) {


                        //add rate
                        $Rate = new Rates();

                        $Rate->start_year = $CloneCheckIn->year;
                        $Rate->start_month = $CloneCheckIn->month;
                        $Rate->start_day = $CloneCheckIn->day;

                        $CloneCheckIn->addDay();

                        $Rate->end_year = $CloneCheckIn->year;
                        $Rate->end_month = $CloneCheckIn->month;
                        $Rate->end_day = $CloneCheckIn->day;

                        $Rate->rate_key = $RateItem['rateKey'];
                        $Rate->meal = MealPlan::where('plan', $RateItem['boardCode'])->first()->ID;
                        $Rate->hotel = $HotelID;
                        $Rate->rate = $DailyRateItem['dailyNet'];
                        $Rate->room_type = $RoomType;
                        $Rate->room_category = $CategoryID;
                        $Rate->market = 1;
                        $Rate->child_count = $RateItem['children'];

                        $Rate->tour_session = $Quotation['tour_session'];

                        $Rate->save();

                    }

                    #set Allotment
                    $CheckInAllt = Carbon::parse($result->checkIn);
                    $CheckOutAllt = Carbon::parse($result->checkOut);

                    $Allotment = new Allotment();


                    $Allotment->start_year = $CheckInAllt->year;
                    $Allotment->start_month = $CheckInAllt->month;
                    $Allotment->start_day = $CheckInAllt->day;
                    $Allotment->end_year = $CheckOutAllt->year;
                    $Allotment->end_month = $CheckOutAllt->month;
                    $Allotment->end_day = $CheckOutAllt->day;
                    $Allotment->hotel = $HotelID;
                    $Allotment->release_period = 0;
                    $Allotment->room = $RateItem['allotment'];


                    $Allotment->save();

                    $SavedMeals[] = $RateItem['boardCode'];

                }


            }


        }


    }

    /**
     * @param $HotelItem
     * @param Collection $RateArray
     * @return array
     */
    static function getHotelCost($HotelItem, Collection $RateArray, $Quotation=null)
    {
        if(isset($Quotation)) {
            $Currencies = Quotation::getFromToCurrency($Quotation);
        }

        $QuotationHotel = new QuotationHotel();

        $check_in = $HotelItem['check_in'];
        $check_out = $HotelItem['check_out'];


        foreach ($HotelItem['room_type'] as $RoomTypeID => $RoomCount) {
            $room_type[$RoomTypeID] = intval($RoomCount);
        }

        $Bookdates = $QuotationHotel->getBookdatesNightCheckIn($check_in, $check_out);


        $DateRates = [];
        foreach ($Bookdates as $Day => $Bookdate) {//loop all the book dates

            $DateRates['child']['cwb'][$Day] = ["rate" => 0];
            $DateRates['child']['cnb'][$Day] = ["rate" => 0];

            foreach (($room_type ?? []) as $RoomTypeID => $RoomCount) {//loop all the rooms

                $RateItem = $RateArray->where('adult', $RoomTypeID)->first();

                if ($RoomCount) {//only if room have
                    //adult
                    $DateRates['adult'][$Day][$RoomTypeID] = $RateItem;
                    $DateRates['adult'][$Day][$RoomTypeID]["rate"] = currency($RateItem['rate']['adult'], $Currencies['from']??null, $Currencies['to']??null, false);

                    //CWB
                    $DateRates['child']['cwb'][$Day] = $RateItem;
                    $DateRates['child']['cwb'][$Day]["rate"] = currency($RateItem['rate']['cwb'], $Currencies['from']??null, $Currencies['to']??null, false);
                    //CNB
                    $DateRates['child']['cnb'][$Day] = $RateItem;
                    $DateRates['child']['cnb'][$Day] ["rate"] = currency($RateItem['rate']['cnb'], $Currencies['from']??null, $Currencies['to']??null, false);
                }

            }

         break;
        }
        return $DateRates;
    }

    /**
     * @param $HotelItem
     * @param bool $json
     * @param $Pax
     * @param int $limit
     * @return string
     */
     static function extractRespond($HotelItem, $json = false, $Pax, $QuotationArray,$limit = 10)
    {

        $data['name'] = $HotelItem['name'];
        $data['id'] = $HotelItem['code'];
        $room_category = collect($HotelItem['rooms'])->pluck('name', 'code')->toArray();
        $data['room_category'] = [];
        $data['meal_types'] = [];
        $data['combine'] = [];

        $baseCurrency = $QuotationArray['base_currency'];
        $currency = \App\Currency::find($baseCurrency)->code;


        foreach ($HotelItem['rooms'] as $value) {

            $LocalCategory = HotelBed::getLocalCategory($value['code'], $room_category);
            $data['room_category'][$LocalCategory->ID] = $LocalCategory->name;
            $data['combine'][$LocalCategory->ID] = [];

            if (count($data['combine'][$LocalCategory->ID]) === $limit)
                break;

            foreach ($value['rates'] as $RoomItem) {

                $MealType = Meal::where('plan', $RoomItem['boardCode'])->first();

                if (!$MealType)
                    continue;

                $data['meal_types'][$MealType->ID] = $RoomItem['boardCode'];


                $SubCollection = [
                    'meal' => $MealType->ID,
                    'adult' => $RoomItem['adults'],
                    'child' => $RoomItem['children'],
                    'rooms' => $RoomItem['rooms'],
                    'category' => $LocalCategory->ID,
                ];
                $isAvailable = isCollectionAvailable($data['combine'][$LocalCategory->ID][$MealType->ID] ?? [], $SubCollection);
                if (!$isAvailable) {

                    $SubCollection['rateKey'] = $RoomItem['rateKey'];

                    $SubCollection['rateClass'] = $RoomItem['rateClass'];

                    $rate = $RoomItem['net'];
                    if($currency !== "USD"){
                        $rate = currency($rate,"USD", $currency, false);
                    }
                    $SubCollection['net'] = $rate ;
                    $SubCollection['rate'] = HotelBed::getRateExtract($SubCollection, $Pax);
                    $data['combine'][$LocalCategory->ID][$MealType->ID][] = $SubCollection;
                }

            }
        }

        if ($json)
            return json_encode($data);
        else
            return $data;
    }

    /**
     * @param $RateItem
     * @param $Pax
     * @return mixed
     */
    static function getRateExtract($RateItem, $Pax)
    {
        $Cost['cost'] = $RateItem['net'];


        $AdultPart = $Pax['adult'];
        $cwbPart = $Pax['cwb'] / 2;
        $cnbPart = $Pax['cnb'] / 4;
        $TotalPart = $AdultPart + $cwbPart + $cnbPart;

        $Cost['adult'] = $RateItem['net'] * $AdultPart / $TotalPart;
        $Cost['cwb'] = $RateItem['net'] * $cwbPart / $TotalPart;
        $Cost['cnb'] = $RateItem['net'] * $cwbPart / $TotalPart;

        return $Cost;
    }

    /**
     * @param $BookDates
     * @param $CurrentKey
     * @param $SelectedKeys
     * @param $Index
     * @return array
     */
    function getHotelSettingsQuery($BookDates, $CurrentKey, $SelectedKeys, $Index)
    {

        $QueryArray = [];
        foreach ($BookDates as $HotelIndex => $CurrentDate) {

            $QueryCurrent = false;
            if ($CurrentDate['provider'] == 'hotelbeds') {
                if ($Index == $HotelIndex)
                    $QueryCurrent = (string)$SelectedKeys;
                else
                    $QueryCurrent = (string)$CurrentKey[$HotelIndex];
            }
            $QueryArray[$HotelIndex] = $QueryCurrent;
        }

        return ($QueryArray);

    }

    /**
     * @param $SelectedRate
     * @return array
     */
    static function getRoomTypesFromRate($SelectedRate)
    {
        $RoomTypes = [];

        foreach (($SelectedRate ?? []) as $RateItem) {
            $RoomTypes[$RateItem['adult']] = $RateItem['rooms'];
        }

        return $RoomTypes;

    }

    /**
     * @param $CategoryID
     * @param $room_category_list
     * @return Model|null|object|static
     */
    static function getLocalCategory($CategoryID, $room_category_list)
    {

        $Category = RoomCategory::where('category_code', $CategoryID)->first();

        if ($Category) {
            return $Category;
        } else {
            $RoomCategory = new RoomCategory();
            $RoomCategory->name = $room_category_list[$CategoryID];
            $RoomCategory->category_code = $CategoryID;
            $RoomCategory->save();
        }

        return RoomCategory::where('category_code', $CategoryID)->first();
    }

    function getRateClass($rate)
    {
        $rateClass = "";
        switch ($rate) {
            case "NOR" :
                $rateClass = "Normal";
                break;
            case "NRF" :
                $rateClass = "Non-refundable";
                break;
            case "SPE" :
                $rateClass = "Special";
                break;
            case  "OFE":
                $rateClass = "Offer";
                break;
            case "PAQ" :
                $rateClass = "Package";
                break;
            case "NRP" :
                $rateClass = "Non-refundable package";
                break;
            default:
                $rateClass = "Normal";
        }
        return $rateClass;
    }


}
