<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\TransportRate
 *
 * @property int $id
 * @property string $start_date
 * @property string $end_date
 * @property int $vehicle_type_type
 * @property float $adult
 * @property float $child
 * @property int $hotel
 * @property int $city
 * @property int $distance
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereAdult($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereChild($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereVehicleTypeType($value)
 * @mixin \Eloquent
 * @property int $vehicle_type
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\TransportRate whereVehicleType($value)
 */
class TransportRate extends Model
{
    protected $table = "apple_hotel_transport_rates";
}
