<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\OutsideRestaurant
 *
 * @property int $ID
 * @property string $type
 * @property float|null $cost
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\OutsideRestaurant whereUploadId($value)
 * @mixin \Eloquent
 */
class OutsideRestaurant extends Model
{
    protected $table = 'apple_hotel_outside_rest';

    protected $guarded = 'ID';
}
