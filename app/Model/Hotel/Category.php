<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\Category
 *
 * @property int $ID
 * @property string|null $name
 * @property string|null $category_code
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereCategoryCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Category whereUploadId($value)
 * @mixin \Eloquent
 */
class Category extends Model
{
    protected $table = 'apple_hotel_room_category';
	
	
}
