<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\HotelBedImage
 *
 * @property int $ID
 * @property int $hotel
 * @property string|null $path
 * @property string|null $room_code
 * @property string|null $room_type
 * @property string|null $timestamp
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereRoomCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereRoomType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereTimestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\HotelBedImage whereUploadId($value)
 * @mixin \Eloquent
 */
class HotelBedImage extends Model
{
    protected $table = 'apple_hotel_hotelbeds_images';

}
