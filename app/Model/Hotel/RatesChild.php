<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\SoftDeletes;
/**
 * App\Model\Hotel\RatesChild
 *
 * @property int $ID
 * @property int $start_year
 * @property int $start_month
 * @property int $start_day
 * @property int $end_year
 * @property int $end_month
 * @property int $end_day
 * @property int $age_from
 * @property int $age_to
 * @property int $meal
 * @property int $hotel
 * @property float $rate
 * @property int $room_type
 * @property int $room_category
 * @property int $market
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property int|null $upload_id
 * @property string $special
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\RatesChild onlyTrashed()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereAgeFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereAgeTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereEndDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereEndMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereEndYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereMarket($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereRoomCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereRoomType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereSpecial($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereStartDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereStartMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereStartYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\RatesChild whereUploadId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\RatesChild withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\Model\Hotel\RatesChild withoutTrashed()
 * @mixin \Eloquent
 */
class RatesChild extends Model
{

    protected $table = 'apple_hotel_room_rates_child';
    protected $guarded = 'ID';
    use SoftDeletes;
    protected $dates = ['deleted_at'];

//    function getAllRates(){
//
//        return DB::table('apple_hotel_room_rates_child')->chunk(100,function ($r){
//
//            foreach ($r as $t){
//
//                sd($t);
//            }
//
//        });
//
//        return DB::table('apple_hotel_room_rates_child')
//            ->join('apple_hotels','apple_hotels.ID','=','apple_hotel_room_rates_child.hotel')
//            ->join('apple_hotel_room_type','apple_hotel_room_type.ID','=','apple_hotel_room_rates_child.room_type')
//            ->join('apple_hotel_room_category','apple_hotel_room_category.ID','=','apple_hotel_room_rates_child.room_category')
//            ->join('apple_market','apple_market.ID','=','apple_hotel_room_rates_child.market')
//            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"),"<=",'')
//            ->select('apple_market.market as rate_market','apple_hotel_room_category.name as rate_room_category','apple_hotel_room_type.type as rate_room_type','apple_hotels.name as rate_hotel','apple_hotel_room_rates_child.*')
//            ->get();
//
////        sd($rates);


//    }

}
