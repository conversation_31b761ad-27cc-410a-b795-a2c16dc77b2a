<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\Supplement
 *
 * @property int $ID
 * @property int $start_year
 * @property int $start_month
 * @property int $start_day
 * @property int $end_year
 * @property int $end_month
 * @property int $end_day
 * @property string $name
 * @property int $type
 * @property int $hotel
 * @property int|null $meal_type
 * @property float $adult_rate
 * @property int $child_rate
 * @property int $additional
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereAdditional($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereAdultRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereChildRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereEndDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereEndMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereEndYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereMealType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereStartDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereStartMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereStartYear($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Supplement whereUploadId($value)
 * @mixin \Eloquent
 */
class Supplement extends Model
{
    protected $table = 'apple_hotel_supplement';
    protected $guarded = 'ID';


}
