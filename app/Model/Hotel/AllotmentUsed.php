<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use DB;
/**
 * App\Model\Hotel\AllotmentUsed
 *
 * @property int $ID
 * @property int $quatation
 * @property int $use_room
 * @property int $hotel
 * @property int $year
 * @property int $month
 * @property int $day
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $created_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereDay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereHotel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereQuatation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereUseRoom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\AllotmentUsed whereYear($value)
 * @mixin \Eloquent
 */
class AllotmentUsed extends Model
{
    protected $table = 'apple_hotel_room_allotment_used';


    /**
     * @param $Hotel
     * @param bool $BookDate
     * @return \Illuminate\Support\Collection
     */
    function getAllotmentsUsed($Hotel, $BookDate = false){

        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Allotment = AllotmentUsed::where('hotel',$Hotel);




        return $Allotment->get();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Quotation()
    {
        return $this->hasOne('App\Model\QuotationManage\Quotation', 'ID', 'quatation');
    }
}
