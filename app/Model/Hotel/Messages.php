<?php

namespace App\Model\Hotel;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Hotel\Room
 *
 * @property int $ID
 * @property string|null $type
 * @property string $short_name
 * @property string $other_name
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereOtherName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereShortName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Hotel\Room whereUploadId($value)
 * @mixin \Eloquent
 */
class Messages extends Model
{
    	protected $table = 'apple_hotel_messages';

}
