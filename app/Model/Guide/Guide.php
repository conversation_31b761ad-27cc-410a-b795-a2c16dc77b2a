<?php

namespace App\Model\Guide;

use Illuminate\Database\Eloquent\Model;
use DB;

/**
 * App\Model\Guide\Guide
 *
 * @mixin \Eloquent
 */
class Guide extends Model
{


    public const ICONS = '454';

    protected $guide_type_table = 'apple_guide_types';

    /**
     * @param bool $array
     * @return \Illuminate\Support\Collection
     */
    public function getGuideTypes($array = true)
    {

        $Types = DB::table($this->guide_type_table);

        if ($array)
            return $Types->pluck('type', 'ID');

        return $Types->get();

    }

    function test():void{

    }


}