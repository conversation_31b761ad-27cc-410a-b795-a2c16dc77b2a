<?php

namespace App\Model\Meal;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Meal\MealTime
 *
 * @property int $ID
 * @property string $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealTime whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealTime whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealTime whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealTime whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealTime whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealTime whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealTime whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealTime whereUploadId($value)
 * @mixin \Eloquent
 */
class MealTime extends Model
{
    protected $table = 'apple_meal_time';

}
