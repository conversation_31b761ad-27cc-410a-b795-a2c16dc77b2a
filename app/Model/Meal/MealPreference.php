<?php

namespace App\Model\Meal;

use App\Model\Place\Place;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MealPreference
 * @package App\Model\Meal
 */
class MealPreference extends Model
{
    protected $table = "apple_meal_type";
    protected $primaryKey = 'id';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function country()
    {
        return $this->hasOne(Place::class, 'id', 'country');
    }
}
