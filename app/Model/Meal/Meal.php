<?php

namespace App\Model\Meal;

use App\Model\Meal\MealType;
use App\Model\Restaurant\Restaurant;
use DB;
use Illuminate\Database\Eloquent\Model;
use Session;

use App\Model\Meal\Meal as MealModel;
use App\Model\Place\Place;
use App\Model\Restaurant\RestaurantMealRate;
use App\Model\QuotationManage\QuotationMeal;

/**
 * App\Model\Meal\Meal
 *
 * @property int $ID
 * @property string $meal
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\Meal whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\Meal whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\Meal whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\Meal whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\Meal whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\Meal whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\Meal whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\Meal whereUploadId($value)
 * @mixin \Eloquent
 */
class Meal extends Model
{
    /**
     * @var string
     */
    protected $table = 'apple_meal';


    /**
     * @param $ID
     * @return mixed
     */
    static function getDefaultOutsideResturentRate($ID)
    {

        return DB::table('apple_meal_price_default')
            ->where('ID', '=', $ID)
            ->get()[0];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    static function getMealRate()
    {
        return DB::table('apple_meal_price_default')
            ->get();
    }

    /**
     * @param $QuotationArray
     * @return array|bool|mixed
     */
    static function getMeal($QuotationArray)
    {

        if (isset($QuotationArray["rate"]["meal"])) {
            $MealRates['adult'] = $QuotationArray["rate"]["meal"];
            $MealRates['child'] = $QuotationArray["rate"]["meal_child"];
        } else {
            // dd($QuotationArray);
            $MealRates = [];
            if (empty($QuotationArray['meal'])) return false;

            foreach ($QuotationArray['meal'] as $Index => $MealList) {

                foreach ($MealList as $MealTime => $MealItem) {

                    $MealRates['adult'][$Index][$MealTime] = $MealItem;//set meal setting
                    $MealRates['adult'][$Index][$MealTime]['meal_type'] = isset($MealRates['adult'][$Index][$MealTime]['meal_type']) ? $MealRates['adult'][$Index][$MealTime]['meal_type'] : 0;


                    if ($MealItem['meal_where'] == 4)//if meal NI not include

                        $MealRates['adult'][$Index][$MealTime] = 0;

                    elseif ($MealItem['meal_where'] == 2) {
                        if (empty($MealItem['restaurant']) && !empty($MealItem['meal_preference']) && !is_null($MealItem['meal_preference'])) {
                            ///////////////////////////////////////
                            /// meal not specified custom price
                            /*if(isset($MealRates[$Index][$MealTime]['rate'])) {
                                $MealRates[$Index][$MealTime] = $MealRates[$Index][$MealTime]['rate'];
                            } else {
                                $MealRates[$Index][$MealTime] = MealType::find($MealItem['meal_preference'])->rate;
                            }*/
                            //////////////////////////////////////

                            $MealRates['adult'][$Index][$MealTime] = MealType::find($MealItem['meal_preference'])->rate;
                            $MealRates['child'][$Index][$MealTime] = MealType::find($MealItem['meal_preference'])->child_rate;
                        } elseif (!empty($MealItem['meal_preference']) && !is_null($MealItem['meal_preference']) && $MealTime) {
                            if(isset($QuotationArray['ID'])) {
                                $Rate = 0;
                                $RateItem = QuotationMeal::where('reference_id', $QuotationArray['ID'])->where('day', $Index)->where('food_time', $MealTime)->first();

                                if ($RateItem = QuotationMeal::where('reference_id', $QuotationArray['ID'])->where('day', $Index)->where('food_time', $MealTime)->first()) {
                                    if ($Rate = $RateItem->rate->first())
                                        $Rate = $Rate->rate;
                                }

                                $MealRates['adult'][$Index][$MealTime] = $Rate;
                                $MealRates['child'][$Index][$MealTime] = $Rate;
                            } else {
                                $MealRates['adult'][$Index][$MealTime] = MealType::find($MealItem['meal_preference'])->rate;
                                $MealRates['child'][$Index][$MealTime] = MealType::find($MealItem['meal_preference'])->child_rate;
                            }
                        } else { // change by priyantha.. This else code ned to remove totally.. root course : $MealItem['meal_preference'] come as null.
                            $Rate = 0;
                            $RateItem = QuotationMeal::where('reference_id', $QuotationArray['ID'])->where('day', $Index)->where('food_time', $MealTime)->first();

                            if ($RateItem = QuotationMeal::where('reference_id', $QuotationArray['ID'])->where('day', $Index)->where('food_time', $MealTime)->first()) {
                                if ($Rate = $RateItem->rate->first())
                                    $Rate = $Rate->rate;
                            }

                            $MealRates['adult'][$Index][$MealTime] = $Rate;
                            $MealRates['child'][$Index][$MealTime] = $Rate;
                        }

                    } elseif($MealItem['meal_where'] == 3){

                        $MealRates['adult'][$Index][$MealTime] = 12;
                        $MealRates['child'][$Index][$MealTime] = 6;
                    }
                    else//hotel
                        $MealRates['adult'][$Index][$MealTime] = 0;
                        $MealRates['child'][$Index][$MealTime] = 0;
                }
            }
        }

        return $MealRates;
    }


    /**
     * @param $DaysDetail
     * @param bool $MealSession
     * @return array
     */
    function getMealSettings($DaysDetail, $MealSession = false)
    {

        $LastPlace = false;
        $LastDay = count($DaysDetail);
        $Meal = [];

        foreach ($DaysDetail as $Day => $DayItem) {

            if (!empty($MealSession[$Day])) {
                $Meal[$Day] = $MealSession[$Day];
                continue;
            }

            $tempIndex = $Day - 1;
            $Index = $DayItem['index'];
            $CruiseMeal = false;

            if (Session::has("quotation.accommodation." . $DayItem['index']) && (Session::get("quotation.accommodation." . $DayItem['index']) == 1 || Session::get("quotation.accommodation." . $DayItem['index']) == 2)) {
                $MealType = Session::get("quotation.hotel." . $DayItem['index'] . ".meal_type");
            } else if (Session::has("quotation.accommodation." . $DayItem['index']) && Session::get("quotation.accommodation." . $DayItem['index']) == 4) {
                $MealType = Session::get("quotation.cruise." . $DayItem['index'] . ".meal_type");
                $CruiseMeal = true;
            } else {

            }
            // $MealType = empty($MealType) ? 1 : $MealType;

            if (!$LastPlace) {//first day set NA
                $Meal[$Day][1]['meal_where'] = 4;
                $Meal[$Day][1]['restaurant'] = 0;
                $Meal[$Day][1]['meal_preference'] = 0;

            }
            //own arrangement

            if (empty($MealType) && !$LastPlace) {//first day set NA
                $Meal[$Day][2]['meal_where'] = 2;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 2;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;

            } elseif (empty($MealType) && $LastPlace) {//set outside
                $Meal[$Day][1]['meal_where'] = 2;
                $Meal[$Day][1]['restaurant'] = 0;
                $Meal[$Day][1]['meal_preference'] = 0;

                $Meal[$Day][2]['meal_where'] = 2;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 2;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }
            //RO  first day
            if ($MealType == 4 && !$LastPlace) {
                $Meal[$Day][2]['meal_where'] = 4;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 4;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }
            //BB  first day
            if ($MealType == 1 && !$LastPlace) {
                $Meal[$Day][2]['meal_where'] = 4;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 4;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }
            //HB  first day
            if ($MealType == 2 && !$LastPlace) {
                $Meal[$Day][2]['meal_where'] = 4;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 1;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            } //FB first day
            elseif ($MealType == 3 && !$LastPlace) {
                $Meal[$Day][2]['meal_where'] = 4;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 1;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            } elseif ($MealType == 5 && !$LastPlace) {
                $Meal[$Day][2]['meal_where'] = 4;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 1;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }

            //RO  other days
            if ($MealType == 4 && $LastPlace) {
                $Meal[$Day][1]['meal_where'] = 4;
                $Meal[$Day][1]['restaurant'] = 0;
                $Meal[$Day][1]['meal_preference'] = 0;

                $Meal[$Day][2]['meal_where'] = 4;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 4;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }
            //BB  other days
            if ($MealType == 1 && $LastPlace) {
                $Meal[$Day][1]['meal_where'] = 1;
                $Meal[$Day][1]['restaurant'] = 0;
                $Meal[$Day][1]['meal_preference'] = 0;

                $Meal[$Day][2]['meal_where'] = 4;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 4;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            } //HB  other days
            elseif ($MealType == 2 && $LastPlace) {
                $Meal[$Day][1]['meal_where'] = 1;
                $Meal[$Day][1]['restaurant'] = 0;
                $Meal[$Day][1]['meal_preference'] = 0;

                $Meal[$Day][2]['meal_where'] = 4;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 1;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            } //FB  other days
            elseif ($MealType == 3 && $LastPlace) {
                $Meal[$Day][1]['meal_where'] = 1;
                $Meal[$Day][1]['restaurant'] = 0;
                $Meal[$Day][1]['meal_preference'] = 0;

                $Meal[$Day][2]['meal_where'] = 1;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 1;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            } elseif ($MealType == 5 && $LastPlace) {
                $Meal[$Day][1]['meal_where'] = 1;
                $Meal[$Day][1]['restaurant'] = 0;
                $Meal[$Day][1]['meal_preference'] = 0;

                $Meal[$Day][2]['meal_where'] = 1;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 1;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }

            if ($CruiseMeal) {
                $Meal[$Day][1]['meal_where'] = 1;
                $Meal[$Day][1]['restaurant'] = 0;
                $Meal[$Day][1]['meal_preference'] = 0;

                $Meal[$Day][2]['meal_where'] = 1;
                $Meal[$Day][2]['restaurant'] = 0;
                $Meal[$Day][2]['meal_preference'] = 0;

                $Meal[$Day][3]['meal_where'] = 1;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }

            //HB  last days
            if ($MealType == 2 && $LastPlace && $LastDay == $Day) {
                $Meal[$Day][3]['meal_where'] = 4;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;

            }
            //FB  last days
            if ($MealType == 3 && $LastPlace && $LastDay == $Day) {

                $Meal[$Day][3]['meal_where'] = 4;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }
            if ($MealType == 5 && $LastPlace && $LastDay == $Day) {

                $Meal[$Day][3]['meal_where'] = 4;
                $Meal[$Day][3]['restaurant'] = 0;
                $Meal[$Day][3]['meal_preference'] = 0;
            }
            $LastPlace = $DayItem['place'];


        }
        // dd($Meal);
        return $Meal;
    }
}
