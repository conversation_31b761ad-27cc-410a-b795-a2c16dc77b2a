<?php

namespace App\Model\Meal;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Meal\MealType
 *
 * @property int $ID
 * @property string $meal
 * @property float $rate
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereMeal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealType whereUploadId($value)
 * @mixin \Eloquent
 */
class MealType extends Model
{
    protected $table = 'apple_meal_type';
}
