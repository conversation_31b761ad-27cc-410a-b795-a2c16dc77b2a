<?php

namespace App\Model\Meal;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Meal\MealPlan
 *
 * @property int $ID
 * @property string|null $plan
 * @property string $long_name
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan whereLongName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan wherePlan($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Meal\MealPlan whereUploadId($value)
 * @mixin \Eloquent
 */
class MealPlan extends Model
{
    protected $table = 'apple_meal_plan';
}
