<?php

namespace App\Model\Cruise;

use Illuminate\Database\Eloquent\Model;

class CruiseCabinRateChild extends Model
{
    protected $table = 'apple_cruise_cabin_rates_child';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Package()
    {
        return $this->belongsTo('App\Model\Cruise\CruisePackage', 'ID', 'package');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function CabinType()
    {
        return $this->belongsTo('App\Model\Cruise\CruiseCabinType', 'ID', 'cabin_type');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function CabinOccupancyType()
    {
        return $this->belongsTo('App\Model\Cruise\CruiseCabinOccupancyType', 'ID', 'cabin_occupancy_type');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function CabinMeal()
    {
        return $this->belongsTo('App\Model\Cruise\CruiseMeal', 'ID', 'meal');
    }
}
