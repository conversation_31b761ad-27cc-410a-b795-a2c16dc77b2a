<?php

namespace App\Model\Cruise;

use Illuminate\Database\Eloquent\Model;

class CruiseCabinType extends Model
{
    protected $table = 'apple_cruise_cabin_type';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\Cruise\CruiseCabinRate', 'cabin_type', 'ID' );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function RateChild()
    {
        return $this->hasMany('App\Model\Cruise\CruiseCabinRateChild', 'cabin_type', 'ID' );
    }

}
