<?php

namespace App\Model\Cruise;

use Illuminate\Database\Eloquent\Model;

class CruiseMeal extends Model
{
    protected $table = 'apple_cruise_meal';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\Cruise\CruiseCabinRate', 'meal', 'ID' );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function RateChild()
    {
        return $this->hasMany('App\Model\Cruise\CruiseCabinRateChild', 'meal', 'ID' );
    }

    /**
     * @param $package
     * @return array
     */
    function getMealPackage($package) {
        $packageObj = CruisePackage::find($package);
        $Dine=['breakfast'=>0, 'lunch'=>0, 'dinner'=>0];

        for ($i=0; $packageObj['nights']>=$i; $i++) {
            if($i==1) {

                if($packageObj['start_time'] <= "08:00:00") {
                    $Dine['breakfast']+=1;
                }
                if($packageObj['start_time'] <= "13:00:00") {
                    $Dine['lunch']+=1;
                }
                if($packageObj['start_time'] <= "22:00:00") {
                    $Dine['dinner']+=1;
                }
            } else if($i==$packageObj['nights']) {
                if($packageObj['end_time'] >= "08:00:00") {
                    $Dine['breakfast']+=1;
                }
                if($packageObj['end_time'] >= "13:00:00") {
                    $Dine['lunch']+=1;
                }
                if($packageObj['end_time'] >= "22:00:00") {
                    $Dine['dinner']+=1;
                }
            } else {
                $Dine['breakfast']+=1;
                $Dine['lunch']+=1;
                $Dine['dinner']+=1;
            }
        }

        return $Dine;
    }

    /**
     * @param $package
     * @return array
     */
    function shortShowMealPackage($package) {

        $Dine = $this->getMealPackage($package);
        $hortMeal = $Dine['breakfast'] . "-B, " . $Dine['lunch'] . "-L, " . $Dine['dinner'] . "-D";

        return $hortMeal;
    }
}
