<?php

namespace App\Model\Cruise;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CruisePackage extends Model
{
    use SoftDeletes;

    protected $primaryKey = "ID";

    protected $table = 'apple_cruise_packages';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Cruise()
    {
        return $this->belongsTo('App\Model\Cruise\Cruise', 'ID', 'cruise');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\Cruise\CruiseCabinRate', 'package', 'ID' );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function RateChild()
    {
        return $this->hasMany('App\Model\Cruise\CruiseCabinRateChild', 'package', 'ID' );
    }
}
