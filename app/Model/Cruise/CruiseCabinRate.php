<?php

namespace App\Model\Cruise;

use App\Model\Hotel\MealRates;
use App\Model\Place\Place;
use App\Model\Quotation\QuotationCruise;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use DB;
use phpDocumentor\Reflection\Types\This;
use Illuminate\Database\Eloquent\SoftDeletes;

class CruiseCabinRate extends Model
{
    var $BookDate;
    protected $table = 'apple_cruise_cabin_rates';
    protected $table_child = 'apple_cruise_cabin_rates_child';

    var $Error;
    var $RateShow = false;
    protected $dates = ['deleted_at'];

    use SoftDeletes;


    /**
     * @param Carbon $Date
     * @param $RateArray
     * @return array
     */
    function hasRateArray(Carbon $Date, $RateArray)
    {

        $Data = [];

        foreach ($RateArray as $RateItem) {

            $StartDate = Carbon::parse($RateItem->start_year . '/' . $RateItem->start_month . '/' . $RateItem->start_day);
            $EndDate = Carbon::parse($RateItem->end_year . '/' . $RateItem->end_month . '/' . $RateItem->end_day);


            if ($Date >= $StartDate && $Date <= $EndDate) {

                return $RateItem;

            }
        }


        return $Data;

    }

    function getAvailabilityCruisePlace($Place, $BookDate) {
        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rates = DB::table(DB::raw($this->table . " rate"));
        $Rates->select(DB::raw("STR_TO_DATE(CONCAT( rate.end_day, '/', rate.end_month, '/', rate.end_year ) ,'%d/%m/%Y' )"));
        $Rates->select("cruise.type");
        $Rates->join(DB::raw('apple_cruise cruise'), 'cruise.ID', '=', 'rate.cruise');//join hotel table for search city
        $Rates->where(DB::raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"),  '<=', $BookDateFormat->format('Y-m-d'));
        $Rates->where(DB::raw("STR_TO_DATE(CONCAT( rate.end_day, '/', rate.end_month, '/', rate.end_year ) ,'%d/%m/%Y' )"), '>=', $BookDateFormat->format('Y-m-d'));
        $Rates->where('cruise.availability', '=', 1); // availability
        $Rates->where('cruise.city', '=', $Place); // availability
        $Rates->whereNull("rate.deleted_at");
        $Rates->groupBy('rate.cruise');

        if($Rates->get()->count() > 0) {
            /*$RaresObj = $Rates->first();
            if(isset($RaresObj->type)) {
                return $RaresObj->type;
            }*/
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param $Place
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param int $Limit
     * @param bool $HotelSettingsModified
     * @param bool $dateRange ---------------------> only show hotels available for a specific range
     * @return bool|\Illuminate\Support\Collection
     * @throws \ReflectionException
     */
    function getLowestCruisePlace($Place, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false,
                                 $Limit = 1, $CruiseSettingsModified = false, $dateRange = false, $package = false)
    {

        //Filters
        $Filters = isset($CruiseSettingsModified['filter']) ? $CruiseSettingsModified['filter'] : false;


        #get room type required
        $SelectedCruiseRateValue = 0;
        if (!$RoomType && $CruiseSettingsModified && !empty($CruiseSettingsModified['cruise'])) {

            $CruiseSettingsModified = arrayMapMulti('getActualDataType', $CruiseSettingsModified);

            if (!empty($CruiseSettingsModified['cabin_type'][2])) {
                $RoomType = 2;
            } elseif (!empty($CruiseSettingsModified['cabin_type'][1])) {
                $RoomType = 1;
            } elseif (!empty($CruiseSettingsModified['cabin_type'][3])) {
                $RoomType = 3;
            } else {
                $RoomType = 2;
            }

            $CruiseID = $CruiseSettingsModified['cruise'];
            $RateSelectedCriuse = $this->getRate($CruiseID, $CruiseSettingsModified['check_in']??false, $RoomType,
                $CruiseSettingsModified['meal_type']??false, $CruiseSettingsModified['room_category']??false);

            if (!empty($RateSelectedHotel[0])) {
                $SelectedCruiseRateValue = $RateSelectedCriuse[0]->rate;
            } else
                $SelectedCruiseRateValue = 0;


        }

        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $PlaceObj = Place::where('ID', $Place)->first();

        //match book date first
        $Rates = DB::table(DB::raw($this->table . " rate"))
            ->select(DB::raw("rate.*, rate.cruise , cruise.city as CityH, cruise.class as ClassH, package.package as PackageH, package.nights as nightH, cruise.type as TypeH,
            
            round(( 111.111 * DEGREES(acos( cos( radians($PlaceObj->latitude) ) * cos( radians( latitude) ) 
										   * cos( radians(longitude) - radians($PlaceObj->longitude)) + sin(radians($PlaceObj->latitude)) 
										   * sin( radians(latitude))))),2) as distance_from_city,
            
            (rate.rate-$SelectedCruiseRateValue) as rate_difference
            
            "));


        $Rates->join(DB::raw('apple_cruise cruise'), 'cruise.ID', '=', 'rate.cruise');//join hotel table for search city

        $Rates->join(DB::raw('apple_cruise_packages package'), 'package.ID', '=', 'rate.package');

        $Rates->where('cruise.availability', '=', 1); // availability

        if ($dateRange) {
            // $Rates->where('cruise.preferred', '=', 1); // preferred is validated from view
        }

        $Rates->whereNull("rate.deleted_at");

        // End Checking for deleted hotels and hotels rate by priyantha.

        #If they can change rate  give them any hotel
        if (!\Auth::user()->can("change_hotel_rates")) {
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat);
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

            #####filters room######	###################
            //if Set room type
            if ($RoomType) {
                $Rates->where(function ($query) use ($RoomType) {
                    $query->where("rate.cabin_occupancy_type", "=", $RoomType);
                    $query->orWhereIn("rate.cabin_occupancy_type", [1, 2, 3]);
                });
            } else {
                $Rates->whereIn("rate.cabin_occupancy_type", [1, 2, 3]);
            }

            //if Set Meal
            if ($Meal)
                $Rates->where("rate.meal", "=", $Meal);

            //if Set RoomCategory
            if ($RoomCategory)
                $Rates->where("rate.cabin_type", "=", $RoomCategory);

            //if Set Market
            if ($Market) {
                $Rates->where(function ($query) use ($Market) {
                    $query->where("market", "=", $Market)
                        ->orWhere("market", "=", 1);
                });
            }

        } else
            $Rates->where("rate.cabin_occupancy_type", '=', 2);//by default select double for lowest rate calculate

        if ($Filters) {
            if(isset($Filters['name'])) {
                $Rates->where("cruise.name", "LIKE", "%{$Filters['name']}%");
            }

            if (isset($Filters['city'])) {
                $Rates->whereIn("cruise.city", $Filters['city']);

            } else {
                $Rates->Having("CityH", "=", $Place);
            }

            if (isset($Filters['class']))
                $Rates->whereIn("cruise.class", $Filters['class']);


        } else {
            $Rates->Having("CityH", "=", $Place);
        }

        //limit
        if ($Limit)
            $Rates->limit($Limit);
        #######end of filters ######################

        $Rates->groupBy('rate.cruise');


        $Rates->orderBy('package.nights', 'asc');
        $Rates->orderBy('rate.rate', 'asc');

        $RateList = $Rates->get();

        if ($RateList->isEmpty())
            return false;
        else
            return $RateList;

    }

    /**
     * @param $Hotel
     * @param bool $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param bool $GroupBy
     * @param bool $OnlyHotelBeds
     * @param int $limit
     * @return array|\Illuminate\Support\Collection
     */
    function getRate($ID, $BookDate = false, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false, $GroupBy = false, $OnlyHotelBeds = false, $limit = 500, $Package = false)
    {
        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rates = DB::table($this->table . ' AS parent')
            ->select(DB::Raw("*
            
            ,CONCAT(start_year,start_month,start_day) as start_date
            ,CONCAT(start_year,start_month,start_day,end_year,end_month,end_day) as start_end_date
            ,(SELECT ID
                FROM apple_cruise_cabin_rates
                WHERE
                     ID!=  parent.ID and
                     cruise = parent.cruise and 
                     meal = parent.meal and 
                     cabin_occupancy_type = parent.cabin_occupancy_type and 
                     market = parent.market and 
                     cabin_type = parent.cabin_type and
                     
                     
                     STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' ) = STR_TO_DATE(CONCAT(  parent.start_day, '/',  parent.start_month, '/',  parent.start_year ) ,'%d/%m/%Y' ) and
                     STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' ) = STR_TO_DATE(CONCAT(  parent.end_day, '/',  parent.end_month, '/',  parent.end_year ) ,'%d/%m/%Y' )
                     ORDER BY `ID` DESC
                     limit 1
                     
                     
                ) AS duplicate"));



        if ($BookDate) {
            $Rates->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
                ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        }


        $Rates->where("cruise", $ID);
        $Rates->whereNull('deleted_at');

        //if Set room type
        if ($RoomCategory) {
            if (is_array($RoomCategory))
                $Rates->whereIn("cabin_type", $RoomCategory);
            else
                $Rates->where("cabin_type", '=', $RoomCategory);
        }

        //if Set Meal
        if ($Meal) {
            if (is_array($Meal))
                $Rates->whereIn("meal", $Meal);
            else
                $Rates->where("meal", '=', $Meal);
        }

        //if Set package
        if ($Package) {
            if (is_array($Package))
                $Rates->whereIn("package", $Package);
            else
                $Rates->where("package", '=', $Package);
        }


        //if Set RoomCategory(Occupancy Type)
        if ($RoomType) {

            if (is_array($RoomType))
                $Rates->whereIn("cabin_occupancy_type", $RoomType);
            elseif ($RoomType == 'first')
                $Rates->where("cabin_occupancy_type", '=', DB::Raw("(select cabin_occupancy_type from {$this->table} where hotel = {$RoomType} limit 1)"));
            else {
                $Rates->where(function ($query) use ($RoomType) {
                    $query->where("cabin_occupancy_type", "=", $RoomType);
                    #->orWhere(DB::Raw(1), '=', 1);
                });


            }
        }

        if ($Market) {
            if (is_array($Market))
                $Rates->whereIn("market", $Market);
            elseif ($this->RateShow) {

                $Rates->where("market", $Market);
            } else {
                $Rates->where(function ($query) use ($Market) {
                    $query->where("market", "=", $Market)
                        ->orWhere("market", '=', 1);
                });
            }
        }

        if ($GroupBy) {
            $Rates->GroupBy($GroupBy);
        }

        $Rates->OrderBy('rate', 'asc');

        $Rates->limit($limit);

        return $Rates->get();

    }


    /**
     * @param $Hotel
     * @param $BookDate
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param bool $AgeFrom
     * @param bool $AgeTo
     * @param bool $RoomType
     * @return \Illuminate\Support\Collection
     */
    function getRateChild($RateRefference, $Age)
    {

        $Rates = DB::table($this->table_child)
            ->select(DB::Raw("*,CONCAT(start_year,start_month,start_day) as start_date,CONCAT(start_year,start_month,start_day,end_year,end_month,end_day) as start_end_date"));
        $Rates->where("adult_reference", $RateRefference);

        //Age
        if ($Age !== false) {
            $Rates->where("age_from", "<=", $Age);
            $Rates->where("age_to", ">=", $Age);
        }

        $Rates->whereNull('deleted_at');

        return $Rates->get();

    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availableCategory($ID)
    {
        return CruiseCabinType::WhereIn('ID', CruiseCabinRate::where('cruise', $ID)->select('cabin_type')->groupBy('cabin_occupancy_type')->pluck('cabin_occupancy_type'))->pluck('name', 'ID');
    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availablePackages($ID)
    {
        return CruisePackage::WhereIn('ID', CruiseCabinRate::where('cruise', $ID)->select('package')->groupBy('package')->pluck('package'))->get(['package', 'ID', 'nights']);
    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availableMarket($ID)
    {
        return Market::WhereIn('ID', CruiseCabinRate::where('cruise', $ID)->select('market')->groupBy('market')->pluck('market'))->pluck('market', 'ID');

    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availableCategoryChild($ID)
    {
        return CruiseCabinType::WhereIn('ID', CruiseCabinRateChild::where('cruise', $ID)->select('cabin_type')->groupBy('cabin_occupancy_type')->pluck('cabin_occupancy_type'))->pluck('name', 'ID');
    }

    /**
     * @param $ID
     * @return \Illuminate\Support\Collection
     */
    function availableMarketChild($ID)
    {
        return Market::WhereIn('ID', CruiseCabinRateChild::where('cruise', $ID)->select('market')->groupBy('market')->pluck('market'))->pluck('market', 'ID');

    }

    /**
     * @param $ID
     * @return bool|Carbon
     */
    function getAdultRateMaxDate($ID)
    {
        try {
            return new Carbon(@CruiseCabinRate::select(DB::Raw("STR_TO_DATE(CONCAT( start_day,'/', start_month, '/', start_year ) ,'%d/%m/%Y') as start_date"))
                ->where('cruise', $ID)
                ->orderBy('start_date', 'desc')->first()->start_date);
        } catch (\Exception $e) {
            return false;
        }

    }

    /**
     * @param $ID
     * @return bool|Carbon
     */
    function getAdultRateMinDate($ID)
    {
        try {
            return new Carbon(@CruiseCabinRate::select(DB::Raw("STR_TO_DATE(CONCAT( start_day,'/', start_month, '/', start_year ) ,'%d/%m/%Y') as start_date"))
                ->where('hotel', $ID)
                ->orderBy('start_date', 'asc')->first()->start_date);
        } catch (\Exception $e) {
            return false;
        }


    }

    /**
     * @param $Hotel
     * @return array
     */
    function getMaxMinBookDates($Cruise)
    {

        $MinBookDate = false;
        $MaxBookDate = false;

        $BookMax = DB::table($this->table)
            ->where("cruise", "=", $Cruise)
            ->orderBy('start_year', 'desc')
            ->orderBy('start_month', 'desc')
            ->orderBy('start_day', 'desc')
            ->limit(1);


        $BookMin = DB::table($this->table)
            ->where("cruise", "=", $Cruise)
            ->orderBy('start_year', 'ASC')
            ->orderBy('start_month', 'ASC')
            ->orderBy('start_day', 'ASC')
            ->limit(1);

        $BookMax = $BookMax->get();
        $BookMin = $BookMin->get();

        if (isset($BookMin[0]))
            $MinBookDate = array('year' => $BookMin[0]->start_year, 'month' => $BookMin[0]->start_month, 'day' => $BookMin[0]->start_day);
        if (isset($BookMax[0]))
            $MaxBookDate = array('year' => $BookMax[0]->start_year, 'month' => $BookMax[0]->start_month, 'day' => $BookMax[0]->start_day);

        return array($MaxBookDate, $MinBookDate);

    }

    /**
     * @param $Hotel
     * @param $BookDateCheckIn
     * @param $BookDateCheckOut
     * @param $Market
     * @return array|\Illuminate\Support\Collection
     */
    function getAvailableCabinCategory($Cruise, $BookDateCheckIn, $BookDateCheckOut, $Market)
    {

        $BookDateCheckInFormat = Carbon::create($BookDateCheckIn['year'], $BookDateCheckIn['month'], $BookDateCheckIn['day'], 0);
        $BookDateCheckOutFormat = Carbon::create($BookDateCheckOut['year'], $BookDateCheckOut['month'], $BookDateCheckOut['day'], 0);

        $AvailableRoomCategory = DB::table(DB::raw($this->table . " rate"))
            ->select("*")
            ->where(DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateCheckInFormat)
            ->where(DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateCheckInFormat)
            ->where("cruise", "=", $Cruise)
            ->whereNull("rate.deleted_at")
            ->join(DB::raw('apple_cruise_cabin_type cabin_type'), 'cabin_type.ID', '=', 'rate.cabin_type')
            ->groupBy("cabin_type")->pluck("cabin_type.name", "rate.cabin_type");

        //$Rates->where("market","=", $Market);


        if ($AvailableRoomCategory)
            return $AvailableRoomCategory;
        return
            array();

    }

    /**
     * @param $RateArray
     * @param $HotelSettings
     * @param string $PaxType
     * @return bool
     */
    function searchInRateArrayCruiseSettings($RateArray, $CruiseSettings, $PaxType = 'adult')
    {

        $QuotationCruise = new QuotationCruise();
        $Bookdates = $QuotationCruise->getBookdatesNightCheckIn($CruiseSettings['check_in'], $CruiseSettings['check_out']);


        foreach ($Bookdates as $Day => $Bookdate) {

            if (empty($HotelSettings['cabin_type']))
                continue;

            foreach ($HotelSettings['cabin_type'] as $RoomType => $RoomCount) {
                if (!$RoomCount) continue;


                if (isset($RateArray[$PaxType][$Day][$RoomType]) && isset($RateArray[$PaxType][$Day][$RoomType]['hotel'])) {

                    $RateItem = $RateArray[$PaxType][$Day][$RoomType];


                    if ($HotelSettings['cruise'] != $RateItem['cruise']) {
                        $this->Error = "Hotel Not Mach!";
                        return false;
                    }

                    if ($HotelSettings['cabin_occupancy_type'] != $RateItem['cabin_occupancy_type']) {
                        $this->Error = "Category Not Mach!";
                        return false;
                    }
                    if ($HotelSettings['meal_type'] != $RateItem['meal']) {
                        $this->Error = "Meal Not Mach!";
                        return false;
                    }


                    //validate date
                    $BookdateUnix = strtotime($Bookdate['year'] . '-' . $Bookdate['month'] . '-' . $Bookdate['day']);
                    $StartDateUnix = strtotime($RateItem['year'] . '-' . $RateItem['month'] . '-' . $RateItem['day']);
                    #$EndDateUnix = strtotime($RateItem['end_year'] . '-' . $RateItem['end_month'] . '-' . $RateItem['end_day']);


//                    if ($BookdateUnix < $StartDateUnix || $BookdateUnix > $EndDateUnix) {
                    if ($BookdateUnix != $StartDateUnix) {

                        $this->Error = "Date Not Match!";
                        return false;
                    }

                }
                else {

                    //sd($RateArray[$PaxType][$Day]);

                    $this->Error = "Room Type Not Mach! {$HotelSettings['cruise']} ";
                    return false;//no room type
                }


            }

        }

        return true;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Package()
    {
        return $this->belongsTo('App\Model\Cruise\CruisePackage', 'ID', 'package');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function CabinType()
    {
        return $this->belongsTo('App\Model\Cruise\CruiseCabinType', 'ID', 'cabin_type');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function CabinOccupancyType()
    {
        return $this->belongsTo('App\Model\Cruise\CruiseCabinOccupancyType', 'ID', 'cabin_occupancy_type');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function CabinMeal()
    {
        return $this->belongsTo('App\Model\Cruise\CruiseMeal', 'ID', 'meal');
    }
	
}
