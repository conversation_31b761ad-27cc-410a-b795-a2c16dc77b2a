<?php

namespace App\Model\Cruise;

use Illuminate\Database\Eloquent\Model;

class CruiseOccupancyType extends Model
{
    protected $table = 'apple_cruise_occupancy_type';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Rate()
    {
        return $this->hasMany('App\Model\Cruise\CruiseCabinRate', 'cabin_occupancy_type', 'ID' );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function RateChild()
    {
        return $this->hasMany('App\Model\Cruise\CruiseCabinRateChild', 'cabin_occupancy_type', 'ID' );
    }
}
