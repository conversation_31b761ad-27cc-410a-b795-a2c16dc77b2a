<?php

namespace App\Model\Cruise;

use App\CountryCurrency;
use App\Currency;
use App\Model\Hotel\HotelClass;
use App\Model\Image\Image;
use App\Model\Place\Place;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Cruise extends Model
{
    use SoftDeletes;

    protected $primaryKey = "ID";

    protected $table = 'apple_cruise';
	
	/**
     * @var array
     */
    protected $appends = ['image'];
	
    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
	public function Package()
	{
		return $this->hasMany('App\Model\Cruise\CruisePackage', 'cruise', 'ID');
	}
	
	public function Place()
	{
		return $this->belongsTo('App\Model\Place\Place', 'ID', 'city');
	}

    public function Rate()
    {
        return $this->belongsTo('App\Model\Cruise\CruiseCabinRate', 'ID', 'cruise');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function City()
    {
        return $this->hasOne(Place::class, 'id', 'city')
            ->select('id','name','popularity','longitude','latitude');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function class()
    {
        return $this->hasOne(HotelClass::class, 'id', 'class')
            ->select('id','class AS hotel_stars','star');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function Contact()
    {
        return $this->hasMany('App\Model\Cruise\CruiseContact', 'cruise', 'ID');
    }

	/**
     * @param $ID
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @return array
     * @throws \ReflectionException
     */
    public static function getStatus($ID, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false, $Package=false)
    {

        $Cruise = new self();

        if ($RateStatus = $Cruise->hasRate($ID, $BookDate, $RoomType, $Meal, $RoomCategory, $Market, $Package)) {

            return [1, 'rate' => $RateStatus];//available

        }
        return [3];//not available
    }

    /**
     * @return array
     */
    public function getImageAttribute()
    {
        return Image::getImageDirect($this->ID, '2x', 'cruise', 1, $this->name);
    }

    /**
     * @param $HotelID
     * @return bool|Model|mixed|null|object|static
     * @throws \Httpful\Exception\ConnectionErrorException
     */
    public static function getCruiseFromAll($CruiseID)
    {

        $data = Cruise::find($CruiseID);

        return $data;
    }


    /**
     * @param $ID
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @param int $limit
     * @return bool|mixed
     * @throws \ReflectionException
     */
    public static function hasRate($ID, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false, $Package = false, $limit = 1)
    {

        $Rate = new CruiseCabinRate();

        $RoomTypeKey = false;
        if (is_array($RoomType))
            $RoomTypeKey = array_keys(filter_array_false(arrayMapMulti("intval", $RoomType)));

        $RateList = $Rate->getRate($ID, $BookDate, $RoomTypeKey, $Meal, $RoomCategory, $Market, false, false, $limit, $Package);

        if (!$RateList) {
            return false;
        } else {
            return objectToArray($RateList->first());
        }

    }

    /**
     * @param $ID
     * @param $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @return bool|mixed
     * @throws \ReflectionException
     */
    public static function hasRateChild($ID, $BookDate, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false)
    {

        $Rate = new CruiseCabinRate();

        $RoomTypeKey = false;
        if (is_array($RoomType))
            $RoomTypeKey = array_keys(filter_array_false(arrayMapMulti("intval", $RoomType)));

        $RateList = $Rate->getRateChild($ID, $BookDate, $Meal, $RoomCategory, $Market, false, false, $RoomTypeKey);


        if (!$RateList) {
            return false;
        } else {
            return objectToArray($RateList->first());
        }

    }


    /**
     * @param $Index
     * @param $Hotel
     * @param bool $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @return bool
     * @throws \ReflectionException
     */
    static function hasRateSession($Index, $Package, $BookDate = false, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false)
    {

        $CruiseRate = Session::get("quotation.rate.cruise." . $Index);
        $RoomTypeKey = array_keys(filter_array_false(arrayMapMulti("intval", $RoomType)));


        if ($CruiseRate) {

            if (!empty($CruiseRate['adult'])) {
                foreach ($RoomTypeKey as $CruiseTypeID) {

                    if (isset($CruiseRate['adult'][0][$CruiseTypeID])) {

                        if ($CruiseRate['adult'][0][$CruiseTypeID]['package'] == $Package &&
                            $CruiseRate['adult'][0][$CruiseTypeID]['year'] == $BookDate['year'] &&
                            $CruiseRate['adult'][0][$CruiseTypeID]['month'] == $BookDate['month'] &&
                            $CruiseRate['adult'][0][$CruiseTypeID]['day'] == $BookDate['day'] &&
                            $CruiseRate['adult'][0][$CruiseTypeID]['room_category'] == $RoomCategory &&
                            $CruiseRate['adult'][0][$CruiseTypeID]['meal'] == $Meal &&
                            $CruiseRate['adult'][0][$CruiseTypeID]['room_type'] == $CruiseTypeID) {
                            return true;
                        } else
                            return false;


                    } else
                        return false;

                }

            } else
                return false;


        } else
            return false;


    }

    /**
     * @param $Index
     * @param $Hotel
     * @param bool $BookDate
     * @param bool $RoomType
     * @param bool $Meal
     * @param bool $RoomCategory
     * @param bool $Market
     * @return bool
     */
    static function hasRateChildSession($Index, $Package, $BookDate = false, $RoomType = false, $Meal = false, $RoomCategory = false, $Market = false)
    {

        $CruiseRate = Session::get("quotation.rate.hotel." . $Index);
        $return = true;

        if ($CruiseRate) {

            if (!empty($CruiseRate['child'])) {

                if (isset($CruiseRate['child']['cwb']) && $CruiseRate['child']['cwb'][0]['hotel'] == $Package &&
                    $CruiseRate['child']['cwb'][0]['year'] == $BookDate['year'] &&
                    $CruiseRate['child']['cwb'][0]['month'] == $BookDate['month'] &&
                    $CruiseRate['child']['cwb'][0]['day'] == $BookDate['day']) {

                    if ($CruiseRate['child']['cwb'][0]['is_modified'] && $CruiseRate['child']['cwb'][0]['modified_rate'] > 0)
                        $return = true;
                    elseif (!$CruiseRate['child']['cwb'][0]['is_modified'] && $CruiseRate['child']['cwb'][0]['rate'] > 0)
                        $return = true;
                    else
                        return false;

                }

                if (isset($CruiseRate['child']['cnb']) && $CruiseRate['child']['cnb'][0]['hotel'] == $Package &&
                    $CruiseRate['child']['cnb'][0]['year'] == $BookDate['year'] &&
                    $CruiseRate['child']['cnb'][0]['month'] == $BookDate['month'] &&
                    $CruiseRate['child']['cnb'][0]['day'] == $BookDate['day']) {

                    if ($CruiseRate['child']['cnb'][0]['is_modified'] && $CruiseRate['child']['cnb'][0]['modified_rate'] > 0)
                        $return = true;
                    elseif (!$CruiseRate['child']['cnb'][0]['is_modified'] && $CruiseRate['child']['cnb'][0]['rate'] > 0)
                        $return = true;
                    else
                        return false;

                }

            } else
                return false;


        }
        else
            return false;


        return $return;

    }

    /**
     * @param $ID
     * @param $BookDate
     * @return array
     */
    function getAvailableSettings($ID, $BookDate)
    {


        $BookDateFormat = Carbon::create($BookDate['year'], $BookDate['month'], $BookDate['day'], 0);

        $Rate = $this::find($ID)->Rate()
            ->where(\DB::Raw("STR_TO_DATE(CONCAT( start_day, '/', start_month, '/', start_year ) ,'%d/%m/%Y' )"), "<=", $BookDateFormat)
            ->where(\DB::Raw("STR_TO_DATE(CONCAT( end_day, '/', end_month, '/', end_year ) ,'%d/%m/%Y' )"), ">=", $BookDateFormat);

        $RoomTypeList = $Rate->groupBy('cabin_occupancy_type')->select('cabin_occupancy_type')->get()->toArray();
        $RoomTypeList = array_column($RoomTypeList, 'cabin_occupancy_type');

        return ['cabin_occupancy_type' => $RoomTypeList];


    }


    /**
     * @param $longitude
     * @param $latitude
     * @param float $AreaDistance
     * @return bool|\Illuminate\Support\Collection
     */
    function getAreaCruises($longitude, $latitude, $AreaDistance = 0.3)
    {//area between hotels

        $Places = DB::table($this->table)
            ->select("ID", "name", "longitude", "latitude")
            ->where("longitude", "<", $longitude + $AreaDistance)
            ->where("latitude", "<", $latitude + $AreaDistance)
            ->where("longitude", ">", $longitude - $AreaDistance)
            ->where("latitude", ">", $latitude - $AreaDistance)
            ->limit(10)
            ->get();

        $Places = $Places->map(function($d){
            $d->image = Image::getImageDirect($d->ID, '2x', 'cruise', 1, $d->name);
            return $d;
        });

        if (!empty($Places))
            return $Places;
        else
            return false;

    }

    /**
     * @param $Place
     * @return bool|\Illuminate\Support\Collection
     */
    function getHotelsInPlaces($Place)
    {//get place hotels

        $Hotels = DB::table($this->table)
            ->select("$this->table.*", "apple_places.name as city_name", "apple_places.ID as city_id")
            ->join('apple_places', "$this->table.city", '=', 'apple_places.ID')
            ->where("city", "=", $Place)
            ->get();

        if (!empty($Hotels))
            return $Hotels;
        else
            return false;


    }

    /**
     * @param $Place
     * @param $BookDateFormat
     * @return \Illuminate\Support\Collection
     */
    function getCruiseList($Place, $BookDateFormat)
    {

        $Hotels = DB::table($this->table . ' AS parent')
            ->select("$this->table.*");

        return $Hotels->get();


    }

    /**
     * @param $Place
     * @param int $Limit
     * @return bool|mixed
     */
    function getLowestCriuseInPlaces($Place, $Limit = 1)
    {//get place hotels lowest Rate

        $Crises = DB::table($this->table)
            ->select("$this->table.*", "apple_places.name as city_name", "apple_places.ID as city_id")
            ->join('apple_places', "$this->table.city", '=', 'apple_places.ID')
            ->where("city", "=", $Place)
            ->take($Limit)
            ->get();

        if (!empty($Crises))
            return $Crises[0];
        else
            return false;


    }

    /**
     * @param string $size
     * @param int $limit
     * @return array
     */
    function image($size = '1x', $limit = 1)
    {
        $Image = new Image();
        return $Image->getImage($this->ID, $size, 'cruise', $limit);
    }

    /**
     * @param $HotelSettingsArray
     * @param $Type
     * @param $ArrivalDate
     * @return array
     * @throws \ReflectionException
     */
    function getHotelSettingsBookDatesChange($HotelSettingsArray, $Type, $ArrivalDate)
    {


        $ArrivalDate = Carbon::create($ArrivalDate['year'], $ArrivalDate['month'], $ArrivalDate['day'], 0);

        $QuotationHotel = new QuotationHotel();
        $BookDateArray = [];

        $HotelSettingsQueryArray = $HotelSettingsArray['hotel_setting'];//get encoded query

        unset($HotelSettingsArray['hotel_setting']);//remove encoded query
        $HotelModifySettings = arrayMapMulti('getActualDataType', $HotelSettingsArray);//set hotel modified settings


        foreach ($HotelSettingsQueryArray as $CurrentHotelIndex => $HotelSettings) {

            $CurrentHotelSettings = arrayMapMulti('getActualDataType', $QuotationHotel->getDecodeArray($HotelSettings));


            $BookDateArray[$CurrentHotelIndex]['place'] = $CurrentHotelSettings['place'];
            $BookDateArray[$CurrentHotelIndex]['provider'] = $CurrentHotelSettings['provider'];


            if (isset($HotelModifySettings['index']) && intval($CurrentHotelIndex) == intval($HotelModifySettings['index'])) {//if modify hotel is this one...

                $BookDateArray[$CurrentHotelIndex]['hotel'] = $HotelModifySettings['hotel'] ?? null;

                #set night
                $BookDateArray[$CurrentHotelIndex]['nights'] = $HotelModifySettings['nights'];

                #first set the days to check in
                $BookDateArray[$CurrentHotelIndex]['check_in'] = ['year' => $ArrivalDate->year, 'month' => $ArrivalDate->month, 'day' => $ArrivalDate->day];
                $BookDateArray[$CurrentHotelIndex]['check_in_format'] = $ArrivalDate->toFormattedDateString();

                #add night certain hotel
                $ArrivalDate->addDays($HotelModifySettings['nights']);//add modified nights

                #add again add check out day
                $BookDateArray[$CurrentHotelIndex]['check_out'] = ['year' => $ArrivalDate->year, 'month' => $ArrivalDate->month, 'day' => $ArrivalDate->day];
                $BookDateArray[$CurrentHotelIndex]['check_out_format'] = $ArrivalDate->toFormattedDateString();


            } else {//this is for all other hotels
                if(isset($CurrentHotelSettings['hotel'])) {
                    $BookDateArray[$CurrentHotelIndex]['hotel'] = $CurrentHotelSettings['hotel'];
                }

                #set night
                $BookDateArray[$CurrentHotelIndex]['nights'] = $CurrentHotelSettings['nights'];

                #first set the days to check in
                $BookDateArray[$CurrentHotelIndex]['check_in'] = ['year' => $ArrivalDate->year, 'month' => $ArrivalDate->month, 'day' => $ArrivalDate->day];
                $BookDateArray[$CurrentHotelIndex]['check_in_format'] = $ArrivalDate->toFormattedDateString();

                #add night certain hotel
                $ArrivalDate->addDays($CurrentHotelSettings['nights']);//add current nights

                #add again add check out day
                $BookDateArray[$CurrentHotelIndex]['check_out'] = ['year' => $ArrivalDate->year, 'month' => $ArrivalDate->month, 'day' => $ArrivalDate->day];
                $BookDateArray[$CurrentHotelIndex]['check_out_format'] = $ArrivalDate->toFormattedDateString();
            }


            #Set other data
            if (/*!empty($HotelSettingsArray['hotel']) &&*/
            !($HotelSettingsArray['index'] == $CurrentHotelIndex && isset($HotelSettingsArray['transportation']))) {//first check if there's a hotel


                if ($Type == 'all') {//if it apply for all


                    $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($HotelModifySettings['meal_type'])->plan; //get meal plan text
                    $BookDateArray[$CurrentHotelIndex]['meal'] = $HotelModifySettings['meal_type'];//get meal id
                    $BookDateArray[$CurrentHotelIndex]['room_category'] = $CurrentHotelSettings['room_category'];//don't set catogory for all hotels
                    $BookDateArray[$CurrentHotelIndex]['room_type'] = $HotelModifySettings['room_type']; //room type count set for all


                    if ($CurrentHotelIndex == $HotelModifySettings['index']) {//if modify hotel is this one apply for only this one for modified data
                        $BookDateArray[$CurrentHotelIndex]['room_category'] = $HotelModifySettings['room_category'];//set catogory for this hotels
                    }


                    foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                        if (intval($RoomCount))//only if room count requested
                            $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                    }


                } else {//only for one

                    if ($CurrentHotelIndex == $HotelModifySettings['index']) {//if modify hotel is this one apply for only this one for modified data

                        $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($HotelModifySettings['meal_type'])->plan; //get meal plan text
                        $BookDateArray[$CurrentHotelIndex]['meal'] = $HotelModifySettings['meal_type'];//get meal id
                        $BookDateArray[$CurrentHotelIndex]['room_category'] = $HotelModifySettings['room_category'];//set catogory for this hotels
                        $BookDateArray[$CurrentHotelIndex]['room_type'] = $HotelModifySettings['room_type']; //room type count set for all


                        foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                            if (intval($RoomCount))//only if room count requested
                                $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                        }
                    } else {
                        if (!empty($CurrentHotelSettings['hotel'])) {
                            $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($CurrentHotelSettings['meal_type'])->plan; //get meal plan text
                            $BookDateArray[$CurrentHotelIndex]['meal'] = $CurrentHotelSettings['meal_type'];//get meal id
                            $BookDateArray[$CurrentHotelIndex]['room_category'] = $CurrentHotelSettings['room_category'];//don't set catogory for all hotels
                            $BookDateArray[$CurrentHotelIndex]['room_type'] = $CurrentHotelSettings['room_type']; //room type count set for all


                            foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                                if (intval($RoomCount))//only if room count requested
                                    $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                            }
                        } else {#own arrangement


                        }
                    }


                }

            } elseif (empty($HotelModifySettings)) {//only date  want to change date


                $BookDateArray[$CurrentHotelIndex]['meal_text'] = Meal::find($CurrentHotelSettings['meal_type'])->plan; //get meal plan text
                $BookDateArray[$CurrentHotelIndex]['meal'] = $CurrentHotelSettings['meal_type'];//get meal id
                $BookDateArray[$CurrentHotelIndex]['room_category'] = $CurrentHotelSettings['room_category'];//don't set catogory for all hotels
                $BookDateArray[$CurrentHotelIndex]['room_type'] = $CurrentHotelSettings['room_type']; //room type count set for all


                foreach ($BookDateArray[$CurrentHotelIndex]['room_type'] as $RoomTypeID => $RoomCount) {//in the hotel slide we need to sho how many rooms like 1 doubele 2 tripple...
                    if (intval($RoomCount))//only if room count requested
                        $BookDateArray[$CurrentHotelIndex]['room_type_text'][$RoomTypeID] = intval($RoomCount) . ' ' . Room::find($RoomTypeID)->short_name; //get the text type
                }


            }


        }

        return $BookDateArray;

    }


    /**
     * @param $NewSetting
     * @param $BookDates
     * @return array
     * @throws \ReflectionException
     */
    function getHotelSettingsQuery($NewSetting, $BookDates)
    {

        $CompanyID = \Auth::user()->Profile()->first()->company;


        unset($NewSetting['hotel_setting']);
        $QueryArray = [];
        $HotelStatusArray = [];
        $CanApply = true;

        $Market = false;

        if (Session::has('quotation.market')) {
            $Market = Session::get('quotation.market');
        }


        foreach ($BookDates as $HotelIndex => $CurrentDate) {


            $NewSetting['check_in'] = $CurrentDate['check_in'];
            $NewSetting['check_out'] = $CurrentDate['check_out'];
            $NewSetting['nights'] = $CurrentDate['nights'];
            $NewSetting['place'] = $CurrentDate['place'];
            $NewSetting['hotel'] = $CurrentDate['hotel'] ?? null;
            $NewSetting['provider'] = $CurrentDate['provider'];
            $NewSetting['extrabed'] = $CurrentDate['extrabed'] ?? 0;
            $NewSetting['index'] = $HotelIndex;


            if (!empty($CurrentDate['hotel'])) {


                $NewSetting['room_type'] = $CurrentDate['room_type'];
                $NewSetting['meal_type'] = $CurrentDate['meal'];
                $NewSetting['room_category'] = $CurrentDate['room_category'];


                $RequestedRoomCount = array_sum($NewSetting['room_type']);

                if (!empty($CurrentDate['hotel']) && $CurrentDate['provider'] === 'local') {

                    $HotelDateStatus = "(" . Hotel::find($NewSetting['hotel'])->name . " {$NewSetting['check_in']['year']}-{$NewSetting['check_in']['month']}-{$NewSetting['check_in']['day']})";

                    #Check Allotment and type and okayyayay mother fuckerrree
                    if ($Allotment = Hotel::hasAllotment($NewSetting['hotel'], $NewSetting['check_in'])) {


                        if ($RequestedRoomCount > $Allotment) {
                            $Text = "You have requested $RequestedRoomCount rooms but we have only $Allotment rooms!";
                            $Satus = false;

                            #so if no allotment they cant apply
                            $CanApply = true;

                        } else {
                            $Satus = true;
                            $Text = ($Allotment - $RequestedRoomCount) . " more rooms are remaining!";
                        }
                        $HotelStatusArray[$HotelIndex][] = ['status' => $Satus, 'text' => $Text, 'hotel_name' => Hotel::find($NewSetting['hotel'])->name];

                    } else {

                        $Text = "There are no any allotments!";
                        $HotelStatusArray[$HotelIndex][] = ['status' => false, 'text' => $Text, 'hotel_date' => $HotelDateStatus, 'hotel_name' => Hotel::find($NewSetting['hotel'])->name];

                    }


                    #Check rates

                    if ($RateAvailable = Hotel::hasRate($NewSetting['hotel'], $NewSetting['check_in'], $NewSetting['room_type'], $NewSetting['meal_type'], $NewSetting['room_category'], $Market)) {
                        $Text = "Rates are available!";


                        $HotelStatusArray[$HotelIndex][] = ['status' => true, 'text' => $Text, 'hotel_date' => $HotelDateStatus];

                    } else {
                        $Text = "There are no any rates!";
                        if (Hotel::hasRate($NewSetting['hotel'], $NewSetting['check_in'], $NewSetting['room_type'], $NewSetting['meal_type'], $NewSetting['room_category'])) {
                            $Text .= ' (for ' . Market::find($Market)->market . ")";
                        }
                        $HotelStatusArray[$HotelIndex][] = ['status' => false, 'text' => $Text, 'hotel_date' => $HotelDateStatus, 'hotel_name' => Hotel::find($NewSetting['hotel'])->name];

                        $CanApply = false;
                    }
                }


                if (isset($NewSetting['selected_rooms'])) {
                    foreach ($NewSetting['selected_rooms'] as $RoomCode => $On) {
                        $NewSetting['rate_keys'][] = $NewSetting['room'][$RoomCode];
                    }
                }
            }

            unset($NewSetting['room']);

            $QueryArray[$HotelIndex] = http_build_query($NewSetting);
        }

        if (\Auth::user()->can("change_hotel_rates")) {
            $CanApply = true;
        }


        return ['Query' => $QueryArray, 'HotelStatusArray' => $HotelStatusArray, 'CanApply' => $CanApply];
    }

    /**
     * @param $Pax
     * @return array
     */
    static function getHotelRoomCountWithPax($Pax)
    {

        $Adult = $Pax['adult'];
        $CWB = $Pax['cwb'];
        $CNB = $Pax['cnb'];

        $single = 0;
        $double = 0;
        $tripple = 0;

        if ($CWB == 0) {
            $tripple = floor($Adult / 3);
            if ($tripple > 0) $Adult -= $tripple * 3;
        }

        $double = floor($Adult / 2);
        if ($double > 0)
            $Adult -= $double * 2;
        $single = floor($Adult);

        return ['1' => $single, '2' => $double, '3' => $tripple, '4' => 0, '5' => 0];

    }


    /**
     * @param $HotelID
     * @return Model|\Illuminate\Database\Eloquent\Relations\HasOne|null|object
     */
    static function getCruiseCurrency($CruiseID){
        $Country = Place::find(Hotel::find($CruiseID)->city)->country()->first()->ID;
        return Currency::find(CountryCurrency::getCurrency($Country)->hotel);
    }

}
