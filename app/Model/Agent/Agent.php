<?php

namespace App\Model\Agent;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Agent\Agent
 *
 * @property int $ID
 * @property string $name
 * @property string $address
 * @property int $agent_type
 * @property int $status
 * @property int $user
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereAgentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\Agent whereUser($value)
 * @mixin \Eloquent
 */
class Agent extends Model
{
    protected $table = "apple_agent";
    protected $primaryKey = 'ID';

    public function agent_type()
    {
        return $this->hasOne(AgentType::class, 'id', 'agent_type')->select('id','type');
    }

}
