<?php

namespace App\Model\Agent;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Agent\AgentType
 *
 * @property int $ID
 * @property string $type
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\AgentType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\AgentType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\AgentType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\AgentType whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\AgentType whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\AgentType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\AgentType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Agent\AgentType whereUploadId($value)
 * @mixin \Eloquent
 */
class AgentType extends Model
{
    protected $primaryKey = 'ID';
    protected $table = "apple_agent_type";
}
