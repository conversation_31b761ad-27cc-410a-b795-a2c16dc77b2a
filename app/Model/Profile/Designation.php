<?php

namespace App\Model\Profile;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Profile\Designation
 *
 * @property int $ID
 * @property string $designation
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Designation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Designation whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Designation whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Designation whereDesignation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Designation whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Designation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Designation whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Designation whereUploadId($value)
 * @mixin \Eloquent
 */
class Designation extends Model
{
    protected $table = "apple_designation";
}
