<?php

namespace App\Model\Profile;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Profile\Company
 *
 * @property int $ID
 * @property string $name
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Company whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Company whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Company whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Company whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Company whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Company whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Company whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Company whereUploadId($value)
 * @mixin \Eloquent
 */
class Company extends Model
{
    protected $table = "apple_company";

}
