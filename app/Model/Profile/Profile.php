<?php

namespace App\Model\Profile;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Model\Profile\Profile
 *
 * @property int $ID
 * @property int $user
 * @property string $first_name
 * @property int $designation
 * @property int $company
 * @property string $last_name
 * @property string $nic
 * @property string $dob
 * @property string $tel
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $upload_id
 * @property-read \App\User $User
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereDesignation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereDob($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereID($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereNic($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereTel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereUploadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Model\Profile\Profile whereUser($value)
 * @mixin \Eloquent
 */
class Profile extends Model
{
    protected $table = 'apple_user_profile';

    protected $fillable = ['user','first_name','designation','company','company','last_name','nic','dob','tel'];

    protected $primaryKey = 'ID';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function User()
    {
        return $this->belongsTo('App\User','ID','user');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function designation()
    {
        return $this->hasOne('App\Model\Profile\Designation','ID','designation');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function company()
    {
        return $this->hasOne('App\Model\Profile\Company','ID','company');
    }
}
