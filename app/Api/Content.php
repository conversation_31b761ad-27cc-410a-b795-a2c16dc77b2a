<?php namespace App\Api;


use App\Api\ContentType\AgentType;
use App\Api\ContentType\AttractionType;
use App\Api\ContentType\CityTourType;
use App\Api\ContentType\ExcludeType;
use App\Api\ContentType\ExcursionType;
use App\Api\ContentType\HotelAvailabilityType;
use App\Api\ContentType\HotelClassType;
use App\Api\ContentType\HotelType;
use App\Api\ContentType\HotelRoomCategory;
use App\Api\ContentType\CruiseType;
use App\Api\ContentType\IncludeType;
use App\Api\ContentType\MarketType;
use App\Api\ContentType\MealPlanType;
use App\Api\ContentType\MealPreferenceType;
use App\Api\ContentType\MealTimeType;
use App\Api\ContentType\MealWhereType;
use App\Api\ContentType\PlaceType;
use App\Api\ContentType\PlaceTypeType;
use App\Api\ContentType\RestaurantExpensiveType;
use App\Api\ContentType\RestaurantType;
use App\Api\ContentType\VehicleType;
use App\Exceptions\ApiHandler;

/**
 * Class Repository
 * @package App\Repositories
 */
class Content
{

    use ResponseTrait;


    const HOTEL = 'hotel';
    const CRUISE = 'cruise';
    const HOTEL_AVAILABILITY = 'hotel_availability';
    const HOTEL_CLASS = 'hotel_class';
    const HOTEL_CATEGORY = 'hotel_room_category';

    const MARKET = 'market';

    const MEAL_WHERE = 'meal_where';
    const MEAL_PLAN = 'meal_plan';
    const MEAL_TIME = 'meal_time';
    const MEAL_PREFERENCE = 'meal_preference';

    const RESTAURANT = 'restaurant';
    const RESTAURANT_EXPENSIVE = 'restaurant_expensive';

    const PLACE = "place";
    const PLACE_TYPE = "place_type";

    const AGENT = 'agent';

    const ATTRACTION = 'attraction';
    const CITY_TOUR = 'city_tour';
    const EXCURSION = 'excursion';

    const INCLUDES = "include";
    const EXCLUDES = "exclude";
    const VEHICLE = "vehicle";


    /**
     * @param $type
     * @param array $param
     * @return ApiResponse
     * @throws ApiHandler
     */
    public function get($type, $param = [])
    {
        switch ($type) {
            case $this::HOTEL:
                return new HotelType($param);
                break;
            case $this::CRUISE:
                return new CruiseType($param);
                break;
            case $this::HOTEL_AVAILABILITY;
                return new HotelAvailabilityType($param);
                break;
            case $this::HOTEL_CLASS;
                return new HotelClassType($param);
                break;
            case $this::HOTEL_CATEGORY;
                return new HotelRoomCategory($param);
                break;
            case $this::MARKET;
                return new MarketType($param);
                break;
            case $this::MEAL_WHERE;
                return new MealWhereType($param);
                break;
            case $this::MEAL_PLAN;
                return new MealPlanType($param);
                break;
            case $this::MEAL_TIME;
                return new MealTimeType($param);
                break;
            case $this::MEAL_PREFERENCE;
                return new MealPreferenceType($param);
                break;
            case $this::RESTAURANT;
                return new RestaurantType($param);
                break;
            case $this::RESTAURANT_EXPENSIVE;
                return new RestaurantExpensiveType($param);
                break;
            case $this::PLACE;
                return new PlaceType($param);
                break;
            case $this::PLACE_TYPE;
                return new PlaceTypeType($param);
                break;
            case $this::AGENT;
                return new AgentType($param);
                break;
            case $this::ATTRACTION;
                return new AttractionType($param);
                break;
            case $this::CITY_TOUR;
                return new CityTourType($param);
                break;
            case $this::EXCURSION;
                return new ExcursionType($param);
                break;
            case $this::INCLUDES;
                return new IncludeType($param);
                break;
            case $this::EXCLUDES;
                return new ExcludeType($param);
                break;
            case $this::VEHICLE;
                return new VehicleType($param);
                break;
            default:
                $this::throwError("Invalid content type!");
        }

    }


}
