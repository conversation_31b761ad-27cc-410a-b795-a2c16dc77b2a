<?php

namespace App\Api;


use Carbon\Carbon;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class ApiResponse
 * @package App\Api
 */
class ApiResponse extends JsonResponse
{
    private $body;
    private $error;

    /**
     * ApiResponse constructor.
     * @param $parameters
     */
    public function __construct($parameters)
    {

        $this->body = $parameters[0] ?? false;
        $this->error = $parameters[1] ?? false;
        $header = $this->getHeader();
        parent::__construct($this->get(), $this->error ? 500 : 200, $header);

    }

    /**
     * @return array
     */
    function getHeader()
    {
        return [
            'Content-Type' => 'application/json',
            'ip' => request()->ip(),
        ];
    }

    /**
     * @return array
     */
    public function get()
    {
        $response['audit'] = $this->getAudit();

        if ($this->getData())
            $response['body'] = $this->getData();
        if ($this->getError())
            $response['error'] = $this->getError();


        return $response;
    }

    /**
     * @return array
     */
    function getAudit()
    {
        return [
            "datetime" => Carbon::now()->toDateTimeString(),
            'ip' => request()->ip(),
        ];
    }

    /**
     * @return array
     */
    function getStatus()
    {
        return [
            "datetime" => Carbon::now()->toDateTimeString(),
            'ip' => request()->ip(),
        ];
    }

    /**
     * @return mixed
     */
    function getData()
    {
        return jsona($this->body)->getData();
    }

    /**
     * @return mixed
     */
    function getError()
    {
        return $this->error;
    }

}
