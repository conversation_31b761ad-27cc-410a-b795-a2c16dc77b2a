<?php

namespace App\Api\ContentType;


use App\Http\Resources\HotelListResource;
use App\Model\Cruise\Cruise;
use App\Model\Hotel\Hotel;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class CruiseType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {

        $validator = Validator::make($this->param, [
            'id' => 'exists:apple_cruise,ID|max:10',
            'name' => 'string|max:255',
            'city' => 'exists:apple_places,ID|max:10',
            'class' => 'exists:apple_hotel_class,ID|max:10',
        ], config('api.error_messages.hotel'));

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }


        $id = $this->param['id'] ?? null;
        $name = $this->param['name'] ?? null;
        $city = $this->param['city'] ?? null;
        $class = $this->param['class'] ?? null;
        $limit = $this->param['limit'] ?? $this->limit;

        $data = Cruise::select("*");

        //Filters
        $id ? $data->find($id) : null;
        $name ? $data->where('name', "LIKE", "%$name%") : null;
        $city ? $data->where('city', $city) : null;
        $class ? $data->where('class', $class) : null;

        $data = $data->with('class', 'Contact', 'City', 'Package')->paginate($limit);

        if ($data->isEmpty()) {
            $this::throwError("Cruise(s) not found!");
        }

        return $data->toArray();

    }
}
