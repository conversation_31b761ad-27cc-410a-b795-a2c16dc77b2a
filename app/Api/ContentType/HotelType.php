<?php

namespace App\Api\ContentType;


use App\Http\Resources\HotelListResource;
use App\Model\Hotel\Hotel;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use App\Model\Image\Image;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class HotelType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {
        $Image = new Image();
        $validator = Validator::make($this->param, [
            'id' => 'exists:apple_hotels,ID|max:10',
            'name' => 'string|max:255',
            'city' => 'exists:apple_places,ID|max:10',
            'class' => 'exists:apple_hotel_class,ID|max:10',
        ], config('api.error_messages.hotel'));

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }


        $id = $this->param['id'] ?? null;
        $name = $this->param['name'] ?? null;
        $city = $this->param['city'] ?? null;
        $class = $this->param['class'] ?? null;
        $limit = $this->param['limit'] ?? $this->limit;

        $data = Hotel::select(config('api.content.fields.hotel'));


        //Filters
        $id ? $data->find($id) : null;
        $name ? $data->where('name', "LIKE", "%$name%") : null;
        $city ? $data->where('city', $city) : null;
        $class ? $data->where('class', $class) : null;
        $data->orderBy('preferred', 'desc');//preferred first

        $data = $data->with(['class', 'Contact', 'City'])->paginate($limit);

        if ($data->isEmpty()) {
            $this::throwError("Hotel(s) not found!");
        }

        $data = json_encode($data);

        $data = str_replace('class', 'hotel_class', $data);
        $data = json_decode($data, true);

        if(isset($data)) {
            foreach ($data["data"] as $key => $hotel) {
                $data["data"][$key]['hotel_image'] = $Image->getImage($hotel['id'],'3x',"hotel",1,$hotel['name'])[0] or "";
                $data["data"][$key]['hotel_image_large'] = $Image->getImage($hotel['id'],'300',"hotel",1,$hotel['name'])[0] or "";
            }
        }

        return replaceNullWithEmptyString($data);
    }



}
