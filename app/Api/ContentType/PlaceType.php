<?php

namespace App\Api\ContentType;


use App\Currency;
use App\Model\Place\Place;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use App\Model\Weather\Weather;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class PlaceType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {

        $validator = Validator::make($this->param, [
            'id' => 'exists:apple_places,ID|max:10',
            'name' => 'string|max:255',
            'country' => 'exists:apple_places,ID|max:10',
//            'type' => 'exists:apple_hotel_class,ID|max:10'
            'type' => 'exists:apple_places,type|max:10'
        ], config('api.error_messages.place'));

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }


        $id = $this->param['id'] ?? null;
        $name = $this->param['name'] ?? null;
        $country = $this->param['country'] ?? null;
        $type = $this->param['type'] ?? null;
        $limit = $this->param['limit'] ?? $this->limit;

        $onlyID = $this->param['only_id'] ?? null;
        $availability = $this->param['available'] ?? null;

//        $data = Place::select('id','name');

        if ($onlyID == 1){
            $data = Place::select('id','name');
        } else {
            $data = Place::select(config('api.content.fields.place'));
        }

        //Filters - Apply all filters simultaneously
        if ($id) {
            $data = $data->where('ID', $id);
        }

        if ($name) {
            $data = $data->where('name', 'LIKE', "%$name%");
        }

        if ($country) {
            $data = $data->where('country', $country);
        }

        if ($type) {
            $data = $data->where('type', $type);
        }

        if ($availability) {
            $data = $data->where('availability', $availability);
        }

//        $data = $data->with('type')->paginate($limit);

        if ($onlyID == 1){
            $data = $data->get();
        } else {
            $data = $data->with('currency')->get();
        }

        if ($data->isEmpty()) {
            $this::throwError("Place(s) not found!");
        }

//        $currency = Currency::find($data->currency);

        $data = $data->toArray();

        /*if (!empty($name) || !empty($country) || !empty($id)){

            $Weather = new Weather();

            foreach($data as $place) {
                $place['weather'] = toArray($Weather->getPlaceWeather($place['id']));
                $data = [];
                array_push($data, $place);
            }
        }*/

        return replaceNullWithEmptyString($data);

    }

}
