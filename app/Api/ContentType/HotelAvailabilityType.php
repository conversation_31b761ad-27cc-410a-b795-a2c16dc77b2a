<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Availability;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class HotelAvailabilityType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {
        $data = Availability::select(config('api.content.fields.hotel_availability'))->paginate();
        return $data->toArray();
    }

}
