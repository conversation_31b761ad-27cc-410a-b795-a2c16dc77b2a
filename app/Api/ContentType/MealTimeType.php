<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Hotel;
use App\Model\Meal\MealTime;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class MealTimeType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     */
    function set()
    {
        $data = MealTime::select(config('api.content.fields.meal_time'))->paginate();
        return $data->toArray();
    }

}
