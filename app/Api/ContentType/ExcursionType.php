<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Hotel;
use App\Model\Place\Excursion;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class ExcursionType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {
        // Add validation for input parameters
        $validator = Validator::make($this->param, [
            'name' => 'string|max:255',
            'from' => 'integer|exists:apple_places,ID',
            'to' => 'integer|exists:apple_places,ID',
        ]);

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }

        // Get parameters
        $name = $this->param['name'] ?? null;
        $from = $this->param['from'] ?? null;
        $to = $this->param['to'] ?? null;

        // Build query with relationships
        $query = Excursion::select(config('api.content.fields.excursion'))->with('Type'); // 'Rate',

        // Apply filters
        if ($name) {
            $query->where('name', 'LIKE', "%$name%");
        }

        if ($from) {
            $query->where('from', $from);
        }

        if ($to) {
            $query->where('to', $to);
        }

        $data = $query->paginate();

        return replaceNullWithEmptyString($data->toArray());
    }

}
