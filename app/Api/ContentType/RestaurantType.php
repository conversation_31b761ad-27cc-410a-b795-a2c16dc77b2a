<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Hotel;
use App\Model\Restaurant\Restaurant;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class RestaurantType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {

        $validator = Validator::make($this->param, [
            'id' => 'exists:apple_restaurant,ID|max:10',
            'name' => 'string|max:255',
            'from' => 'exists:apple_places,ID|max:10',
            'to' => 'exists:apple_places,ID|max:10',
            'expensive' => 'exists:apple_restaurant_expensive,ID|max:10',
            'limit' => 'integer|max:100',
        ], config('api.error_messages.restaurant'));

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }


        $id = $this->param['id'] ?? null;
        $name = $this->param['name'] ?? null;
        $from = $this->param['from'] ?? null;
        $to = $this->param['to'] ?? null;
        $expensive = $this->param['expensive'] ?? null;
        $limit = $this->param['limit'] ?? $this->limit;

        $data = Restaurant::select(config('api.content.fields.restaurant'));


        //Filters
        $id ? $data->find($id) : null;
        $name ? $data->where('name', "LIKE", "%$name%") : null;
        $from ? $data->where('from', $from) : null;
        $to ? $data->where('to', $to) : null;
        $expensive ? $data->where('expensive', $expensive) : null;

//        $data = $data->with('expensive','rate')->paginate($limit);
        $data = $data->with('expensive')->paginate($limit);

        if ($data->isEmpty()) {
            $this::throwError("Restaurant(s) not found!");
        }

//        return $this->data = $data->toArray();
        return replaceNullWithEmptyString($data->toArray());
    }

}
