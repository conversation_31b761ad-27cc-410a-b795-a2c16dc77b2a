<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Hotel;
use App\Model\Meal\MealPreference;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class MealPreferenceType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {

        $validator = Validator::make($this->param, [
            'country' => 'required|exists:apple_places,ID|max:10',
        ], config('api.error_messages.meal_preference'));

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }

        $country = $this->param['country'] ?? null;

        $data = MealPreference::select(config('api.content.fields.meal_preference'));

        //Filters
        $country ? $data->where('country', $country) : null;

        $data = $data->paginate();

        if ($data->isEmpty()) {
            $this::throwError("There are no any meal preference for selected country!");
        }

        return $this->data = $data->toArray();
    }

}
