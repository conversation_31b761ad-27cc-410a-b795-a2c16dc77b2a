<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Hotel;
use App\Model\Hotel\RoomCategory;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class HotelRoomCategory extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }
    /**
     * @return mixed
     */
    function set()
    {
        // Get parameters
        $name = $this->param['name'] ?? null;

        $query = RoomCategory::select(config('api.content.fields.hotel_room_category'));
        
        if ($name) {
            $query->where('name', 'LIKE', "%$name%");
        }

        $data = $query->paginate();
        
        return replaceNullWithEmptyString($data->toArray());
    }

}
