<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Hotel;
use App\Model\Hotel\HotelClass;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class HotelClassType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }
    /**
     * @return mixed
     */
    function set()
    {
        $data = HotelClass::select(config('api.content.fields.hotel_class'))->paginate();
        return replaceNullWithEmptyString($data->toArray());
    }

}
