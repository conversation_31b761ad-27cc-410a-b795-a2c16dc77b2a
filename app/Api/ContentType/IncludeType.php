<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Hotel;
use App\Model\Quotation\QuotationInclude;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class IncludeType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {

        $data = QuotationInclude::select(config('api.content.fields.include'))->paginate();
        return replaceNullWithEmptyString($data->toArray());
    }

}
