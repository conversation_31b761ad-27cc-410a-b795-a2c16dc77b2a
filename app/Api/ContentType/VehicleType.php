<?php

namespace App\Api\ContentType;


use App\Model\Hotel\Hotel;
use App\Model\Meal\MealPlan;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use App\Model\Vehicle\Vehicle;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class VehicleType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {
        $validator = Validator::make($this->param, [
            'country' => 'required|exists:apple_places,ID|max:10',
        ], config('api.error_messages.vehicle'));

        if ($validator->fails()) {
            $this::throwError($validator->errors()->first());
        }

        $country = $this->param['country'] ?? null;

        $data = Vehicle::select(config('api.content.fields.vehicle'));

        //Filters
        $country ? $data->where('country', $country) : null;

        $data = $data->paginate();

        if ($data->isEmpty()) {
            $this::throwError("There are no any vehicles!");
        }

//        $data = $data->toArray();

        $dataTwo = [];

        foreach ($data as $vehicle){
            $vehicleType = \App\Model\Vehicle\VehicleType::find($vehicle['vehicle_type']);
            $vehicle['vehicle_name'] = $vehicleType->name;
//            $data = [];
            array_push($dataTwo, $vehicle);
        }

        return $dataTwo;

    }

}
