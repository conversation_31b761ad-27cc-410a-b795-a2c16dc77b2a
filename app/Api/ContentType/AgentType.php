<?php

namespace App\Api\ContentType;


use App\Model\Agent\Agent;
use App\Model\Hotel\Hotel;
use App\Api\ApiResponse;
use App\Api\ResponseTrait;
use Validator;

/**
 * Class HotelType
 * @package App\Api\ContentType
 */
class AgentType extends ApiResponse implements ContentInterface
{
    use ResponseTrait;

    private $param;
    private $limit = 100;

    /**
     * ApiResponse constructor.
     * @param $param
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($param)
    {
        $this->param = $param;
        $data = $this->set();
        parent::__construct([$data]);

    }


    /**
     * @return mixed
     * @throws \App\Exceptions\ApiHandler
     */
    function set()
    {
        $query = Agent::select(config('api.content.fields.agent'))->with('agent_type');

        // Add name filtering if 'name' parameter is provided
        if (isset($this->param['name']) && !empty($this->param['name'])) {
            $query->where('name', 'LIKE', '%' . $this->param['name'] . '%');
        }

        $data = $query->paginate();

        return replaceNullWithEmptyString($data->toArray());

    }

}
