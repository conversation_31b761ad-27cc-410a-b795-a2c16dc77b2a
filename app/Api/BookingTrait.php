<?php

namespace App\Api;


use App\Model\Place\Stop;
use Carbon\Carbon;

trait BookingTrait
{
    function apiArrayToQuotationArray(array $json): array
    {

        //required
        $QuotationArray = [];

        $QuotationArray = array_merge($QuotationArray, $json["config"]??[]);
        $QuotationArray = array_merge($QuotationArray, $this->getGeneralDataArray($json["genaral_data"]??[]));
        $QuotationArray = array_merge($QuotationArray, $json["country_data"]??[]);
        $QuotationArray = array_merge($QuotationArray, $this->getPlaceArray($json["destination_data"]));
        $QuotationArray = array_merge($QuotationArray, $this->getAccommodationDataArray($json["accommodation_data"]??[]));
        $QuotationArray = array_merge($QuotationArray, $this->getToursArray($json["tours_data"]??[]));
        $QuotationArray = array_merge($QuotationArray, $json["markup_data"]??[]);
        $QuotationArray = array_merge($QuotationArray, $this->getConfirmArray($json['confirm']??[]));
        $QuotationArray = array_merge($QuotationArray, $this->getOwnEll($json['own']??null));

        return $QuotationArray;

    }

    private function getGeneralDataArray(array $array)
    {
        $data = $array;

        // Convert arrival_date from "dd/mm/yyyy" format to {"year": yyyy, "month": mm, "day": dd}
        if (isset($array['arrival_date']) && is_string($array['arrival_date'])) {
            $data['arrival_date'] = $this->convertDateStringToArray($array['arrival_date']);
        }

        return $data;
    }

    private function getAccommodationDataArray(array $array)
    {
        $data = $array;

        // Convert hotel check_in and check_out dates from string to array format
        if (isset($array['hotel']) && is_array($array['hotel'])) {
            foreach ($array['hotel'] as $index => $hotel) {
                // Convert check_in date
                if (isset($hotel['check_in']) && is_string($hotel['check_in'])) {
                    $data['hotel'][$index]['check_in'] = $this->convertDateStringToArray($hotel['check_in']);
                }

                // Convert check_out date
                if (isset($hotel['check_out']) && is_string($hotel['check_out'])) {
                    $data['hotel'][$index]['check_out'] = $this->convertDateStringToArray($hotel['check_out']);
                }

                // Convert room_type from array format to object format
                if (isset($hotel['room_type']) && is_array($hotel['room_type'])) {
                    $data['hotel'][$index]['room_type'] = $this->convertRoomTypeArrayToObject($hotel['room_type']);
                }
            }
        }

        return $data;
    }

    private function getPlaceArray(array $array)
    {

        $data['place_full'] = $array['place'];
        $data['place_type'] = $array['place_pretend_type'];
        $data['place'] = collect($array['place'])->filter(function ($item) {
            return !Stop::where('place', $item)->first();
        })->toArray();
        $data['place'] = array_values($data['place']);

        return $data;
    }

    private function getConfirmArray(array $array)
    {
        $data["confirm"] = $array??[];

        return $data;
    }

    private function getOwnEll($ele)
    {
        $data["own"] = $ele??null;

        return $data;
    }

    private function getToursArray(array $toursData)
    {
        $data = [];

        // Initialize arrays for different tour types
        $attractions = [];
        $cityTours = [];
        $excursions = [];
        $attractionTimes = [];
        $cityTourTimes = [];
        $excursionTimes = [];
        $attractionPax = [];
        $cityTourPax = [];
        $excursionPax = [];

        // Process each tour item
        foreach ($toursData as $tour) {
            $day = $tour['day'];
            $id = $tour['id'];
            $type = $tour['type'];
            $pax = $tour['pax'] ?? [];
            $timeId = $tour['time_id'] ?? null;

            switch ($type) {
                case 'attraction':
                    // Group attractions by day
                    if (!isset($attractions[$day])) {
                        $attractions[$day] = [];
                    }
                    $attractions[$day][] = $id;

                    // Store time and pax data
                    if ($timeId) {
                        $attractionTimes[$id] = $timeId;
                    }
                    if (!empty($pax)) {
                        $attractionPax[$id] = $pax;
                    }
                    break;

                case 'city_tour':
                    // Group city tours by day
                    if (!isset($cityTours[$day])) {
                        $cityTours[$day] = [];
                    }
                    $cityTours[$day][] = $id;

                    // Store time and pax data
                    if ($timeId) {
                        $cityTourTimes[$id] = $timeId;
                    }
                    if (!empty($pax)) {
                        $cityTourPax[$id] = $pax;
                    }
                    break;

                case 'excursion':
                    // Group excursions by day
                    if (!isset($excursions[$day])) {
                        $excursions[$day] = [];
                    }
                    $excursions[$day][] = $id;

                    // Store time and pax data
                    if ($timeId) {
                        $excursionTimes[$id] = $timeId;
                    }
                    if (!empty($pax)) {
                        $excursionPax[$id] = $pax;
                    }
                    break;
            }
        }

        // Add processed data to result array
        if (!empty($attractions)) {
            $data['attraction'] = $attractions;
        }
        if (!empty($cityTours)) {
            $data['city_tour'] = $cityTours;
        }
        if (!empty($excursions)) {
            $data['excursion'] = $excursions;
        }

        // Add time data if available
        if (!empty($attractionTimes) || !empty($cityTourTimes) || !empty($excursionTimes)) {
            $data['time'] = [
                'attraction' => []
            ];

            if (!empty($attractionTimes)) {
                $data['time']['attraction']['attraction'] = $attractionTimes;
            }
            if (!empty($cityTourTimes)) {
                $data['time']['attraction']['city_tour'] = $cityTourTimes;
            }
            if (!empty($excursionTimes)) {
                $data['time']['attraction']['excursion'] = $excursionTimes;
            }
        }

        // Add pax data if available
        if (!empty($attractionPax) || !empty($cityTourPax) || !empty($excursionPax)) {
            $data['pax_attraction'] = [];

            if (!empty($attractionPax)) {
                $data['pax_attraction']['attraction'] = $attractionPax;
            }
            if (!empty($cityTourPax)) {
                $data['pax_attraction']['city_tour'] = $cityTourPax;
            }
            if (!empty($excursionPax)) {
                $data['pax_attraction']['excursion'] = $excursionPax;
            }
        }

        return $data;
    }

    private function convertDateStringToArray(string $dateString): array
    {
        // Handle different date formats: "dd/mm/yyyy", "dd-mm-yyyy", "yyyy-mm-dd"
        $dateString = trim($dateString);

        // Try to parse different formats
        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $dateString, $matches)) {
            // Format: dd/mm/yyyy
            return [
                'year' => (int)$matches[3],
                'month' => (int)$matches[2],
                'day' => (int)$matches[1]
            ];
        } elseif (preg_match('/^(\d{1,2})-(\d{1,2})-(\d{4})$/', $dateString, $matches)) {
            // Format: dd-mm-yyyy
            return [
                'year' => (int)$matches[3],
                'month' => (int)$matches[2],
                'day' => (int)$matches[1]
            ];
        } elseif (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $dateString, $matches)) {
            // Format: yyyy-mm-dd
            return [
                'year' => (int)$matches[1],
                'month' => (int)$matches[2],
                'day' => (int)$matches[3]
            ];
        }

        // If no pattern matches, try to use Carbon to parse the date
        try {
            $carbon = Carbon::createFromFormat('d/m/Y', $dateString);
            return [
                'year' => $carbon->year,
                'month' => $carbon->month,
                'day' => $carbon->day
            ];
        } catch (\Exception $exception) {
            // If all parsing fails, return current date as fallback
            // Suppressing unused variable warning for $exception
            unset($exception);
            $now = Carbon::now();
            return [
                'year' => $now->year,
                'month' => $now->month,
                'day' => $now->day
            ];
        }
    }

    private function convertRoomTypeArrayToObject(array $roomTypeArray): array
    {
        $roomTypeObject = [];

        // Convert array format [{"id": 2, "count": 1}] to object format {"2": "1"}
        foreach ($roomTypeArray as $roomType) {
            if (isset($roomType['id']) && isset($roomType['count'])) {
                $roomTypeObject[(string)$roomType['id']] = (string)$roomType['count'];
            }
        }

        return $roomTypeObject;
    }

    private function getRateArray(array $array)
    {
        $data['place_full'] = $array['place'];
        $data['place_type'] = $array['place_pretend_type'];
        $data['place'] = collect($array['place'])->filter(function ($item) {
            return !Stop::where('place', $item)->first();
        })->toArray();

        return $data;
    }
}
