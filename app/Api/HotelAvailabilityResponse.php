<?php
/**
 * Created by PhpStorm.
 * User: supun
 * Date: 9/9/18
 * Time: 11:14 PM
 */

namespace App\Api;


use App\Model\Hotel\Availability;

class HotelAvailabilityResponse extends ApiResponse
{
    use ResponseTrait;
    private $Availability;

    /**
     * ApiResponse constructor.
     * @param $Availability
     * @throws \ReflectionException
     */
    public function __construct($Availability)
    {
        $this->Availability = $Availability;
        $data = $this->set($Availability);
        parent::__construct([$data]);

    }


    /**
     * @param $array
     * @return mixed
     * @throws \ReflectionException
     */
    function set($array)
    {
        $data = [];
        foreach ($array[0] as $rateItem) {

            $data[] = [
                "hotel" => $rateItem->hotel,
                "status" => Availability::find((\App\Model\Hotel\Hotel::getStatus($rateItem->hotel, $array[1],$rateItem->room_type,$rateItem->meal,$rateItem->room_category, $rateItem->market)[0]))->status,
                "from" => [
                    "year" => $rateItem->start_year,
                    "month" => $rateItem->start_month,
                    "day" => $rateItem->start_day,
                ],
                "to" => [
                    "year" => $rateItem->end_year,
                    "month" => $rateItem->end_month,
                    "day" => $rateItem->end_day,
                ],

                "meal" => $rateItem->meal,
                "rate" => $rateItem->rate,
                "room_type" => $rateItem->room_type,
                "room_category" => $rateItem->room_category,
                "market" => $rateItem->market,
                "child_count" => $rateItem->child_count,
                "special" => $rateItem->special,
                "CityH" => $rateItem->CityH,
                "ClassH" => $rateItem->ClassH,
                "allotment" => $rateItem->allotment,
                "distance_from_city" => $rateItem->distance_from_city,
            ];
        }

        return $data;
    }

}
