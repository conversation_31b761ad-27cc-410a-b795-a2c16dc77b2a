<?php
/**
 * Created by PhpStorm.
 * User: supun
 * Date: 8/30/18
 * Time: 10:22 PM
 */

namespace App\Api;


use App\Model\Hotel\Rates;

class Hotel
{


    /**
     * @param $HotelSettings
     * @return bool|\Illuminate\Support\Collection
     * @throws \ReflectionException
     */
    function availability($HotelSettings){
        $Rate = new Rates();
        return $Rate->getLowestHotelPlace($HotelSettings['place'], $HotelSettings['check_in'], $HotelSettings['check_out'], false, false, false, false, 8);
    }

}
