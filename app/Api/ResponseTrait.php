<?php

namespace App\Api;


use App\Exceptions\ApiHandler;

/**
 * Trait ResponseTrait
 * @package App\Api
 */
trait ResponseTrait
{

    /**
     * @param array $data
     * @return mixed
     */
    static function Response(array $data){
        return app()->make('ApiResponse', [$data]);
    }

    /**
     * @param $Message
     * @throws ApiHandler
     */
    static function throwError($Message){
        throw new ApiHandler(request(),  new \Exception($Message));
    }

}
