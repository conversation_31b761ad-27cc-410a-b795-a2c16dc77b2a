<?php
/**
 * Created by PhpStorm.
 * User: supun
 * Date: 9/9/18
 * Time: 11:14 PM
 */

namespace App\Api;


class BookingResponse extends ApiResponse
{
    use ResponseTrait;
    private $QuotationSave;

    /**
     * ApiResponse constructor.
     * @param $QuotationSave
     * @throws \App\Exceptions\ApiHandler
     */
    public function __construct($QuotationSave)
    {
        $this->QuotationSave = $QuotationSave;
        $data = $this->set($QuotationSave);
        parent::__construct([$data]);

    }


    /**
     * @param $QuotationSave
     * @return mixed
     */
    function set($QuotationSave)
    {
        return $QuotationSave;
    }

}
