<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesQuotationPaxesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_quotation_paxes', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('reference_id');
            $table->integer('no_of_adults');
            $table->integer('no_of_cwb');
            $table->integer('no_of_cnb');
            $table->string('vehicle_name');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_quotation_paxes');
    }
}
