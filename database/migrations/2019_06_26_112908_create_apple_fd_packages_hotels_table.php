<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesHotelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_hotels', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('fd_package_id');
            $table->integer('place_id');
            $table->integer('hotel_id');
            $table->integer('no_of_nights');
            $table->integer('meal_type');
            $table->integer('room_category');
            $table->decimal('adult_rate_single_room',10,2)->nullable();
            $table->decimal('adult_rate_double_room',10,2)->nullable();
            $table->decimal('adult_rate_triple_room',10,2)->nullable();
            $table->decimal('child_rate',10,2);
            $table->decimal('child_w_out_bed_rate',10,2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_hotels');
    }
}
