<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesSupplimentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_suppliments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('fd_package_id');
            $table->date('from');
            $table->date('to');
            $table->decimal('adult_cost',10,2)->comment('Charges');
            $table->decimal('cwb_cost',10,2)->comment('Charges')->nullable();
            $table->decimal('cnb_cost',10,2)->comment('Charges')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_suppliments');
    }
}
