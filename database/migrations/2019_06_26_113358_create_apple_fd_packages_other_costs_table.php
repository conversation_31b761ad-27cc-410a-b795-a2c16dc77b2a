<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesOtherCostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_other_costs', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('fd_package_id');
            $table->string('cost_name');
            $table->string('cost_type');
            $table->decimal('adult_rate',10,2);
            $table->decimal('child_rate',10,2);
            $table->decimal('child_w_out_bed_rate',10,2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_other_costs');
    }
}
