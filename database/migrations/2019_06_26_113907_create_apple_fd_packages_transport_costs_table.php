<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesTransportCostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_transport_costs', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('fd_package_id');
            $table->integer('distance');
            $table->decimal('distance_price',10,2);
            $table->decimal('bata',10,2);
            $table->integer('no_of_days');
            $table->decimal('paging',10,2);
            $table->decimal('grand_total',10,2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_transport_costs');
    }
}
