<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesQuotationHotelRatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_quotation_hotel_rates', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('reference_id');
            $table->integer('place_id');
            $table->integer('hotel_id');
            $table->date('check_in');
            $table->date('check_out');
            $table->string('provider');
            $table->string('roomTypeText');
            $table->integer('no_of_nights');
            $table->integer('meal_type');
            $table->integer('room_category');
            $table->decimal('adult_single_room_rate_pp',10,2)->nullable();
            $table->decimal('adult_single_room_rate_total',10,2)->nullable();
            $table->integer('adult_single_room_count')->nullable();
            $table->decimal('adult_double_room_rate_pp',10,2)->nullable();
            $table->decimal('adult_double_room_rate_total',10,2)->nullable();
            $table->integer('adult_double_room_count')->nullable();
            $table->decimal('adult_triple_room_rate_pp',10,2)->nullable();
            $table->decimal('adult_triple_room_rate_total',10,2)->nullable();
            $table->integer('adult_triple_room_count')->nullable();
            $table->decimal('child_rate',10,2)->nullable();
            $table->decimal('child_w_bed_rate',10,2)->nullable();
            $table->integer('email_status')->comment('0 not sent 1 sent');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_quotation_hotel_rates');
    }
}
