<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleCancelChargesMarkupRatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_cancel_charges_markup_rates', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('city');
            $table->integer('nights');
            $table->integer('mark_up')->comment('% Percentage');
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_cancel_charges_markup_rates');
    }
}
