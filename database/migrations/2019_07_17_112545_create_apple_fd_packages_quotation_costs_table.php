<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesQuotationCostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_quotation_costs', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('reference_id');
            $table->decimal('adult_package_cost',10,2);
            $table->decimal('cwb_package_cost',10,2);
            $table->decimal('cnb_package_cost',10,2);
            $table->decimal('adult_single_room_cost',10,2)->nullable();
            $table->decimal('adult_double_room_cost',10,2)->nullable();
            $table->decimal('adult_triple_room_cost',10,2)->nullable();
            $table->decimal('cwb_cost',10,2)->nullable();
            $table->decimal('cnb_cost',10,2)->nullable();
            $table->decimal('total_hotel_cost',10,2);
            $table->decimal('grand_cost',10,2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_quotation_costs');
    }
}
