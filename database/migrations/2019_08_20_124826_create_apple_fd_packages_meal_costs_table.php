<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesMealCostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_meal_costs', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('fd_package_id');
            $table->decimal('bf_adult_meal_rate',10,2);
            $table->decimal('bf_child_meal_rate',10,2);
            $table->decimal('lunch_adult_meal_rate',10,2);
            $table->decimal('lunch_child_meal_rate',10,2);
            $table->decimal('dinner_adult_meal_rate',10,2);
            $table->decimal('dinner_child_meal_rate',10,2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_meal_costs');
    }
}
