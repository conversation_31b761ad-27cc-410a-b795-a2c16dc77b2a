<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesQuotationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_quotations', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('quotation_no');
            $table->integer('status')->comment('2 -  Confirmed 3 - Cancelled');
            $table->integer('currency')->nullable();
            $table->integer('fd_package_id');
            $table->string('meal_plan_types');
            $table->date('arrival_date');
            $table->string('nights_days_count');
            $table->integer('honorific_id');
            $table->string('client_name');
            $table->integer('agent_id');
            $table->integer('user_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_quotations');
    }
}
