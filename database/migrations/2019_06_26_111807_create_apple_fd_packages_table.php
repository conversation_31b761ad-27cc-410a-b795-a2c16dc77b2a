<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages', function (Blueprint $table) {
            $table->increments('id');
            $table->string('package_name');
            $table->date('date_from');
            $table->date('date_to');
            $table->text('itinerary_desc')->nullable();
            $table->text('includes')->nullable();
            $table->text('excludes')->nullable();
            $table->integer('pick_up');
            $table->integer('drop_off');
            $table->integer('country');
            $table->integer('group_package')->comment('0 - no 1 - yes');
            $table->date('arrival_date')->nullable();
            $table->integer('max_pax')->nullable();
            $table->integer('user_id');
            $table->integer('status')->comment('0 - disabled 1 - enabled');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages');
    }
}
