<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesPackagePricesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_package_prices', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('fd_package_id');
            $table->decimal('adult_rate_1',10,2)->comment('total pax 1 -2');
            $table->decimal('adult_rate_2',10,2)->comment('total pax 4 - 6');
            $table->decimal('adult_rate_3',10,2)->comment('total pax 7 - 10');
            $table->decimal('adult_rate_4',10,2)->comment('total pax 11 - 15');
            $table->decimal('child_rate',10,2);
            $table->decimal('child_w_out_bed_rate',10,2);
            $table->decimal('adult_profit',10,2);
            $table->decimal('child_profit',10,2);
            $table->decimal('child_w_out_profit',10,2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_package_prices');
    }
}
