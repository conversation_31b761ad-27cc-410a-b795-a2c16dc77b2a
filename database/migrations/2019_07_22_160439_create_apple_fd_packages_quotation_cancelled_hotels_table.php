<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesQuotationCancelledHotelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_quotation_cancelled_hotels', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('reference_id');
            $table->integer('place_id');
            $table->integer('hotel_id');
            $table->text('cancel_remark')->nullable();
            $table->text('cancel_note')->nullable();
            $table->text('cancel_emails')->nullable();
            $table->integer('email_status')->comment('0 not sent 1 sent');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_quotation_cancelled_hotels');
    }
}
