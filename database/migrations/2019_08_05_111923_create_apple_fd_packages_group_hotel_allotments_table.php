<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesGroupHotelAllotmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_group_hotel_allotments', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('hotel_id');
            $table->integer('no_of_rooms');
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('release_period');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_group_hotel_allotments');
    }
}
