<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAppleFdPackagesAttractionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apple_fd_packages_attractions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('fd_package_id');
            $table->integer('place_id');
            $table->integer('attraction_id');
            $table->string('attraction_type');
            $table->integer('day');
            $table->decimal('adult_rate',10,2);
            $table->decimal('child_rate',10,2);
            $table->decimal('child_w_out_bed_rate',10,2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apple_fd_packages_attractions');
    }
}
